import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';

import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
import { ImpactedRuleModel } from '@models/ImpactedRule';

export type ImpactedRuleModelPagination = PaginationResponseModel<ImpactedRuleModel>;

export class ImpactedRuleApi extends BaseApi {
  static baseUrl = BaseUrl.impactedRules;

  static fetchRuleImpacteds(pagination: PaginationRequestModel<ImpactedRuleModel>) {
    return BaseApi.postData<PaginationResponseModel<ImpactedRuleModel>>(
      `${this.baseUrl}/filter`,
      pagination,
      {},
      {},
      { useLoading: false, useErrorNotification: true },
    );
  }

  static getRuleById(id: number) {
    return BaseApi.getData<ImpactedRuleModel>(`${this.baseUrl}/${id}`);
  }

  static createRuleImpacted(data: ImpactedRuleModel) {
    return BaseApi.postData<ImpactedRuleModel>(`${this.baseUrl}`, data);
  }

  static updateRuleImpacted(id: number, data: ImpactedRuleModel) {
    return BaseApi.putData<ImpactedRuleModel>(`${this.baseUrl}/${id}`, data);
  }
  static deleteRuleImpacteds(ids: number[]) {
    return BaseApi.deleteData<ImpactedRuleModel>(`${this.baseUrl}`, {
      ids,
    });
  }
  static updateRuleImpactedStatus(id: number, data: boolean) {
    return BaseApi.putData<boolean>(`${this.baseUrl}/${id}/status?status=${data}`);
  }
  // static applyRuleImpacteds(ids: number[]) {
  //   return BaseApi.putData<boolean>(`${this.baseUrl}/apply`, {}, { ids });
  // }
  // static formatAndInitRuleImpacteds() {
  //   return BaseApi.postData<ImpactedRuleModel[]>(`${this.baseUrl}/initialize`, undefined, {}, {}, { useLoading: false, useErrorNotification: true });
  // }
}
