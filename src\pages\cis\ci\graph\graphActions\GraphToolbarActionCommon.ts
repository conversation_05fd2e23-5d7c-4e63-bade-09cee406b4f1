import { handleConnectToNode } from '@common/utils/GoJsHelper';
import { CiRelationshipDirectionEnum } from '@models/ImpactedRule';

/**
 * Applies ImpactTo/ImpactBy logic based on checkbox state.
 *
 * @param {go.Diagram | undefined} goDiagram - The GoJS diagram instance.
 * @param {boolean} isImpactTo - Whether the logic is for ImpactTo (true) or ImpactBy (false).
 * @param {boolean} isChecked - Checkbox state (checked or unchecked).
 * @param {boolean} impactBy - Current state of ImpactBy.
 * @param {boolean} impactTo - Current state of ImpactTo.
 */
export const handleImpactToOrImpactBy = (
  goDiagram: go.Diagram | undefined,
  isImpactTo: boolean,
  isChecked: boolean,
  impactBy: boolean,
  impactTo: boolean,
) => {
  const handleImpactTransaction = 'handleImpactTransaction';

  if (!goDiagram) {
    return;
  }

  goDiagram.startTransaction(handleImpactTransaction);

  const allLinks = goDiagram.links;
  const setLinkVisibility = (link: go.Link, isShow: boolean) => {
    if (!link.data?.isUpstream) {
      link.visible = isShow;
    }
  };

  const nodeRoot = goDiagram.findNodesByExample({ isRoot: true }).first();
  if (!nodeRoot) {
    return;
  }

  // Iterate through all the links to identify and show/hide nodes
  allLinks.each((link) => {
    // Get the relationship direction (OUT, IN)
    const direction = link.data?.direction || [];
    // If OUT exists, this means ImpactTo (those impacting the root CI)
    const isAffectedBy = direction.includes(CiRelationshipDirectionEnum.OUT);
    // If IN exists, this means ImpactBy (those impacted by the root CI)
    const affectsTarget = direction.includes(CiRelationshipDirectionEnum.IN);

    let isShow = false;

    // Case where both impactBy, impactTo are true (both checkboxes checked)
    if ((impactBy && isImpactTo && isChecked) || (impactTo && !isImpactTo && isChecked)) {
      isShow = isAffectedBy || affectsTarget;
    }
    // Case when both impactBy and impactTo are false (both checkboxes unchecked)
    else if ((!impactBy && isImpactTo && !isChecked) || (!impactTo && !isImpactTo && !isChecked)) {
      isShow = true;
    }
    // Case when impactTo is true
    else if (isImpactTo) {
      // Show/hide based on the checkbox and link direction
      isShow = isChecked ? isAffectedBy : impactBy ? affectsTarget : false;
    }
    // Case when impactBy is true
    else {
      // Show/hide based on the checkbox and link direction
      isShow = isChecked ? affectsTarget : impactTo ? isAffectedBy : false;
    }

    setLinkVisibility(link, isShow);
  });

  // Update the visibility of nodes based on whether they are connected to the root node
  handleConnectToNode(goDiagram, nodeRoot);

  goDiagram.commitTransaction(handleImpactTransaction);
};
