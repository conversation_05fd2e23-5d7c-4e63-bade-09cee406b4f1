import { CiSdpRequestModel } from './ChangeSdp';
import type { CiRelationshipCountModel } from './CiRelationship';
import type { ConfigItemAttrModel } from './ConfigItemAttr';
import type { ConfigItemAttrCustomModel } from './ConfigItemAttrCustom';
import { CiAttributeInfoModel } from './ConfigItemTypeAttr';

export type CiChangeModel = {
  ciId: number;
  sdpChangeId?: number;
  cmdbChangeId?: number;
  cmdbChangeDraftId?: string;
  sdpChangeLink?: string;
  cmdbChangeLink?: string;
  changeInfo?: CiSdpRequestModel;
};
export type ConfigItemModel = {
  id: number;
  ciTypeId: number;
  name: string;
  description?: string;
  type?: string;
  attachedType?: string;
  author?: string;
  ciAttributes?: CiAttributeInfoModel[];
};

export type ConfigItemInfoModel = {
  ci?: ConfigItemModel;
  attributes: ConfigItemAttrModel[];
  attributeCustoms: ConfigItemAttrCustomModel[];
  historyDescription?: string;
  tempId?: number;
};
export type ConfigItemFullModel = ConfigItemInfoModel;

export type ConfigItemWithCiTypeModel = CiRelationshipCountModel & {
  ciId: number;
  ciName: string;
  ciTypeId: string;
  ciTypeName: string;
};

export type CiInfoModel = {
  ciId: number;
  ciTypeId: number;
  serviceMapId?: number;
};

export default 1;
