import { ServiceMappingApi } from '@api/ServiceMappingApi';
import { NotificationError } from '@common/utils/NotificationUtils';
import { KanbanConfirmModal } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import ViewServiceModal from '@pages/cis/ci/graph/ViewServiceModal';
import React, { useCallback, useState } from 'react';
import { forwardRef, useImperativeHandle } from 'react';

export type ViewServiceMappingDetailMethods = {
  viewServiceMappingDetail: (ciId: number) => void;
};

export const ViewServiceMappingDetail = forwardRef<ViewServiceMappingDetailMethods>((_, ref) => {
  const [currentServiceId, setCurrentServiceId] = useState<number | undefined>();
  const [openedModalServiceMappingDetail, { close: closeModalServiceMappingDetail, open: openModalServiceMappingDetail }] = useDisclosure(false);

  const fetchServiceInfo = useCallback(
    (ciId: number) => {
      if (ciId > 0) {
        ServiceMappingApi.getByCiId(ciId)
          .then((res) => {
            const serviceMapId = res.data.id;
            if (serviceMapId && serviceMapId > 0) {
              setCurrentServiceId(serviceMapId);
              openModalServiceMappingDetail();
            } else {
              NotificationError({
                title: `Error`,
                message: 'Cannot find service map id',
              });
            }
          })
          .catch(() => {})
          .finally(() => {});
      }
    },
    [openModalServiceMappingDetail],
  );

  useImperativeHandle<any, ViewServiceMappingDetailMethods>(
    ref,
    () => ({
      viewServiceMappingDetail: (ciId: number) => {
        fetchServiceInfo(ciId);
      },
    }),
    [fetchServiceInfo],
  );

  return (
    <>
      {/* modal view detail info  of Service mapping */}
      <KanbanConfirmModal
        title={'View service'}
        onConfirm={undefined}
        onClose={closeModalServiceMappingDetail}
        opened={openedModalServiceMappingDetail}
        modalProps={{
          size: '80%',
        }}>
        {currentServiceId && <ViewServiceModal serviceMapId={currentServiceId} activeTab='2' />}
      </KanbanConfirmModal>
    </>
  );
});
ViewServiceMappingDetail.displayName = 'ViewServiceMappingDetail';
export default ViewServiceMappingDetail;
