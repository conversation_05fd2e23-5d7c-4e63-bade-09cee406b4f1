import { useCallback, useEffect, useState } from 'react';
import { Combobox, Loader, ScrollArea, TextInput, useCombobox } from '@mantine/core';
import React from 'react';
import { EntityUserInfoResponse, EntityUserInfoResponsePagingResponse, UsersApi } from '@api/systems/UsersApi';
import type { PaginationRequestModel } from '@models/EntityModelBase';
import type { ApiResponse } from '@core/api/ApiResponse';
import { useDebounceCallback } from 'kanban-design-system';

export type UserPickerProps = {
  onChange?: (value: string) => void;
  value?: string;
  allowInput?: boolean;
  disabled?: boolean;
  disabledLoading?: boolean;
  loadUser?: (pageInfo: PaginationRequestModel<EntityUserInfoResponse>) => Promise<ApiResponse<EntityUserInfoResponsePagingResponse> | null>;
};

const loadUserDefault = (pageInfo: PaginationRequestModel<EntityUserInfoResponse>) => {
  return UsersApi.getAllUserWithPaging(pageInfo);
};

export const UserPicker = (props: UserPickerProps) => {
  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
  });

  const { loadUser = loadUserDefault } = props;

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<EntityUserInfoResponse[] | null>(null);
  const [value, setValue] = useState(props.value ?? '');
  const [isSubmit, setIsSubmit] = useState(false);

  useEffect(() => {
    setValue(props.value ?? '');
  }, [props.value]);

  const fetchOptions = useCallback(
    async (query: string) => {
      setLoading(true);

      try {
        const pageInfo: PaginationRequestModel<EntityUserInfoResponse> = {
          page: 0,
          size: 50,
          search: query || '',
          sortBy: 'userName' as keyof EntityUserInfoResponse,
          isReverse: false,
        };
        const res = await loadUser(pageInfo);
        const data = res?.data;
        setData(data?.content || []);
      } catch (error) {
        /* error */
      } finally {
        setLoading(false);
      }
    },
    [loadUser],
  );

  const inputDebounce = useDebounceCallback(fetchOptions, 500);

  const options = (data || []).map((item) => (
    <Combobox.Option
      value={item.userName}
      key={item.id}
      onClick={(e) => {
        e.stopPropagation();
      }}>
      {item.userName}
    </Combobox.Option>
  ));

  return (
    <Combobox
      onOptionSubmit={(optionValue) => {
        setValue(optionValue);
        if (props.onChange) {
          props.onChange(optionValue);
          setIsSubmit(true);
        }
        combobox.closeDropdown();
      }}
      disabled={props.disabled}
      withinPortal={true}
      store={combobox}>
      <Combobox.Target>
        <TextInput
          placeholder='Search username'
          value={value}
          disabled={props.disabled}
          onChange={(event) => {
            const val = event.currentTarget.value;
            if (props.allowInput && props.onChange) {
              props.onChange(val);
            }

            setValue(val);
            inputDebounce(val);
            setIsSubmit(false);
            combobox.resetSelectedOption();
            combobox.openDropdown();
          }}
          onClick={(e) => {
            e.stopPropagation();
            combobox.openDropdown();
          }}
          onFocus={() => {
            combobox.openDropdown();
            if (!data || data.length === 0) {
              fetchOptions(value);
            }
          }}
          onBlur={() => {
            combobox.closeDropdown();
            if (!props.allowInput) {
              const itemSelect = (data || []).find((x) => x.userName === value);
              const val = itemSelect?.userName || '';
              if (!isSubmit) {
                setValue(val);
                if (props.onChange) {
                  props.onChange(val);
                }
              }
            }
          }}
          rightSection={!props.disabledLoading && loading && <Loader size={18} />}
        />
      </Combobox.Target>

      <Combobox.Dropdown hidden={data === null}>
        <Combobox.Options>
          <ScrollArea.Autosize type='scroll' mah={250}>
            {options}
          </ScrollArea.Autosize>
          {data?.length === 0 && <Combobox.Empty>No results found</Combobox.Empty>}
        </Combobox.Options>
      </Combobox.Dropdown>
    </Combobox>
  );
};

export default UserPicker;
