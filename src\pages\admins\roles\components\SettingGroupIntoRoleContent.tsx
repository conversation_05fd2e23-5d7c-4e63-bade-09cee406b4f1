import { GroupResponse, GroupsApi, UserSettingGroupModel } from '@api/systems/GroupsApi';
import { ColumnType, KanbanIconButton, KanbanTableProps, TableAffactedSafeType } from 'kanban-design-system';
import { KanbanTable, KanbanTableSelectHandleMethods } from 'kanban-design-system';
import { IconX } from '@tabler/icons-react';
import { IconCircleCheck } from '@tabler/icons-react';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import equal from 'fast-deep-equal';
import { tableAffectedToPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import type { RoleResponse } from '@api/SysPermissionOthersApi';
type SettingGroupIntoRoleContentProps = {
  role: RoleResponse;
  isFetched: boolean;
  mountData: (value: boolean) => void;
  onUpdateSelected: (datas: UserSettingGroupModel[]) => void;
  // groupSelecteds?: UserSettingGroupModel[];
  isShowChecked?: boolean;
  isShowActiveColumn: boolean;
  // totalUserSelectDataEmpty?: number;
  selectedItemCheckeds?: UserSettingGroupModel[];
  updateCheckedListByUserAction?: (
    initialList: UserSettingGroupModel[],
    addedItems: UserSettingGroupModel[],
    removedItems: UserSettingGroupModel[],
  ) => void;
};

export type SettingGroupIntoRoleContentMethod = {
  clearSelected: () => void;
  fetchData: () => void;
};

export const SettingGroupIntoRoleContent = forwardRef<SettingGroupIntoRoleContentMethod, SettingGroupIntoRoleContentProps>((props, ref) => {
  const {
    // groupSelecteds,
    isFetched,
    isShowActiveColumn,
    isShowChecked,
    mountData,
    onUpdateSelected,
    role,
    selectedItemCheckeds,
    // totalUserSelectDataEmpty,
    updateCheckedListByUserAction,
  } = props;

  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);

  const [totalRecords, setTotalRecords] = useState(0);
  const [groupResponses, setGroupResponses] = useState<GroupResponse[]>([]);

  const [groupTableAffected, setGroupTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);

  const [currentGroupSelected, setCurrentGroupSelected] = useState<UserSettingGroupModel[]>([]);

  const fetchGroups = useCallback(() => {
    if (!groupTableAffected) {
      return;
    }
    GroupsApi.getAllGroupInRolePageToView(tableAffectedToPaginationRequestModel(groupTableAffected), role.id)
      .then((response) => {
        const data = response.data;
        setGroupResponses(data.content);
        setTotalRecords(data.totalElements);
        mountData(false);
        // isFirstPage.current = false;
      })
      .catch(() => {
        mountData(false);
      });
  }, [groupTableAffected, mountData, role.id]);

  const clearSelected = useCallback(() => {
    tableRef.current?.deselectAll();
  }, []);

  useImperativeHandle<any, SettingGroupIntoRoleContentMethod>(
    ref,
    () => ({
      clearSelected: clearSelected,
      fetchData: fetchGroups,
    }),
    [clearSelected, fetchGroups],
  );

  useEffect(() => {
    if (isShowChecked && selectedItemCheckeds) {
      setCurrentGroupSelected([...selectedItemCheckeds]);
    }
  }, [isShowChecked, selectedItemCheckeds]);

  useEffect(() => {
    if (isFetched) {
      fetchGroups();
    }
  }, [fetchGroups, isFetched]);

  const columns: ColumnType<UserSettingGroupModel>[] = useMemo(
    () => [
      ...(isShowActiveColumn
        ? [
            {
              name: 'groupActiveId',
              title: 'Group in role',
              customRender: (data: number) => {
                return (
                  <KanbanIconButton variant='transparent' c={data ? undefined : 'red'}>
                    {data ? <IconCircleCheck /> : <IconX />}
                  </KanbanIconButton>
                );
              },
            },
          ]
        : []),
      {
        name: 'name',
        title: 'Group name',
        width: '25%',
      },
      { name: 'description', title: 'Description', width: '25%' },
    ],
    [isShowActiveColumn],
  );

  const tableViewListGroupProps: KanbanTableProps<UserSettingGroupModel> = useMemo(() => {
    return {
      title: 'List groups setting',
      columns: columns,
      data: groupResponses,
      pagination: { enable: true },
      searchable: { enable: true, debounceTime: 300 },
      showNumericalOrderColumn: true,
      selectableRows: {
        enable: true,
        onSelectedRowsChanged(rows) {
          const oldCheckedItems = [...(selectedItemCheckeds ?? [])];
          const oldCheckedItemIds = selectedItemCheckeds?.map((item) => item.id) ?? [];
          const oldCheckedItemIdOfRows = rows.map((item) => item.id);

          const addedItems = rows.filter((item) => !oldCheckedItemIds.includes(item.id));

          const removedItems = oldCheckedItems.filter((item) => !oldCheckedItemIdOfRows.includes(item.id));
          if (updateCheckedListByUserAction) {
            updateCheckedListByUserAction(selectedItemCheckeds ?? [], addedItems, removedItems);
          }
          onUpdateSelected(rows);
        },
        crossPageSelected: {
          rowKey: 'id',
          selectedRows: currentGroupSelected,
          setSelectedRows: setCurrentGroupSelected,
        },
      },
      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(groupTableAffected, dataSet)) {
            setGroupTableAffected(dataSet);
            mountData(true);
          }
        },
      },
    };
  }, [
    columns,
    currentGroupSelected,
    groupResponses,
    groupTableAffected,
    mountData,
    onUpdateSelected,
    selectedItemCheckeds,
    totalRecords,
    updateCheckedListByUserAction,
  ]);

  return <KanbanTable ref={tableRef} {...tableViewListGroupProps} />;
});
SettingGroupIntoRoleContent.displayName = 'SettingGroupIntoRoleContent';
export default SettingGroupIntoRoleContent;
