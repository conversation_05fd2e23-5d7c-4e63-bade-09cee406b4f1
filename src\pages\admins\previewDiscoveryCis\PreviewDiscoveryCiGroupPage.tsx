import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  ColumnType,
  KanbanButton,
  KanbanTableSelectHandleMethods,
  KanbanText,
  KanbanTooltip,
  renderDateTime,
  type TableAffactedSafeType,
} from 'kanban-design-system';
import { KanbanTable, type KanbanTableProps } from 'kanban-design-system';
import { IconEye } from '@tabler/icons-react';
import { KanbanIconButton } from 'kanban-design-system';
import { Badge, Group, Tooltip } from '@mantine/core';
import { DiscoveryPreviewDataCiApi } from '@api/discovery/DiscoveryPreviewDataCiApi';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import { DiscoveryMethodEnum, DiscoveryPreviewDataCiGroupModel } from '@models/DiscoveryPreviewDataCi';
import equal from 'fast-deep-equal';
import { useNavigate } from 'react-router-dom';
import { BreadcrumbComponent } from '../breadcrumb/BreadcrumbComponent';
import { buildDiscoveryPreviewDataCiGroupDetailPath, buildJobDiscoveryConfigUrl } from '@common/utils/RouterUtils';
import { formatSmartNumber } from '@common/utils/CommonUtils';
import { JobDiscoveryAction } from '@common/constants/JobDiscoveryActionEnum';
import { MAX_NUMBER_LENGTH, MAX_TEXT_LENGTH } from '@common/constants/FieldLengthConstants';

type DiscoveryMethodColorMap = {
  [key in DiscoveryMethodEnum]: string;
};

export const mapDiscoveryMethodColor: DiscoveryMethodColorMap = {
  [DiscoveryMethodEnum.JOB]: 'lime',
  [DiscoveryMethodEnum.MANUAL]: 'yellow',
};

export const getPreviewJobName = (data: string | null | undefined, rowData: DiscoveryPreviewDataCiGroupModel) => {
  if (data) {
    return data;
  }
  if (rowData.jobHistoryId) {
    return '(Deleted)';
  }
  return '(None)';
};

export const PreviewDiscoveryCiGroupPage: React.FC = () => {
  const navigate = useNavigate();

  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType>();
  const [listDciGroup, setListDciGroup] = useState<DiscoveryPreviewDataCiGroupModel[]>([]);
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);

  const fetchListDcis = useCallback(
    (controller?: AbortController) => {
      if (!tableAffected) {
        return;
      }

      const dataSend = tableAffectedToMultiColumnFilterPaginationRequestModel<DiscoveryPreviewDataCiGroupModel>(
        tableAffected.sortedBy ? tableAffected : { ...tableAffected, sortedBy: 'createdDate', isReverse: true },
      );

      DiscoveryPreviewDataCiApi.getAllDiscoveryPreviewDataCiGroup(dataSend, controller)
        .then((res) => {
          if (res.data) {
            const data = res.data;
            if (data) {
              setListDciGroup(data.content);
              setTotalRecords(data.totalElements);
            }
          }
        })
        .catch(() => {});
    },
    [tableAffected],
  );

  const columns: ColumnType<DiscoveryPreviewDataCiGroupModel>[] = useMemo(() => {
    return [
      {
        title: 'History Id',
        name: 'jobHistoryId',
        width: '3%',
        advancedFilter: {
          variant: 'number',
          filterModes: ['equals', 'notEquals', 'greaterThan', 'greaterThanOrEqualTo', 'lessThan', 'lessThanOrEqualTo'],
          customProps: { maxLength: MAX_NUMBER_LENGTH },
        },
        customRender: (data: number) => {
          return (
            <KanbanButton size='compact-xs' variant={'subtle'}>
              <KanbanText>{data || `__`}</KanbanText>
            </KanbanButton>
          );
        },
      },
      {
        title: 'Time',
        name: 'createdDate',
        width: '8%',
        customRender: renderDateTime,
        advancedFilter: {
          variant: 'date',
          customProps: {
            popoverProps: {
              withinPortal: false,
            },
          },
        },
      },
      {
        title: 'Schedule',
        name: 'jobName',
        width: '15%',
        customRender: (data: string, rowData: DiscoveryPreviewDataCiGroupModel) => {
          const previewJobName = getPreviewJobName(data, rowData);
          const isDeleted = previewJobName === '(Deleted)';
          const color = isDeleted ? 'red' : undefined;
          const methodColor = mapDiscoveryMethodColor[rowData.discoveryMethod];

          return (
            <Group justify='space-between' gap={'xs'} maw={'230px'}>
              <KanbanButton
                size='compact-xs'
                variant={'subtle'}
                maw={'80%'}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  if (rowData.jobId && rowData.jobName) {
                    window.open(buildJobDiscoveryConfigUrl(rowData.jobId || 0, JobDiscoveryAction.UPDATE), '_blank');
                  }
                }}>
                <KanbanTooltip label={previewJobName}>
                  <KanbanText truncate maw={'100%'} c={color}>
                    {previewJobName}
                  </KanbanText>
                </KanbanTooltip>
              </KanbanButton>
              <Badge color={methodColor} radius='sm' size='xs' mb={0}>
                {rowData.discoveryMethod}
              </Badge>
            </Group>
          );
        },
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Target(CI Type)',
        name: 'ciType',
        customRender: (data: any) => {
          return (
            <KanbanTooltip label={data}>
              <KanbanText lineClamp={2}>{data}</KanbanText>
            </KanbanTooltip>
          );
        },
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Data Source',
        name: 'dataSourceName',
        width: '10%',
        customRender: (data: any) => {
          return (
            <KanbanTooltip label={data}>
              <KanbanText truncate maw={'100px'}>
                {data}
              </KanbanText>
            </KanbanTooltip>
          );
        },
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Mapping/ Transform',
        name: 'transformMap',
        width: '10%',
        customRender: (data: any) => {
          return (
            <KanbanTooltip label={data}>
              <KanbanText lineClamp={2}>{data}</KanbanText>
            </KanbanTooltip>
          );
        },
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Run By',
        name: 'runBy',
        width: '5%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Run Time(seconds)',
        name: 'runTime',
        width: '5%',
        sortable: false,
        customRenderHeader: () => (
          <>
            <KanbanText truncate='end' fw={700}>
              Run Time
            </KanbanText>
            <KanbanText truncate='end' fw={700}>
              (seconds)
            </KanbanText>
          </>
        ),
        customRender: (data: any) => {
          return <KanbanText lineClamp={2}>{data ? `${formatSmartNumber(data)} (s)` : ''}</KanbanText>;
        },
        advancedFilter: {
          enable: false,
        },
      },
      {
        title: 'Total',
        name: 'summaryDetail',
        width: '20%',
        sortable: false,
        customRender: (data: any) => {
          return (
            <KanbanTooltip label={data}>
              <KanbanText lineClamp={3}>{data}</KanbanText>
            </KanbanTooltip>
          );
        },
        advancedFilter: {
          enable: false,
        },
      },
    ];
  }, []);

  const tableProps: KanbanTableProps<DiscoveryPreviewDataCiGroupModel> = useMemo(() => {
    return {
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 1000,
      },
      sortable: {
        enable: true,
      },
      advancedFilterable: {
        enable: true,
        debounceTime: 1000,
        resetOnClose: true,
        compactMode: true,
      },
      onRowClicked: (rowData: DiscoveryPreviewDataCiGroupModel) => {
        navigate(buildDiscoveryPreviewDataCiGroupDetailPath(rowData.jobHistoryId, rowData.summaryId));
      },
      columns: columns,
      data: listDciGroup,
      key: columns,
      serverside: {
        totalRows: totalRecords,
        onTableAffected(dataSet) {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
      pagination: {
        enable: true,
      },
      actions: {
        customAction: (rowData: DiscoveryPreviewDataCiGroupModel) => {
          return (
            <>
              <Tooltip label='View detail'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    navigate(buildDiscoveryPreviewDataCiGroupDetailPath(rowData.jobHistoryId, rowData.summaryId));
                  }}>
                  <IconEye />
                </KanbanIconButton>
              </Tooltip>
            </>
          );
        },
      },
    };
  }, [columns, listDciGroup, navigate, tableAffected, totalRecords]);

  useEffect(() => {
    const controller = new AbortController();
    fetchListDcis(controller);
    return () => controller.abort();
  }, [fetchListDcis]);

  return (
    <>
      <BreadcrumbComponent />
      <KanbanTable ref={tableRef} {...tableProps} />
    </>
  );
};
