import { Pill, Text } from '@mantine/core';
import React from 'react';
import { DragTranfromMapType } from '@models/DiscoveryTransformMap';
import { DropableItemBase } from './DropableItemBase';

interface SourceColumnItemProps {
  item: DragTranfromMapType;
  classes: Record<string, string>;
  index: number;
  onClick: () => void;
  dragableRef?: React.Ref<HTMLDivElement | null>;
}

export const SourceColumnMapItem = React.memo(({ classes, dragableRef, index, item, onClick }: SourceColumnItemProps) => {
  return (
    <DropableItemBase
      dragableRef={dragableRef}
      item={item}
      index={index}
      classes={classes}
      onClick={onClick}
      draggableIdPrefix='sourceMap'
      paperStyle={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}
      renderContent={(item) => (
        <>
          <Text w={200} truncate='end'>
            {item.isStatic ? item.staticValue : item.label}
          </Text>
          <Pill
            style={{
              backgroundColor: item.isStatic ? '#228be6' : '#40c057',
              color: 'white',
            }}>
            {item.isStatic ? 'static' : 'staging Table'}
          </Pill>
        </>
      )}
    />
  );
});

SourceColumnMapItem.displayName = 'SourceColumnMapItem';
