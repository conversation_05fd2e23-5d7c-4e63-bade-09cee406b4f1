import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import SelectCiTypeComponent from '@components/commonCi/SelectCiTypeComponent';
import { KanbanButton } from 'kanban-design-system';
import { KanbanIconButton } from 'kanban-design-system';
import { KanbanCheckbox } from 'kanban-design-system';
import { KanbanInput } from 'kanban-design-system';
import { KanbanTextarea } from 'kanban-design-system';
import { KanbanModal } from 'kanban-design-system';
import { KanbanSelect } from 'kanban-design-system';
import { KanbanTable } from 'kanban-design-system';
import { ActionIcon, Container, Divider, Flex, Group, InputBase, Pill, Stack } from '@mantine/core';
import { UseFormReturnType, useForm } from '@mantine/form';
import { useDisclosure } from '@mantine/hooks';
import { CiTypeAttributeDataType } from '@models/CiType';
import type { CiTypeRelationAttrModel, CiTypeRelationModel } from '@models/CiTypeRelation';
import type { ConfigItemTypeInfoModel } from '@models/ConfigItemTypeInfo';
import { ciRelationshipTypesSlice, getCiRelationshipTypes } from '@slices/CiRelationshipTypesSlice';
import { IconAffiliate, IconCircleMinus, IconCirclePlus, IconEdit, IconEye, IconPlus, IconTrash } from '@tabler/icons-react';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { v4 as uuid } from 'uuid';
import type { ConfigItemOptionPickerModel } from './CiTypeAttributeDetail';
import { KanbanConfirmModal } from 'kanban-design-system';
import { KanbanText } from 'kanban-design-system';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';

export type CiTypeRelationshipProps = {
  ciTypeId: number;
  setCiTypeInfoUpdate: React.Dispatch<React.SetStateAction<ConfigItemTypeInfoModel>>;
  setIsChange: (value: boolean) => void;
};

type FormAddOption = {
  label: string;
  value: string;
};

type FormCreateAttribute = {
  name: string;
  type: string;
  description: string;
};

type FormAddRelationship = {
  toCiType: number;
  relationshipId: number;
};

const validateRequired = (value: string | number | undefined) => {
  if (!value || value === 0) {
    return 'This field is required';
  }
  return undefined; // No error
};

const validateText = (value: string | undefined) => {
  const trimValue = value?.trim() || '';
  if (!trimValue) {
    return 'This field is required';
  }
  return undefined; // No error
};

export const ConfigItemTypeRelation = (props: CiTypeRelationshipProps) => {
  const { ciTypeId, setCiTypeInfoUpdate, setIsChange } = props;
  const dispatch = useDispatch();
  const formAddOptionRef = useRef<UseFormReturnType<FormAddOption> | null>(null);
  const formCreateAttrRef = useRef<UseFormReturnType<FormCreateAttribute> | null>(null);

  const [ciTypeRelations, setCiTypeRelations] = useState<CiTypeRelationModel[]>([]);
  const [ciTypeRelationsUpdate, setCiTypeRelationsUpdate] = useState<CiTypeRelationModel[]>([]);
  const [ciTypeRelationsRemove, setCiTypeRelationsRemove] = useState<CiTypeRelationModel[]>([]);

  const [allCiTypeRelationAttr, setAllCiTypeRelationAttr] = useState<CiTypeRelationAttrModel[]>([]);

  const [ciTypeRelationAttrs, setCiTypeRelationAttrs] = useState<CiTypeRelationAttrModel[]>([]);
  const [ciTypeRelationAttrsUpdate, setCiTypeRelationAttrsUpdate] = useState<CiTypeRelationAttrModel[]>([]);
  const [ciTypeRelationAttrsRemove, setCiTypeRelationAttrsRemove] = useState<CiTypeRelationAttrModel[]>([]);

  const [allCiTypeRelationAttrsUpdate, setAllCiTypeRelationAttrsUpdate] = useState<CiTypeRelationAttrModel[]>([]);
  const [allCiTypeRelationAttrsRemove, setAllCiTypeRelationAttrsRemove] = useState<CiTypeRelationAttrModel[]>([]);

  const [openedModalCreateNew, { close: closeModalCreateNew, open: openModalCreateNew }] = useDisclosure(false);
  const [isOpenDeleteItemAttr, { close: closeDeleteItemAttr, open: openDeleteItemAttr }] = useDisclosure(false);
  const [isOpenDeleteItem, { close: closeDeleteItem, open: openDeleteItem }] = useDisclosure(false);
  const [isInverse, setIsInverse] = useState(false);
  const [allowEdit, setAllowEdit] = useState(true);
  const [showAddAttribute, setShowAddAttribute] = useState(false);
  const [selectedRelationObject, setSelectedRelationObject] = useState<CiTypeRelationModel>({
    id: 0,
    fromCiType: 0,
    toCiType: 0,
    relationshipId: 0,
    referCiTypeId: 0,
    tempId: uuid(),
  });
  const [newItem, setNewItem] = useState<CiTypeRelationModel>({
    id: 0,
    fromCiType: 0,
    toCiType: 0,
    relationshipId: 0,
    referCiTypeId: 0,
  });
  const [newItemAttr, setNewItemAttr] = useState<CiTypeRelationAttrModel>({
    id: 0,
    ciTypeRelationshipId: 0,
    name: '',
    type: CiTypeAttributeDataType.TEXT,
  });
  const [currentDeleteAttr, setCurrentDeleteAttr] = useState<CiTypeRelationAttrModel>({
    id: 0,
    ciTypeRelationshipId: 0,
    name: '',
  });
  const [currentDeleteItem, setCurrentDeleteItem] = useState<CiTypeRelationModel>({
    id: 0,
    fromCiType: 0,
    toCiType: 0,
    relationshipId: 0,
    referCiTypeId: 0,
  });

  // for add picker option
  const [optionLabel, setOptionLabel] = useState('');
  const [optionContent, setOptionContent] = useState('');
  const [pillData, setPillData] = useState<ConfigItemOptionPickerModel[]>([]);

  const ciColumnTypeList: string[] = Object.values(CiTypeAttributeDataType).filter((x) => CiTypeAttributeDataType.REFERENCE !== x);

  const fetchCiTypeRelation = useCallback(() => {
    if (ciTypeId > 0) {
      ConfigItemTypeApi.getAllRelationships(ciTypeId)
        .then((res) => {
          const resData = (res.data || []).map((item) => ({
            ...item,
            tempId: uuid(),
          }));
          setCiTypeRelations(resData);
        })
        .catch(() => {});

      ConfigItemTypeApi.getAllRelationshipAttributes(ciTypeId)
        .then((res) => {
          const resData = (res.data || []).map((item) => ({
            ...item,
            tempId: uuid(),
          }));
          setAllCiTypeRelationAttr(resData);
        })
        .catch(() => {});
    }
  }, [ciTypeId]);

  useEffect(() => {
    fetchCiTypeRelation();
  }, [fetchCiTypeRelation]);

  const ciRelationshipTypes = useSelector(getCiRelationshipTypes)?.data;

  const listRelationshipCombobox = useMemo(() => {
    return ciRelationshipTypes.map((item) => {
      return {
        value: `${item.id}`,
        label: `${item.type} - ${item.inverseType}`,
      };
    });
  }, [ciRelationshipTypes]);

  useEffect(() => {
    dispatch(ciRelationshipTypesSlice.actions.fetchForEmpty());
  }, [dispatch]);

  const ciTypes = useGetCiTypes();

  useEffect(() => {
    setCiTypeInfoUpdate((prevConfigInfo) => ({
      ...prevConfigInfo,
      relationshipsUpdate: ciTypeRelationsUpdate,
    }));
  }, [ciTypeRelationsUpdate, setCiTypeInfoUpdate]);

  useEffect(() => {
    setCiTypeInfoUpdate((prevConfigInfo) => ({
      ...prevConfigInfo,
      relationshipsDelete: ciTypeRelationsRemove,
    }));
  }, [ciTypeRelationsRemove, setCiTypeInfoUpdate]);

  useEffect(() => {
    setCiTypeInfoUpdate((prevConfigInfo) => ({
      ...prevConfigInfo,
      relationshipAttributeUpdate: allCiTypeRelationAttrsUpdate,
    }));
  }, [allCiTypeRelationAttrsUpdate, setCiTypeInfoUpdate]);

  useEffect(() => {
    setCiTypeInfoUpdate((prevConfigInfo) => ({
      ...prevConfigInfo,
      relationshipAttributeDelete: allCiTypeRelationAttrsRemove,
    }));
  }, [allCiTypeRelationAttrsRemove, setCiTypeInfoUpdate]);

  const onChangeSelectCiType = (e: number | React.FormEvent<HTMLDivElement> | undefined, name: string) => {
    let targetValue = 0;
    if (typeof e === 'number') {
      targetValue = e;
    }
    setNewItem((prev) => {
      return {
        ...prev,
        [name]: targetValue,
      };
    });
  };

  // handle create/update ci type relationship
  const onCreateUpdateRelation = () => {
    form.validate();
    if (!form.isValid()) {
      return;
    }

    const dataNew = { ...newItem };
    dataNew.fromCiType = ciTypeId;
    if (isInverse) {
      dataNew.fromCiType = dataNew.toCiType;
      dataNew.toCiType = ciTypeId;
    }

    const fromCiTypeObject = (ciTypes.data || []).find((x) => x.id === dataNew.fromCiType);
    const toCiTypeNameObject = (ciTypes.data || []).find((x) => x.id === dataNew.toCiType);

    dataNew.fromCiTypeName = fromCiTypeObject ? fromCiTypeObject.name : '';
    dataNew.toCiTypeName = toCiTypeNameObject ? toCiTypeNameObject.name : '';

    // change data view
    const index = ciTypeRelations.findIndex((item) => item.tempId === newItem.tempId);

    if (index !== -1) {
      const updatedItems = [...ciTypeRelations];
      updatedItems[index] = dataNew;
      setCiTypeRelations(updatedItems);
    } else {
      dataNew.tempId = selectedRelationObject.tempId;
      setCiTypeRelations([dataNew, ...ciTypeRelations]);
    }

    // save draft change relationship
    changeCiTypeRelationsUpdate(dataNew);
    // save data draft attribute
    onSaveChangeAttribute();

    onCloseModalCreate();
  };

  const changeCiTypeRelationsUpdate = (newItem: CiTypeRelationModel) => {
    const index = ciTypeRelationsUpdate.findIndex((item) => item.tempId === newItem.tempId);

    if (index !== -1) {
      const updatedItems = [...ciTypeRelationsUpdate];
      updatedItems[index] = newItem;
      setCiTypeRelationsUpdate(updatedItems);
    } else {
      setCiTypeRelationsUpdate([...ciTypeRelationsUpdate, newItem]);
    }
    setIsChange(true);
  };

  const onDeleteRelation = () => {
    const obj = { ...currentDeleteItem };
    setCiTypeRelations((prev) => {
      return prev.filter((x) => x.tempId !== obj.tempId);
    });
    changeCiTypeRelationsRemove(obj);
  };

  const changeCiTypeRelationsRemove = (newItem: CiTypeRelationModel) => {
    if (newItem.id > 0) {
      const index = ciTypeRelationsRemove.findIndex((item) => item.tempId === newItem.tempId);

      if (index !== -1) {
        const updatedItems = [...ciTypeRelationsRemove];
        updatedItems[index] = newItem;
        setCiTypeRelationsRemove(updatedItems);
      } else {
        setCiTypeRelationsRemove([...ciTypeRelationsRemove, newItem]);
      }
    } else {
      setCiTypeRelationsUpdate((prev) => {
        return prev.filter((x) => x.tempId !== newItem.tempId);
      });
    }
    setIsChange(true);
  };

  const onCloseModalCreate = () => {
    closeModalCreateNew();

    // reset for relationship
    setNewItem({
      id: 0,
      fromCiType: 0,
      toCiType: 0,
      relationshipId: 0,
      referCiTypeId: 0,
    });
    setIsInverse(false);

    // reset for relationship attribute
    setSelectedRelationObject({
      id: 0,
      fromCiType: 0,
      toCiType: 0,
      relationshipId: 0,
      referCiTypeId: 0,
      tempId: uuid(),
    });
    setCiTypeRelationAttrs([]);

    setCiTypeRelationAttrsUpdate([]);
    setCiTypeRelationAttrsRemove([]);
    setAllowEdit(true);
    setShowAddAttribute(false);
  };

  const { setValues, ...form } = useForm<FormAddRelationship>({
    initialValues: {
      toCiType: 0,
      relationshipId: 0,
    },
    validate: {
      toCiType: validateRequired,
      relationshipId: validateRequired,
    },
  });

  useEffect(() => {
    if (setValues) {
      setValues({ toCiType: newItem.toCiType, relationshipId: newItem.relationshipId });
    }
  }, [newItem, setValues]);

  const formCreateAttr = useForm<FormCreateAttribute>({
    initialValues: {
      name: '',
      type: '',
      description: '',
    },
    validate: {
      name: validateText,
      type: validateRequired,
    },
  });

  useEffect(() => {
    if (formCreateAttrRef.current) {
      const { setValues } = formCreateAttrRef.current;
      setValues({
        name: newItemAttr.name,
        type: newItemAttr.type,
        description: newItemAttr.description,
      });
    }
  }, [newItemAttr]);

  useEffect(() => {
    formCreateAttrRef.current = formCreateAttr;
  }, [formCreateAttr]);

  const onChangeSelect = (val: string | null, name: string) => {
    setNewItemAttr((prev) => {
      return {
        ...prev,
        [name]: val,
      };
    });
  };

  const handleEditAttr = (objAttribute: CiTypeRelationAttrModel) => {
    setNewItemAttr(objAttribute);
    const arrayOption = objAttribute.options ? JSON.parse(objAttribute.options) : [];
    setPillData(arrayOption);
    setShowAddAttribute(true);
  };

  const handleViewRelation = (objRelation: CiTypeRelationModel) => {
    setAllowEdit(false);
    handleEditRelation(objRelation);
  };

  const handleEditRelation = (objRelation: CiTypeRelationModel) => {
    let toCiId = objRelation.toCiType;
    if (toCiId === ciTypeId) {
      toCiId = objRelation.fromCiType;
      setIsInverse(true);
    }

    setNewItem({
      id: objRelation.id,
      fromCiType: 0,
      toCiType: toCiId,
      relationshipId: objRelation.relationshipId,
      tempId: objRelation.tempId,
      referCiTypeId: objRelation.referCiTypeId,
    });

    // view attribute
    const currentCiTypeRelationAttr = allCiTypeRelationAttr.filter(
      (e) => (e.ciTypeRelationshipId !== 0 && e.ciTypeRelationshipId === objRelation.id) || e.referId === objRelation.tempId,
    );
    setCiTypeRelationAttrs(currentCiTypeRelationAttr);
    setSelectedRelationObject(objRelation);

    if (objRelation.referCiTypeId !== 0 && objRelation.referCiTypeId !== ciTypeId) {
      setAllowEdit(false);
    }

    openModalCreateNew();
  };

  // delete relationship attribute
  const onDeleteAttribute = () => {
    const obj = { ...currentDeleteAttr };
    // data current view
    setCiTypeRelationAttrs((prev) => {
      return prev.filter((x) => x.tempId !== obj.tempId);
    });
    obj.referId = selectedRelationObject.tempId;
    changeCiTypeRelationAttributesRemove([obj]);
  };

  const changeCiTypeRelationAttributesRemove = (newItems: CiTypeRelationAttrModel[]) => {
    // update list data remove
    const updatedItems = [...ciTypeRelationAttrsRemove];

    newItems.forEach((newItem) => {
      const index = updatedItems.findIndex((item) => item.tempId === newItem.tempId);

      if (index !== -1) {
        updatedItems[index] = newItem;
      } else {
        updatedItems.push(newItem);
      }
    });

    setCiTypeRelationAttrsRemove(updatedItems);

    // remove in list create/update
    setCiTypeRelationAttrsUpdate((prev) => {
      return prev.filter((x) => !newItems.some((newItem) => newItem.tempId === x.tempId));
    });
  };

  // action create/update relationship attribute
  const onUpdateAttribute = () => {
    formCreateAttr.validate();
    if (!formCreateAttr.isValid()) {
      return;
    }

    const indexDuplicate = ciTypeRelationAttrs.findIndex(
      (item) => item.tempId !== newItemAttr.tempId && item.name.toLowerCase() === newItemAttr.name.toLowerCase(),
    );
    if (indexDuplicate !== -1) {
      formCreateAttr.setFieldError('name', 'Duplicate value detected');
      return;
    }

    if (CiTypeAttributeDataType.PICK_LIST === newItemAttr.type && !(pillData && pillData.length > 0)) {
      formCreateAttr.setFieldError('type', 'The PICK_LIST type must have a list of items');
      return;
    }

    const dataNew = { ...newItemAttr, name: newItemAttr.name.trim() };

    // change data view
    if (dataNew.tempId) {
      const updatedItems = ciTypeRelationAttrs.map((item) => (item.tempId === dataNew.tempId ? dataNew : item));
      setCiTypeRelationAttrs(updatedItems);
    } else {
      dataNew.tempId = uuid();
      setCiTypeRelationAttrs([dataNew, ...ciTypeRelationAttrs]);
    }

    // save draft change
    dataNew.referId = selectedRelationObject.tempId;
    dataNew.ciTypeRelationshipId = selectedRelationObject.id;
    changeCiTypeRelationAttrsUpdate(dataNew);

    // reset data
    setNewItemAttr({
      id: 0,
      ciTypeRelationshipId: 0,
      name: '',
      type: CiTypeAttributeDataType.TEXT,
    });
    setPillData([]);
  };

  const changeCiTypeRelationAttrsUpdate = (newItem: CiTypeRelationAttrModel) => {
    const index = ciTypeRelationAttrs.findIndex((item) => item.tempId === newItem.tempId);

    if (index !== -1) {
      const updatedItems = [...ciTypeRelationAttrsUpdate];
      updatedItems[index] = newItem;
      setCiTypeRelationAttrsUpdate(updatedItems);
    } else {
      setCiTypeRelationAttrsUpdate([...ciTypeRelationAttrsUpdate, newItem]);
    }
  };

  const onSaveChangeAttribute = () => {
    const newObjectUpdate = [...allCiTypeRelationAttrsUpdate];
    const newObjectsView = [...allCiTypeRelationAttr];
    ciTypeRelationAttrsUpdate.forEach((item1) => {
      // save draft attribute update
      const index = allCiTypeRelationAttrsUpdate.findIndex((item2) => item2.tempId === item1.tempId);
      if (index !== -1) {
        newObjectUpdate[index] = { ...item1 };
      } else {
        newObjectUpdate.push({ ...item1 });
      }

      // save draft view data
      const index2 = allCiTypeRelationAttr.findIndex((item2) => item2.tempId === item1.tempId);
      if (index2 !== -1) {
        newObjectsView[index2] = { ...item1 };
      } else {
        newObjectsView.push({ ...item1 });
      }
    });

    setAllCiTypeRelationAttr(newObjectsView);
    setAllCiTypeRelationAttrsUpdate(newObjectUpdate);

    ciTypeRelationAttrsRemove.forEach((item1) => {
      if (item1.id > 0) {
        // save draft attribute remove
        const index = allCiTypeRelationAttrsRemove.findIndex((item2) => item2.tempId === item1.tempId);
        if (index !== -1) {
          const updatedObjects = [...allCiTypeRelationAttrsRemove];
          updatedObjects[index] = { ...item1 };
          setAllCiTypeRelationAttrsRemove(updatedObjects);
        } else {
          setAllCiTypeRelationAttrsRemove((prev) => [...prev, { ...item1 }]);
        }
      }

      setAllCiTypeRelationAttr((prev) => {
        return prev.filter((x) => x.tempId !== item1.tempId);
      });
    });
    setIsChange(true);
  };

  const pills = pillData.map((data) => (
    <Pill key={data.value} withRemoveButton onRemove={() => handleRemoveOption(data.value)}>
      {data.label}
    </Pill>
  ));

  const handleRemoveOption = (val: string) => {
    const updatedPillData = pillData.filter((data) => data.value !== val);
    setPillData(updatedPillData);
  };

  const formAddOption = useForm<FormAddOption>({
    initialValues: {
      label: '',
      value: '',
    },

    validate: {
      label: validateText,
      value: validateText,
    },
  });

  useEffect(() => {
    formAddOptionRef.current = formAddOption;
  }, [formAddOption]);

  useEffect(() => {
    if (formAddOptionRef.current) {
      const { setValues } = formAddOptionRef.current;

      const nameInput = optionLabel ? optionLabel.trim() : '';
      const valueInput = optionContent ? optionContent.trim() : '';
      setValues({ label: nameInput, value: valueInput });
    }
  }, [optionLabel, optionContent]);

  const handleAddOption = () => {
    formAddOption.validate();
    if (!formAddOption.isValid()) {
      return;
    }
    const nameFilter = pillData.find((x) => x.label.toUpperCase() === optionLabel.toUpperCase());
    if (nameFilter) {
      formAddOption.setFieldError('label', 'Duplicate value detected (case-insensitive), cannot add.');
      return;
    }
    const valueFilter = pillData.find((x) => x.value.toUpperCase() === optionContent.toUpperCase());
    if (valueFilter) {
      formAddOption.setFieldError('value', 'Duplicate value detected (case-insensitive), cannot add.');
      return;
    }
    setPillData((ob) => [...ob, { label: optionLabel, value: optionContent }]);
    setOptionLabel('');
    setOptionContent('');
  };

  useEffect(() => {
    const optionsStr = pillData ? JSON.stringify(pillData) : '';
    setNewItemAttr((prev) => {
      return {
        ...prev,
        options: optionsStr,
      };
    });
  }, [pillData]);

  const onCancelUpdateAttribute = () => {
    setNewItemAttr({
      id: 0,
      ciTypeRelationshipId: 0,
      name: '',
      type: CiTypeAttributeDataType.TEXT,
    });
    setPillData([]);
    setShowAddAttribute(false);
  };

  const onDeleteMultiItems = (items: CiTypeRelationAttrModel[]) => {
    if (!(items && items.length > 0)) {
      return;
    }
    items.forEach((item) => {
      const obj = { ...item };
      // data current view
      setCiTypeRelationAttrs((prev) => {
        return prev.filter((x) => x.tempId !== obj.tempId);
      });
      item.referId = selectedRelationObject.tempId;
    });
    changeCiTypeRelationAttributesRemove(items);
  };

  useEffect(() => {
    if (!showAddAttribute) {
      onCancelUpdateAttribute();
    }
  }, [showAddAttribute]);

  return (
    <>
      {/* Modal create/update relationship */}
      <KanbanModal
        size={'lg'}
        opened={openedModalCreateNew}
        onClose={onCloseModalCreate}
        title={allowEdit ? 'Create/update relationship' : 'View relationship'}
        actions={allowEdit ? <KanbanButton onClick={onCreateUpdateRelation}>Save Draft</KanbanButton> : <></>}>
        <KanbanSelect
          searchable
          label='Relationship'
          withAsterisk
          {...form.getInputProps('relationshipId')}
          value={`${newItem.relationshipId}`}
          data={listRelationshipCombobox}
          disabled={!allowEdit}
          onChange={(value) => {
            setNewItem((prev) => {
              const current = { ...prev };
              current.relationshipId = value ? Number(value) : 0;
              return current;
            });
          }}></KanbanSelect>

        <KanbanCheckbox
          label='Inverse relationship'
          disabled={!allowEdit}
          checked={isInverse}
          onChange={(e) => {
            const value = e.target.checked;
            setIsInverse(value);
          }}
        />

        <SelectCiTypeComponent
          label='CI Type'
          withAsterisk
          disabled={!allowEdit}
          {...form.getInputProps('toCiType')}
          onChange={(e) => {
            onChangeSelectCiType(e, 'toCiType');
          }}
        />

        {/* Create new attribute */}
        <Container mt={'xl'} p={0} fluid style={{ backgroundColor: 'var(--mantine-color-gray-0)' }}>
          <Group gap={5}>
            <KanbanText tt='uppercase' c='dimmed'>
              Relationship Attribute(s)
            </KanbanText>
            {allowEdit && (
              <>
                {showAddAttribute ? (
                  <IconCircleMinus
                    color='var(--mantine-color-dimmed)'
                    onClick={() => {
                      setShowAddAttribute(!showAddAttribute);
                    }}
                  />
                ) : (
                  <IconCirclePlus
                    color='var(--mantine-color-dimmed)'
                    onClick={() => {
                      setShowAddAttribute(!showAddAttribute);
                    }}
                  />
                )}
              </>
            )}
          </Group>
          {showAddAttribute && (
            <>
              <Group align='top' mt={'ld'}>
                <Stack gap={0} w={'45%'}>
                  <KanbanInput
                    label='Name'
                    maxLength={255}
                    withAsterisk
                    {...formCreateAttr.getInputProps('name')}
                    value={newItemAttr.name || ''}
                    onChange={(e) => {
                      const { target } = e;
                      setNewItemAttr((prev) => {
                        return {
                          ...prev,
                          name: target.value,
                        };
                      });
                    }}
                  />
                  <KanbanSelect
                    label='Field Type'
                    placeholder='Pick value'
                    withAsterisk
                    {...formCreateAttr.getInputProps('type')}
                    value={newItemAttr.type}
                    data={ciColumnTypeList}
                    disabled={newItemAttr.id > 0}
                    allowDeselect={false}
                    onChange={(e) => {
                      onChangeSelect(e, 'type');
                    }}
                  />
                </Stack>
                <KanbanTextarea
                  label='Description'
                  w={'50%'}
                  maxLength={2000}
                  value={newItemAttr.description || ''}
                  onChange={(e) => {
                    const { target } = e;
                    setNewItemAttr((prev) => {
                      return {
                        ...prev,
                        description: target.value,
                      };
                    });
                  }}
                />
              </Group>

              {newItemAttr.type && CiTypeAttributeDataType.PICK_LIST === newItemAttr.type && (
                <>
                  <Flex justify='space-between' gap={'xs'}>
                    <KanbanInput
                      placeholder='Enter label'
                      {...formAddOption.getInputProps('label')}
                      w={'45%'}
                      maxLength={50}
                      value={optionLabel}
                      onChange={(e) => setOptionLabel(e.currentTarget.value)}
                    />
                    <KanbanInput
                      placeholder='Enter value'
                      {...formAddOption.getInputProps('value')}
                      w={'45%'}
                      maxLength={50}
                      value={optionContent}
                      onChange={(e) => setOptionContent(e.currentTarget.value)}
                    />
                    <ActionIcon w={'5%'} mt={5} onClick={handleAddOption}>
                      <IconPlus size='1rem' />
                    </ActionIcon>
                  </Flex>
                  <InputBase component='div' multiline>
                    <Pill.Group>{pills}</Pill.Group>
                  </InputBase>
                </>
              )}

              <Flex justify={'flex-end'} gap={5}>
                {allowEdit ? (
                  <>
                    {newItemAttr.tempId && (
                      <KanbanButton size='xs' onClick={onCancelUpdateAttribute}>
                        Cancel
                      </KanbanButton>
                    )}
                    <KanbanButton size='xs' onClick={onUpdateAttribute} disabled={!newItemAttr.name || !newItemAttr.type}>
                      {newItemAttr.tempId ? 'Update' : 'Add'}
                    </KanbanButton>
                  </>
                ) : (
                  <></>
                )}
              </Flex>
            </>
          )}

          <KanbanTable
            searchable={{
              enable: false,
            }}
            sortable={{
              enable: true,
            }}
            columns={[
              {
                title: 'ID',
                name: 'id',
                hidden: true,
              },
              {
                title: 'Name',
                name: 'name',
              },
              {
                title: 'Type of Field',
                name: 'type',
              },
              {
                title: 'Values',
                name: 'options',
                customRender: (data, rowData) => {
                  if (CiTypeAttributeDataType.PICK_LIST === rowData.type) {
                    const arrayOption = data ? JSON.parse(data) : [];
                    return <KanbanSelect data={arrayOption}></KanbanSelect>;
                  } else {
                    return '';
                  }
                },
              },
              {
                title: 'Description',
                name: 'description',
                hidden: true,
              },
            ]}
            data={ciTypeRelationAttrs}
            title=''
            selectableRows={{
              enable: allowEdit,
              onDeleted(rows) {
                onDeleteMultiItems(rows);
              },
            }}
            actions={
              allowEdit
                ? {
                    customAction: (data) => (
                      <div>
                        <KanbanIconButton
                          key={1}
                          title='Edit'
                          size='sm'
                          variant='transparent'
                          onClick={() => {
                            handleEditAttr(data);
                          }}>
                          <IconEdit />
                        </KanbanIconButton>
                        <KanbanIconButton
                          key={2}
                          title='Delete'
                          size='sm'
                          color='red'
                          variant='transparent'
                          onClick={() => {
                            setCurrentDeleteAttr(data);
                            openDeleteItemAttr();
                          }}>
                          <IconTrash />
                        </KanbanIconButton>
                      </div>
                    ),
                  }
                : {}
            }
          />
        </Container>
      </KanbanModal>

      {/* Modal confirm delete attribute */}
      <KanbanConfirmModal
        opened={isOpenDeleteItemAttr}
        onClose={closeDeleteItemAttr}
        title='Confirm delete'
        onConfirm={() => {
          onDeleteAttribute();
          closeDeleteItemAttr();
          setCurrentDeleteAttr({
            id: 0,
            ciTypeRelationshipId: 0,
            name: '',
          });
        }}>
        Are you sure to delete this item?
      </KanbanConfirmModal>

      {/* Modal confirm delete relationship */}
      <KanbanConfirmModal
        opened={isOpenDeleteItem}
        onClose={closeDeleteItem}
        title='Confirm delete'
        onConfirm={() => {
          onDeleteRelation();
          closeDeleteItem();
          setCurrentDeleteItem({
            id: 0,
            fromCiType: 0,
            toCiType: 0,
            relationshipId: 0,
            referCiTypeId: 0,
          });
        }}>
        Are you sure to delete this item?
      </KanbanConfirmModal>

      <Flex justify={'flex-end'}>
        <GuardComponent requirePermissions={[AclPermission.createCiRelationship]} hiddenOnUnSatisfy>
          <KanbanButton size='xs' onClick={openModalCreateNew} leftSection={<IconAffiliate />}>
            Add relationship
          </KanbanButton>
        </GuardComponent>
      </Flex>
      <Divider my={10} />
      <KanbanTable
        columns={[
          {
            title: 'ID',
            name: 'id',
            hidden: true,
          },
          {
            title: 'From CI Type',
            name: 'fromCiTypeName',
            customRender: (data, rowData) => {
              const isCurrentCi = rowData.fromCiType === ciTypeId;
              return (
                <KanbanButton size='compact-xs' radius={'lg'} variant={isCurrentCi ? 'outline' : ''}>
                  {isCurrentCi ? (
                    'Current CI'
                  ) : (
                    <>
                      {rowData.referCiTypeId !== 0 && rowData.referCiTypeId !== ciTypeId && rowData.referCiTypeId === rowData.fromCiType
                        ? 'Parent CI'
                        : data}
                    </>
                  )}
                </KanbanButton>
              );
            },
          },
          {
            title: 'Relationship',
            name: 'relationshipId',
            customRender: (value) => {
              const currentRelationship = ciRelationshipTypes.find((x) => x.id === value);
              if (!currentRelationship) {
                return '';
              }

              return `${currentRelationship.type} - ${currentRelationship.inverseType}`;
            },
          },
          {
            title: 'To CI Type',
            name: 'toCiTypeName',
            customRender: (data, rowData) => {
              const isCurrentCi = rowData.toCiType === ciTypeId;
              return (
                <KanbanButton size='compact-xs' radius={'lg'} variant={isCurrentCi ? 'outline' : ''}>
                  {isCurrentCi ? (
                    'Current CI'
                  ) : (
                    <>
                      {rowData.referCiTypeId !== 0 && rowData.referCiTypeId !== ciTypeId && rowData.referCiTypeId === rowData.toCiType
                        ? 'Parent CI'
                        : data}
                    </>
                  )}
                </KanbanButton>
              );
            },
          },
        ]}
        data={ciTypeRelations}
        title='Relationships'
        searchable={{
          enable: true,
        }}
        sortable={{
          enable: true,
        }}
        actions={{
          customAction: (data) => (
            <>
              <KanbanIconButton
                key={1}
                title='Attribute Relationship'
                size='sm'
                variant='transparent'
                onClick={() => {
                  handleViewRelation(data);
                }}>
                <IconEye />
              </KanbanIconButton>
              {data.referCiTypeId === 0 || data.referCiTypeId === ciTypeId ? (
                <>
                  <KanbanIconButton
                    key={2}
                    title='Edit'
                    size='sm'
                    variant='transparent'
                    onClick={() => {
                      handleEditRelation(data);
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                  <KanbanIconButton
                    key={3}
                    title='Delete'
                    color='red'
                    size='sm'
                    variant='transparent'
                    onClick={() => {
                      setCurrentDeleteItem(data);
                      openDeleteItem();
                    }}>
                    <IconTrash />
                  </KanbanIconButton>
                </>
              ) : (
                <></>
              )}
            </>
          ),
        }}
      />
    </>
  );
};

export default ConfigItemTypeRelation;
