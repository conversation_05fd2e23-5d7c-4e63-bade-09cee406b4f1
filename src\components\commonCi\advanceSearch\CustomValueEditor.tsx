import UserPicker from './UserPicker';
import { OperatorEnum } from '@common/constants/OperatorEnum';
import CiPicker from './CiPicker';
import { KanbanNumberInput, KanbanSelect } from 'kanban-design-system';
import React from 'react';
import { MantineValueEditor } from '@react-querybuilder/mantine';
import type { DateValue } from '@mantine/dates';
import { KanbanDateTimePicker } from 'kanban-design-system';
import { Flex } from '@mantine/core';
import type { ValueEditorProps } from 'react-querybuilder';
import { ISO_DATE_FORMAT, dateToString } from '@common/utils/DateUtils';
import { listOperatorTypeEmpty } from './AdvanceSearchComponent';

type ItemComboboxProp = {
  value: string;
  label: string;
};

type ValueEditorPropsCustom = ValueEditorProps & {
  listCiType?: ItemComboboxProp[];
  listRelationship?: ItemComboboxProp[];
  ciTypeId?: number;
};

const toLocalISOString = (date: DateValue | null): string => {
  if (!date) {
    return '';
  }

  return dateToString(date, ISO_DATE_FORMAT);
};

const isDateString = (value: string) => {
  return !isNaN(Date.parse(value));
};

const CustomValueEditor = (props: ValueEditorPropsCustom) => {
  const { ciTypeId, disabled, field, inputType, operator, value, values } = props;

  if (listOperatorTypeEmpty.includes(operator)) {
    return null;
  }
  if (inputType === 'user') {
    return (
      <UserPicker
        disabled={disabled}
        key={props.field}
        value={value}
        allowInput
        onChange={(x) => {
          props.handleOnChange(x);
        }}></UserPicker>
    );
  }
  if (inputType === 'ci') {
    return (
      <CiPicker
        key={props.field}
        value={value}
        onChange={(x) => {
          props.handleOnChange(x);
        }}
        disabled={disabled}
        ciTypeId={ciTypeId}></CiPicker>
    );
  }
  if (field === 'ciTypeId') {
    return (
      <KanbanSelect
        key={props.field}
        disabled={disabled}
        placeholder='Choose value'
        w={'250px'}
        searchable
        value={props.value}
        mb={0}
        data={props.listCiType}
        onChange={(x) => {
          props.handleOnChange(x);
        }}
      />
    );
  }
  if (field === 'relationshipType') {
    return (
      <KanbanSelect
        key={props.field}
        disabled={disabled}
        placeholder='Choose value'
        w={'250px'}
        searchable
        mb={0}
        value={props.value}
        data={props.listRelationship}
        onChange={(x) => {
          props.handleOnChange(x);
        }}
      />
    );
  }
  if (inputType?.toLowerCase() === 'date') {
    if (OperatorEnum.BETWEEN === props.operator) {
      const [dateStartStr = '', dateEndStr = ''] = props.value ? props.value.split(',') : [];

      const valDateStart = dateStartStr ? new Date(dateStartStr) : undefined;
      const valDateEnd = dateEndStr ? new Date(dateEndStr) : undefined;
      const handleChangeDateRange = (val: DateValue, isStart: boolean) => {
        const x = toLocalISOString(val);
        const valDateRange = isStart ? `${x},${dateEndStr}` : `${dateStartStr},${x}`;
        props.handleOnChange(valDateRange);
      };
      return (
        <Flex gap={8}>
          <KanbanDateTimePicker
            disabled={disabled}
            key={props.field}
            value={valDateStart}
            w={'240px'}
            mb={0}
            onChange={(x) => {
              handleChangeDateRange(x, true);
            }}
          />
          <KanbanDateTimePicker
            disabled={disabled}
            key={`${props.field}_end`}
            value={valDateEnd}
            w={'240px'}
            mb={0}
            onChange={(x) => {
              handleChangeDateRange(x, false);
            }}
          />
        </Flex>
      );
    }

    const valDate = isDateString(props.value) ? new Date(props.value) : undefined;

    const handleChangeDate = (val: DateValue) => {
      const dateIsoStr = toLocalISOString(val);
      props.handleOnChange(dateIsoStr);
    };

    return (
      <KanbanDateTimePicker
        disabled={disabled}
        key={props.field}
        w={'250px'}
        mb={0}
        value={valDate}
        onChange={(x) => {
          handleChangeDate(x);
        }}
      />
    );
  }

  if (inputType?.toLowerCase() === 'pick_list') {
    return (
      <KanbanSelect
        disabled={disabled}
        placeholder='Choose value'
        w={'250px'}
        searchable
        value={props.value}
        mb={0}
        data={values}
        onChange={(x) => {
          props.handleOnChange(x);
        }}
      />
    );
  }

  if (inputType?.toLowerCase() === 'number') {
    return (
      <KanbanNumberInput
        w={'240px'}
        mb={0}
        disabled={disabled}
        key={props.field}
        value={value}
        onChange={(x) => {
          props.handleOnChange(x);
        }}></KanbanNumberInput>
    );
  }

  return <MantineValueEditor {...props} />;
};

export default CustomValueEditor;
