import type { GoJs } from '@common/libs';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { makeDiagram } from '@common/utils/GoJsHelper';
import { useGetRelationshipTypes } from '@slices/CiRelationshipTypesSlice';
import type { ServiceMapingDetailsModel } from '@models/ServiceMapping';
import go from '@common/libs/gojs/go';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import styled from 'styled-components';
import { appendNodeServiceMap } from '@common/utils/ServiceMapHelper';

const Diagram = styled.div`
height: 100%;
min-height: calc(var(--kanban-modal-content-max-height, 80vh) - 90px);
width: 100%;
background: var(--mantine-color-white);
  }
`;

export interface ViewServiceMappingHandle {
  getGoDiagram: () => GoJs.Diagram | undefined;
}

type ViewServiceMappingProps = {
  serviceMapDetails?: ServiceMapingDetailsModel;
};

export const ServiceMappingCiImpactGraphTab = forwardRef<ViewServiceMappingHandle, ViewServiceMappingProps>((props, ref) => {
  const { serviceMapDetails } = props;
  const [goDiagram, setGoDiagram] = useState<GoJs.Diagram | undefined>(undefined);
  const diagramRef = useRef<HTMLDivElement | null>(null);
  const ciRelationshipTypes = useGetRelationshipTypes().data;
  const ciTypes = useGetCiTypes().data;
  const relationships = useGetRelationshipTypes().data;

  useImperativeHandle(
    ref,
    () => ({
      getGoDiagram: () => {
        return goDiagram;
      },
    }),
    [goDiagram],
  );

  const getRelationshipTypeById = useCallback(
    (id: number) => {
      return ciRelationshipTypes.find((x) => x.id === id);
    },
    [ciRelationshipTypes],
  );

  useEffect(() => {
    if (diagramRef.current && !goDiagram) {
      const myDiagram = makeDiagram({
        dom: diagramRef.current,
        showLayout: true,
        isEdit: false,
        isShowSelectionAdornment: false,
        isShowTotalChild: false,
      });

      myDiagram.layout = go.GraphObject.make(go.LayeredDigraphLayout, {
        layerSpacing: 120,
        columnSpacing: 30,
        direction: 0,
      });
      setGoDiagram(myDiagram);
    }
  }, [diagramRef, goDiagram]);

  useEffect(() => {
    if (!goDiagram || !serviceMapDetails) {
      return;
    }
    appendNodeServiceMap(goDiagram, serviceMapDetails, getRelationshipTypeById, ciTypes, relationships);
  }, [goDiagram, getRelationshipTypeById, serviceMapDetails, ciTypes, relationships]);

  return <Diagram ref={diagramRef} />;
});

ServiceMappingCiImpactGraphTab.displayName = 'ServiceMappingCiImpactGraphTab';
