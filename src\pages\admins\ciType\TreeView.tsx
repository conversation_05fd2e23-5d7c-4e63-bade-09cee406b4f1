import { ActionIcon, Group, useMantineTheme } from '@mantine/core';
import { IconCaretDownFilled, IconCaretRightFilled, IconFolderMinus, IconFolderPlus, IconPlus, IconTrash, IconEdit } from '@tabler/icons-react';
import React, { useEffect, useState } from 'react';
import stylesCss from './ConfigItemType.module.scss';
import type { ConfigItemTypeModel } from '@models/ConfigItemType';

export type TreeProps = {
  depth: number;
  expandAll: boolean;
  handleAdd: (node: ConfigItemTypeModel) => void;
  handleRemove: (node: ConfigItemTypeModel) => void;
  handleEdit: (node: ConfigItemTypeModel) => void;
  handleView: (node: ConfigItemTypeModel) => void;
  showChild: boolean;
};

export type TreeViewProps = TreeProps & {
  items: ConfigItemTypeModel[];
};

export type ItemViewProps = TreeViewProps & {
  item: ConfigItemTypeModel;
};

export const ItemView = (props: ItemViewProps) => {
  const { depth, expandAll, handleAdd, handleEdit, handleRemove, handleView, item, items, showChild } = props;

  const theme = useMantineTheme();
  const [showChildren, setShowChildren] = useState(true);

  const childItems = items.filter((ob) => ob.parentId === item.id);

  useEffect(() => {
    setShowChildren(expandAll);
  }, [expandAll]);

  const displayValue = showChild ? 'block' : 'none';

  return (
    <>
      <div>
        <div
          style={{
            marginLeft: `${depth + 15}px`,
            display: depth > 0 ? displayValue : 'block',
            marginTop: '10px',
            //width: `calc(100% - ${depth + 15}px)`,
          }}>
          <div
            className={stylesCss['tree-item']}
            onClick={() => {
              // setShowChildren(!showChildren);
              handleView(item);
            }}>
            <span
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: 10,
                justifyContent: 'space-between',
              }}>
              <span
                style={{
                  padding: '.3rem',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 10,
                  justifyContent: 'space-between',
                  // width: '100%'
                }}>
                {!showChildren ? (
                  <Group
                    justify='flex-start'
                    gap='xs'
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowChildren(!showChildren);
                    }}>
                    <IconCaretRightFilled size={'1rem'} />
                    <IconFolderPlus size={'1rem'} color={theme.colors.blue[6]} />
                  </Group>
                ) : (
                  <Group
                    justify='flex-start'
                    gap='xs'
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowChildren(!showChildren);
                    }}>
                    <IconCaretDownFilled size={'1rem'} />
                    <IconFolderMinus size={'1rem'} color={theme.colors.blue[6]} />
                  </Group>
                )}

                <span>{`${item.name}`}</span>
              </span>

              <Group justify='flex-end' gap='xs' className={stylesCss['tree-item-action']}>
                <ActionIcon
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAdd(item);
                  }}>
                  <IconPlus size='1rem' />
                </ActionIcon>
                <ActionIcon
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEdit(item);
                  }}>
                  <IconEdit size='1rem' />
                </ActionIcon>
                <ActionIcon
                  color={theme.colors.red[5]}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemove(item);
                  }}>
                  <IconTrash size='1rem' />
                </ActionIcon>
              </Group>
            </span>
          </div>

          {childItems.length > 0 ? (
            <>
              {childItems.map((obj: ConfigItemTypeModel, idx: number) => (
                <div key={idx}>
                  <ItemView
                    item={obj}
                    items={items}
                    depth={depth + 10}
                    showChild={showChildren}
                    expandAll={expandAll}
                    handleAdd={handleAdd}
                    handleRemove={handleRemove}
                    handleEdit={handleEdit}
                    handleView={handleView}
                  />
                </div>
              ))}
            </>
          ) : (
            <></>
          )}
        </div>
      </div>
    </>
  );
};

export const TreeView = (props: TreeViewProps) => {
  const { depth, expandAll, handleAdd, handleEdit, handleRemove, handleView, items, showChild } = props;

  // get root item
  const childItems = items.filter((ob) => ob.parentId === null);
  return (
    <>
      {childItems.length > 0 ? (
        <>
          {childItems.map((obj: ConfigItemTypeModel, idx: number) => (
            <div key={idx}>
              <ItemView
                item={obj}
                items={items}
                depth={depth}
                showChild={showChild}
                expandAll={expandAll}
                handleAdd={handleAdd}
                handleRemove={handleRemove}
                handleEdit={handleEdit}
                handleView={handleView}
              />
            </div>
          ))}
        </>
      ) : (
        <></>
      )}
    </>
  );
};
export default TreeView;
