import React, { forwardRef, useEffect, useImperativeHandle } from 'react';
import { Card, Stack, PasswordInput } from '@mantine/core';
import { useForm, Controller } from 'react-hook-form';
import { defaultDatabaseInfo, Option, SourceConfigDatabaseInfo } from '@models/discovery/DiscoverySourceConfig';
import { KanbanInput, KanbanNumberInput, KanbanTitle } from 'kanban-design-system';
import { DbType } from '@common/constants/DiscoverySourceConfigEnum';
import { SourceDataAction } from '@common/constants/SourceDataActionEnum';
export type DbDetailsRef = {
  getData: () => Promise<SourceConfigDatabaseInfo>;
};

type DbDetailsProps = {
  defaultData?: SourceConfigDatabaseInfo;
  action: SourceDataAction;
};

export const dbTypeOptionsMap: Record<DbType, Option[]> = {
  [DbType.MSSQL]: [{ name: 'Instance Name', value: 'MSSQLSERVER' }],
};

const DbDetails = forwardRef<DbDetailsRef, DbDetailsProps>(({ action, defaultData }, ref) => {
  const { control, handleSubmit, reset } = useForm<SourceConfigDatabaseInfo>({
    defaultValues: defaultDatabaseInfo,
  });
  const isViewAction = SourceDataAction.VIEW === action;
  useEffect(() => {
    if (defaultData) {
      reset(defaultData);
      // replace(defaultData.extraOptions ?? []);
    }
  }, [defaultData, reset]);

  useImperativeHandle(ref, () => ({
    getData: async () => {
      let result: SourceConfigDatabaseInfo = {};
      await handleSubmit((data) => (result = data))();
      return result;
    },
  }));

  return (
    <Card withBorder p='md' radius='md'>
      <KanbanTitle order={4}>Database Details</KanbanTitle>
      <Stack gap='md'>
        <Controller
          name='server'
          control={control}
          rules={{ required: 'Server is required' }}
          render={({ field, fieldState }) => (
            <KanbanInput label='Server' {...field} error={fieldState.error?.message} required disabled={isViewAction} />
          )}
        />
        <Controller
          name='port'
          control={control}
          rules={{ required: 'Port is required' }}
          render={({ field, fieldState }) => (
            <KanbanNumberInput min={1} max={65535} label='Port' {...field} error={fieldState.error?.message} required disabled={isViewAction} />
          )}
        />
        <Controller
          name='databaseName'
          control={control}
          rules={{ required: 'Database name is required' }}
          render={({ field, fieldState }) => (
            <KanbanInput label='Database Name' {...field} error={fieldState.error?.message} required disabled={isViewAction} />
          )}
        />
        <>
          <Controller
            name='username'
            rules={{ required: 'Username is required' }}
            control={control}
            render={({ field, fieldState }) => (
              <KanbanInput label='Username' {...field} error={fieldState.error?.message} required disabled={isViewAction} />
            )}
          />
          <Controller
            name='password'
            control={control}
            rules={{ required: 'Password is required' }}
            render={({ field, fieldState }) => (
              <PasswordInput label='Password' {...field} error={fieldState.error?.message} required disabled={isViewAction} />
            )}
          />
        </>
      </Stack>
    </Card>
  );
});
DbDetails.displayName = 'DbDetails';
export default DbDetails;
