import { KanbanComponentWithLabel, type KanbanComponentWithLabelProps } from 'kanban-design-system';
import { KanbanText } from 'kanban-design-system';
import { CloseButton, Combobox, Group, HoverCard, Input, InputBase, ScrollArea, useCombobox } from '@mantine/core';
import type { CiTypeRelationAttrModel } from '@models/CiTypeRelation';
import React, { useEffect, useMemo, useState } from 'react';

export type SuggestRelationshipSelectProps = {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
} & KanbanComponentWithLabelProps;

export type SuggestRelationshipSelectItemDTO = {
  relationshipId: number;
  relationshipTypeName?: string;
  toCiTypeName?: string;
};

export type SuggestRelationshipSelectViewProps = SuggestRelationshipSelectProps & {
  items: SuggestRelationshipSelectItemDTO[];
  itemAttributes: CiTypeRelationAttrModel[];
  withinPortal?: boolean;
};

export const CiTypeRelationshipSelect = (props: SuggestRelationshipSelectViewProps) => {
  const { disabled, itemAttributes, items, onChange, value, withinPortal = true, ...cloneProps } = props;
  const [search, setSearch] = useState('');
  const [objectSelected, setObjectSelected] = useState<SuggestRelationshipSelectItemDTO>();
  const combobox = useCombobox({
    onDropdownClose: () => {
      combobox.resetSelectedOption();
      setSearch('');
    },
  });

  useEffect(() => {
    const objSelect = items.find((ob) => ob.relationshipId === Number(value));
    if (objSelect) {
      setObjectSelected(objSelect);
    }
  }, [value, items]);

  const filteredOptions = useMemo(() => {
    return items.filter(
      (x) =>
        (x.relationshipTypeName && x.relationshipTypeName.toLowerCase().includes(search || '')) ||
        (x.toCiTypeName && x.toCiTypeName.toLowerCase().includes(search || '')),
    );
  }, [items, search]);

  const renderHoverCard = (item: SuggestRelationshipSelectItemDTO) => {
    const dataFilter = itemAttributes.filter((o) => o.ciTypeRelationshipId === item.relationshipId);
    return (
      <>
        {dataFilter && dataFilter.length > 0 ? (
          <>
            <HoverCard.Dropdown>
              {dataFilter.map((x, index) => (
                <Group key={index} my={3}>
                  <KanbanText size='xs' fw={600} w={'50%'}>
                    {x.name}
                  </KanbanText>
                  <KanbanText size='xs' w={'40%'}>
                    {x.type}
                  </KanbanText>
                </Group>
              ))}
            </HoverCard.Dropdown>
          </>
        ) : (
          <>
            <HoverCard.Dropdown>
              <KanbanText size='xs'>No attribute found</KanbanText>
            </HoverCard.Dropdown>
          </>
        )}
      </>
    );
  };

  return (
    <KanbanComponentWithLabel {...cloneProps}>
      <Combobox
        store={combobox}
        withinPortal={withinPortal}
        disabled={disabled}
        onOptionSubmit={(val) => {
          if (onChange) {
            onChange(val);
          }

          const objSelect = items.find((ob) => ob.relationshipId === Number(val));
          if (objSelect) {
            setObjectSelected(objSelect);
          }
          combobox.closeDropdown();
        }}>
        <Combobox.Target>
          <InputBase
            component='button'
            type='button'
            disabled={disabled}
            pointer
            rightSection={
              !value || disabled ? (
                <Combobox.Chevron />
              ) : (
                <CloseButton
                  size='sm'
                  onMouseDown={(e) => {
                    e.preventDefault();
                  }}
                  onClick={() => {
                    if (onChange) {
                      onChange('');
                    }
                    setObjectSelected({ relationshipId: 0 });
                  }}
                  aria-label='Clear value'
                />
              )
            }
            onClick={() => combobox.toggleDropdown()}>
            {objectSelected && objectSelected.relationshipId > 0 ? (
              <Group gap={2}>
                <KanbanText size='sm'>{objectSelected.relationshipTypeName} -</KanbanText>
                <KanbanText fw={650} size='sm'>
                  {objectSelected.toCiTypeName || ''}
                </KanbanText>
              </Group>
            ) : (
              <Input.Placeholder>{props.placeholder || ''}</Input.Placeholder>
            )}
          </InputBase>
        </Combobox.Target>

        <Combobox.Dropdown>
          <Combobox.Search
            value={search}
            onChange={(event) => {
              const val = event?.currentTarget?.value.toLowerCase() || '';
              setSearch(val);
            }}
            placeholder='Search data'
          />
          <Combobox.Options>
            <ScrollArea.Autosize mah={200} type='scroll'>
              {filteredOptions.length > 0 ? (
                <>
                  {filteredOptions.map((item) => (
                    <Combobox.Option value={`${item.relationshipId}`} key={item.relationshipId} py={0}>
                      <HoverCard width={280} shadow='sm' position='right'>
                        <HoverCard.Target>
                          <Group gap={2} py={6}>
                            <KanbanText size='sm'>{item.relationshipTypeName} -</KanbanText>
                            <KanbanText fw={650} size='sm'>
                              {item.toCiTypeName || ''}
                            </KanbanText>
                          </Group>
                        </HoverCard.Target>
                        {renderHoverCard(item)}
                      </HoverCard>
                    </Combobox.Option>
                  ))}
                </>
              ) : (
                <Combobox.Empty>Nothing found</Combobox.Empty>
              )}
            </ScrollArea.Autosize>
          </Combobox.Options>
        </Combobox.Dropdown>
      </Combobox>
    </KanbanComponentWithLabel>
  );
};
export default CiTypeRelationshipSelect;
