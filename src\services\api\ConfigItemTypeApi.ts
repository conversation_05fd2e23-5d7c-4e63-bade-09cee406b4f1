import type { ApiResponseDataBase } from '@core/api/ApiResponse';
import { BaseApi, ExtraConfigApiType } from '@core/api/BaseApi';
import type { ConfigItemResponse, ConfigItemResponsePagingResponse } from './ConfigItemApi';
import type { ConfigItemTypeAttrResponse } from './ConfigItemTypeAttrApi';
import type { ConfigItemTypeModel } from '@models/ConfigItemType';
import type { PaginationRequestModel, QueryRequestModel } from '@models/EntityModelBase';
import type { CiTypeRelationResponse } from './CiTypeRelationApi';
import type { CiTypeRelationAttrResponse } from './CiTypeRelationAttrApi';
import type { ConfigItemTypeInfoModel } from '@models/ConfigItemTypeInfo';
import type { AdvanceSearchData } from '@components/commonCi/advanceSearch/AdvanceSearchComponent';
import type { AttributeInfoDTO, ExportFileRequest } from '@models/ExportInfo';
import type { LoadFileResponse } from '@models/ImportFileResponse';
import type { CiAttributeInfoModel, CiTypeReferenceModel } from '@models/ConfigItemTypeAttr';
import { BaseUrl } from '@core/api/BaseUrl';

export type ConfigItemTypeResponse = ApiResponseDataBase & ConfigItemTypeModel;
export type CiTypeReferenceResponse = ApiResponseDataBase & CiTypeReferenceModel;
export class ConfigItemTypeApi extends BaseApi {
  static baseUrl = BaseUrl.ciTypes;

  static downloadTemplateImportFile(ciTypeId: number) {
    return BaseApi.getData<string>(`${this.baseUrl}/${ciTypeId}/template`);
  }

  static loadFileImportCI(data: File, ciTypeId: number) {
    const formData = new FormData();
    formData.append('file', data);
    return BaseApi.postData<LoadFileResponse>(`${this.baseUrl}/${ciTypeId}/load-file`, formData);
  }
  static getAllCiTypeIdsWithPermissions() {
    return BaseApi.getData<number[]>(`${this.baseUrl}/permissions`);
  }
  static getAllConfigItemType() {
    return BaseApi.getData<ConfigItemTypeResponse[]>(this.baseUrl);
  }
  static createOrUpdateConfigItemType(data: ConfigItemTypeModel) {
    return BaseApi.postData<ConfigItemTypeResponse>(this.baseUrl, data);
  }
  static deleteConfigItemType(id: number) {
    return BaseApi.deleteData<ConfigItemTypeResponse>(`${this.baseUrl}/${id}`);
  }
  static deleteAllConfigItemType(ids: number[]) {
    return BaseApi.deleteData<ConfigItemTypeResponse>(`${this.baseUrl}`, { ids: ids });
  }

  static getAllCis(ciTypeId: number) {
    return BaseApi.getData<ConfigItemResponse[]>(`${this.baseUrl}/${ciTypeId}/cis`);
  }
  static getAllCisWithChildren(ciTypeId: number) {
    return BaseApi.postData<ConfigItemResponsePagingResponse>(`${this.baseUrl}/${ciTypeId}/cis/with-children`, {});
  }
  static getTopCiNamesByCiTypeId(ciTypeId: number, size: number) {
    return BaseApi.getData<string[]>(`${this.baseUrl}/${ciTypeId}/cis/names`, { size });
  }
  static getAllCisWithChildrenPaging(
    ciTypeId: number,
    pagination: PaginationRequestModel<ConfigItemResponse>,
    advanceSearch?: AdvanceSearchData,
    useLoading: boolean = true,
    controller?: AbortController,
    ciTypeAttributeIds?: number[],
  ) {
    // const isLoading = useLoading !== undefined ? useLoading : true;
    return BaseApi.postData<ConfigItemResponsePagingResponse>(
      `${this.baseUrl}/${ciTypeId}/cis/with-children`,
      { ...pagination, advanceSearch },
      { ciTypeAttributeIds },
      {},
      { useLoading: useLoading, useErrorNotification: true },
      controller,
    );
  }

  static findAllByCiTypeIdWithPermission(
    ciTypeId: number,
    pagination: PaginationRequestModel<ConfigItemResponse>,
    advanceSearch?: AdvanceSearchData,
    useLoading: boolean = true,
  ) {
    // const isLoading = useLoading !== undefined ? useLoading : true;
    return BaseApi.postData<ConfigItemResponsePagingResponse>(
      `${this.baseUrl}/${ciTypeId}/cis`,
      { ...pagination, advanceSearch },
      {},
      {},
      { useLoading: useLoading, useErrorNotification: true },
    );
  }

  static getAllAttributeCisInCiType(
    ciTypeId: number,
    pagination: QueryRequestModel<ConfigItemResponse>,
    advanceSearch?: AdvanceSearchData,
    useLoading?: boolean,
  ) {
    const isLoading = useLoading !== undefined ? useLoading : true;
    return BaseApi.postData<AttributeInfoDTO[]>(
      `${this.baseUrl}/${ciTypeId}/cis/attributes`,
      { ...pagination, advanceSearch },
      {},
      {},
      { useLoading: isLoading, useErrorNotification: true },
    );
  }
  static exportAllCiInCiTypes(ciTypeId: number, exportFileRequest: ExportFileRequest, useLoading?: boolean) {
    const isLoading = useLoading !== undefined ? useLoading : true;
    return BaseApi.postData<Blob>(
      `${this.baseUrl}/${ciTypeId}/cis/export`,
      exportFileRequest,
      {},
      { responseType: 'blob' },
      { useLoading: isLoading, useErrorNotification: true },
    );
  }

  static getAllAttributes(ciTypeId: number, extraConfigs?: ExtraConfigApiType, isView: boolean = false) {
    return BaseApi.getData<ConfigItemTypeAttrResponse[]>(`${this.baseUrl}/${ciTypeId}/attributes`, { isView }, undefined, extraConfigs);
  }
  static getAllRelationships(ciTypeId: number) {
    return BaseApi.getData<CiTypeRelationResponse[]>(`${this.baseUrl}/${ciTypeId}/relationships`);
  }
  static getAllRelationshipAttributes(ciTypeId: number) {
    return BaseApi.getData<CiTypeRelationAttrResponse[]>(`${this.baseUrl}/${ciTypeId}/relationship/attributes`);
  }
  static getAllReferenceFields(ciTypeId: number) {
    return BaseApi.getData<CiTypeReferenceResponse[]>(`${this.baseUrl}/${ciTypeId}/reference-fields`);
  }
  static createOrUpdateCiTypeDetailInfos(ciTypeId: number, data: ConfigItemTypeInfoModel) {
    return BaseApi.postData<ConfigItemTypeResponse>(`${this.baseUrl}/${ciTypeId}/detail`, data);
  }
  static getAllCiTypeHasRelationship(id: number) {
    return BaseApi.getData<ConfigItemTypeResponse[]>(`${this.baseUrl}/info/${id}`);
  }
  static getAllReferAttributeValueSuggestion(ciTypId: number, ciTypeReferenceIds: number[] = []) {
    return BaseApi.getData<CiAttributeInfoModel[]>(
      `${this.baseUrl}/${ciTypId}/reference-attributes/ci-attribute-values`,
      { ciTypId: ciTypId, ciTypeReferenceIds: ciTypeReferenceIds },
      {},
      { useLoading: false, useErrorNotification: true },
    );
  }
}
