import { SqlExecutionModel, SuperiorsApi } from '@api/systems/superiors/SuperiorsApi';
import { KanbanButton } from 'kanban-design-system';
import { KanbanInput } from 'kanban-design-system';
import { KanbanTextarea } from 'kanban-design-system';
import { ColumnType, KanbanTable } from 'kanban-design-system';
import { Flex, Space } from '@mantine/core';
import React, { useMemo, useState } from 'react';
import { BreadcrumbComponent } from '../breadcrumb/BreadcrumbComponent';

export const SqlExecutionPage = () => {
  const [query, setQuery] = useState('');
  const [password, setPassword] = useState('');
  const [countQuery, setCountQuery] = useState(0);

  const [currentResult, setCurrentResult] = useState<SqlExecutionModel | undefined>(undefined);

  const columns: ColumnType<Record<string, any>>[] = useMemo(() => {
    if (!currentResult || currentResult.isNonQuery || !currentResult.listColumns.length) {
      return [];
    }
    const result: ColumnType<Record<string, any>>[] = currentResult.listColumns.map((x) => {
      return {
        name: x,
        title: x,
      };
    });
    return result;
  }, [currentResult]);
  const listData: Record<string, unknown>[] = useMemo(() => {
    if (!currentResult || currentResult.isNonQuery || !currentResult.listDataMappings) {
      return [];
    }

    const result: Record<string, unknown>[] = [];
    for (const item of currentResult.listDataMappings) {
      const currentItem: Record<string, unknown> = {};

      for (const currentRow of item.listSqlMappingColumnDatas) {
        currentItem[currentRow.column] = currentRow.value;
      }
      result.push(currentItem);
    }

    return result;
  }, [currentResult]);

  const isValid = query.trim() && password.trim();

  const onSubmit = () => {
    if (!isValid) {
      return;
    }
    SuperiorsApi.sqlExection({
      sqlQuery: btoa(query.trim()),
      password: password.trim(),
    })
      .then((response) => {
        setCountQuery((prev) => prev + 1);
        setCurrentResult(response.data);
      })
      .catch(() => {
        setCountQuery((prev) => prev + 1);
        setCurrentResult(undefined);
      });
  };

  return (
    <>
      {/* 4736 sql execution */}
      <BreadcrumbComponent />

      <KanbanTextarea
        label='Sql query'
        placeholder='Type your query'
        value={query}
        autosize
        minRows={5}
        maxRows={10}
        onChange={(e) => {
          setQuery(e.target.value);
        }}
      />

      <KanbanInput
        label='Private password'
        type='password'
        value={password}
        onChange={(e) => {
          setPassword(e.target.value || '');
        }}
        inputContainer={(children) => {
          return (
            <Flex gap={'md'} align={'flex-end'}>
              <div
                style={{
                  flex: 1,
                }}>
                {children}
              </div>
              <KanbanButton
                disabled={!isValid}
                onClick={() => {
                  onSubmit();
                }}>
                Execute
              </KanbanButton>
            </Flex>
          );
        }}
      />

      <Space h={'lg'} />

      <KanbanTable key={countQuery} title='Result' columns={columns} data={listData} />
    </>
  );
};

export default SqlExecutionPage;
