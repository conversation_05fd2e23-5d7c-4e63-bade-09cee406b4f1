import type { ConfigItemResponse } from '@api/ConfigItemApi';
import type { EntityModelBase, PaginationRequestModel } from './EntityModelBase';
import type { ServiceAttachedTypeEnum } from '@common/constants/CiDetail';
import type { TableAffactedSafeType } from 'kanban-design-system';
import type { CiImpactedHistoryDto } from '@api/ImpactedCiHistoryApi';
import type { CiManagementResponse } from '@api/CiManagementApi';
import type { ImpactedCiTableViewEnum } from '@common/constants/ChangeAssessmentConstants';
import { FlaggedImpactedCiAttachedType } from '@common/constants/CiManagement';

export type ChangeAssessment = EntityModelBase & {
  changeId: number;
  author: string;
  changeDraftId: string;
  changeDraftStatus: ChangeAssessmentDraftStatus;
};

export type ChangeAssessmentDTO = ChangeAssessment & {
  impactedServices: ConfigItemResponse[];
  affectedCis: ConfigItemResponse[];
  //2494: list manual impacted ci
  ciImpactedFlags: ConfigItemResponse[];
};

export type InforCiOfChangeDTO = {
  ciId: number;
  ciName: string;
  typeOfCi?: string;
};

export type SdpRequestMappingModel = {
  id: number;
  requestId: number;
  author: string;
  requestType: string;
};

export enum ChangeAssessmentAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  COPY = 'COPY',
  VIEW = 'VIEW',
  SDP_UPDATE = 'SDP_UPDATE',
}

export enum ImpactedTypeTable {
  CI = 'CI',
  SERVICE = 'SERVICE',
  GRAPH = 'GRAPH',
  CI_IMPACTED_FLAG = 'CI_IMPACTED_FLAG',
}

export enum ChangeAssessmentDraftStatus {
  LOCK = 'LOCK',
  UNLOCK = 'UNLOCK',
}

export enum ImpactedAssessmentLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export type ImpactedChangeProps = {
  cis: ConfigItemResponse[];
};

export type ChangeAffectedCiDto = {
  ciId: number;
  type: ImpactedTypeTable;
  attachedType?: ServiceAttachedTypeEnum;
};

export type ChangeAssessmentRequestDto = {
  changeId?: number;
  changeAffectedCisNew?: ChangeAffectedCiDto[];
  changeAffectedCisRemove?: ChangeAffectedCiDto[];
  ciImpactedHistoryEntities?: CiImpactedHistoryDto[];
};

export type ImpactedCiParameters = {
  affectedCiIds: number[];
};

export const CHANGE_ASSESSMENT_ACTIONS_TO_CREATE: ChangeAssessmentAction[] = [ChangeAssessmentAction.CREATE, ChangeAssessmentAction.COPY];
export const CHANGE_ACTIONS_DISABLE_LINK_SDP: ChangeAssessmentAction[] = [ChangeAssessmentAction.VIEW];

export type CiImpactedInformationResponseDto = EntityModelBase & {
  ciChangeName: string;
  ciTypeChangeName: string;
  ciImpactedName: string;
  ciTypeImpactedName: string;
  createdByCiImpacted: string;
  createdDateCiImpacted: string;

  //2494: miss with backend CiImpactedInformationCommonDto
  relationshipId: number;
  relationshipName: string;
};
export type CiImpactedByRelationshipInformationResponseDto = {
  id?: number;
  ciImpactedHistoryId?: number;
  ciChangeId: number;
  ciImpactedId: number;
  ciTypeChangeId: number;
  ciChangeCreatedBy: string;
  ciChangeCreatedDate: Date;
  ciChangeDescription: string;
  ciTypeImpactedId: number;
  relationshipLevel: number;
  rootCiId: number;
  markRelated?: boolean;
  rootCiName: string;
  rootCiTypeId: number;
  rootCiTypeName: string;
  deleted: boolean;
  flowNodes: string;
  attachedType: FlaggedImpactedCiAttachedType;
  //Ci change(s) of a manual add impacted ci
  ciChangePlans?: ConfigItemResponse[];
  userComment?: string;
  impactedAssessmentLevel?: string;
} & CiImpactedInformationResponseDto;

export type CiImpactedByRelationshipRequestDto = {
  paginationRequestDto: TableAffactedSafeType | PaginationRequestModel;
  ciChangeIds: number[];
  showAllCiImpacted: boolean;
  level: number;
  ciImpactedId?: number;
};
export type ImpactedCiComponentProps = {
  //2494: list input affected ci
  listCiChange: ConfigItemResponse[];
  allowEdit: boolean;
  //2494: open affected ci advanced search table , func add ci, func delete ci  - advanced search affected table
  //3130: remove table manual impacted ci => remove deleteCi()
  onProcessAdd: boolean;
  onAddCi: (val: boolean) => void;
  listFlagCi: CiImpactedByRelationshipInformationResponseDto[];
  setListFlagCi: (val: CiImpactedByRelationshipInformationResponseDto[]) => void;

  isChangeCorForImpactedCi?: boolean;
};
export interface ImpactedCiTableComponentProps {
  allowEdit: boolean;
  level: number;
  showAllCiImpacted?: ImpactedCiTableViewEnum;
  listCiChange: ConfigItemResponse[];
  listFlagCi: CiImpactedByRelationshipInformationResponseDto[];
  listImpactedCi: CiImpactedByRelationshipInformationResponseDto[];
  setListImpactedCi: (val: CiImpactedByRelationshipInformationResponseDto[]) => void;

  listCiDraft: CiManagementResponse[];

  listImpactedCiNew: CiImpactedByRelationshipInformationResponseDto[];
  setListImpactedCiNew: (val: CiImpactedByRelationshipInformationResponseDto[]) => void;
}

export interface ImpactedCiHistoryProps {
  ciImpactedHistoryId: number;
}
export interface ImpactedCiFlagTableProps {
  allowEdit: boolean;
  listFlagCi: CiImpactedByRelationshipInformationResponseDto[];
  setListFlagCi: (val: CiImpactedByRelationshipInformationResponseDto[]) => void;
  listCiDraft: CiManagementResponse[];
  listCiChange: ConfigItemResponse[];
  isChangeCorForImpactedCi?: boolean;
}
