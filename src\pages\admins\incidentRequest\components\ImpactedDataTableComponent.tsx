import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { KanbanButton } from 'kanban-design-system';
import type { ConfigItemResponse } from '@api/ConfigItemApi';
import { KanbanTable, type ColumnType, type KanbanTableProps } from 'kanban-design-system';
import { KanbanText } from 'kanban-design-system';
import { useSelector } from 'react-redux';
import { getCiTypes } from '@slices/CiTypesSlice';
import { useNavigate } from 'react-router-dom';
import { buildCiTypeUrl, buildCiUrl } from '@common/utils/RouterUtils';
import { renderDateTime } from 'kanban-design-system';
import type { ImpactedTypeTable } from '@models/ChangeAssessment';
import { IconAffiliate, IconClipboardList, IconEye, IconEyeUp, IconLayoutList } from '@tabler/icons-react';
import CiUpdateSole, { CiUpdateSoleMethods } from '@pages/cis/ci/CiUpdateSole';
import CiRelationship from '@pages/cis/ci/relationship/CiRelationship';
import { KanbanConfirmModal } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import { KanbanIconButton } from 'kanban-design-system';
import { Tooltip } from '@mantine/core';
import { ViewServiceMappingDetail, type ViewServiceMappingDetailMethods } from './ViewServiceMappingDetail';
import { CI_TYPE_ID_OF_SERVICE_MAP } from '@common/constants/CommonConstants';
import { klona } from 'klona';
import { CiManagementApi, CiManagementResponse } from '@api/CiManagementApi';
import CiManagementDetailViewPopup, { CiManagementDetailViewPopupMethods } from '@pages/cis/ciManagement/modal/CiManagementDetailViewPopup';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import type { CiInfoModel } from '@models/ConfigItem';
interface ImpactedDataTableComponentProps {
  typeTable: ImpactedTypeTable;
  cis: ConfigItemResponse[];
  onDelete: (val: number[]) => void;
  allowEdit: boolean;
}

export const ImpactedDataTableComponent: React.FC<ImpactedDataTableComponentProps> = ({ allowEdit, cis, onDelete }) => {
  const [listCi, setListCi] = useState<ConfigItemResponse[]>([]);
  const [listCiDraft, setListCiDraft] = useState<CiManagementResponse[]>([]);
  const allCiTypes = useSelector(getCiTypes);
  const navigate = useNavigate();

  const [openedModalViewCi, { close: closeModalViewCi, open: openModalViewCi }] = useDisclosure(false);
  const [openedModalRelationship, { close: closeModalRelationship, open: openModalRelationship }] = useDisclosure(false);
  const [currentCiInfo, setCurrentCiInfo] = useState<CiInfoModel | undefined>();
  const [currentCiTemp, setCurrentCiTemp] = useState<CiManagementResponse>();

  const onClickViewDraft = useCallback(
    (ciId: number) => {
      const ciTempData = listCiDraft.find((x) => x.ciId === ciId);
      setCurrentCiTemp(ciTempData);
      childRefViewDetail.current?.openPopupView();
    },
    [listCiDraft],
  );

  const columns: ColumnType<ConfigItemResponse>[] = useMemo(() => {
    return [
      {
        title: 'Id',
        name: 'id',
        hidden: true,
      },
      {
        title: 'Name',
        name: 'name',
        width: '10%',
      },
      {
        title: 'Description',
        name: 'description',
        customRender: (data) => {
          return <KanbanText lineClamp={2}>{data}</KanbanText>;
        },
        width: '40%',
      },
      {
        title: 'Ci Type',
        name: 'ciTypeId',
        customRender: (data: number) => {
          const ciType = allCiTypes.data.find((x) => x.id === data);
          if (!ciType) {
            return <></>;
          }
          return (
            <KanbanButton
              size='compact-xs'
              radius={'lg'}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(buildCiTypeUrl(ciType.id));
              }}>
              {ciType.name}
            </KanbanButton>
          );
        },
        width: '10%',
      },
      {
        title: 'Created by',
        name: 'author',
        width: '10%',
      },
      {
        title: 'Created date',
        name: 'createdDate',
        customRender: renderDateTime,
        width: '10%',
      },
    ];
  }, [navigate, allCiTypes.data]);

  const tableProps: KanbanTableProps<ConfigItemResponse> = useMemo(() => {
    return {
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 300,
      },
      sortable: {
        enable: true,
      },

      columns: columns,
      data: listCi,

      pagination: {
        enable: true,
      },
      selectableRows: {
        enable: true,
        onDeleted(rows) {
          const ids = rows.map((x) => x.id);
          onDelete(ids);
        },
      },
      actions: {
        deletable: {
          onDeleted(data) {
            onDelete([data.id]);
          },
        },
        customAction: (data) => {
          return (
            <>
              <Tooltip label='View change'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  disabled={listCiDraft.every((x) => x.ciId !== data.id)}
                  onClick={() => {
                    onClickViewDraft(data.id);
                  }}>
                  <IconClipboardList />
                </KanbanIconButton>
              </Tooltip>
              <GuardComponent requirePermissions={AclPermission.createViewCiPermissions(data.id, data.ciTypeId)} hiddenOnUnSatisfy>
                <Tooltip label='Go to detail'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      window.open(buildCiUrl(Number(data.ciTypeId), Number(data.id)), '_blank');
                    }}>
                    <IconEyeUp />
                  </KanbanIconButton>
                </Tooltip>
              </GuardComponent>

              <GuardComponent requirePermissions={AclPermission.createViewCiPermissions(data.id, data.ciTypeId)} hiddenOnUnSatisfy>
                <Tooltip label='View info'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setCurrentCiInfo({
                        ciId: data.id,
                        ciTypeId: data.ciTypeId,
                      });
                      openModalViewCi();
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                </Tooltip>
              </GuardComponent>

              <Tooltip label='View relationship'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    setCurrentCiInfo({
                      ciId: data.id,
                      ciTypeId: data.ciTypeId,
                    });
                    openModalRelationship();
                  }}>
                  <IconAffiliate />
                </KanbanIconButton>
              </Tooltip>

              {data.ciTypeId === CI_TYPE_ID_OF_SERVICE_MAP && (
                <Tooltip label='View service info'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      childRefViewServiceMapping.current?.viewServiceMappingDetail(data.id);
                    }}>
                    <IconLayoutList />
                  </KanbanIconButton>
                </Tooltip>
              )}
            </>
          );
        },
      },
    };
  }, [columns, listCi, listCiDraft, onClickViewDraft, onDelete, openModalRelationship, openModalViewCi]);

  const customTableProps = useMemo(() => {
    // const tablePropsUpdate = { ...tableProps };
    const tablePropsUpdate = klona(tableProps);
    if (!allowEdit) {
      const { actions, selectableRows, ...rest } = tablePropsUpdate;
      if (selectableRows) {
        selectableRows.enable = false;
      }
      if (actions && actions.deletable) {
        delete actions.deletable;
      }

      return { ...rest, selectableRows, actions };
    }

    return tablePropsUpdate;
  }, [allowEdit, tableProps]);

  const fetchDataDraft = useCallback(() => {
    const listIds = listCi.map((x) => x.id);
    if (listIds.length > 0) {
      CiManagementApi.getCiTempByCiIdIn(listIds)
        .then((res) => {
          if (res.data && res.data.length > 0) {
            setListCiDraft(res.data);
            return;
          }
        })
        .catch(() => {});
    }
  }, [listCi]);

  useEffect(() => {
    fetchDataDraft();
  }, [fetchDataDraft]);

  useEffect(() => {
    setListCi(cis);
  }, [cis]);

  const childRef = useRef<CiUpdateSoleMethods | null>(null);
  const childRefViewDetail = useRef<CiManagementDetailViewPopupMethods | null>(null);
  const childRefViewServiceMapping = useRef<ViewServiceMappingDetailMethods | null>(null);

  return (
    <>
      {/* modal view detail info of CI management draft */}
      <CiManagementDetailViewPopup ref={childRefViewDetail} initData={currentCiTemp} />

      {/* Modal view service mapping */}
      <ViewServiceMappingDetail ref={childRefViewServiceMapping} />

      <KanbanConfirmModal
        title={'CI Detail'}
        onConfirm={undefined}
        onClose={closeModalViewCi}
        opened={openedModalViewCi}
        modalProps={{
          size: '80%',
        }}>
        {currentCiInfo && <CiUpdateSole readOnly={true} ciId={currentCiInfo.ciId} ciTypeId={currentCiInfo.ciTypeId} forwardedRef={childRef} />}
      </KanbanConfirmModal>

      <KanbanConfirmModal
        title={'Relationship'}
        onConfirm={undefined}
        onClose={closeModalRelationship}
        opened={openedModalRelationship}
        modalProps={{
          size: '80%',
        }}>
        {currentCiInfo && <CiRelationship ciId={currentCiInfo.ciId} ciTypeId={currentCiInfo.ciTypeId} isView isFromBusinessView={true} />}
      </KanbanConfirmModal>
      <KanbanTable {...customTableProps} />
    </>
  );
};
