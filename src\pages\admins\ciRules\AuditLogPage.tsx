import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { KanbanButton, KanbanTable2, KanbanTable2ColumnDef, renderDateTime, useKanbanTable2 } from 'kanban-design-system';

import { AuditLogApi, PaginationRequestMultiModel } from '@api/AuditLogApi';
import { AuditLogModel, EntityAuditLogTypeEnum } from '@models/AuditLog';
import { ColumnFiltersState, SortingState } from '@tanstack/table-core';
import { useDebouncedState } from '@mantine/hooks';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { RenderHighlightValue } from '@components/commonCi/RenderHighlightValue';
import CiReconciliationRuleNoticeChange from './ciReconciliationRules/history/CiReconciliationRuleNoticeChange';
import CiIdentifierRuleNoticeChange from './history/CiIdentifierRuleNoticeChange';
import CiAbsentRuleNoticeChange from './ciAbsentRules/history/CiAbsentRuleNoticeChange';

const getFilterVariantByField = (field: keyof AuditLogModel) => {
  switch (field) {
    case 'createdDate':
      return 'date-range';
    default:
      return 'text';
  }
};

export const AuditLogPage = ({ auditLogType, entityId }: { auditLogType: EntityAuditLogTypeEnum; entityId: number }) => {
  const [logs, setLogs] = useState<AuditLogModel[]>([]);

  const [pagination, setPagination] = useState<{ pageSize: number; pageIndex: number }>({
    pageSize: 10,
    pageIndex: 0,
  });

  const [pageInfo, setPageInfo] = useState<{ pageCount: number; rowCount: number }>({
    pageCount: 0,
    rowCount: 0,
  });

  const [sorting, setSorting] = useState<SortingState>([{ id: 'createdDate', desc: true }]);
  const [globalFilter, setGlobalFilter] = useDebouncedState<string | undefined>(undefined, 1500);
  const [columnFilters, setColumnFilters] = useDebouncedState<ColumnFiltersState>([], 1500);

  const columnsV2 = useMemo<KanbanTable2ColumnDef<AuditLogModel>[]>(
    () => [
      {
        accessorKey: 'createdDate',
        header: 'At',

        accessorFn: (data) => {
          return <div key={data.id}>{renderDateTime(data.createdDate)}</div>;
        },
        filterVariant: getFilterVariantByField('createdDate'),
      },
      {
        accessorKey: 'createdBy', //normal accessorKey
        header: 'CreatedBy',
        filterVariant: getFilterVariantByField('createdBy'),
        accessorFn: (data) => {
          return <RenderHighlightValue text={data.createdBy} />;
        },
      },
      {
        accessorKey: 'data',
        header: 'History',
        enableSorting: false,
        size: 800,
        filterVariant: getFilterVariantByField('data'),
        accessorFn: (rowData) => {
          if (EntityAuditLogTypeEnum.CI_IDENTIFIER_RULE === auditLogType) {
            return rowData.data && <CiIdentifierRuleNoticeChange dataChange={rowData.data} />;
          } else if (EntityAuditLogTypeEnum.CI_RECONCILIATION_RULE === auditLogType) {
            return rowData.data && <CiReconciliationRuleNoticeChange dataChange={rowData.data} />;
          } else if (EntityAuditLogTypeEnum.CI_ABSENT_RULE === auditLogType) {
            return rowData.data && <CiAbsentRuleNoticeChange dataChange={rowData.data} />;
          }
        },
      },
    ],
    [auditLogType],
  );
  const tableV2 = useKanbanTable2({
    columns: columnsV2,
    data: logs,
    enablePagination: true,
    enableFilters: true,
    enableSorting: true,
    manualFiltering: true,
    manualPagination: true,
    manualSorting: true,
    enableFilterMatchHighlighting: false,
    mantineTableProps: {
      highlightOnHover: false,
    },
    state: {
      pagination,
      sorting,
      globalFilter,
      columnFilters,
    },
    initialState: {
      density: 'xs',
    },
    //paging
    pageCount: pageInfo.pageCount,
    rowCount: pageInfo.rowCount,
    onPaginationChange: setPagination,

    //sort multi
    onSortingChange: setSorting,
    //search all
    onGlobalFilterChange: setGlobalFilter,
    // column search
    onColumnFiltersChange: setColumnFilters,
  });
  const paging = useMemo(() => {
    const sortObj: { sortBy: keyof AuditLogModel | undefined; isReverse: boolean } = sorting[0]
      ? { sortBy: sorting[0].id as keyof AuditLogModel, isReverse: sorting[0].desc }
      : { sortBy: undefined, isReverse: false };
    const paging: PaginationRequestMultiModel<AuditLogModel> = {
      page: pagination.pageIndex,
      size: pagination.pageSize,
      sortBy: sortObj.sortBy,
      search: globalFilter,
      isReverse: sortObj.isReverse,
      columnFilters: columnFilters.map((it) => ({ ...it, filterVariant: getFilterVariantByField(it.id as keyof AuditLogModel) || '' })),
    };
    return paging;
  }, [columnFilters, globalFilter, pagination.pageIndex, pagination.pageSize, sorting]);
  const findLogs = useCallback(() => {
    AuditLogApi.findByTypeAndEntityId(auditLogType, entityId, paging)
      .then((res) => {
        setLogs(res.data.content);
        setPageInfo({
          pageCount: res.data.totalPages,
          rowCount: res.data.totalElements,
        });
      })
      .catch(() => {})
      .finally(() => {});
  }, [auditLogType, entityId, paging]);

  const createIndex = useCallback(() => {
    AuditLogApi.createIndex()
      .then((res) => {
        if (res && res.data) {
          NotificationSuccess({ message: 'Create index successfully' });
        }
      })
      .catch(() => {})
      .finally(() => {});
  }, []);
  useEffect(() => {
    findLogs();
  }, [findLogs]);

  return (
    <>
      {/* <Flex align={'start'}> */}
      <KanbanTable2 table={tableV2} />
      {/* </Flex> */}
      <KanbanButton variant={'transparent'} color={'var(--mantine-color-black-9)'} onClick={createIndex}>
        Create index
      </KanbanButton>
    </>
  );
};

export default AuditLogPage;
