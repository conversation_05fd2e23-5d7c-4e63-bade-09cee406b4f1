import { ConfigItemApi } from '@api/ConfigItemApi';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import type { ConfigItemTypeAttrResponse } from '@api/ConfigItemTypeAttrApi';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { ColumnType, KanbanTable } from 'kanban-design-system';
import { Flex, rem, Box, Grid, Divider, Alert } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IconFileImport, IconFileSpreadsheet, IconDownload, IconLocationCancel, IconAlertTriangle, IconLiveView } from '@tabler/icons-react';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { NotificationSuccess, NotificationError } from '@common/utils/NotificationUtils';
import { KanbanText } from 'kanban-design-system';
import { KanbanModal } from 'kanban-design-system';
import { KanbanSelect } from 'kanban-design-system';
import classes from './CITypeImport.module.scss';
import { type DetailInfoRowImportDTO, type FileUploadRequest, DetailInfoType, ImportFileCiResponse } from '@models/ImportFileResponse';
import { validateUploadedFile, exportFileFromBase64, getColorTypeError, addPrefixToFileName, sortedDetailErrors } from 'common/utils/ExcelUtils';
import GuardComponent from '@components/GuardComponent';
import { getAllFormatDateAtExcel } from '@common/utils/DateUtils';
import { FileImportMessage } from '@models/FileImportMessage';
import SelectCiTypeComponent from '@components/commonCi/SelectCiTypeComponent';
import { KanbanConfirmModal } from 'kanban-design-system';
import { useNavigate } from 'react-router-dom';
import { buildCiTypeUrl } from '@common/utils/RouterUtils';
import { KanbanNumberInput } from 'kanban-design-system';
import { KanbanCheckbox } from 'kanban-design-system';
import CiRequestDetailPopup, { CiRequestDetailPopupMethods } from '@pages/cis/ciManagement/modal/CiRequestDetailPopup';
import { ActionType } from '@common/constants/CiManagement';
import type { CiManagementResponse } from '@api/CiManagementApi';
import { KanbanFileInput } from 'kanban-design-system';
import { useSelector } from 'react-redux';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { AclPermission } from '@models/AclPermission';
import { PermissionAction } from '@common/constants/PermissionAction';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { CiTypeAttributeDataType } from '@models/CiType';

interface LoadFileInfoState {
  excelFields: string[];
  show?: boolean;
  mappingField?: string;
  totalRowValue: number;
  isTemplateFile: boolean;
}

interface FileUploadState {
  file: File | null;
}

const defaultLoadFileInfoState: LoadFileInfoState = {
  excelFields: [],
  show: false,
  totalRowValue: 0,
  isTemplateFile: false,
};

const defaultImportFileInfoState: ImportFileCiResponse = {
  totalRecordSuccess: 0,
  totalRecordFail: 0,
  detailErrors: [],
  urlFile: '',
  ciTempEntities: [],
};

const defaultFileUploadState: FileUploadState = {
  file: null,
};

interface AttributeDTO {
  id: number;
  name: string;
  mandatory: boolean | undefined;
  value: string | null;
}

const formatDateDefault = 'dd/MM/yyyy';
export const hasAnySuccess = (rows: DetailInfoRowImportDTO[]): boolean => {
  return rows.some((row) => row.type === DetailInfoType.SUCCESS);
};
export const CiImportPage = () => {
  const currentUser = useSelector(getCurrentUser);
  const isSuperAdmin = currentUser.data?.isSuperAdmin === true;
  const [openedModalViewPreview, { close: closeModalViewPreview, open: openModalViewPreview }] = useDisclosure(false);
  const [listRowSelected, setListRowSelected] = useState<number[]>([]);

  const navigate = useNavigate();
  const { id } = useParams();
  const limitDefault = 5000;
  const [limit, setLimit] = useState<number>(limitDefault);
  const minimum = 2;
  const [listCiTypeAttribute, setListCiTypeAttribute] = useState<ConfigItemTypeAttrResponse[]>([]);

  const [fileState, setFileState] = useState<FileUploadState>(defaultFileUploadState);
  const [loadFileInfoState, setLoadFileInfoState] = useState<LoadFileInfoState>(defaultLoadFileInfoState);
  const [importFileInfoState, setImportFileInfoState] = useState<ImportFileCiResponse>(defaultImportFileInfoState);
  const [ciTempEntities, setCiTempEntities] = useState<CiManagementResponse[]>([]);
  const [formatDate, setFormatDate] = useState<string>(formatDateDefault);

  const [ciTypeId, setCiTypeId] = useState<number>(Number(id));
  const [allAttributeCI, setAllAttributeCI] = useState<AttributeDTO[]>([]);

  const messageWarning = (totalRow: number) => {
    return `Your file has ${totalRow}, the system can only process the first 5000 lines, the rest will be ignored, do you agree?`;
  };

  const [openedModalViewLog, { close: closeModalViewLog, open: openModalViewLog }] = useDisclosure(false);
  const [isOpenConfirmImport, { close: closeConfirmImport, open: openConfirmImport }] = useDisclosure(false);
  const [isOpenConfirmPreview, { close: closeConfirmPreview, open: openConfirmPreview }] = useDisclosure(false);
  const iconImportFile = <IconFileSpreadsheet style={{ width: rem(18), height: rem(18) }} stroke={1.5} />;
  const [tableImportErrorData, setTableImportErrorData] = useState<DetailInfoRowImportDTO[]>([]);
  const [initMappingFields, setInitMappingFields] = useState<Map<number, number | null>>(new Map());
  const ciRequestDetailRef = useRef<CiRequestDetailPopupMethods | null>(null);
  const [isImportOldVersion, setIsImportOldVersion] = useState<boolean>(false);
  const [hasPermission, setHasPermission] = useState<boolean>(false);
  const [existsReferenceAttribute, setExistsReferenceAttribute] = useState<boolean>(false);

  useEffect(() => {
    const ciImportPermission = AclPermission.createCiTypePermission(PermissionAction.CI__IMPORT, ciTypeId);
    if (isCurrentUserMatchPermissions([ciImportPermission])) {
      setHasPermission(true);
    }
  }, [ciTypeId]);

  const openPopupConfirmImport = () => {
    const mappingObject = validateWhenImportFile();
    if (!mappingObject) {
      return;
    }
    if (loadFileInfoState.totalRowValue > limit) {
      openConfirmImport();
    } else {
      importFileCI(true, true);
    }
  };
  const onChangeSelectCiType = (e: number | React.FormEvent<HTMLDivElement> | undefined) => {
    let targetValue = 0;
    if (typeof e === 'number') {
      targetValue = e;
    }
    // Update check permission before choose Ci Type
    const ciImportPermission = AclPermission.createCiTypePermission(PermissionAction.CI__IMPORT, targetValue);
    if (isCurrentUserMatchPermissions([ciImportPermission])) {
      setCiTypeId(targetValue);
      setHasPermission(true);
    } else {
      setHasPermission(false);
      NotificationError({
        message: `You don't have permissions: Import CI.`,
      });
    }
  };

  const fetchCiTypesAttribute = useCallback(() => {
    ConfigItemTypeApi.getAllAttributes(ciTypeId)
      .then((res) => {
        if (res && res.data) {
          const ciAttributes = res.data.filter((obj) => {
            return CiTypeAttributeDataType.REFERENCE !== obj.type;
          });

          const isExistsReference = ciAttributes.length !== res.data.length;

          setExistsReferenceAttribute(isExistsReference);
          setListCiTypeAttribute(ciAttributes);
        }
      })
      .catch(() => {});
  }, [ciTypeId]);

  useEffect(() => {
    fetchCiTypesAttribute();
  }, [fetchCiTypesAttribute]);

  useEffect(() => {
    if (Object.keys(importFileInfoState).length > 0) {
      const errorDetailsArray: DetailInfoRowImportDTO[] = importFileInfoState.detailErrors;
      setTableImportErrorData(errorDetailsArray);
    }
  }, [importFileInfoState]);

  const formatDates = useMemo(() => {
    return getAllFormatDateAtExcel();
  }, []);

  const attributes = useMemo(() => {
    const attributeCIDefault: AttributeDTO[] = [
      { id: 99999999999999, name: 'ID CI', mandatory: false, value: null },
      { id: 999999999999, name: 'Name', mandatory: true, value: null },
      { id: 9999999999999, name: 'Description', mandatory: false, value: null },
    ];
    const transformedListCiTypeAttribute: AttributeDTO[] = listCiTypeAttribute.map((item) => ({
      id: item.id,
      name: item.name,
      mandatory: item.mandatory,
      value: null,
    }));
    const attributes = [...attributeCIDefault, ...transformedListCiTypeAttribute];
    return attributes;
  }, [listCiTypeAttribute]);

  const updateAttributeValue = (idToUpdate: number, newValue: string) => {
    setAllAttributeCI((prevAttributes) =>
      prevAttributes.map((attribute) => (attribute.id === idToUpdate ? { ...attribute, value: newValue } : attribute)),
    );
  };

  const resetAllAttributeValues = () => {
    setAllAttributeCI((prevAttributes) => prevAttributes.map((attribute) => ({ ...attribute, value: null })));
  };

  useEffect(() => {
    setAllAttributeCI([...attributes]);
  }, [attributes]);

  const initializeMappingFields = useCallback((attributes: AttributeDTO[]) => {
    const mappingField = new Map();
    attributes.forEach((attribute) => {
      mappingField.set(attribute.id, null);
    });
    setInitMappingFields(mappingField);
  }, []);

  useEffect(() => {
    initializeMappingFields(attributes);
  }, [attributes, initializeMappingFields]);

  const validateWhenImportFile = () => {
    const missingMappingValues = attributes
      .filter((attribute) => {
        const value = initMappingFields.get(attribute.id);
        return attribute.mandatory && (value === null || value === undefined || value < 0);
      })
      .map((attribute) => attribute.name);
    if (missingMappingValues.length > 0) {
      NotificationError({
        message: `${missingMappingValues.join(', ')}: ${FileImportMessage.MISSING_REQUIRED_FIELD}.`,
      });
      return null;
    }
    const mappingObject: { [key: string]: number | null } = {};
    initMappingFields.forEach((value, key) => {
      mappingObject[key.toString()] = value;
    });
    return mappingObject;
  };

  const executeWhenImportOrPreviewFileFail = () => {
    closeConfirmImport();
    closeConfirmPreview();
    resetAllAttributeValues();
    setFileState((prevState) => ({
      ...prevState,
      file: null,
    }));
    initializeMappingFields(attributes);
  };

  const importFileCI = (isImportAll: boolean, finalize: boolean) => {
    const mappingObject = validateWhenImportFile();
    if (!mappingObject) {
      return;
    }
    if (fileState.file) {
      if (!isImportAll && listRowSelected.length === 0) {
        const messageError = minLine === 0 && maxLine === 0 ? FileImportMessage.FILE_NOT_DATA : FileImportMessage.NO_SELECTED_ROW;
        NotificationError({
          message: messageError,
        });
        return;
      }
      const request: FileUploadRequest = {
        file: fileState.file,
        mappingFields: JSON.stringify(mappingObject),
        ciTypeId: ciTypeId,
        formatDate: formatDate,
        rowImportSelected: listRowSelected,
        importAll: isImportAll,
        finalize: finalize,
        isImportOldVersion: false,
      };
      ConfigItemApi.importFileCI(request)
        .then((res) => {
          setImportFileInfoState(res.data);
          const ciTemps = res.data.ciTempEntities.map((item) => {
            return { ...item, dataParse: JSON.parse(item.data) };
          });
          setCiTempEntities(ciTemps);
          NotificationSuccess({
            message: FileImportMessage.IMPORT_FILE_SUCCESS,
          });
          closeConfirmImport();
          closeConfirmPreview();
          if (!isImportAll) {
            closeModalViewPreview();
          }
          openModalViewLog();
          initializeMappingFields(attributes);
        })
        .catch(() => {
          executeWhenImportOrPreviewFileFail();
          NotificationError({
            message: FileImportMessage.IMPORT_FILE_FAIL,
          });
        });
    }
  };

  const previewFileImportCI = () => {
    const mappingObject = validateWhenImportFile();
    if (!mappingObject) {
      return;
    }
    if (fileState.file) {
      const rowRange = Array.from({ length: limit - minimum + 1 }, (_, index) => minimum + index);
      const request: FileUploadRequest = {
        file: fileState.file,
        mappingFields: JSON.stringify(mappingObject),
        ciTypeId: ciTypeId,
        formatDate: formatDate,
        rowImportSelected: rowRange,
        importAll: false,
        finalize: false,
      };
      ConfigItemApi.importFileCI(request)
        .then((res) => {
          setImportFileInfoState(res.data);
          NotificationSuccess({
            message: FileImportMessage.PREVIEW_FILE_SUCCESS,
          });
          closeConfirmImport();
          closeConfirmPreview();
          openModalViewPreview(); // open modal preview
        })
        .catch(() => {
          executeWhenImportOrPreviewFileFail();
          NotificationError({
            message: FileImportMessage.PREVIEW_FILE_FAIL,
          });
        });
    }
  };

  const openPopupConfirmPreview = () => {
    const mappingObject = validateWhenImportFile();
    if (!mappingObject) {
      return;
    }
    if (loadFileInfoState.totalRowValue > limit) {
      openConfirmPreview();
    } else {
      previewFileImportCI();
    }
  };

  const handleFileChange = (payload: File | null) => {
    if (!payload) {
      setFileState((prevState) => ({
        ...prevState,
        file: null,
      }));
      setLoadFileInfoState((prevState) => ({
        ...prevState,
        totalRowValue: 0,
        show: false,
      }));
      resetAllAttributeValues();
      initializeMappingFields(attributes);
      return;
    }

    if (ciTypeId === 0) {
      NotificationError({
        message: FileImportMessage.CI_TYPE_REQUIRED,
      });
    } else if (!validateUploadedFile(payload)) {
      NotificationError({
        message: FileImportMessage.INVALID_EXCEL_FORMAT,
      });
    } else {
      setListRowSelected([]);
      ConfigItemTypeApi.loadFileImportCI(payload, ciTypeId)
        .then((res) => {
          const numberRow = res.data.totalRowValue;
          const maxRow = numberRow > limitDefault ? limitDefault + 1 : minimum + numberRow - 1;
          if (numberRow !== 0) {
            setLimit(maxRow);
            setMinLine(minimum);
          } else {
            setLimit(0);
            setMinLine(0);
          }
          setFileState((prevState) => ({
            ...prevState,
            file: payload,
          }));
          setLoadFileInfoState((prevState) => ({
            ...prevState,
            excelFields: res.data.excelFields,
            show: true,
            totalRowValue: res.data.totalRowValue,
            isTemplateFile: res.data.templateFile,
          }));

          if (res.data.templateFile) {
            const newInitMappingFields = new Map();
            const attributeDTOs: AttributeDTO[] = [];
            const attributes = [...allAttributeCI];
            attributes.forEach((attribute) => {
              const modifiedValue = attribute.mandatory ? `${attribute.name} *` : attribute.name;
              const valueAttribute = modifiedValue || '';
              const namesHeader = res.data.excelFields;
              if (namesHeader.includes(valueAttribute)) {
                const indexInArray = namesHeader.indexOf(valueAttribute);
                attribute.value = modifiedValue;
                newInitMappingFields.set(attribute.id, indexInArray);
              }
              attributeDTOs.push(attribute);
            });

            setAllAttributeCI(attributeDTOs);
            setInitMappingFields(newInitMappingFields);
          } else {
            resetAllAttributeValues();
            initializeMappingFields(attributes);
          }
        })
        .catch(() => {
          setFileState((prevState) => ({
            ...prevState,
            file: payload,
          }));
          setLoadFileInfoState(defaultLoadFileInfoState);
          resetAllAttributeValues();
          initializeMappingFields(attributes);
          // NotificationError({
          //   message: FileImportMessage.LOAD_FILE_FAIL,
          // });
        });
    }
  };

  const onChangeSelectField = (val: string | null, index: number) => {
    const excelFields = loadFileInfoState.excelFields;
    const attribute = attributes[index];
    initMappingFields.set(attribute.id, excelFields.indexOf(val || ''));
    updateAttributeValue(attribute.id, val || '');
  };

  const onChangeFormatDate = (val: string | null) => {
    setFormatDate(val || '');
  };

  const excuteWhenCloseImportFileModal = () => {
    setFileState(defaultFileUploadState);
    setLoadFileInfoState(defaultLoadFileInfoState);
    setImportFileInfoState(defaultImportFileInfoState);
    resetAllAttributeValues();
    setFormatDate(formatDateDefault);
  };

  const excuteWhenCloseLogImportFileModal = () => {
    excuteWhenCloseImportFileModal();
    closeModalViewLog();
    closeConfirmImport();
    setFormatDate(formatDateDefault);
  };

  const downloadFileLog = () => {
    const nameFile = fileState.file ? fileState.file.name : 'file';
    const nameFile_ = addPrefixToFileName(nameFile, '_log');
    exportFileFromBase64(importFileInfoState.urlFile, nameFile_);
  };

  const downloadTemplateFile = () => {
    if (ciTypeId === 0) {
      NotificationError({
        message: 'Please choose CiType before download template.',
      });
      return;
    }
    ConfigItemTypeApi.downloadTemplateImportFile(ciTypeId)
      .then((res) => {
        exportFileFromBase64(res.data, 'Template.xlsx');
      })
      .catch(() => {});
  };

  const onCancel = () => {
    const path = buildCiTypeUrl(Number(id));
    navigate(path);
  };

  const openPopupConfirmImportOld = () => {
    const mappingObject = validateWhenImportFile();
    if (!mappingObject) {
      return;
    }
    if (loadFileInfoState.totalRowValue > limit) {
      openConfirmImport();
    } else {
      importFileCiOld(true, true);
    }
  };
  const importFileCiOld = (isImportAll: boolean, finalize: boolean) => {
    const mappingObject = validateWhenImportFile();
    if (!mappingObject) {
      return;
    }
    if (fileState.file) {
      if (!isImportAll && listRowSelected.length === 0) {
        const messageError = minLine === 0 && maxLine === 0 ? FileImportMessage.FILE_NOT_DATA : FileImportMessage.NO_SELECTED_ROW;
        NotificationError({
          message: messageError,
        });
        return;
      }
      const request: FileUploadRequest = {
        file: fileState.file,
        mappingFields: JSON.stringify(mappingObject),
        ciTypeId: ciTypeId,
        formatDate: formatDate,
        rowImportSelected: listRowSelected,
        importAll: isImportAll,
        finalize: finalize,
        isImportOldVersion: true,
      };
      ConfigItemApi.importFileCI(request)
        .then((res) => {
          setImportFileInfoState(res.data);
          NotificationSuccess({
            message: FileImportMessage.IMPORT_FILE_SUCCESS,
          });
          closeConfirmImport();
          closeConfirmPreview();
          if (!isImportAll) {
            closeModalViewPreview();
          }
          openModalViewLog();
          initializeMappingFields(attributes);
        })
        .catch(() => {
          executeWhenImportOrPreviewFileFail();
          NotificationError({
            message: FileImportMessage.IMPORT_FILE_FAIL,
          });
        });
    }
  };

  const baseColumns: ColumnType<DetailInfoRowImportDTO>[] = useMemo(() => {
    return [
      {
        name: 'type',
        title: 'Type',
        customRender: (data) => {
          return (
            <KanbanText lineClamp={2} c={getColorTypeError(data)}>
              {data}
            </KanbanText>
          );
        },
      },
      {
        name: 'line',
        title: 'Line',
      },
      {
        name: 'detail',
        title: 'Description',
      },
    ];
  }, []);
  const additionalColumns: ColumnType<DetailInfoRowImportDTO>[] = useMemo(() => {
    return [
      {
        name: 'checkbox',
        title: '',
        sortable: false,
        customRenderHeader: () => {
          return (
            <KanbanCheckbox
              checked={listRowSelected.length === tableImportErrorData.length}
              onChange={(e) => {
                const checked = e.target.checked;
                if (checked) {
                  const lines: number[] = tableImportErrorData.map((row) => row.line);
                  setListRowSelected(lines);
                } else {
                  setListRowSelected([]);
                }
              }}
            />
          );
        },
        customRender: (_, row) => {
          return (
            <KanbanCheckbox
              checked={listRowSelected.some((x) => x === row.line)}
              onChange={(e) => {
                const checked = e.target.checked;
                let selected = [...listRowSelected];
                if (checked) {
                  selected.push(row.line);
                } else {
                  selected = selected.filter((x) => x !== row.line);
                }

                setListRowSelected(selected);
              }}
            />
          );
        },
      },
    ];
  }, [listRowSelected, tableImportErrorData]);

  const [minLine, setMinLine] = useState<string | number>(minimum || '');
  const [maxLine, setMaxLine] = useState<string | number>(limit || '');

  const excuteWhenCloseViewPreView = () => {
    initializeMappingFields(attributes);
    excuteWhenCloseImportFileModal();
    closeModalViewPreview();
    closeConfirmImport();
  };

  useEffect(() => {
    setMaxLine(limit);
  }, [limit]);

  const chooseLineImport = () => {
    const minLineNumber = Number(minLine);
    const maxLineNumber = Number(maxLine);
    if (!minLine || !maxLine) {
      NotificationError({
        message: 'Invalid range selected. Please ensure that the From row and To row are not empty.',
      });
      return;
    }
    if (minLineNumber > maxLineNumber) {
      NotificationError({
        message: 'Invalid range selected. Please ensure that the From row is less than the To row',
      });
      return;
    } else {
      const rowRange = Array.from({ length: maxLineNumber - minLineNumber + 1 }, (_, index) => minLineNumber + index);
      // const unionRow = new Set([...listRowSelected, ...rowRange]);
      setListRowSelected(rowRange);
    }
  };

  const sendRequest = () => {
    closeModalViewLog();
    ciRequestDetailRef.current?.openPopupReview();
  };
  return (
    <>
      <KanbanConfirmModal
        opened={isOpenConfirmImport}
        onClose={closeConfirmImport}
        title='Confirm Import'
        onConfirm={() => {
          if (isImportOldVersion) {
            importFileCiOld(true, true);
          } else {
            importFileCI(true, true);
          }
        }}>
        {messageWarning(loadFileInfoState.totalRowValue)}
      </KanbanConfirmModal>

      <KanbanConfirmModal opened={isOpenConfirmPreview} onClose={closeConfirmPreview} title='Confirm Preview' onConfirm={previewFileImportCI}>
        {messageWarning(loadFileInfoState.totalRowValue)}
      </KanbanConfirmModal>

      <KanbanModal
        size={'70%'}
        opened={openedModalViewPreview}
        onClose={excuteWhenCloseViewPreView}
        title={'Log Detail When Verifying File'}
        closeOnClickOutside={false}
        actions={
          <Flex gap='md'>
            <KanbanButton
              ml='sm'
              leftSection={<IconFileImport />}
              variant='outline'
              onClick={() => {
                importFileCI(false, true);
              }}
              disabled={!fileState.file || (minLine === 0 && maxLine === 0)}>
              Import
            </KanbanButton>
            <KanbanButton leftSection={<IconDownload size={14} />} onClick={downloadFileLog}>
              Download
            </KanbanButton>
          </Flex>
        }>
        <KanbanText mt='sm' size='sm' fw={500}>
          Total number of verified rows: {importFileInfoState.totalRecordSuccess + importFileInfoState.totalRecordFail}
        </KanbanText>
        <KanbanText mt='sm' size='sm' fw={500}>
          Total record verified success: {importFileInfoState.totalRecordSuccess}
        </KanbanText>
        <KanbanText mt='sm' size='sm' fw={500}>
          Total record verified fail: {importFileInfoState.totalRecordFail}
        </KanbanText>
        <KanbanText mt='sm' size='sm' fw={500}>
          Choose line import:
        </KanbanText>
        <Flex align='center'>
          <Box mt='sm' size='sm'>
            <KanbanText>From line:</KanbanText>
            <KanbanNumberInput
              value={minLine}
              max={limit}
              min={minimum}
              allowNegative={false}
              allowDecimal={false}
              onChange={(e) => {
                setMinLine(e);
              }}></KanbanNumberInput>
          </Box>
          <Box mt='sm' ml='sm'>
            <KanbanText size='sm'>To line:</KanbanText>
            <KanbanNumberInput
              value={maxLine}
              max={limit}
              min={minimum}
              allowNegative={false}
              allowDecimal={false}
              onChange={(e) => {
                setMaxLine(e);
              }}></KanbanNumberInput>
          </Box>
          <Box mt='20' ml='sm'>
            <KanbanButton disabled={minLine === 0 && maxLine === 0} onClick={chooseLineImport}>
              Choose
            </KanbanButton>
          </Box>
        </Flex>
        <Divider my='md' />
        <Box mt='sm'>
          <KanbanTable
            columns={[...additionalColumns, ...baseColumns]}
            pagination={{
              enable: true,
            }}
            data={sortedDetailErrors(tableImportErrorData)}
          />
        </Box>
      </KanbanModal>
      <KanbanModal
        size={'70%'}
        opened={openedModalViewLog}
        onClose={excuteWhenCloseLogImportFileModal}
        title={'Log Detail When Import File'}
        closeOnClickOutside={false}
        actions={
          <Flex gap='md'>
            <KanbanButton leftSection={<IconDownload size={14} />} onClick={downloadFileLog}>
              Download
            </KanbanButton>
            {!isImportOldVersion && (
              <KanbanButton disabled={!hasAnySuccess(tableImportErrorData)} color={'cyan'} leftSection={<IconFileImport />} onClick={sendRequest}>
                Approval
              </KanbanButton>
            )}
          </Flex>
        }>
        <Flex>
          <KanbanText mt='sm' size='sm' fw={500}>
            Total number of imported rows: {importFileInfoState.totalRecordSuccess + importFileInfoState.totalRecordFail}
          </KanbanText>
        </Flex>
        <Flex>
          <KanbanText mt='sm' mr='sm' size='sm' fw={500}>
            Total record import success: {importFileInfoState.totalRecordSuccess}
          </KanbanText>
          {!isImportOldVersion && (
            <KanbanText size='xs' fw={500} color={'cyan'} className={classes.width_message_element}>
              Not yet approved. Please click the approval button to submit the approval request or submit the approval request later at the approval
              inbox.
            </KanbanText>
          )}
        </Flex>
        <KanbanText mt='sm' size='sm' fw={500}>
          Total record import fail: {importFileInfoState.totalRecordFail}
        </KanbanText>
        <Divider my='md' />
        <Box mt='sm'>
          <KanbanTable
            columns={baseColumns}
            pagination={{
              enable: true,
            }}
            data={tableImportErrorData}
            // searchable={{
            //     enable: true
            // }}
          />
        </Box>
      </KanbanModal>

      <HeaderTitleComponent
        title={'Import CI'}
        rightSection={
          //TODO Permission
          <GuardComponent requirePermissions={[]} hiddenOnUnSatisfy>
            <Flex gap='xs'>
              <KanbanButton leftSection={<IconDownload />} onClick={downloadTemplateFile}>
                Download Template
              </KanbanButton>
              {/* <KanbanButton leftSection={<IconFileImport />}  onClick={openConfirmImport} disabled= {!importFileState.file}>Import</KanbanButton> */}
            </Flex>
          </GuardComponent>
        }
      />
      <Box>
        <Flex gap={3} align='center'>
          <SelectCiTypeComponent
            label='Select CI Type'
            value={ciTypeId}
            hasPermission={hasPermission}
            withAsterisk
            onChange={(e: number | React.FormEvent<HTMLDivElement> | undefined) => {
              onChangeSelectCiType(e);
            }}
          />
          {ciTypeId === 0 && (
            <KanbanText c={'red'} mt={'sm'}>
              Please choose Ci Type
            </KanbanText>
          )}
        </Flex>
      </Box>
      <Box>
        <Box>
          <KanbanFileInput
            leftSection={iconImportFile}
            accept='.xlsx, .xls'
            label='Choose file'
            placeholder='File import'
            // rightSectionPointerEvents="none"
            required
            mt='lg'
            w='20%'
            disabled={ciTypeId === 0 || fileState.file !== null}
            value={fileState.file}
            onChange={handleFileChange}
            clearable
          />
        </Box>

        {loadFileInfoState.totalRowValue > limit && (
          <Alert mt={'md'} variant='light' color='red' title={messageWarning(loadFileInfoState.totalRowValue)} icon={<IconAlertTriangle />}></Alert>
        )}
      </Box>

      {loadFileInfoState.show && (
        <Box mt='sm'>
          <Grid justify='space-between'>
            <Grid.Col span={6}>
              <KanbanText fw={500} size='sm'>
                Customize Mapping
              </KanbanText>
            </Grid.Col>
            <Grid.Col span={6}>
              <Flex justify='flex-end' align='center'>
                <KanbanText fw={500} size='sm'>
                  <span className={classes.required}>*</span> Mandatory Field
                </KanbanText>
              </Flex>
            </Grid.Col>
          </Grid>

          <Divider mt='sm' />

          {existsReferenceAttribute && (
            <Alert my={'5'} variant='light' color={'yellow'} icon={<IconAlertTriangle />}>
              <KanbanText>
                Current CI Type has field attribute type is REFERENCE, you cannot import this attribute type, only edit manually after import.
              </KanbanText>
            </Alert>
          )}

          <Box mt='md'>
            <Box>
              <KanbanText size='sm'>
                {' '}
                The column headers in the excel file are displayed in the drop down boxes. Kindly choose the appropriate column header for the fields
                below.
              </KanbanText>
            </Box>
            <Box>
              <Flex>
                <KanbanText mr='sm' size='sm'>
                  {' '}
                  All the date fields in the csv file should be in the same format. Kindly choose the data format required for the date fields :
                </KanbanText>
                <KanbanSelect
                  placeholder='Pick value'
                  data={formatDates}
                  defaultValue='dd/MM/yyyy'
                  onChange={(e) => {
                    onChangeFormatDate(e);
                  }}
                />
              </Flex>
            </Box>
          </Box>

          <Box mt='sm' w='50%'>
            {allAttributeCI.map((attribute, index) => (
              <Grid justify='space-between' align='stretch' mb='sm' key={index}>
                <Grid.Col span={6}>
                  <KanbanText size='sm'>
                    {attribute.name} {attribute.mandatory && <span className={classes.required}>*</span>}
                  </KanbanText>
                </Grid.Col>
                <Grid.Col span={6}>
                  <KanbanSelect
                    label='Select a field from Excel data'
                    clearable
                    placeholder='Pick value'
                    value={attribute.value}
                    data={loadFileInfoState.excelFields}
                    onChange={(e) => {
                      onChangeSelectField(e, index);
                    }}
                  />
                </Grid.Col>
              </Grid>
            ))}

            <Box>
              <Flex gap='xs' justify='center' align='center' direction='row' wrap='wrap'>
                <KanbanButton leftSection={<IconLiveView />} variant='outline' onClick={openPopupConfirmPreview} disabled={!fileState.file}>
                  Preview
                </KanbanButton>
                <KanbanButton
                  leftSection={<IconFileImport />}
                  onClick={() => {
                    openPopupConfirmImport();
                    setIsImportOldVersion(false);
                  }}
                  disabled={!fileState.file}>
                  Import
                </KanbanButton>
                {isSuperAdmin && (
                  <KanbanButton
                    variant='outline'
                    color='cyan'
                    leftSection={<IconFileImport />}
                    onClick={() => {
                      openPopupConfirmImportOld();
                      setIsImportOldVersion(true);
                    }}
                    disabled={!fileState.file}>
                    Import (Admin)
                  </KanbanButton>
                )}
                <KanbanButton leftSection={<IconLocationCancel />} onClick={onCancel} variant='default'>
                  Cancel
                </KanbanButton>
              </Flex>
            </Box>

            <CiRequestDetailPopup
              ref={ciRequestDetailRef}
              listData={ciTempEntities}
              screenAction={ActionType.SEND}
              onCloseModal={excuteWhenCloseImportFileModal}
            />
          </Box>
        </Box>
      )}
    </>
  );
};
export default CiImportPage;
