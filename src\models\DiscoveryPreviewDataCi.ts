import { ConfigItemTypeAttrResponse } from '@api/ConfigItemTypeAttrApi';
import { ConfigItemInfoModel } from './ConfigItem';
import { EntityModelBase } from './EntityModelBase';
import { ConfigItemAttrModel } from './ConfigItemAttr';
import { DiscoveryDataCiTransformStatus, DiscoveryPreviewDataCiAction } from '@common/constants/DiscoveryPreviewDataCiConstants';

export type DiscoveryPreviewDataCiModel = EntityModelBase & {
  id: number;
  action?: DiscoveryPreviewDataCiAction;
  identifierRuleName?: string;
  identifierRuleId?: number;
  transformMapName?: string;
  transformMapId?: number;
  tranformMappingVersion?: number;
  ciTypeName?: string;
  ciTypeId?: number;
  ciName?: string;
  ciId?: number;
  sourceDiscoveryCiName?: string;
  listDataCiDetail?: DiscoveryPreviewDataCiDetailModel[];
  status?: DiscoveryDataCiTransformStatus;
  statusIdentifier?: string;
  ciInfo?: ConfigItemInfoModel;
  listCiTypeAttribute?: ConfigItemTypeAttrResponse[];
  //dci data
  data?: string;
  discoveryCiTypeAttributeNames?: string;
  listCiAttributeDiscovered?: ConfigItemAttrModel[];
  listCiAttributeLive?: ConfigItemAttrModel[];
  listCiAttributeChange?: string[];
  description?: string;
  ciTempId?: number;
  jobHistoryId?: number;
};

export type DiscoveryPreviewDataCiDetailModel = EntityModelBase & {
  id: number;
  discoveryDataCiId?: number;
  ciTypeAttributeId?: number;
  ciTypeAttributeName?: string;
  ciAttributeValue?: string;
};

export enum DiscoveryMethodEnum {
  JOB = 'JOB',
  MANUAL = 'MANUAL',
}

export type DiscoveryPreviewDataCiGroupModel = {
  summaryId: number;
  jobHistoryId?: number;
  jobId?: number;
  jobName?: string;
  targetCiType: string;
  dataSourceId?: number;
  dataSourceName?: string;
  transformMap?: string;
  runBy: string;
  runTime: number;
  createdDate: Date;
  startDate: Date;
  endDate: Date;
  summaryDetail?: string;
  discoveryMethod: DiscoveryMethodEnum;
};
