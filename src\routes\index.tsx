import React from 'react';
import {
  IconFilters,
  IconAccessible,
  IconBrandAppstore,
  IconBrandCodesandbox,
  IconLayoutList,
  IconArrowsExchange,
  IconAlertTriangle,
  IconIcons,
  IconCalendarWeek,
  IconId,
  IconWall,
  IconFlagSearch,
} from '@tabler/icons-react';
import DesignSystemPage from '@pages/developGuides/designSystems';
import DemoUseStorePage from '@pages/developGuides/demoUseStore';
import DemoUseContextPage from '@pages/developGuides/demoUseContext';
import DemoForbiddenPage, { DemoForbiddenComponentPage } from '@pages/developGuides/demoForbiddenPage';
import GuardRoute from '../components/GuardRoute';
import { AclPermission } from 'models/AclPermission';
import { type RouterType } from '@components/appShell';
import Admin from '@pages/admins';
import UsersSettingsPage from '@pages/admins/users/UsersSettingsPage';
import SelectionPage from '@pages/admins/SelectionPage';
import type { RouteProps } from 'react-router-dom';
import CiTypesPage from '@pages/admins/ciType/CiTypesPage';
import GroupsSettingsPage from '@pages/admins/groups/GroupsSettingsPage';
import {
  ciPath,
  ciTypePath,
  ciImportPath,
  businessViewsPath,
  ciManagementPath,
  ciManagementDetailPath,
  ciRelationShipImportPath,
  businessViewDetaisPath,
  serviceMappingPath,
  changeAssessmentPath,
  changeAssessmentDetailPath,
  incidentRequestPath,
  incidentRequestDetailPath,
  createOrUpdateRolePath,
  roleDetailPath,
  impactedRulesPath,
  impactedRuleDetailPath,
  dataSourcePath,
  transformMapPath,
  createOrUpdateDataSourcePath,
  discoveryPreviewDataCisPath,
  discoveryPreviewDataCiDetailPagePath,
  createOrUpdateDiscoveryTransfromMap,
  ciIdentifierRulePath,
  businessDomainPath,
  businessDomainDetailPath,
  businessDomainAggregatePath,
  integrationPath,
  staggingTablePath,
  jobDiscoveryConfigPath,
  createOrUpdateJobDiscoveryConfigPath,
  ciIdentifierRuleDetailPath,
  ciReconciliationRulePath,
  ciReconciliationRuleDetailPath,
  discoveryPreviewDataCiGroupDetailPath,
  dataSourceConfigPath,
  dataSourceConfigDetailPath,
  ciAbsentRulePath,
  ciAbsentRuleDetailPath,
} from '@common/utils/RouterUtils';
import CiPage from '@pages/cis/ci';
import DomainDetaiPage from '@pages/admins/businessDomain/detail';
import CiTypePage from '@pages/cis/ciType';
import RelationshipTypes from '@pages/admins/relationshipTypes';
import CiImportPage from '@pages/cis/ci/import';
import BussinessViews from '@pages/cis/ci/graph';
import CiRelationShipImportPage from '@pages/admins/ciRelationship/import';
import CiManagementPage from '@pages/cis/ciManagement/CiManagementPage';
import CiManagementDetailPage from '@pages/cis/ciManagement/detail/CiManagementDetailPage';
import BusinessViewPage from '@pages/cis/ci/graph/BusinessViewPage';
import { ServiceMappingPage } from '@pages/cis/ci/graph/ServiceMappingPage';
import ChangeAssessmentPage from '@pages/admins/changeAssessment';
import ChangeAssessmentDetailPage from '@pages/admins/changeAssessment/ChangeAssessmentDetailPage';
import SqlExecutionPage from '@pages/admins/superiors/SqlExecutionPage';
import IncidentRequestDetailPage from '@pages/admins/incidentRequest/IncidentRequestDetailPage';
import IncidentRequestPage from '@pages/admins/incidentRequest';
import BusinessDomainPage from '@pages/admins/businessDomain';
import BusinessDomainAggregatePage from '@pages/admins/businessDomain/aggregate';
import { PermissionActionType } from '@common/constants/PermissionActionType';
import OutgoingMailPage from '@pages/admins/mailServerConfig';
import RoleManagementPage from '@pages/admins/roles';
import RoleDetailPage from '@pages/admins/roles/RoleDetailPage';
import CreateOrUpdateRolePage from '@pages/admins/roles/CreateOrUpdateRolePage';
import { NotificaticationTemplateListPage } from '@pages/admins/notificationRule';
import { getConfigs } from '@core/configs/Configs';
import SystemParameterPage from '@pages/admins/systemParameter/SystemParameterPage';
import { ImpactedRulesPage } from '@pages/admins/impacts/rules';
import { ImpactedRuleDetailPage } from '@pages/admins/impacts/rules/components/ImpactedRuleDetailPage';
import { DiscoveryTransfromMap } from '@pages/admins/discoveryTransfromMap';
import { IconDatabaseImport } from '@tabler/icons-react';
import { IconTransform } from '@tabler/icons-react';
import CreateOrUpdateDataSourcePage from '@pages/admins/discovery/sourceData/CreateOrUpdateSourceDataPage';
import { ResourceApplicationType } from '@common/constants/ResourceTypeEnum';
import { DiscoveryPreviewDataCiListPage } from '@pages/admins/previewDiscoveryCis';
import DiscoveryCiDataDetailPage from '@pages/admins/previewDiscoveryCis/DiscoveryCiDataDetailPage';
import { CreateOrUpdateDiscoveryTransfromMap } from '@pages/admins/discoveryTransfromMap/CreateOrUpdateDiscoveryTransfromMap';
import { DroppedItemsRefsProvider } from '@context/DragScrollContext';
import { DataTransformProvider } from '@context/DataTransformContext';
import { JsonTransformProvider } from '@context/JsonTransformContext';
import CiIdentifierRulePage from '@pages/admins/ciRules/CiIdentifierRulePage';
import CiIdentifierRuleDetailPage from '@pages/admins/ciRules/CiIdentifierRuleDetailPage';
import { JobConfigListPage } from '@pages/admins/jobConfigs';
import { CreateOrUpdateJobDiscoveryConfig } from '@pages/admins/jobConfigs/CreateOrUpdateJobDiscoveryConfig';
import CiReconciliationRulePage from '@pages/admins/ciRules/ciReconciliationRules/CiReconciliationRulePage';
import CiReconciliationRuleDetailPage from '@pages/admins/ciRules/ciReconciliationRules/CiReconciliationRuleDetailPage';
import { PreviewDiscoveryCiGroupPage } from '@pages/admins/previewDiscoveryCis/PreviewDiscoveryCiGroupPage';
import DataSourcePage from '@pages/admins/discovery/sourceData';
import SourcesPage from '@pages/admins/discovery/sourceConfigs';
import DiscoverySourceConfigDetailPage from '@pages/admins/discovery/sourceConfigs/DiscoverySourceConfigDetailPage';
import CiAbsentRulePage from '@pages/admins/ciRules/ciAbsentRules/CiAbsentRulePage';
import CiAbsentRuleDetailPage from '@pages/admins/ciRules/ciAbsentRules/CiAbsentRuleDetailPage';

export const navbarConfigs: RouterType[] = [];

const configs = getConfigs();

export const discoveryLinkConfigs: RouterType[] = [
  // {
  //   path: integrationPath,
  //   name: 'Integration',
  //   icon: IconPresentation,
  //   requirePermissions: [],
  //   resourceApplicationType: ResourceApplicationType.DISCOVERY_RESOURCE,
  // },
  {
    path: dataSourceConfigPath,
    name: 'Sources',
    icon: IconDatabaseImport,
    requirePermissions: [],
    resourceApplicationType: ResourceApplicationType.DISCOVERY_RESOURCE,
  },
  {
    path: dataSourcePath,
    name: 'Data Sources',
    icon: IconDatabaseImport,
    requirePermissions: [],
    resourceApplicationType: ResourceApplicationType.DISCOVERY_RESOURCE,
  },
  // {
  //   path: staggingTablePath,
  //   name: 'Stagging Tables',
  //   icon: IconDeviceTabletStar,
  //   requirePermissions: [],
  //   resourceApplicationType: ResourceApplicationType.DISCOVERY_RESOURCE,
  // },
  {
    path: transformMapPath,
    name: 'Transform Maps',
    icon: IconTransform,
    requirePermissions: [],
    resourceApplicationType: ResourceApplicationType.DISCOVERY_RESOURCE,
  },
  {
    path: discoveryPreviewDataCisPath,
    name: 'Preview Discovery',
    icon: IconIcons,
    requirePermissions: [],
    resourceApplicationType: ResourceApplicationType.DISCOVERY_RESOURCE,
  },
  {
    path: jobDiscoveryConfigPath,
    name: 'Schedule Jobs',
    icon: IconCalendarWeek,
    requirePermissions: [AclPermission.viewListJobConfig],
    resourceApplicationType: ResourceApplicationType.DISCOVERY_RESOURCE,
  },
];

export const discoveryLinkPaths = discoveryLinkConfigs.map((config) => config.path);

export const ciRuleLinkConfigs: RouterType[] = [
  {
    path: ciIdentifierRulePath,
    name: 'CI Identifiers',
    tooltip: 'Used to identify based on 1 (some) attributes to decide to create new or update CI.',
    icon: IconId,
    requirePermissions: [],
    resourceApplicationType: ResourceApplicationType.CI_RULE_RESOURCE,
  },
  {
    path: ciReconciliationRulePath,
    name: 'CI Reconcile',
    tooltip: 'Used to decide how to update a CI in priority order? (auto which source, manual,..).',
    icon: IconWall,
    requirePermissions: [],
    resourceApplicationType: ResourceApplicationType.CI_RULE_RESOURCE,
  },
  {
    path: ciAbsentRulePath,
    name: 'CI Absent',
    tooltip: 'Used to bind / monitor whether CIs are in Out Of Update state? (if removed at Source, information is needed for CMDB).',
    icon: IconFlagSearch,
    requirePermissions: [],
    resourceApplicationType: ResourceApplicationType.CI_RULE_RESOURCE,
  },
];

export const ciRuleLinkPaths = ciRuleLinkConfigs.map((config) => config.path);

export const headerLinkConfigs: RouterType[] = [
  {
    name: 'Develop Guide',
    icon: IconAccessible,
    children: [
      {
        path: '/develop-guide/design-system',
        name: 'Design system',
        icon: IconFilters,
      },
      {
        path: '/develop-guide/use-store',
        name: 'Use Store',
        icon: IconBrandCodesandbox,
      },
      {
        path: '/develop-guide/use-context',
        name: 'Use Context',
        icon: IconBrandAppstore,
      },
      {
        path: '/develop-guide/demo-forbidden',
        name: 'Demo forbidden page',
        icon: IconBrandAppstore,
      },
      {
        path: '/develop-guide/demo-forbidden-component',
        name: 'Demo forbidden component',
        icon: IconBrandAppstore,
      },
    ],
    requirePermissions: [AclPermission.viewDevelopGuide],
    isHidden: !configs.showDevelopmentGuide,
  },
  {
    path: businessViewsPath,
    name: 'Business Views',
    icon: IconLayoutList,
    requirePermissions: [AclPermission.viewListBusinessView],
  },
  {
    path: serviceMappingPath,
    name: 'Service Mappings',
    icon: IconLayoutList,
    requirePermissions: [AclPermission.viewListServiceMap],
  },
  {
    path: changeAssessmentPath,
    name: 'Change Assessments',
    icon: IconArrowsExchange,
    requirePermissions: [AclPermission.viewListChangeAssessment],
  },
  {
    path: incidentRequestPath,
    name: 'Incident Requests',
    icon: IconAlertTriangle,
    requirePermissions: [AclPermission.viewListIncidentRequest],
  },
  {
    path: businessDomainPath,
    name: 'Business Architecture Management',
    icon: IconLayoutList,
  },
];

type RoutePropsOmit = Omit<RouteProps, 'children'>;
export type RoutePropsType = RoutePropsOmit & {
  children?: RoutePropsOmit[];
};

export const routeConfigs: RoutePropsType[] = [
  {
    path: '/admins',
    element: <Admin />,
    children: [
      {
        index: true,
        element: (
          <GuardRoute requirePermissions={[AclPermission.manageSystem]}>
            <SelectionPage />
          </GuardRoute>
        ),
      },
      {
        path: 'users',
        element: (
          <GuardRoute requirePermissions={[AclPermission.viewListUser]}>
            <UsersSettingsPage />
          </GuardRoute>
        ),
      },
      {
        path: 'ci-types',
        element: (
          <GuardRoute requirePermissions={[AclPermission.viewListCiType]}>
            <CiTypesPage />
          </GuardRoute>
        ),
      },
      {
        path: 'groups',
        element: (
          <GuardRoute requirePermissions={[AclPermission.viewListGroupUser]}>
            <GroupsSettingsPage />
          </GuardRoute>
        ),
      },
      {
        path: 'relationship-types',
        element: <RelationshipTypes />,
      },
      {
        path: 'superiors/sql-execution',
        element: <SqlExecutionPage />,
      },
      {
        path: 'parameter',
        element: (
          <GuardRoute requirePermissions={[AclPermission.manageSystem]}>
            <SystemParameterPage />
          </GuardRoute>
        ),
      },
      {
        path: 'roles',
        element: (
          <GuardRoute requirePermissions={[AclPermission.viewListRole]}>
            <RoleManagementPage />
          </GuardRoute>
        ),
      },
      {
        path: 'outgoing-mail-configs',
        element: (
          <GuardRoute requirePermissions={[AclPermission.viewOutGoingMailConfig]}>
            <OutgoingMailPage />
          </GuardRoute>
        ),
      },
      {
        path: 'notification-templates',
        element: (
          <GuardRoute requirePermissions={[AclPermission.viewListNotificationTemplate]}>
            <NotificaticationTemplateListPage />
          </GuardRoute>
        ),
      },
    ],
  },
  {
    path: '/develop-guide/design-system',
    element: (
      <GuardRoute requirePermissions={[AclPermission.viewDevelopGuide]}>
        <DesignSystemPage />
      </GuardRoute>
    ),
  },
  {
    path: '/develop-guide/use-store',
    element: (
      <GuardRoute requirePermissions={[AclPermission.viewDevelopGuide]}>
        <DemoUseStorePage />
      </GuardRoute>
    ),
  },
  {
    path: '/develop-guide/use-context',
    element: (
      <GuardRoute requirePermissions={[AclPermission.viewDevelopGuide]}>
        <DemoUseContextPage />
      </GuardRoute>
    ),
  },
  {
    path: '/develop-guide/demo-forbidden',
    element: (
      <GuardRoute requirePermissions={[AclPermission.viewDevelopGuide]}>
        <DemoForbiddenPage />
      </GuardRoute>
    ),
  },
  {
    path: '/develop-guide/demo-forbidden-component',
    element: (
      <GuardRoute requirePermissions={[AclPermission.viewDevelopGuide]}>
        <DemoForbiddenComponentPage />
      </GuardRoute>
    ),
  },

  // {
  //   path: '/applications',
  //   element: <ApplicationsPage />,
  // },
  {
    path: ciIdentifierRulePath,
    element: <CiIdentifierRulePage />,
  },
  {
    path: ciIdentifierRuleDetailPath,
    element: <CiIdentifierRuleDetailPage />,
  },
  {
    path: ciReconciliationRulePath,
    element: <CiReconciliationRulePage />,
  },
  {
    path: ciReconciliationRuleDetailPath,
    element: <CiReconciliationRuleDetailPage />,
  },
  {
    path: ciAbsentRulePath,
    element: <CiAbsentRulePage />,
  },
  {
    path: ciAbsentRuleDetailPath,
    element: <CiAbsentRuleDetailPage />,
  },
  {
    path: createOrUpdateRolePath,
    element: (
      <GuardRoute requirePermissions={[AclPermission.createRole, AclPermission.updateRole]}>
        <CreateOrUpdateRolePage />
      </GuardRoute>
    ),
  },
  {
    path: roleDetailPath,
    element: (
      <GuardRoute requirePermissions={[AclPermission.viewDetailRole]}>
        <RoleDetailPage />
      </GuardRoute>
    ),
  },
  {
    path: ciTypePath,
    element: (
      <GuardRoute requirePermissions={[]} permissionType={PermissionActionType.CI_TYPE}>
        <CiTypePage />
      </GuardRoute>
    ),
  },
  {
    path: ciPath,
    element: (
      <GuardRoute requirePermissions={[]} permissionType={PermissionActionType.CI}>
        <CiPage />
      </GuardRoute>
    ),
  },
  {
    path: businessViewDetaisPath,
    element: (
      <GuardRoute requirePermissions={[AclPermission.viewDetailBusinessView]}>
        <BusinessViewPage />
      </GuardRoute>
    ),
  },
  {
    path: ciImportPath,
    element: (
      <GuardRoute requirePermissions={[]} permissionType={PermissionActionType.CI_TYPE}>
        <CiImportPage />
      </GuardRoute>
    ),
  },
  {
    path: businessViewsPath,
    element: (
      <GuardRoute requirePermissions={[AclPermission.viewListBusinessView]}>
        <BussinessViews />
      </GuardRoute>
    ),
  },
  {
    path: serviceMappingPath,
    element: (
      <GuardRoute requirePermissions={[AclPermission.viewListServiceMap]}>
        <ServiceMappingPage />
      </GuardRoute>
    ),
  },
  {
    path: ciRelationShipImportPath,
    element: (
      <GuardRoute requirePermissions={[AclPermission.importCiRelationship]}>
        <CiRelationShipImportPage />
      </GuardRoute>
    ),
  },
  {
    path: ciManagementPath,
    element: <CiManagementPage />,
  },
  {
    path: ciManagementDetailPath,
    element: <CiManagementDetailPage />,
  },
  {
    path: changeAssessmentPath,
    element: (
      <GuardRoute requirePermissions={[AclPermission.viewListChangeAssessment]}>
        <ChangeAssessmentPage />
      </GuardRoute>
    ),
  },
  {
    path: changeAssessmentDetailPath,
    element: (
      <GuardRoute requirePermissions={[AclPermission.viewDetailChangeAssessment, AclPermission.createChangeAssessment]}>
        <ChangeAssessmentDetailPage />
      </GuardRoute>
    ),
  },
  {
    path: incidentRequestPath,
    element: (
      <GuardRoute requirePermissions={[AclPermission.viewListIncidentRequest]}>
        <IncidentRequestPage />
      </GuardRoute>
    ),
  },
  {
    path: incidentRequestDetailPath,
    element: (
      <GuardRoute requirePermissions={[AclPermission.viewDetailIncidentRequest, AclPermission.createIncidentRequest]}>
        <IncidentRequestDetailPage />
      </GuardRoute>
    ),
  },
  {
    path: impactedRulesPath,
    element: (
      <GuardRoute requirePermissions={[AclPermission.viewListImpactedRule]}>
        <ImpactedRulesPage />
      </GuardRoute>
    ),
  },
  {
    path: impactedRuleDetailPath,
    element: (
      // route to not permission page
      <GuardRoute requirePermissions={[AclPermission.viewDetailImpactedRule]}>
        <ImpactedRuleDetailPage />
      </GuardRoute>
    ),
  },
  // Phase 2: Service discovery
  {
    path: createOrUpdateDataSourcePath,
    element: (
      <GuardRoute requirePermissions={[]}>
        <DroppedItemsRefsProvider>
          <DataTransformProvider>
            <JsonTransformProvider>
              <CreateOrUpdateDataSourcePage />
            </JsonTransformProvider>
          </DataTransformProvider>
        </DroppedItemsRefsProvider>
      </GuardRoute>
    ),
  },
  // Phase 2: Service Discovery
  {
    path: integrationPath,
    element: (
      <GuardRoute requirePermissions={[]}>
        <>Integrations</>
      </GuardRoute>
    ),
  },
  {
    path: dataSourceConfigPath,
    element: (
      <GuardRoute requirePermissions={[]}>
        <SourcesPage />
      </GuardRoute>
    ),
  },
  {
    path: dataSourceConfigDetailPath,
    element: (
      <GuardRoute requirePermissions={[]}>
        <DiscoverySourceConfigDetailPage />
      </GuardRoute>
    ),
  },
  {
    path: dataSourcePath,
    element: (
      <GuardRoute requirePermissions={[]}>
        <DataSourcePage />
      </GuardRoute>
    ),
  },
  {
    path: staggingTablePath,
    element: (
      <GuardRoute requirePermissions={[]}>
        <>Stagging Tables</>
      </GuardRoute>
    ),
  },
  {
    path: transformMapPath,
    element: (
      <GuardRoute requirePermissions={[]}>
        <DiscoveryTransfromMap />
      </GuardRoute>
    ),
  },
  {
    path: createOrUpdateDiscoveryTransfromMap,
    element: <CreateOrUpdateDiscoveryTransfromMap />,
  },
  {
    path: discoveryPreviewDataCisPath,
    element: (
      <GuardRoute requirePermissions={[]}>
        <PreviewDiscoveryCiGroupPage />
      </GuardRoute>
    ),
  },
  {
    path: discoveryPreviewDataCiGroupDetailPath,
    element: (
      <GuardRoute requirePermissions={[]}>
        <DiscoveryPreviewDataCiListPage />
      </GuardRoute>
    ),
  },
  {
    path: jobDiscoveryConfigPath,
    element: (
      <GuardRoute requirePermissions={[AclPermission.viewListJobConfig]}>
        <JobConfigListPage />
      </GuardRoute>
    ),
  },
  {
    path: createOrUpdateJobDiscoveryConfigPath,
    element: (
      <GuardRoute requirePermissions={[AclPermission.createJobConfig, AclPermission.updateJobConfig]}>
        <CreateOrUpdateJobDiscoveryConfig />
      </GuardRoute>
    ),
  },
  {
    // fix-bug CMDB-4770
    path: businessDomainPath,
    element: <BusinessDomainPage />,
  },
  {
    // fix-bug CMDB-4770
    path: businessDomainDetailPath,
    element: <DomainDetaiPage />,
  },
  {
    path: businessDomainAggregatePath,
    element: <BusinessDomainAggregatePage />,
  },
  {
    path: discoveryPreviewDataCiDetailPagePath,
    element: (
      <GuardRoute requirePermissions={[]}>
        <DiscoveryCiDataDetailPage />
      </GuardRoute>
    ),
  },
];
