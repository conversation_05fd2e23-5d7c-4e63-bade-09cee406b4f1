import { useMemo } from 'react';
import {
  KanbanTable2,
  useKanbanTable2,
  type KanbanTable2ColumnDef,
  KanbanTable2GlobalFilterTextInput,
  KanbanTable2ToggleFiltersButton,
} from 'kanban-design-system';
import { Box, Button, Flex, Menu, Text, Title } from '@mantine/core';
import { IconUserCircle, IconSend } from '@tabler/icons-react';
import React from 'react';
import { data } from './MakeData1';
export type Employee = {
  firstName: string;
  lastName: string;
  email: string;
  jobTitle: string;
  salary: number;
  startDate: string;
  signatureCatchPhrase: string;
  avatar: string;
};

const KanbanTable2Demo = () => {
  const columns = useMemo<KanbanTable2ColumnDef<Employee>[]>(
    () => [
      {
        id: 'employee',
        header: 'Employee',
        columns: [
          {
            accessorFn: (row) => `${row.firstName} ${row.lastName}`,
            id: 'name',
            header: 'Name',
            size: 250,
            filterVariant: 'autocomplete',
            Cell: ({ renderedCellValue }) => (
              <Box
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '16px',
                }}>
                <span>{renderedCellValue}</span>
              </Box>
            ),
          },
          {
            accessorKey: 'email',
            enableClickToCopy: true,
            header: 'Email',
            size: 300,
          },
        ],
      },
      {
        id: 'id',
        header: 'Job Info',
        columns: [
          {
            accessorKey: 'salary',
            header: 'Salary',
            size: 200,
            filterVariant: 'range-slider',
            mantineFilterRangeSliderProps: {
              color: 'indigo',
              label: (value) =>
                value?.toLocaleString?.('en-US', {
                  style: 'currency',
                  currency: 'USD',
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 0,
                }),
            },
            Cell: ({ cell }) => (
              <Box
                style={(theme) => ({
                  backgroundColor:
                    cell.getValue<number>() < 50_000
                      ? theme.colors.red[9]
                      : cell.getValue<number>() >= 50_000 && cell.getValue<number>() < 75_000
                        ? theme.colors.yellow[9]
                        : theme.colors.green[9],
                  borderRadius: '4px',
                  color: '#fff',
                  maxWidth: '9ch',
                  padding: '4px',
                })}>
                {cell.getValue<number>()?.toLocaleString?.('en-US', {
                  style: 'currency',
                  currency: 'USD',
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 0,
                })}
              </Box>
            ),
          },
          {
            accessorKey: 'jobTitle',
            header: 'Job Title',
            filterVariant: 'multi-select',
            size: 350,
          },
          {
            accessorFn: (row) => {
              const sDay = new Date(row.startDate);
              sDay.setHours(0, 0, 0, 0);
              return sDay;
            },
            id: 'startDate',
            header: 'Start Date',
            filterVariant: 'date-range',
            sortingFn: 'datetime',
            enableColumnFilterModes: false,
            Cell: ({ cell }) => cell.getValue<Date>()?.toLocaleDateString(),
            Header: ({ column }) => <em>{column.columnDef.header}</em>,
          },
        ],
      },
    ],
    [],
  );

  const table = useKanbanTable2({
    columns,
    data, //must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    enableColumnFilterModes: true,
    enableColumnOrdering: true,
    enableFacetedValues: true,
    enableGrouping: true,
    enablePinning: true,
    enableRowActions: true,
    enableRowSelection: true,
    initialState: { showColumnFilters: true, showGlobalFilter: true },
    paginationDisplayMode: 'pages',
    positionToolbarAlertBanner: 'bottom',
    mantineSearchTextInputProps: {
      placeholder: 'Search Employees',
    },
    renderDetailPanel: ({ row }) => (
      <Box
        style={{
          display: 'flex',
          justifyContent: 'flex-start',
          alignItems: 'center',
          gap: '16px',
          padding: '16px',
        }}>
        <img alt='avatar' height={200} src={row.original.avatar} style={{ borderRadius: '50%' }} />
        <Box style={{ textAlign: 'center' }}>
          <Title>Signature Catch Phrase:</Title>
          <Text>&quot;{row.original.signatureCatchPhrase}&quot;</Text>
        </Box>
      </Box>
    ),
    renderRowActionMenuItems: () => (
      <>
        <Menu.Item leftSection={<IconUserCircle />}>View Profile</Menu.Item>
        <Menu.Item leftSection={<IconSend />}>Send Email</Menu.Item>
      </>
    ),
    renderTopToolbar: ({ table }) => {
      const handleDeactivate = () => {
        table.getSelectedRowModel().flatRows.map((row) => {
          // eslint-disable-next-line no-alert
          alert(`deactivating ${row.getValue('name')}`);
        });
      };

      const handleActivate = () => {
        table.getSelectedRowModel().flatRows.map((row) => {
          // eslint-disable-next-line no-alert
          alert(`activating ${row.getValue('name')}`);
        });
      };

      const handleContact = () => {
        table.getSelectedRowModel().flatRows.map((row) => {
          // eslint-disable-next-line no-alert
          alert(`contact ${row.getValue('name')}`);
        });
      };

      return (
        <Flex p='md' justify='space-between'>
          <Flex gap='xs'>
            {/* import KanbanTable2 sub-components */}
            <KanbanTable2GlobalFilterTextInput table={table} />
            <KanbanTable2ToggleFiltersButton table={table} />
          </Flex>
          <Flex style={{ gap: '8px' }}>
            <Button color='red' disabled={!table.getIsSomeRowsSelected()} onClick={handleDeactivate} variant='filled'>
              Deactivate
            </Button>
            <Button color='green' disabled={!table.getIsSomeRowsSelected()} onClick={handleActivate} variant='filled'>
              Activate
            </Button>
            <Button color='blue' disabled={!table.getIsSomeRowsSelected()} onClick={handleContact} variant='filled'>
              Contact
            </Button>
          </Flex>
        </Flex>
      );
    },
  });

  return <KanbanTable2 table={table} />;
};

export default KanbanTable2Demo;
