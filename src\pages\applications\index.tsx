import { ApplicationsApi, ApplicationResponse } from '@api/ApplicationsApi';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { KanbanInput } from 'kanban-design-system';
import { KanbanModal } from 'kanban-design-system';
import { KanbanSplitPagingContainer } from 'kanban-design-system';
import { KanbanTable } from 'kanban-design-system';
import { renderDateTime } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import type { ApplicationModel } from '@models/Application';
import { IconPlus } from '@tabler/icons-react';
import React, { useCallback, useEffect, useState } from 'react';
import styled from 'styled-components';

const ListApplicationWrapper = styled.div`
  display: grid;
  column-gap: var(--mantine-spacing-xs);
  row-gap: var(--mantine-spacing-xs);
  grid-template-columns: auto auto auto;
`;
const ApplicationItem = styled.div`
  padding: var(--mantine-spacing-xs);
  border: 1px solid gray;
  cursor: pointer;
  &:hover {
    border-color: var(--mantine-color-primary-6);
  }
  .title {
    font-size: var(--mantine-font-size-md);
  }
  .description {
    color: gray;
    font-size: var(--mantine-font-size-sm);
  }
`;

export const ApplicationsPage = () => {
  const [openedModal, { close: closePopup, open: openModal }] = useDisclosure(false);
  const [newItem, setNewItem] = useState<ApplicationModel>({
    id: 0,
    name: '',
  });
  const [listApplications, setListApplications] = useState<ApplicationResponse[]>([]);

  const fetchApplications = useCallback(() => {
    ApplicationsApi.getAllApplications()
      .then((response) => {
        const data = response.data;
        setListApplications(data || []);
      })
      .catch(() => {});
  }, []);
  useEffect(() => {
    fetchApplications();
  }, [fetchApplications]);

  const closeModal = () => {
    setNewItem({
      id: 0,
      name: '',
    });
    closePopup();
  };

  const onCreateNew = () => {
    ApplicationsApi.createOrUpdateApplication(newItem)
      .then((_res) => {
        fetchApplications();
      })
      .catch(() => {});
    closeModal();
  };
  const onDelete = (id: number) => {
    ApplicationsApi.deleteApplication(id)
      .then(() => {})
      .catch(() => {});
  };
  const onChangeNewItem = (e: React.ChangeEvent<HTMLInputElement>, name: string) => {
    const { target } = e;
    setNewItem((prev) => {
      return {
        ...prev,
        [name]: target.value,
      };
    });
  };
  return (
    <div>
      <KanbanModal
        size={'lg'}
        opened={openedModal}
        onClose={closeModal}
        zIndex={999}
        centered
        title={'Create/Update application'}
        actions={<KanbanButton onClick={onCreateNew}> {newItem.id > 0 ? 'Update' : 'Create'}</KanbanButton>}>
        <KanbanInput
          label='Name'
          value={newItem.name || ''}
          onChange={(e) => {
            onChangeNewItem(e, 'name');
          }}></KanbanInput>
        <KanbanInput
          label='Description'
          value={newItem.description || ''}
          onChange={(e) => {
            onChangeNewItem(e, 'description');
          }}></KanbanInput>
      </KanbanModal>

      <HeaderTitleComponent
        title={'Application'}
        rightSection={
          <KanbanButton leftSection={<IconPlus />} onClick={openModal}>
            Create new
          </KanbanButton>
        }
      />

      <KanbanTable
        title='List Applications'
        columns={[
          {
            name: 'name',
            title: 'Name',
          },
          {
            name: 'description',
            title: 'Description',
          },
          {
            name: 'createdBy',
            title: 'Created by',
          },
          {
            name: 'createdDate',
            title: 'Created date',
            customRender: renderDateTime,
          },
        ]}
        data={listApplications}
        onRowClicked={(data) => {
          setNewItem(data);
          openModal();
        }}
        pagination={{
          enable: true,
        }}
        actions={{
          deletable: {
            onDeleted(data) {
              onDelete(data.id);
            },
          },
        }}
      />

      {false && (
        <KanbanSplitPagingContainer
          title='List Applications'
          listData={listApplications}
          pagination={{
            itemsPerPage: 10,
          }}
          renderItems={(listData) => {
            return (
              <ListApplicationWrapper>
                {listData.map((x, key) => {
                  return (
                    <ApplicationItem
                      key={key}
                      onClick={() => {
                        setNewItem(x);
                        openModal();
                      }}>
                      <div className='title'>{x.name}</div>
                      <div className='description'>{x.description}</div>
                    </ApplicationItem>
                  );
                })}
              </ListApplicationWrapper>
            );
          }}></KanbanSplitPagingContainer>
      )}
    </div>
  );
};
export default ApplicationsPage;
