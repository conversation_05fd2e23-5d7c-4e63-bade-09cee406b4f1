import React, { useCallback, useMemo, useRef, useState } from 'react';
import { KanbanButton, KanbanSelect, KanbanText, KanbanTextarea, KanbanTooltip } from 'kanban-design-system';
import { KanbanTable, type ColumnType, type KanbanTableProps } from 'kanban-design-system';
import { useNavigate } from 'react-router-dom';
import { IconClipboardList, IconMessage, IconPlaylistAdd, IconPlaylistX, IconSitemap } from '@tabler/icons-react';
import CiUpdateSole, { CiUpdateSoleMethods } from '@pages/cis/ci/CiUpdateSole';
import CiRelationship, { ShowActionDiagramConfigProps } from '@pages/cis/ci/relationship/CiRelationship';
import { KanbanConfirmModal } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import { KanbanIconButton } from 'kanban-design-system';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import type { CiManagementResponse } from '@api/CiManagementApi';
import CiManagementDetailViewPopup, { CiManagementDetailViewPopupMethods } from '@pages/cis/ciManagement/modal/CiManagementDetailViewPopup';
import {
  ImpactedAssessmentLevel,
  type CiImpactedByRelationshipInformationResponseDto,
  type ImpactedCiFlagTableProps,
} from '@models/ChangeAssessment';
import { Badge, Box, ComboboxData, Flex, ScrollArea, Tooltip } from '@mantine/core';
import { IconEye } from '@tabler/icons-react';
import { IconAffiliate } from '@tabler/icons-react';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { buildCiTypeUrl, buildCiUrl } from '@common/utils/RouterUtils';
import {
  keyPairImpactedCiAndAttType,
  keyImpactedCiAndMergedCisInChangeAndAttType as keyImpactedCiAndMergedCisInChangeAndAttType,
} from '@common/utils/ChangeAssessmentUtils';
import { ImpactedCiFlagHistoryTableComponent } from './ImpactedCiFlagHistoryTableComponent';
import { FlaggedImpactedCiAttachedType } from '@common/constants/CiManagement';
import type { ConfigItemTypeModel } from '@models/ConfigItemType';
import type { ConfigItemResponse } from '@api/ConfigItemApi';
import { ManualCiInChangePlan } from './ManualCiInChangePlanTable';
import { CiInfoModel } from '@models/ConfigItem';
import stylesCss from '../Change.module.scss';
import { CiDetailScreenType, CiDetailSubTabType, ScreenTypeEnum } from '@common/constants/CiDetail';
import { CalculatedCiInChangePlanTable } from './CalculatedCiInChangePlanTable';
import { useSelector } from 'react-redux';
import { getSystemParamByKey } from '@slices/SystemParameterSlice';
import { SystemConfigParamKey } from '@common/constants/SystemConfigParam';

export type NumberMapEntry<T> = {
  [key: number]: T;
};
export type StringMapEntry<T> = {
  [key: string]: T;
};
const MAX_LENGTH_CI_NAME_LABEL = 20;

const IMPACTED_LEVEL_OPTIONS: ComboboxData = [
  { label: ImpactedAssessmentLevel.LOW, value: ImpactedAssessmentLevel.LOW },
  { label: ImpactedAssessmentLevel.MEDIUM, value: ImpactedAssessmentLevel.MEDIUM },
  { label: ImpactedAssessmentLevel.HIGH, value: ImpactedAssessmentLevel.HIGH },
  { label: ImpactedAssessmentLevel.CRITICAL, value: ImpactedAssessmentLevel.CRITICAL },
];

const displayNameCliped = (val: string, maxLength: number): string => {
  return val.length > maxLength ? `${val.substring(0, maxLength)}...` : val;
};

export const toolBarConfigImpactMap: ShowActionDiagramConfigProps = {
  toolBar: {
    isShowLocate: true,
    isShowUpStreamAndDownStream: false,
    isShowChangeStyle: true,
    isShowChangeLinkStyle: true,
    isShowImpactToAndImpactBy: false,
  },
  menu: {
    isShowStreamImpact: false,
    isShowCiDetails: true,
  },
};

export const ImpactedCiFlagTableComponent: React.FC<ImpactedCiFlagTableProps> = ({
  allowEdit,
  listCiChange,
  listCiDraft,
  listFlagCi,
  setListFlagCi,
}) => {
  const allCiTypeMap = useGetCiTypes().data.reduce((acc: NumberMapEntry<ConfigItemTypeModel>, item) => {
    acc[item.id] = item; // Use the id as the key and the item as the value
    return acc;
  }, {});
  const navigate = useNavigate();
  const maxLevel = useSelector(getSystemParamByKey(SystemConfigParamKey.CI_MAX_LEVEL));
  const [currentViewHistoryImpactedCiId] = useState(0);
  const [openedModalViewCi, { close: closeModalViewCi, open: openModalViewCi }] = useDisclosure(false);
  const [openedModalRelationship, { close: closeModalRelationship, open: openModalRelationship }] = useDisclosure(false);
  const [openedModalViewImpactedCiRelationshipHistory, { close: closeModalViewImpactedCiRelationshipHistory }] = useDisclosure(false);
  const [currentCiInfo, setCurrentCiInfo] = useState<CiInfoModel | undefined>();
  const [currentCiTemp, setCurrentCiTemp] = useState<CiManagementResponse>();
  const [currentFlagImpactedCi, setCurrentFlagImpactedCi] = useState<CiImpactedByRelationshipInformationResponseDto>();
  const [currentListCiInChangeNewOrRemove, setCurrentListCiInChangeNew] = useState<ConfigItemResponse[]>();

  const [openedManualCiChange, { close: closeManualCiChange, open: openManualCiChange }] = useDisclosure(false);
  const [openedCalculatedCiChange, { close: closeCalculatedCiChange, open: openCalculatedCiChange }] = useDisclosure(false);
  const [openedComment, { close: closeComment, open: openComment }] = useDisclosure(false);

  const [userComment, setUserComment] = useState<string>();

  const [ciIdChangePlans, setCiIdChangePlans] = useState<number[]>();

  const onClickViewDraft = useCallback(
    (ciId: number) => {
      const ciTempData = listCiDraft.find((x) => x.ciId === ciId);
      setCurrentCiTemp(ciTempData);
      childRefViewDetail.current?.openPopupView();
    },
    [listCiDraft],
  );

  const handleOpenSelectCisInChange = useCallback(
    (currentCi: CiImpactedByRelationshipInformationResponseDto) => {
      setCurrentFlagImpactedCi(currentCi);
      //3849: allow change calculated ci 's changeplan
      if (FlaggedImpactedCiAttachedType.CALCULATED === currentCi.attachedType) {
        openCalculatedCiChange();
      } else {
        setCurrentListCiInChangeNew(currentCi.ciChangePlans);
        openManualCiChange();
      }
    },
    [openCalculatedCiChange, openManualCiChange],
  );
  const renderCiType = useCallback(
    (rowData: CiImpactedByRelationshipInformationResponseDto) => {
      const ciChangeFrags = (rowData.ciChangePlans || []).map((it) => {
        const ciChangeDisplayTxt = allCiTypeMap[it.ciTypeId]?.name;
        return (
          <>
            <KanbanTooltip key={it.id} label={ciChangeDisplayTxt}>
              <KanbanButton
                size='compact-xs'
                radius={'lg'}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  //051224 bug click ciTypeInChange Name in flagTable
                  navigate(buildCiTypeUrl(it.ciTypeId));
                }}>
                {displayNameCliped(allCiTypeMap[it.ciTypeId]?.name, MAX_LENGTH_CI_NAME_LABEL)}
              </KanbanButton>
            </KanbanTooltip>
            <br />
          </>
        );
      });

      return <>{ciChangeFrags}</>;
    },
    [allCiTypeMap, navigate],
  );

  const handleUpdateCisInChange = useCallback(() => {
    if (!currentFlagImpactedCi) {
      return;
    }
    let updateList;
    //3849: find 1 calculated flagged row to update cisChangeplan
    if (FlaggedImpactedCiAttachedType.CALCULATED === currentFlagImpactedCi?.attachedType) {
      const toDeleteCiInChange = new Set(currentListCiInChangeNewOrRemove?.map((it) => it.id));
      updateList = listFlagCi
        .map((it) => {
          if (keyPairImpactedCiAndAttType(it) === keyPairImpactedCiAndAttType(currentFlagImpactedCi)) {
            const remainCiInChange = it.ciChangePlans?.filter((ciInChange) => !toDeleteCiInChange.has(ciInChange.id));
            if (!remainCiInChange || remainCiInChange.length === 0) {
              return undefined;
            }
            return { ...it, ciChangePlans: remainCiInChange };
          }
          return it;
        })
        .filter((it) => it !== undefined);
    } else {
      updateList = listFlagCi.map((it) =>
        keyPairImpactedCiAndAttType(it) === keyPairImpactedCiAndAttType(currentFlagImpactedCi)
          ? { ...it, ciChangePlans: currentListCiInChangeNewOrRemove }
          : it,
      );
    }

    setListFlagCi(updateList);
    // setListFlagCi(updateList);
    //3849: flagged table: allow change ciInChnange of caculated ci.
    if (FlaggedImpactedCiAttachedType.CALCULATED === currentFlagImpactedCi?.attachedType) {
      closeCalculatedCiChange();
    } else {
      closeManualCiChange();
    }
  }, [closeCalculatedCiChange, closeManualCiChange, currentFlagImpactedCi, currentListCiInChangeNewOrRemove, listFlagCi, setListFlagCi]);
  const renderCiNamesInChangePlan = useCallback(
    (rowData: CiImpactedByRelationshipInformationResponseDto) => {
      const isAlert = !rowData.ciChangePlans || rowData.ciChangePlans.length === 0;

      const ciChangeFrags =
        rowData.ciChangePlans && rowData.ciChangePlans.length > 0 ? (
          rowData.ciChangePlans.map((it, idx) => {
            return (
              <div className={stylesCss.content} key={idx}>
                <KanbanTooltip key={it.id} label={it.name}>
                  <KanbanButton
                    size='compact-xs'
                    radius={'lg'}
                    variant={'subtle'}
                    onClick={() => {
                      navigate(buildCiUrl(it.ciTypeId, it.id, CiDetailScreenType.RELATIONSHIPS, CiDetailSubTabType.LISTVIEW));
                    }}>
                    {displayNameCliped(it.name, MAX_LENGTH_CI_NAME_LABEL)}
                  </KanbanButton>
                </KanbanTooltip>
                <br />
              </div>
            );
          })
        ) : (
          <div className={stylesCss.content}> </div>
        );

      return (
        <Flex direction={'column'} className={stylesCss.tableCell}>
          {ciChangeFrags}
          {/* 3849 allow calculated ci remove cis in change */}
          {allowEdit && (
            <Tooltip
              label={FlaggedImpactedCiAttachedType.CALCULATED === rowData.attachedType ? 'Remove CIs in change plan' : 'Select CIs in change plan'}>
              <KanbanIconButton
                size={'sm'}
                c={isAlert ? 'var(--mantine-color-red-9)' : 'var(--mantine-color-primary-9)'}
                variant='transparent'
                onClick={() => {
                  handleOpenSelectCisInChange(rowData);
                }}
                className={stylesCss.topRightButton}>
                {FlaggedImpactedCiAttachedType.CALCULATED === rowData.attachedType ? <IconPlaylistX /> : <IconPlaylistAdd />}
              </KanbanIconButton>
            </Tooltip>
          )}
        </Flex>
      );
    },
    [allowEdit, handleOpenSelectCisInChange, navigate],
  );

  const handleOpenCommentPopUp = useCallback(
    (row: CiImpactedByRelationshipInformationResponseDto) => {
      setCurrentFlagImpactedCi(row);
      setUserComment(row.userComment);
      openComment();
    },
    [openComment],
  );

  const handleChangeImpactedLevel = useCallback(
    (row: CiImpactedByRelationshipInformationResponseDto, value?: string) => {
      const updatedCmLst = listFlagCi.map((it) =>
        keyImpactedCiAndMergedCisInChangeAndAttType(it) === keyImpactedCiAndMergedCisInChangeAndAttType(row)
          ? { ...it, impactedAssessmentLevel: value }
          : it,
      );
      setListFlagCi(updatedCmLst);
    },
    [listFlagCi, setListFlagCi],
  );
  const columns: ColumnType<CiImpactedByRelationshipInformationResponseDto>[] = useMemo(() => {
    const cols = [
      {
        title: 'Impacted CI name',
        sortable: true,
        name: 'ciImpactedName',
        width: '30%',
        customRender: (_: string, rowData: CiImpactedByRelationshipInformationResponseDto) => {
          return (
            <KanbanButton
              size='compact-xs'
              radius={'lg'}
              variant={'subtle'}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(buildCiUrl(rowData.ciTypeImpactedId, rowData.ciImpactedId, CiDetailScreenType.RELATIONSHIPS, CiDetailSubTabType.LISTVIEW));
              }}>
              {rowData.ciImpactedName}
            </KanbanButton>
          );
        },
      },
      {
        title: 'Impacted CI type',
        sortable: true,
        name: 'ciTypeImpactedName',
        customRender: (data: string, rowData: CiImpactedByRelationshipInformationResponseDto) => {
          const ciType = allCiTypeMap[rowData.ciTypeImpactedId];
          return (
            ciType && (
              <KanbanButton
                size='compact-xs'
                radius={'lg'}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  navigate(buildCiTypeUrl(ciType.id));
                }}>
                {ciType.name}
              </KanbanButton>
            )
          );
        },
        width: '20%',
      },
      {
        title: 'CI Types in change plan',
        sortable: true,
        name: 'rootCiTypeName',
        width: '20%',
        //051224 hide CI Types in change plan
        hidden: true,
        customRender: (data: string, rowData: CiImpactedByRelationshipInformationResponseDto) => {
          return renderCiType(rowData);
        },
      },
      {
        title: 'CI Names in change plan',
        sortable: true,
        name: 'rootCiName',
        width: '20%',
        customRender: (data: string, rowData: CiImpactedByRelationshipInformationResponseDto) => {
          //display merge ciInChanges when add  multiple records with same impacted ci from By Rule table
          return renderCiNamesInChangePlan(rowData);
        },
      },
      {
        title: 'Impacted level',
        sortable: true,
        //3849: flag table: hidden impact level
        hidden: true,
        name: 'impactedLevelAssessment',
        width: '20%',
        customRender: (data: string, rowData: CiImpactedByRelationshipInformationResponseDto) => {
          return allowEdit ? (
            <>
              <KanbanSelect
                value={rowData.impactedAssessmentLevel}
                ///CMDB-4044
                key={rowData.ciImpactedId}
                clearable
                placeholder='Select impacted level'
                //Actual init view  is init state value of showAllCiImpacted
                data={IMPACTED_LEVEL_OPTIONS}
                onChange={(e) => {
                  handleChangeImpactedLevel(rowData, e || '');
                }}
              />
            </>
          ) : (
            <>{rowData.impactedAssessmentLevel}</>
          );
        },
      },
      {
        //3849 : anyone can comment
        title: 'Comment',
        sortable: true,
        name: 'comment',
        width: '20%',
        customRender: (_: string, rowData: CiImpactedByRelationshipInformationResponseDto) => {
          return (
            <Flex>
              {rowData.userComment && (
                <Tooltip label={rowData.userComment} multiline w={'20%'}>
                  <Flex>
                    <KanbanText className={stylesCss.clipText} fz={'xs'}>
                      {rowData.userComment}
                    </KanbanText>
                  </Flex>
                </Tooltip>
              )}
              <Box w={'20px'} h={'20px'}></Box>
              {/* 051224 , allow view comment popup in VIEW mode */}
              {/* 3849: flagged table required comment */}
              {
                <KanbanIconButton
                  c={FlaggedImpactedCiAttachedType.MANUAL === rowData.attachedType && !rowData.userComment ? 'red' : ''}
                  size={'sm'}
                  variant='transparent'
                  onClick={() => handleOpenCommentPopUp(rowData)}
                  className={stylesCss.topRightButton}>
                  <IconMessage />
                </KanbanIconButton>
              }
            </Flex>
          );
        },
      },

      // {
      //   title: 'Level',
      //   sortable: true,
      //   name: 'relationshipLevel',
      //   width: '5%',
      //   customRender: (data: number) => {
      //     return data <= 0 ? <></> : <>{data}</>;
      //   },
      // },
      // {
      //   title: 'Relationship',
      //   sortable: true,
      //   name: 'relationshipName',
      //   width: '10%',
      //   customRender: (data: string, rowData: CiImpactedByRelationshipInformationResponseDto) => {
      //     //if relation ship level > 1 then not show indirect relation ship
      //     return rowData.relationshipLevel > 1 ? <></> : allRelationShipTypesMap[rowData.relationshipId]?.type;
      //   },
      // },
      {
        title: 'Attached type',
        sortable: true,
        name: 'attachedType',
        width: '20%',
        customRender: (data: string) => {
          const isManual = FlaggedImpactedCiAttachedType.MANUAL === data;
          return (
            <Badge mt='5' color={isManual ? 'var(--mantine-color-green-8)' : 'var(--mantine-color-orange-5)'}>
              {data || FlaggedImpactedCiAttachedType.CALCULATED}
            </Badge>
          );
        },
      },
    ];
    return cols;
  }, [allCiTypeMap, allowEdit, handleChangeImpactedLevel, handleOpenCommentPopUp, navigate, renderCiNamesInChangePlan, renderCiType]);

  const onSearched = useCallback(
    (datas: CiImpactedByRelationshipInformationResponseDto[], search: string): CiImpactedByRelationshipInformationResponseDto[] => {
      const lowerCaseSearch = search.toLowerCase();
      return datas.filter((item) => {
        const ciTypeImpactedName = allCiTypeMap[item.ciTypeImpactedId]?.name || '';
        const rootCiTypeName = allCiTypeMap[item.rootCiTypeId]?.name || '';
        // const relationshipName = allRelationShipTypesMap[item.relationshipId]?.type || '';
        //3130: build list of ciInChange's name => a searchable string.
        const manualCisInChange = item.ciChangePlans?.map((it) => `${it.name}-${allCiTypeMap[it.ciTypeId]?.name}`).join('###');
        // const createDate = item.createdDateCiImpacted ? dateToString(stringToDate(item.createdDateCiImpacted), DD_MM_YYYY_HH_MM_FORMAT) : '';
        // const createBy = item.createdByCiImpacted || '';
        const attachedType = item.attachedType || '';
        return [
          ciTypeImpactedName,
          item.ciImpactedName,
          item.rootCiName,
          rootCiTypeName,
          item.relationshipLevel?.toString() || '',
          // relationshipName,
          item.createdByCiImpacted,
          manualCisInChange,
          // createDate,
          // createBy,
          attachedType,
        ].some((item) => (item || '').toLowerCase().includes(lowerCaseSearch));
      });
    },
    [allCiTypeMap],
  );
  const tableProps: KanbanTableProps<CiImpactedByRelationshipInformationResponseDto> = useMemo(() => {
    return {
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        onSearched: onSearched,
      },
      sortable: {
        enable: true,
      },
      columns: columns,
      data: listFlagCi,
      key: 1,
      //diable cause get all-> filter on FE
      // serverside: {
      //   totalRows: totalRecordsListFlagCi,
      //   onTableAffected(dataSet) {
      //     if (!equal(pagingListFlagCi, dataSet)) {
      //       setPagingListFlagCi(dataSet);
      //     }
      //   },
      // },
      pagination: {
        enable: true,
      },
      selectableRows: {
        enable: true,
        onDeleted(rows) {
          if (allowEdit) {
            const deleteKeySet = new Set(rows.map((item) => keyImpactedCiAndMergedCisInChangeAndAttType(item)));
            setListFlagCi(listFlagCi.filter((item) => !deleteKeySet.has(keyImpactedCiAndMergedCisInChangeAndAttType(item))));
          }
        },
      },
      actions: {
        deletable: {
          onDeleted(listDelete) {
            if (allowEdit) {
              const deleteKeySet = new Set([listDelete].map((item) => keyImpactedCiAndMergedCisInChangeAndAttType(item)));
              setListFlagCi(listFlagCi.filter((item) => !deleteKeySet.has(keyImpactedCiAndMergedCisInChangeAndAttType(item))));
            }
          },
        },
        customAction: (data) => {
          return (
            <>
              <Tooltip label='View change'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  disabled={listCiDraft.every((x) => x.ciId !== data.ciImpactedId)}
                  onClick={() => {
                    onClickViewDraft(data.ciImpactedId);
                  }}>
                  <IconClipboardList />
                </KanbanIconButton>
              </Tooltip>
              <GuardComponent requirePermissions={AclPermission.createViewCiPermissions(data.ciImpactedId, data.ciTypeImpactedId)} hiddenOnUnSatisfy>
                <Tooltip label='View info'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setCurrentCiInfo({
                        ciId: data.ciImpactedId,
                        ciTypeId: data.ciTypeImpactedId,
                      });
                      openModalViewCi();
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                </Tooltip>
              </GuardComponent>
              <Tooltip label={data.attachedType === FlaggedImpactedCiAttachedType.CALCULATED ? 'Impact Map' : 'Relationship Map'}>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    setCurrentCiInfo({
                      ciId: data.ciImpactedId,
                      ciTypeId: data.ciTypeImpactedId,
                    });
                    openModalRelationship();
                    setCurrentFlagImpactedCi(data);
                    setCiIdChangePlans(data.ciChangePlans?.map((ciChangePlan) => ciChangePlan.id));
                  }}>
                  <IconAffiliate />
                </KanbanIconButton>
              </Tooltip>
              {/* only show view history when relationship is current in DB */}
              {/* {data.ciImpactedHistoryId && (
                <Tooltip label='View flagged ci history '>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      onOpenImpactedCiFlagHistory(data.ciImpactedHistoryId ?? 0);
                    }}>
                    <IconHistory />
                  </KanbanIconButton>
                </Tooltip>
              )} */}
              {allowEdit && (
                <Tooltip label='Edit CIs In Change Plan'>
                  <KanbanIconButton
                    size={'sm'}
                    variant='transparent'
                    onClick={() => {
                      handleOpenSelectCisInChange(data);
                    }}>
                    <IconSitemap />
                  </KanbanIconButton>
                </Tooltip>
              )}
            </>
          );
        },
      },
    };
  }, [
    allowEdit,
    columns,
    handleOpenSelectCisInChange,
    listCiDraft,
    listFlagCi,
    onClickViewDraft,
    onSearched,
    openModalRelationship,
    openModalViewCi,
    setListFlagCi,
  ]);
  const customTableProps = useMemo(() => {
    const tablePropsUpdate = { ...tableProps };
    if (!allowEdit) {
      const { actions, selectableRows, ...rest } = tablePropsUpdate;
      if (selectableRows) {
        selectableRows.enable = false;
      }
      if (actions && actions.deletable) {
        delete actions.deletable;
      }

      return { ...rest, selectableRows, actions };
    }
    return tablePropsUpdate;
  }, [allowEdit, tableProps]);

  const handleUpdateComment = useCallback(() => {
    if (currentFlagImpactedCi) {
      const updatedCmLst = listFlagCi.map((it) =>
        keyImpactedCiAndMergedCisInChangeAndAttType(it) === keyImpactedCiAndMergedCisInChangeAndAttType(currentFlagImpactedCi)
          ? { ...it, userComment: userComment }
          : it,
      );
      setListFlagCi(updatedCmLst);
    }
    closeComment();
  }, [closeComment, currentFlagImpactedCi, listFlagCi, setListFlagCi, userComment]);

  const childRef = useRef<CiUpdateSoleMethods>(null);
  const childRefViewDetail = useRef<CiManagementDetailViewPopupMethods>(null);
  return (
    <>
      {/* modal view detail info of CI management draft */}
      <CiManagementDetailViewPopup ref={childRefViewDetail} initData={currentCiTemp} />

      <KanbanConfirmModal
        title={'CI Detail'}
        onClose={closeModalViewCi}
        onConfirm={undefined}
        opened={openedModalViewCi}
        modalProps={{
          size: '80%',
        }}>
        {currentCiInfo && (
          <ScrollArea.Autosize mah={1000} type='scroll'>
            <CiUpdateSole readOnly={true} ciId={currentCiInfo.ciId} ciTypeId={currentCiInfo.ciTypeId} forwardedRef={childRef} />
          </ScrollArea.Autosize>
        )}
      </KanbanConfirmModal>

      <KanbanConfirmModal
        title={FlaggedImpactedCiAttachedType.CALCULATED === currentFlagImpactedCi?.attachedType ? 'Impact Map' : 'Relationship'}
        onConfirm={undefined}
        onClose={closeModalRelationship}
        opened={openedModalRelationship}
        modalProps={{
          size: '80%',
        }}>
        {currentCiInfo && (
          <CiRelationship
            ciId={currentCiInfo.ciId}
            ciTypeId={currentCiInfo.ciTypeId}
            isView
            isFromBusinessView={true}
            ciInChangePlan={ciIdChangePlans}
            impactedCi={currentFlagImpactedCi?.ciImpactedId}
            attachedType={currentFlagImpactedCi?.attachedType}
            screenType={ScreenTypeEnum.FLAGGED_CIS}
            toolBarConfig={toolBarConfigImpactMap}
            defaultLevel={Number(maxLevel?.value || 0)}
          />
        )}
      </KanbanConfirmModal>
      <KanbanConfirmModal
        title={'Impacted CI relationship history'}
        onClose={closeModalViewImpactedCiRelationshipHistory}
        onConfirm={undefined}
        opened={openedModalViewImpactedCiRelationshipHistory}
        modalProps={{
          size: '80%',
        }}>
        <ScrollArea.Autosize mah={1000} type='scroll'>
          <ImpactedCiFlagHistoryTableComponent ciImpactedHistoryId={currentViewHistoryImpactedCiId}></ImpactedCiFlagHistoryTableComponent>
        </ScrollArea.Autosize>
      </KanbanConfirmModal>
      <KanbanConfirmModal
        title={`Comment on flagged ci : ${currentFlagImpactedCi?.ciImpactedName}`}
        onClose={closeComment}
        //051224 , allow view comment popup in VIEW mode
        onConfirm={allowEdit ? handleUpdateComment : undefined}
        opened={openedComment}
        modalProps={{
          size: '50%',
        }}>
        <KanbanTextarea
          value={userComment}
          disabled={!allowEdit}
          resize={'vertical'}
          placeholder='Comment on impacted CI'
          maxLength={3000}
          //051224 cai tien comment box
          className={stylesCss.textArea}
          onChange={(e) => {
            setUserComment(e.target.value);
          }}
        />
      </KanbanConfirmModal>

      <KanbanConfirmModal
        title={`Manual impacted CI: ${currentFlagImpactedCi?.ciImpactedName}  - CIs in change plan `}
        onClose={closeManualCiChange}
        onConfirm={handleUpdateCisInChange}
        opened={openedManualCiChange}
        modalProps={{
          size: '90%',
        }}>
        <ManualCiInChangePlan
          listFlagCi={listFlagCi}
          listCiChange={listCiChange}
          allowEdit
          currentFlagImpactedCi={currentFlagImpactedCi}
          setCurrentListCiInChangeNew={setCurrentListCiInChangeNew}
          currentListCiInChangeNew={currentListCiInChangeNewOrRemove}
        />
      </KanbanConfirmModal>

      <KanbanConfirmModal
        title={`Calculated impacted CI: ${currentFlagImpactedCi?.ciImpactedName}  - CIs in change plan `}
        onClose={closeCalculatedCiChange}
        onConfirm={handleUpdateCisInChange}
        textConfirm='Confirm remove CIs'
        opened={openedCalculatedCiChange}
        modalProps={{
          size: '90%',
        }}>
        {currentFlagImpactedCi && (
          <CalculatedCiInChangePlanTable
            allowEdit={allowEdit}
            currentFlagImpactedCi={currentFlagImpactedCi}
            setCurrentFlagImpactedCi={setCurrentFlagImpactedCi}
            setCurrentListCiInChangeNew={setCurrentListCiInChangeNew}
          />
        )}
      </KanbanConfirmModal>

      <KanbanTable {...customTableProps} />
    </>
  );
};
