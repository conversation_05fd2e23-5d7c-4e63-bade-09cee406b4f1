import { KanbanIconButton } from 'kanban-design-system';
import { Box, NavLink, ScrollArea } from '@mantine/core';
import { IconChevronRight } from '@tabler/icons-react';
import React, { memo, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import classes from './BusinessDomainItem.module.scss';
import type { BusinessDomainItem } from '@models/BusinessDomain';
import { useMounted } from '@mantine/hooks';

export type BusinessDomainTreeDetailComponentProps = {
  data: BusinessDomainItem[];
  value?: number;
  onChange?: (newValue?: number, item?: BusinessDomainItem) => boolean | void; //Return boolean for change value
  onMouseDown?: (value?: number, item?: BusinessDomainItem) => void;
  customActive?: (item: BusinessDomainItem, ref: React.RefObject<HTMLAnchorElement>) => boolean;
  leftSection?: (item: BusinessDomainItem) => React.ReactNode;
  rightSection?: (item: BusinessDomainItem) => React.ReactNode;
  rightSectionHover?: (item: BusinessDomainItem) => boolean;
  customHidden?: (item: BusinessDomainItem) => boolean;
  customExpand?: (item: BusinessDomainItem) => boolean;
  onChangeExpand?: (item: BusinessDomainItem, state: boolean) => void;
  customListCiTypes?: BusinessDomainItem[];
  showAllCIs?: boolean;
  showInfo?: boolean;
  disabled?: (item: BusinessDomainItem) => boolean;
  onCheckBox?: (value?: boolean, item?: BusinessDomainItem, data?: BusinessDomainItem[]) => void;
  dataCheckboxs?: BusinessDomainItem[];
  scrollHeight?: number;
  dataDisables?: number[];
  controlOpenedCiTypes: number[];
};

export const BusinessDomainTreeDetailComponent = memo((props: BusinessDomainTreeDetailComponentProps) => {
  const [controlOpenedCiTypes, setControlOpenedCiTypes] = useState<number[]>(props.controlOpenedCiTypes);
  const [value, setValue] = useState<number | undefined>(props.value);
  const [allowRender, setAllowRender] = useState<boolean>(false);

  useEffect(() => {
    setValue(props.value);
  }, [props.value]);
  useEffect(() => {
    if (!allowRender) {
      setControlOpenedCiTypes(props.controlOpenedCiTypes);
    }
  }, [props.controlOpenedCiTypes, allowRender]);
  const onChangeValue = (item?: BusinessDomainItem) => {
    if (props.onChange) {
      const result = props.onChange(value, item);
      if (result !== false) {
        setValue(value);
      }
    } else {
      setValue(value);
    }
  };

  const listCiTypes = props.data;

  useEffect(() => {
    if (!allowRender && controlOpenedCiTypes.length !== 0 && listCiTypes.length !== 0) {
      setAllowRender(true);
    }
  }, [allowRender, listCiTypes, controlOpenedCiTypes]);

  const roots = useMemo(() => {
    return listCiTypes.filter((item) => item.parentId === null);
  }, [listCiTypes]);
  return (
    <>
      {allowRender && (
        <ScrollArea flex={1} h={props.scrollHeight}>
          <Box>
            {roots.map((item, index) => (
              <RenderCiTypesCustomLink
                key={index}
                isRoot={true}
                isAutoScrollOnActive={true}
                onCheckBox={props.onCheckBox}
                dataCheckboxs={props.dataCheckboxs}
                node={item}
                ciTypes={listCiTypes}
                selectedId={value}
                onClick={onChangeValue}
                customActive={props.customActive}
                customHidden={props.customHidden}
                leftSection={props.leftSection}
                rightSection={props.rightSection}
                customExpand={(node) => {
                  const isExpland = controlOpenedCiTypes.includes(node.id);
                  return isExpland;
                }}
                onChangeExpand={(node, isOpen) => {
                  setControlOpenedCiTypes((prev) => {
                    if (!isOpen) {
                      return prev.filter((x) => x !== node.id);
                    }
                    return [...prev, node.id];
                  });
                }}
                disabled={props.disabled}
                onMouseDown={props.onMouseDown}
                dataDisables={props.dataDisables}
              />
            ))}
          </Box>
        </ScrollArea>
      )}
    </>
  );
});
BusinessDomainTreeDetailComponent.displayName = 'BusinessDomainTreeDetailComponent';
BusinessDomainTreeDetailComponent.whyDidYouRender = true;

const RenderCiTypesCustomLink = (props: {
  isRoot: boolean;
  isForceOpenAll?: boolean;
  node: BusinessDomainItem;
  ciTypes: BusinessDomainItem[];
  selectedId?: number;
  onClick: (item?: BusinessDomainItem) => void;
  onMouseDown?: (value?: number, item?: BusinessDomainItem) => void;
  onCheckBox?: (value?: boolean, item?: BusinessDomainItem) => void;
  isAutoScrollOnActive?: boolean;
  customActive?: (item: BusinessDomainItem, ref: React.RefObject<HTMLAnchorElement>) => boolean;
  customHidden?: (item: BusinessDomainItem) => boolean;
  leftSection?: (item: BusinessDomainItem) => React.ReactNode;
  rightSection?: (item: BusinessDomainItem) => React.ReactNode;
  customExpand?: (item: BusinessDomainItem) => boolean;
  onChangeExpand?: (item: BusinessDomainItem, state: boolean) => void;
  dataCheckboxs?: BusinessDomainItem[];
  dataDisables?: number[];
  disabled?: (item: BusinessDomainItem) => boolean;
}) => {
  const { ciTypes, customActive, isAutoScrollOnActive, node } = props;
  const children = ciTypes.filter((item) => item.parentId === node.id);
  const viewRef = useRef<HTMLAnchorElement>(null);
  const isDisabled = props.disabled && props.disabled(node);
  const isActive = customActive ? customActive(node, viewRef) : node.id === props.selectedId;
  const className = useMemo(() => {
    let final = !props.isRoot ? classes.link : '';
    if (isDisabled) {
      final += ` ${classes.disabled}`;
    }
    return final;
  }, [props.isRoot, isDisabled]);
  const isMounted = useMounted();
  useLayoutEffect(() => {
    if (isMounted && isAutoScrollOnActive && isActive) {
      requestAnimationFrame(() => {
        viewRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
      });
    }
  }, [isActive, isAutoScrollOnActive, isMounted]);
  if (props.customHidden && props.customHidden(node)) {
    return <></>;
  }

  const isOpened = (props.customExpand && props.customExpand(node)) || !!props.isForceOpenAll;

  return (
    <NavLink
      key={node.id}
      ref={viewRef}
      className={className}
      label={node.name}
      disableRightSectionRotation
      rightSection={
        <>
          {props.rightSection && <>{props.rightSection(node)}</>}
          {children.length > 0 && (
            <Box className={!isOpened ? classes.iconSection : classes.iconSectionActive}>
              <KanbanIconButton
                size={'sm'}
                variant='default'
                onClick={(e) => {
                  e.stopPropagation();
                  if (props.onChangeExpand) {
                    props.onChangeExpand(node, !isOpened);
                  }
                }}>
                <IconChevronRight size='0.8rem' stroke={1.5} />
              </KanbanIconButton>
            </Box>
          )}
        </>
      }
      active={isActive}
      variant='light'
      opened={isOpened || false}
      onClick={() => {
        if (isDisabled) {
          return;
        }
        props.onClick(node);
        return;
      }}
      onMouseDown={() => {
        if (props.onMouseDown) {
          props.onMouseDown(node.id, node);
        }
        return;
      }}>
      {children.length
        ? children.map((item, key) => (
            <RenderCiTypesCustomLink
              key={key}
              isRoot={false}
              node={item}
              isAutoScrollOnActive={isAutoScrollOnActive}
              isForceOpenAll={props.isForceOpenAll}
              selectedId={props.selectedId}
              onClick={props.onClick}
              customActive={customActive}
              customHidden={props.customHidden}
              customExpand={props.customExpand}
              onChangeExpand={props.onChangeExpand}
              ciTypes={ciTypes}
              leftSection={props.leftSection}
              rightSection={props.rightSection}
              disabled={props.disabled}
              onMouseDown={props.onMouseDown}
              onCheckBox={props.onCheckBox}
              dataCheckboxs={props.dataCheckboxs}
              dataDisables={props.dataDisables}
            />
          ))
        : null}
    </NavLink>
  );
};
