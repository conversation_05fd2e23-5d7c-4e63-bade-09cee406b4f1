import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import type { CiTypeReferFieldsModel } from '@models/ConfigItemTypeAttr';

export class CiTypeReferenceApi extends BaseApi {
  static baseUrl = BaseUrl.ciTypeReferenceFields;

  static findAllByIdIn(ids: number[]) {
    return BaseApi.getData<CiTypeReferFieldsModel[]>(`${this.baseUrl}`, { ids: ids }, {}, { useLoading: false, useErrorNotification: true });
  }

  static findAllByCiTypeIdAndCiTypeAttributeIdIn(ciTypeId: number, ids: number[]) {
    return BaseApi.getData<CiTypeReferFieldsModel[]>(`${this.baseUrl}`, { ciTypeId, ids });
  }
}
