import { GroupResponse, GroupsApi, UserSettingGroupModel } from '@api/systems/GroupsApi';
import { UserModel, UserResponse, UsersApi } from '@api/systems/UsersApi';
import { KanbanTableSelectHandleMethods, KanbanTabs, KanbanText, KanbanTooltip, useKanbanModals } from 'kanban-design-system';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { NotificationError, NotificationSuccess } from '@common/utils/NotificationUtils';
import GuardComponent from '@components/GuardComponent';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { KanbanIconButton } from 'kanban-design-system';
import { KanbanCheckbox } from 'kanban-design-system';
import { KanbanInput } from 'kanban-design-system';
import { KanbanModal } from 'kanban-design-system';
import { KanbanSplitPagingContainer } from 'kanban-design-system';
import { KanbanTable, KanbanTableProps, TableAffactedSafeType } from 'kanban-design-system';
import { getDefaultTableAffected, renderDateTime } from 'kanban-design-system';
import { Alert, Chip, Flex, Group, Pill, PillsInput, Space } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { AclPermission } from '@models/AclPermission';
import { IconCircleCheck, IconPlus, IconFridge, IconX, IconSettings, IconAlertCircle } from '@tabler/icons-react';
import equal from 'fast-deep-equal';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styled from 'styled-components';
import {
  tableAffectedToMultiColumnFilterPaginationRequestModel,
  tableAffectedToPaginationRequestModelWithSortDefault,
} from '@common/utils/KanbanTableUtils';
import { IconFriendsOff } from '@tabler/icons-react';
import { IconFriends } from '@tabler/icons-react';
import { IconFridgeOff } from '@tabler/icons-react';
import { useSelector } from 'react-redux';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { BreadcrumbComponent } from '../breadcrumb/BreadcrumbComponent';
import { MAX_TEXT_LENGTH } from '@common/constants/FieldLengthConstants';

const ListUserWrapper = styled.div`
  display: grid;
  column-gap: var(--mantine-spacing-xs);
  row-gap: var(--mantine-spacing-xs);
  grid-template-columns: auto auto auto;
`;
const UserItem = styled.div`
  padding: var(--mantine-spacing-xs);
  border: 1px solid gray;
  cursor: pointer;
  &:hover {
    border-color: var(--mantine-color-primary-6);
  }
  .title {
    font-size: var(--mantine-font-size-md);
  }
  .description {
    color: gray;
    font-size: var(--mantine-font-size-sm);
  }
`;
const userDefault: UserModel = {
  id: 0,
  userName: '',
  email: '',
  isActive: false,
  isSuperAdmin: false,
};
const MB_EMAIL_PREFIX = '@mbbank.com.vn';
const COMMA = ',';
const isValidUserName = (userName: string) => {
  const regex = /^[a-zA-Z][a-zA-Z0-9._-]*$/;
  return regex.test(userName) && userName.length <= 100;
};

type UserCreateInfoModel = {
  invalidUsers: string[];
  validUsers: string[];
};
const getUserNames = (userInput: string): UserCreateInfoModel => {
  const usersInput = userInput.split(COMMA);
  const validUsers: string[] = [];
  const invalidUsers: string[] = [];

  usersInput.forEach((user) => {
    let userName = user.trim();
    if (userName.length > 0) {
      if (userName.includes(MB_EMAIL_PREFIX)) {
        userName = userName.split('@')[0];
      }
      if (isValidUserName(userName)) {
        validUsers.push(userName);
      } else {
        invalidUsers.push(userName);
      }
    }
  });

  return { validUsers: validUsers, invalidUsers: invalidUsers };
};

enum UsersSettingTabs {
  BASIC = 'BASIC',
  SETTING_GROUP = 'SETTING_GROUP',
}
export const UsersSettingsPage = () => {
  const [openedModal, { close: closePopup, open: openModal }] = useDisclosure(false);
  const [openedModalSettingGroup, { close: closePopupSettingGroup, open: openModalSettingGroup }] = useDisclosure(false);
  const [openedModalCreateUser, { close: closeModalCreateUser, open: openModalCreateUser }] = useDisclosure(false);
  const [userResponses, setUserResponses] = useState<UserResponse[]>([]);
  const [openedModalUserError, { close: closeModalUserError, open: openModalUserError }] = useDisclosure(false);

  const currentUser = useSelector(getCurrentUser);
  const currentUsername = currentUser.data?.username;
  const isSuperAdmin = currentUser.data?.isSuperAdmin === true;

  // const [groupResponses, setGroupResponses] = useState<GroupResponse[]>([]);
  // const [groupSettingResponses, setGroupSettingResponses] = useState<UserSettingGroupModel[]>([]);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType>(getDefaultTableAffected());

  const [totalRecords, setTotalRecords] = useState(0);

  const [newItem, setNewItem] = useState<UserModel>(userDefault);
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [totalGroupRecords, setTotalGroupRecords] = useState(0);
  const [groupResponses, setGroupResponses] = useState<GroupResponse[]>([]);
  // const [groupSettings, setGroupSettings] = useState<UserSettingGroupModel[]>([]);
  // const [groupsOfUser, setGroupsOfUser] = useState<GroupResponse[]>([]);
  const [groupTableAffected, setGroupTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);
  const [groupUsers, setGroupUsers] = useState<GroupResponse[]>([]);
  const [currentGroupSelected, setCurrentGroupSelected] = useState<UserSettingGroupModel[]>([]);
  const [hasViewListGroupPermission, setHasViewListGroupPermission] = useState<boolean>(false);
  const [hasError, setHasError] = useState<boolean>(false);
  const [newUsers, setNewUsers] = useState<string[]>([]);
  const [newUserInput, setNewUserInput] = useState<string>('');
  const [activeTab, setActiveTab] = useState<string>(UsersSettingTabs.BASIC);
  const [userCreateInfo, setUserCreateInfo] = useState<UserCreateInfoModel | undefined>(undefined);

  const fetchGroups = useCallback(() => {
    if (!groupTableAffected) {
      return;
    }
    GroupsApi.getAllGroupInUserPageToView(tableAffectedToPaginationRequestModelWithSortDefault(groupTableAffected, 'active'), newItem.userName)
      .then((response) => {
        const data = response.data;
        setGroupResponses(data.content);
        setTotalGroupRecords(data.totalElements);
        setHasViewListGroupPermission(true);
      })
      .catch(() => {
        setHasError(true);
      });
  }, [groupTableAffected, newItem.userName]);

  useEffect(() => {
    if (newItem.userName) {
      fetchGroups();
    }
  }, [fetchGroups, newItem.userName]);

  // useEffect(() => {
  //   const roles: UserSettingGroupModel[] = groupResponses.map((group) => ({
  //     ...group,
  //     isSetting: groupsOfUser.some((group_) => group_.id === group.id),
  //   }));
  //   setGroupSettings(roles);
  // }, [groupsOfUser, groupResponses]);

  const tableViewListGroupProps: KanbanTableProps<UserSettingGroupModel> = useMemo(() => {
    return {
      title: 'List group',
      columns: [
        {
          name: 'groupActiveId',
          title: 'Group of user',
          customRender: (data) => {
            if (data) {
              return (
                <KanbanIconButton variant={'transparent'}>
                  <IconCircleCheck></IconCircleCheck>
                </KanbanIconButton>
              );
            } else {
              return (
                <KanbanIconButton variant={'transparent'} c={'red'}>
                  <IconX></IconX>
                </KanbanIconButton>
              );
            }
          },
        },
        {
          name: 'name',
          title: 'Name',
        },
        {
          name: 'description',
          title: 'Description',
        },
        {
          name: 'createdBy',
          title: 'Created by',
        },
        {
          name: 'createdDate',
          title: 'Created date',
          customRender: renderDateTime,
        },
      ],
      data: groupResponses,
      pagination: { enable: true },
      searchable: { enable: true, debounceTime: 300 },
      showNumericalOrderColumn: true,
      selectableRows: {
        enable: true,
        onSelectedRowsChanged(rows) {
          setSelectedItems(rows.map((item) => item.id));
        },
        crossPageSelected: {
          rowKey: 'id',
          selectedRows: currentGroupSelected,
          setSelectedRows: setCurrentGroupSelected,
        },
      },
      serverside: {
        totalRows: totalGroupRecords,
        onTableAffected: (dataSet) => {
          if (!equal(groupTableAffected, dataSet)) {
            setGroupTableAffected(dataSet);
          }
        },
      },
    };
  }, [currentGroupSelected, groupResponses, groupTableAffected, totalGroupRecords]);

  const fetchUsers = useCallback(() => {
    if (!tableAffected) {
      return;
    }
    const dataSend = tableAffectedToMultiColumnFilterPaginationRequestModel<UserResponse>(
      tableAffected.sortedBy ? tableAffected : { ...tableAffected, sortedBy: 'isActive', isReverse: true },
    );

    UsersApi.getAllUserWithAdmin(dataSend)
      .then((response) => {
        const data = response.data.content;
        setTotalRecords(response.data.totalElements);
        setUserResponses(data || []);
      })
      .catch(() => {});
  }, [tableAffected]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const fetchUsersGroup = useCallback((userName: string) => {
    UsersApi.getAllGroupByUserName(userName)
      .then((response) => {
        const data = response.data;
        setGroupUsers(data || []);
      })
      .catch(() => {});
  }, []);

  const closeModal = () => {
    closePopup();
  };

  const onUpdate = () => {
    UsersApi.activeUser(newItem)
      .then((_res) => {
        fetchUsers();
        closeModal();
        NotificationSuccess({
          message: newItem.isActive ? 'Active user successfully' : 'Inactive user successfully',
        });
      })
      .catch(() => {});
  };

  const onCreate = () => {
    UsersApi.createUser(newUsers)
      .then((_res) => {
        closeModalCreateUser();
        fetchUsers();
        NotificationSuccess({
          message: 'Create user successfully',
        });
      })
      .catch(() => {});
  };

  const addGroupForUser = () => {
    GroupsApi.addGroupForUser(selectedItems, newItem.userName)
      .then(() => {
        tableRef.current?.deselectAll();
        fetchUsersGroup(newItem.userName);
        setSelectedItems([]);
        closePopupSettingGroup();
        NotificationSuccess({
          message: 'Update group successfully',
        });
      })
      .catch(() => {});
  };

  const deleteGroupInUser = (datas: number[]) => {
    GroupsApi.deleteGroupInUser(datas, newItem.userName)
      .then(() => {
        tableRef.current?.deselectAll();
        fetchUsersGroup(newItem.userName);
        setSelectedItems([]);
        closePopupSettingGroup();
        NotificationSuccess({
          message: 'Delete group successfully',
        });
      })
      .catch(() => {});
  };

  const getGroupSetting = () => {
    if (!hasViewListGroupPermission && hasError) {
      NotificationError({
        message: `You don't have permissions: View list user groups.`,
      });
      setSelectedItems([]);
      openModalSettingGroup();
    } else {
      fetchGroups();
      setSelectedItems([]);
      openModalSettingGroup();
      // UsersApi.getAllGroupByUserName(newItem.userName)
      //   .then((response) => {
      //     setSelectedItems([]);
      //     setGroupsOfUser(response.data);
      //     openModalSettingGroup();
      //   })
      //   .catch(() => {});
    }
  };

  const onChangeNewItem = (e: React.ChangeEvent<HTMLInputElement>, name: string) => {
    const { target } = e;
    setNewItem((prev) => {
      return {
        ...prev,
        [name]: target.checked,
      };
    });
  };

  const modelProvider = useKanbanModals();

  const deleteUsersByIdIn = useCallback(
    (ids: number[]) => {
      UsersApi.deleteUsers(ids)
        .then((response) => {
          if (response.status === 200) {
            fetchUsers();
            NotificationSuccess({
              message: 'Delete users successfully',
            });
          }
        })
        .catch(() => {});
    },
    [fetchUsers],
  );

  const activeUsers = useCallback(
    (ids: number[]) => {
      UsersApi.activeUsers(ids)
        .then((response) => {
          if (response.status === 200) {
            fetchUsers();
            NotificationSuccess({
              message: 'active users successfully',
            });
          }
        })
        .catch(() => {});
    },
    [fetchUsers],
  );

  const inActiveUsers = useCallback(
    (ids: number[]) => {
      UsersApi.inActiveUsers(ids)
        .then((response) => {
          if (response.status === 200) {
            fetchUsers();
            NotificationSuccess({
              message: 'Inactive users successfully',
            });
          }
        })
        .catch(() => {});
    },
    [fetchUsers],
  );

  const inSettingUsers = useCallback(
    (usernames: string[], isSuperAdmin: boolean) => {
      UsersApi.inSettingUsers(usernames, isSuperAdmin)
        .then((response) => {
          if (response.status === 200) {
            fetchUsers();
            NotificationSuccess({
              message: 'Setting users successfully',
            });
          }
        })
        .catch(() => {});
    },
    [fetchUsers],
  );

  const tableViewListUserProps: KanbanTableProps<UserResponse> = useMemo(() => {
    return {
      title: 'Users setting',

      columns: [
        {
          name: 'userName',
          title: 'User Name',
          advancedFilter: {
            variant: 'text',
            customProps: { maxLength: MAX_TEXT_LENGTH },
          },
        },
        {
          name: 'email',
          title: 'Email',
          advancedFilter: {
            variant: 'text',
            customProps: { maxLength: MAX_TEXT_LENGTH },
          },
        },
        {
          name: 'isActive',
          title: 'Active',
          customRenderHeader: () => (
            <>
              <KanbanTooltip
                label={
                  <>
                    Filter values:
                    <br />1 = ACTIVE
                    <br />0 = INACTIVE
                  </>
                }>
                <KanbanText truncate='end' fw={700} maw={'200px'}>
                  {'Active'}
                </KanbanText>
              </KanbanTooltip>
            </>
          ),
          advancedFilter: {
            variant: 'number',
            filterModes: ['equals', 'notEquals', 'greaterThan', 'greaterThanOrEqualTo', 'lessThan', 'lessThanOrEqualTo'],
            customProps: { min: 0, max: 1, maxLength: 1 },
          },
          customRender: (data) => {
            return <KanbanIconButton variant={'transparent'}>{data ? <IconCircleCheck /> : <IconX style={{ color: 'red' }} />}</KanbanIconButton>;
          },
        },
        {
          name: 'isSuperAdmin',
          title: 'Super Admin',
          customRenderHeader: () => (
            <>
              <KanbanTooltip
                label={
                  <>
                    Filter values:
                    <br />1 = Super Admin
                    <br />0 = User
                  </>
                }>
                <KanbanText truncate='end' fw={700} maw={'200px'}>
                  {'Super Admin'}
                </KanbanText>
              </KanbanTooltip>
            </>
          ),

          advancedFilter: {
            variant: 'number',
            filterModes: ['equals', 'notEquals', 'greaterThan', 'greaterThanOrEqualTo', 'lessThan', 'lessThanOrEqualTo'],
            customProps: { min: 0, max: 1, maxLength: 1 },
          },
          customRender: (data) => {
            return <KanbanIconButton variant={'transparent'}>{data ? <IconCircleCheck /> : <IconX style={{ color: 'red' }} />}</KanbanIconButton>;
          },
        },
        {
          name: 'createdBy',
          title: 'Created by',
          advancedFilter: {
            variant: 'text',
            customProps: { maxLength: MAX_TEXT_LENGTH },
          },
        },
        {
          name: 'createdDate',
          title: 'Created date',
          advancedFilter: {
            variant: 'date',
            customProps: {
              popoverProps: {
                withinPortal: false,
              },
            },
          },
          customRender: renderDateTime,
        },
        {
          title: 'Modified by',
          name: 'modifiedBy',
          hidden: true,
          advancedFilter: {
            variant: 'text',
            customProps: { maxLength: MAX_TEXT_LENGTH },
          },
        },
        {
          title: 'Modified date',
          name: 'modifiedDate',
          hidden: true,
          advancedFilter: {
            variant: 'date',
            customProps: {
              popoverProps: {
                withinPortal: false,
              },
            },
          },
          customRender: renderDateTime,
        },
      ],
      data: userResponses,
      onRowClicked: (data) => {
        if (isCurrentUserMatchPermissions([AclPermission.viewDetailUser])) {
          fetchUsersGroup(data.userName);
          setNewItem(data);
          setActiveTab(UsersSettingTabs.BASIC);
          openModal();
        }
      },
      advancedFilterable: {
        enable: true,
        debounceTime: 1000,
        resetOnClose: true,
        compactMode: true,
      },
      pagination: { enable: true },
      searchable: { enable: true, debounceTime: 300 },
      showNumericalOrderColumn: true,
      selectableRows: {
        enable: !!isCurrentUserMatchPermissions([AclPermission.deleteUser, AclPermission.updateUser]),
        onDeleted: isCurrentUserMatchPermissions([AclPermission.deleteUser])
          ? (rows) => {
              deleteUsersByIdIn(rows.map((item) => item.id));
            }
          : undefined,
        customAction: (rows, methods) => {
          return (
            <>
              <GuardComponent requirePermissions={[AclPermission.updateUser]} hiddenOnUnSatisfy>
                <KanbanButton
                  c={'white'}
                  size='xs'
                  onClick={() => {
                    const idModelActive = modelProvider.openConfirmModal({
                      title: 'Confirm active users',
                      children: 'Are you sure to active these item(s)?',
                      onConfirm: () => {
                        activeUsers(rows.map((item) => item.id));
                        methods.deselectAll();
                        modelProvider.closeModal(idModelActive);
                      },
                    });
                  }}>
                  Active
                </KanbanButton>
                <KanbanButton
                  c={'white'}
                  size='xs'
                  onClick={() => {
                    const idModelInActive = modelProvider.openConfirmModal({
                      title: 'Confirm inactive users',
                      children: 'Are you sure to inactive these item(s)?',
                      onConfirm: () => {
                        inActiveUsers(rows.map((item) => item.id));
                        methods.deselectAll();
                        modelProvider.closeModal(idModelInActive);
                      },
                    });
                  }}>
                  Inactive
                </KanbanButton>
              </GuardComponent>
            </>
          );
        },
      },
      actions: {
        customAction: isCurrentUserMatchPermissions(AclPermission.actionTableUserPermissions)
          ? (data) => (
              <>
                <GuardComponent requirePermissions={[AclPermission.updateUser]} hiddenOnUnSatisfy>
                  <KanbanTooltip label={data.isActive ? 'Inactive user' : 'Active user'}>
                    <KanbanIconButton
                      key={1}
                      size='sm'
                      color='blue'
                      variant='transparent'
                      onClick={() => {
                        const idModelActive = modelProvider.openConfirmModal({
                          title: `Confirm  ${data.isActive ? 'inactive' : 'active'} user`,
                          children: `Are you sure to ${data.isActive ? 'inactive' : 'active'} this user?`,
                          onConfirm() {
                            if (data.isActive) {
                              inActiveUsers([data.id]);
                            } else {
                              activeUsers([data.id]);
                            }
                            modelProvider.closeModal(idModelActive);
                          },
                        });
                      }}>
                      {data.isActive ? <IconFriendsOff /> : <IconFriends />}
                    </KanbanIconButton>
                  </KanbanTooltip>
                </GuardComponent>

                {isSuperAdmin && currentUsername !== data.userName && (
                  <KanbanTooltip label='Setting super admin'>
                    <KanbanIconButton
                      key={3}
                      size='sm'
                      color='blue'
                      variant='transparent'
                      onClick={() => {
                        const idModelActive = modelProvider.openConfirmModal({
                          title: `Confirm ${data.isSuperAdmin ? 'inactive' : 'active'} user is super admin`,
                          children: `Confirm ${data.isSuperAdmin ? 'inactive' : 'active'} user is super admin?`,
                          onConfirm() {
                            if (data.isSuperAdmin) {
                              inSettingUsers([data.userName], false);
                            } else {
                              inSettingUsers([data.userName], true);
                            }
                            modelProvider.closeModal(idModelActive);
                          },
                        });
                      }}>
                      {data.isSuperAdmin ? <IconFridgeOff /> : <IconFridge />}
                    </KanbanIconButton>
                  </KanbanTooltip>
                )}
              </>
            )
          : undefined,
        deletable: isCurrentUserMatchPermissions([AclPermission.deleteUser])
          ? {
              onDeleted: (data) => {
                deleteUsersByIdIn([data.id]);
              },
            }
          : undefined,
      },
      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
    };
  }, [
    activeUsers,
    currentUsername,
    deleteUsersByIdIn,
    fetchUsersGroup,
    inActiveUsers,
    inSettingUsers,
    isSuperAdmin,
    modelProvider,
    openModal,
    tableAffected,
    totalRecords,
    userResponses,
  ]);

  return (
    <div>
      {/* 4763 users list */}
      <BreadcrumbComponent />
      <KanbanModal
        size={'70%'}
        opened={openedModal}
        onClose={closeModal}
        centered
        title={'User details'}
        actions={
          UsersSettingTabs.BASIC === activeTab && (
            <GuardComponent requirePermissions={[AclPermission.updateUser]} hiddenOnUnSatisfy>
              <KanbanButton onClick={onUpdate}>Update</KanbanButton>
            </GuardComponent>
          )
        }>
        <KanbanTabs
          configs={{
            defaultValue: UsersSettingTabs.BASIC,
            value: activeTab,
            variant: 'outline',
            onChange: (value) => {
              if (value) {
                setActiveTab(value);
              }
            },
          }}
          tabs={{
            BASIC: {
              title: 'User Info',
              content: (
                <>
                  <KanbanInput disabled={true} label='ID' value={newItem.id || 0}></KanbanInput>
                  <KanbanInput disabled={true} label='userName' value={newItem.userName || ''}></KanbanInput>
                  <KanbanInput disabled={true} label='Email' value={newItem.email || ''}></KanbanInput>
                  <KanbanCheckbox
                    label='Active'
                    checked={newItem.isActive}
                    onChange={(e) => {
                      onChangeNewItem(e, 'isActive');
                    }}></KanbanCheckbox>
                </>
              ),
            },
            SETTING_GROUP: {
              title: 'Setting Group',
              content: (
                <>
                  <Flex justify={'flex-end'} mb={'md'}>
                    <GuardComponent requirePermissions={[AclPermission.chooseGroupToAddUser, AclPermission.deleteGroupInUser]} hiddenOnUnSatisfy>
                      <KanbanButton leftSection={<IconSettings />} onClick={getGroupSetting} size='sm'>
                        Setting group
                      </KanbanButton>
                    </GuardComponent>
                  </Flex>
                  <KanbanTable
                    title='List Group'
                    columns={[
                      {
                        name: 'name',
                        title: 'Name',
                      },
                      {
                        name: 'description',
                        title: 'Description',
                      },
                      {
                        name: 'createdBy',
                        title: 'Created by',
                      },
                      {
                        name: 'createdDate',
                        title: 'Created date',
                        customRender: renderDateTime,
                      },
                    ]}
                    data={groupUsers}
                    pagination={{ enable: true }}
                    searchable={{
                      enable: true,
                      debounceTime: 300,
                    }}
                    showNumericalOrderColumn={true}
                    selectableRows={{
                      enable: isCurrentUserMatchPermissions([AclPermission.deleteGroupInUser]),
                      onDeleted: isCurrentUserMatchPermissions([AclPermission.deleteGroupInUser])
                        ? () => {
                            deleteGroupInUser(selectedItems);
                          }
                        : undefined,
                      onSelectedRowsChanged(rows) {
                        setSelectedItems(rows.map((item) => item.id));
                      },

                      crossPageSelected: {
                        rowKey: 'id',
                        selectedRows: currentGroupSelected,
                        setSelectedRows: setCurrentGroupSelected,
                      },
                    }}
                    actions={{
                      deletable: isCurrentUserMatchPermissions([AclPermission.deleteGroupInUser])
                        ? {
                            onDeleted: (data) => {
                              deleteGroupInUser([data.id]);
                            },
                          }
                        : undefined,
                    }}
                  />
                </>
              ),
            },
          }}
        />
      </KanbanModal>

      <KanbanModal
        size={'70%'}
        opened={openedModalSettingGroup}
        onClose={() => {
          closePopupSettingGroup();
          tableRef.current?.deselectAll();
        }}
        centered
        title={'Setting group'}
        actions={
          <>
            <Flex>
              <GuardComponent requirePermissions={[AclPermission.chooseGroupToAddUser]} hiddenOnUnSatisfy>
                <KanbanButton disabled={!selectedItems.length} rightSection={<IconCircleCheck />} onClick={addGroupForUser}>
                  Add
                </KanbanButton>
              </GuardComponent>
              <Space w={'xs'} />
              <GuardComponent requirePermissions={[AclPermission.deleteGroupInUser]} hiddenOnUnSatisfy>
                <KanbanButton
                  disabled={!selectedItems.length}
                  rightSection={<IconX />}
                  onClick={() => {
                    deleteGroupInUser(selectedItems);
                  }}>
                  Remove
                </KanbanButton>
              </GuardComponent>
            </Flex>
          </>
        }>
        <KanbanTable ref={tableRef} {...tableViewListGroupProps} />
      </KanbanModal>

      <KanbanModal
        size={'xl'}
        opened={openedModalCreateUser}
        onClose={closeModalCreateUser}
        centered
        title={'Create User'}
        actions={
          <KanbanButton
            disabled={newUsers.length === 0}
            onClick={() => {
              onCreate();
            }}>
            Save
          </KanbanButton>
        }>
        <PillsInput>
          {newUsers.map((user, index) => (
            <Pill key={index} withRemoveButton onRemove={() => setNewUsers((prev) => [...prev.filter((obj) => obj !== user)])}>
              {user}
            </Pill>
          ))}
          <PillsInput.Field
            w={'100%'}
            value={newUserInput}
            onChange={(data) => setNewUserInput(data.target.value)}
            placeholder='Enter UserName'
            onBlur={() => {
              const userInfo = getUserNames(newUserInput);
              if (userInfo.invalidUsers.length === 0) {
                setNewUsers((prev) => Array.from(new Set([...prev, ...userInfo.validUsers])));
                setNewUserInput('');
              } else {
                setUserCreateInfo(userInfo);
                openModalUserError();
              }
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                const userInfo = getUserNames(newUserInput);
                if (userInfo.invalidUsers.length === 0) {
                  setNewUsers((prev) => Array.from(new Set([...prev, ...userInfo.validUsers])));
                  setNewUserInput('');
                } else {
                  setUserCreateInfo(userInfo);
                  openModalUserError();
                }
              }
            }}
          />
        </PillsInput>

        <KanbanModal size={'70%'} opened={openedModalUserError} onClose={closeModalUserError} centered title={'List invalid user'}>
          <>
            <Chip.Group multiple>
              <Group>
                {userCreateInfo?.invalidUsers.map((user, key) => (
                  <Chip size='md' color='indigo' key={key}>
                    {user}
                  </Chip>
                ))}
              </Group>
            </Chip.Group>
            <Alert
              m={10}
              title="Username can only contain letters, digits, '.', '-', and '_'. and Username cannot be longer than 100 characters."
              icon={<IconAlertCircle></IconAlertCircle>}
            />
          </>
        </KanbanModal>
      </KanbanModal>

      <HeaderTitleComponent
        title='User Settings'
        rightSection={
          <GuardComponent requirePermissions={[AclPermission.addUser]} hiddenOnUnSatisfy>
            <KanbanButton
              onClick={() => {
                setNewUsers([]);
                openModalCreateUser();
              }}
              leftSection={<IconPlus />}>
              New User
            </KanbanButton>
          </GuardComponent>
        }
      />
      <KanbanTable {...tableViewListUserProps} />

      {false && (
        <KanbanSplitPagingContainer
          title='List User'
          listData={userResponses}
          pagination={{
            itemsPerPage: 10,
          }}
          renderItems={(listData) => {
            return (
              <ListUserWrapper>
                {listData.map((x, key) => {
                  return (
                    <UserItem
                      key={key}
                      onClick={() => {
                        openModal();
                      }}></UserItem>
                  );
                })}
              </ListUserWrapper>
            );
          }}></KanbanSplitPagingContainer>
      )}
    </div>
  );
};
export default UsersSettingsPage;
