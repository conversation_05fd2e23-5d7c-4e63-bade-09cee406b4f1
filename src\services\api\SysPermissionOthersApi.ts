import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import type { PaginationResponseModel } from '@models/EntityModelBase';
import type { PermissionOtherModel, RoleModel } from '@models/Role';

export type RoleResponse = RoleModel;
export type RolePagingResponse = PaginationResponseModel<RoleResponse>;

export class SysPermissionOthersApi extends BaseApi {
  static baseUrl = BaseUrl.sysPermissionOthers;

  static async getAllPermissionOtherByRoleId(roleId: number) {
    return BaseApi.getData<PermissionOtherModel[]>(`${this.baseUrl}?roleId=${roleId}`);
  }
}
