import { MantineColorsTuple, createTheme } from '@mantine/core';

export const ColorPrimary: MantineColorsTuple = [
  '#f0eefb',
  '#dbd9f1',
  '#b5afe6',
  '#8b82db',
  '#6a5dd1',
  '#5544cc',
  '#4a38cb',
  '#3c2cb3',
  '#3426a0',
  '#2b208d',
];
export const Default = createTheme({
  colors: {
    primary: ColorPrimary,
    secondary: ['#ffeaea', '#fdd4d4', '#f3a7a7', '#ec7777', '#e64f4e', '#e23634', '#e22827', '#c91a1a', '#b31316', '#9d0510'],
    error: ['#ffeaea', '#fdd4d4', '#f3a7a7', '#ec7777', '#e64f4e', '#e23634', '#e22827', '#c91a1a', '#b31316', '#9d0510'],
  },
  primaryColor: 'primary',
});
