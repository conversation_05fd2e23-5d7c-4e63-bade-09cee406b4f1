import { KanbanCheckbox, KanbanInput, KanbanNumberInput, KanbanSelect } from 'kanban-design-system';
import React, { forwardRef, useCallback, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { Box, Group } from '@mantine/core';
import DnDAttributesComponent, { CiTypeAttributeDto, DnDAttributesComponentMethods } from './DnDAttributesComponent';
import { CiIdentifierEntryAttributeDto, CiIdentifierEntryDto, RuleStatus } from '@models/CiIdentifierRule';
import { listStatus } from './CiIdentifierRuleDetailPage';
import { formatStandardName } from '@common/utils/StringUtils';

type CiIdentifierEntryPageProps = {
  entryInfo: CiIdentifierEntryDto;
  listAttributes: CiTypeAttributeDto[];
  isView: boolean;
};

export type CiIdentifierEntryPageMethods = {
  onSaveData: () => CiIdentifierEntryDto;
  validateData: (listEntry: CiIdentifierEntryDto[]) => boolean;
};

type IdentifierEntryError = {
  name?: string;
  priority?: string;
  selectedAttribute?: string;
};

const validateName = (dataEntry: CiIdentifierEntryDto, listEntry: CiIdentifierEntryDto[]): string | undefined => {
  const value = dataEntry.name;
  if (!value || value.trim().length === 0) {
    return 'Name cannot be empty';
  } else if (value.length > 255) {
    return 'Name must be less than 255 characters';
  }
  const index = listEntry.findIndex((x) => x.name === value.trim() && x.tempId !== dataEntry.tempId);
  if (index >= 0) {
    return 'Duplicate name found. Please enter a unique name.';
  }
};

const validatePriority = (dataEntry: CiIdentifierEntryDto, listEntry: CiIdentifierEntryDto[]): string | undefined => {
  const value = dataEntry.priority;
  if (value === null || value === undefined || value.toString().trim().length === 0) {
    return 'Priority cannot be empty';
  }
  const numValue = Number(value);
  if (isNaN(numValue) || numValue < 1 || numValue > 999) {
    return 'Invalid input: Priority value must be a number between 1 and 999';
  }
  const index = listEntry.findIndex((x) => x.priority === value && x.tempId !== dataEntry.tempId);
  if (index >= 0) {
    return 'Duplicate priority found. Please enter a unique value.';
  }
};

const validateAttributes = (attributes: CiTypeAttributeDto[]): string | undefined => {
  if (!attributes?.length) {
    return 'Selected attribute cannot be empty';
  }
  const hasInvalidAttribute = attributes.some((item) => item.deleted);
  if (hasInvalidAttribute) {
    return 'Selection is not allowed for attributes that are marked as deleted.';
  }
};

export const CiIdentifierEntryPage = forwardRef<CiIdentifierEntryPageMethods, CiIdentifierEntryPageProps>((props, ref) => {
  const { entryInfo, isView, listAttributes } = props;
  const [dataEntry, setDataEntry] = useState<CiIdentifierEntryDto>(entryInfo);
  const [errorMessage, setErrorMessage] = useState<IdentifierEntryError>();

  const dnDAttributesRef = useRef<DnDAttributesComponentMethods | null>(null);

  const selectedAttributes: CiTypeAttributeDto[] = useMemo(() => {
    const selectedCiTypeAttributeIds = (entryInfo?.attributes || []).map((x) => x.ciTypeAttributeId);

    return (listAttributes || []).filter((x) => selectedCiTypeAttributeIds.includes(x.attributeId));
  }, [entryInfo?.attributes, listAttributes]);

  const onSaveData = useCallback(() => {
    const selectedAttributesDnD: CiTypeAttributeDto[] = dnDAttributesRef.current?.getSelectedAttributes() || [];

    // convert data
    const attributes: CiIdentifierEntryAttributeDto[] = selectedAttributesDnD.map((x) => {
      return { id: 0, ciIdentifierEntryId: 0, ciTypeAttributeId: x.attributeId, name: x.nameAttribute };
    });

    return { ...dataEntry, name: formatStandardName(dataEntry.name), attributes: attributes };
  }, [dataEntry]);

  const validateData = useCallback(
    (listEntry: CiIdentifierEntryDto[]) => {
      const errorName = validateName(dataEntry, listEntry);
      const errorPriority = validatePriority(dataEntry, listEntry);

      const selectedAttributesDnD: CiTypeAttributeDto[] = dnDAttributesRef.current?.getSelectedAttributes() || [];
      const errorAttribute = validateAttributes(selectedAttributesDnD);
      setErrorMessage({ name: errorName, priority: errorPriority, selectedAttribute: errorAttribute });

      return !(errorName || errorPriority || errorAttribute);
    },
    [dataEntry],
  );

  useImperativeHandle<any, CiIdentifierEntryPageMethods>(
    ref,
    () => ({
      onSaveData: onSaveData,
      validateData: validateData,
    }),
    [onSaveData, validateData],
  );

  return (
    <>
      <Box>
        <KanbanInput
          label='Name'
          placeholder='Input entry name'
          value={dataEntry.name || ''}
          maxLength={255}
          withAsterisk
          error={errorMessage?.name}
          disabled={isView || dataEntry.defaultEntry}
          onChange={(event) => {
            const value = event.target.value ?? '';
            const result = { ...dataEntry, name: value };
            setDataEntry(result);
            setErrorMessage({ ...errorMessage, name: undefined });
          }}
        />
        <Group grow>
          <KanbanNumberInput
            label='Priority'
            placeholder='Input number value'
            value={dataEntry.priority}
            disabled={isView}
            allowNegative={false}
            allowDecimal={false}
            clampBehavior='strict'
            min={1}
            max={999}
            error={errorMessage?.priority}
            withAsterisk
            onChange={(x) => {
              const result = { ...dataEntry, priority: Number(x) };
              setDataEntry(result);
              setErrorMessage({ ...errorMessage, priority: undefined });
            }}
          />
          <KanbanSelect
            label='Active'
            placeholder='Select value'
            withAsterisk
            data={listStatus}
            defaultValue={RuleStatus.ENABLE}
            value={dataEntry.active}
            allowDeselect={false}
            disabled={isView}
            onChange={(val) => {
              const ruleStatus = val && val in RuleStatus ? (val as RuleStatus) : RuleStatus.ENABLE;
              const result = { ...dataEntry, active: ruleStatus };
              setDataEntry(result);
            }}
          />
        </Group>

        <DnDAttributesComponent
          ref={dnDAttributesRef}
          attributeOfCis={listAttributes}
          selectedAttributes={selectedAttributes}
          isView={isView || dataEntry.defaultEntry}
          errorMessage={errorMessage?.selectedAttribute}
        />
      </Box>
      {/* TODO */}
      <Box mt={20} display={'none'}>
        <KanbanCheckbox label='Filter' disabled />
        <Group grow>
          <KanbanSelect
            label='Attribute'
            placeholder='Select attribute'
            withAsterisk
            data={listStatus}
            defaultValue={'1'}
            onChange={() => {}}
            disabled
          />
          <KanbanSelect
            label='Operator'
            placeholder='Select operator'
            withAsterisk
            data={listStatus}
            defaultValue={'1'}
            onChange={() => {}}
            disabled
          />
          <KanbanSelect label='Value' placeholder='Select value' withAsterisk data={listStatus} defaultValue={'1'} onChange={() => {}} disabled />
        </Group>
      </Box>
    </>
  );
});
CiIdentifierEntryPage.whyDidYouRender = true;
CiIdentifierEntryPage.displayName = 'CiIdentifierEntryPage';
export default CiIdentifierEntryPage;
