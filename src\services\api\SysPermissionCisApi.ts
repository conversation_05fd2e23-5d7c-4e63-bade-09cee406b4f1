import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
import type { PermissionCiOrCiTypeRoleModel } from '@models/Role';
import type { ApiResponseDataBase } from '@core/api/ApiResponse';
import { BaseApi } from 'core/api/BaseApi';
import type { SysPermissionCiModel } from './systems/GroupsApi';
import { BaseUrl } from '@core/api/BaseUrl';

export type SysPermissionCiResponse = ApiResponseDataBase & SysPermissionCiModel;

export class SysPermissionCiApi extends BaseApi {
  static baseUrl = BaseUrl.sysPermissionCis;

  static findAllByRoleId(roleId: number) {
    return BaseApi.getData<SysPermissionCiResponse[]>(`${this.baseUrl}?roleId=${roleId}`);
  }
}

export type PermissionCiOrCiTypeRoleResponse = PermissionCiOrCiTypeRoleModel;
export type PermissonCiOrCiTypeRolePagingResponse = PaginationResponseModel<PermissionCiOrCiTypeRoleResponse>;

export class SysPermissionCisApi extends BaseApi {
  static baseUrl = BaseUrl.sysPermissionCis;

  static async getAllPermissionCisByRoleId(roleId: number, pagination: PaginationRequestModel<PermissonCiOrCiTypeRolePagingResponse>) {
    return BaseApi.getData<PermissonCiOrCiTypeRolePagingResponse>(`${this.baseUrl}/roles/${roleId}/cis`, pagination);
  }
  static async getAllPermissionCiTypesByRoleId(roleId: number, pagination: PaginationRequestModel<PermissonCiOrCiTypeRolePagingResponse>) {
    return BaseApi.getData<PermissonCiOrCiTypeRolePagingResponse>(`${this.baseUrl}/roles/${roleId}/ci-types`, pagination);
  }
}
