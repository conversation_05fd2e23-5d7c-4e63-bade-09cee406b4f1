import type { PermissionAction } from '@common/constants/PermissionAction';
import { PermissionActionType } from '@common/constants/PermissionActionType';
import { isPermissionEqual } from '@common/utils/AclPermissionUtils';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import type { AclPermission } from 'models/AclPermission';
import { useSelector } from 'react-redux';

/**
 * check permission CI
 * @param ciTypeId
 * @param ciId
 * @param action
 */
export const useIsPermission = (ciTypeId: number, ciId: number, action: PermissionAction) => {
  const currentUser = useSelector(getCurrentUser);
  if (!currentUser) {
    return false;
  }
  const aclPermissions = currentUser.data?.aclPermissions || [];
  const isExitsCIPermision = aclPermissions.find((permission) => permission.typeId === ciId && permission.type === PermissionActionType.CI);
  if (isExitsCIPermision) {
    const existPerrmisson = aclPermissions.find(
      (permission) => permission.action === action && permission.typeId === ciId && permission.type === PermissionActionType.CI,
    );
    return !!existPerrmisson;
  }

  const isExitsCITypePermision = aclPermissions.find(
    (permission) => permission.typeId === ciTypeId && permission.type === PermissionActionType.CI_TYPE,
  );
  if (isExitsCITypePermision) {
    const existPerrmisson = aclPermissions.find(
      (permission) => permission.action === action && permission.typeId === ciTypeId && permission.type === PermissionActionType.CI_TYPE,
    );
    return !!existPerrmisson;
  }
  return false;
};

/**
 * check permission with AclPermission
 * @param permissions
 */
export const useIsPermissions = (permissions: AclPermission[]) => {
  const currentUser = useSelector(getCurrentUser);
  if (!currentUser) {
    return false;
  }
  const userAclPermissions = currentUser.data?.aclPermissions || [];

  const existsInPermissionAll = permissions.some((p) => userAclPermissions.some((pa) => isPermissionEqual(p, pa)));
  return !!existsInPermissionAll;
};

export const useIsCiTypePermission = (ciTypeId: number, action: PermissionAction) => {
  const currentUser = useSelector(getCurrentUser);
  if (!currentUser) {
    return false;
  }
  const aclPermissions = currentUser.data?.aclPermissions || [];
  const isExitsCITypePermision = aclPermissions.find(
    (permission) => permission.typeId === ciTypeId && permission.type === PermissionActionType.CI_TYPE,
  );
  if (isExitsCITypePermision) {
    const existPerrmisson = aclPermissions.find(
      (permission) => permission.action === action && permission.typeId === ciTypeId && permission.type === PermissionActionType.CI_TYPE,
    );
    return !!existPerrmisson;
  }
  return false;
};
