import type { ConfigItemTypePermissionModel } from '@models/ConfigItemTypePermission';
import { createSlice } from '@reduxjs/toolkit';
import type { RootStoreType } from '@store';

export interface CiTreePermissionsState {
  ciTreePermissions: ConfigItemTypePermissionModel[];
}

const initialState: CiTreePermissionsState = {
  ciTreePermissions: [],
};

export const ciTreePermissionsSlice = createSlice({
  name: 'ciTreePermissions',
  initialState,
  reducers: {
    updateCiTreePermissions(state, action) {
      state.ciTreePermissions = action.payload;
    },
  },
});

export const getCiTreePermissions = (store: RootStoreType) => store.ciTreePermissions;
