import { IconFileText, Icon<PERSON>heck, IconList, IconFolder, IconAlertCircle } from '@tabler/icons-react';
import React from 'react';

import { CustomDataNode, isMatch, JSONNode, JsonToDataNodeResult, TextHighlighterProps } from './JsonTransformHelper';
import { KanbanText } from 'kanban-design-system';
import styles from './RcTree.module.scss';
import './RcTree.scss';
import { Flex } from '@mantine/core';

export const convertJsonToDataNodeWithKeys = (json: JSONNode, parentKey: string = ''): JsonToDataNodeResult => {
  const keys: string[] = [];

  const traverse = (json: JSONNode, parentKey: string = ''): CustomDataNode[] => {
    if (Array.isArray(json)) {
      return json.map((value, index) => {
        const currentKey = parentKey ? `${parentKey}[${index}]` : `[${index}]`;
        keys.push(currentKey);

        const isObjectValue = typeof value === 'object' && value !== null;
        const jsonValue = isObjectValue ? (Object.keys(value).length === 0 ? '{}' : '') : value;
        const displayKey = currentKey.split('.').pop() ?? currentKey;

        return {
          id: currentKey,
          jsonPath: currentKey,
          originalJsonPath: currentKey,
          key: currentKey,
          value: jsonValue,
          icon: getIconByType(value),
          title: (
            <Flex gap='lg' justify='flex-start' align='center'>
              <KanbanText className={styles['node-key']}>{displayKey}</KanbanText>

              {jsonValue !== '' && <KanbanText className={styles['node-value']} size='sm' lineClamp={4}>{` ${jsonValue}`}</KanbanText>}
            </Flex>
          ),
          style: { padding: '5px', marginBottom: '3px' },
          children: isObjectValue ? traverse(value, currentKey) : undefined,
        };
      });
    } else if (typeof json === 'object' && json !== null) {
      return Object.entries(json).map(([key, value]) => {
        const currentKey = parentKey ? `${parentKey}.${key}` : key;
        keys.push(currentKey);

        const isObjectValue = typeof value === 'object' && value !== null;
        const jsonValue = isObjectValue ? (Object.keys(value).length === 0 ? '{}' : '') : value;

        return {
          id: currentKey,
          jsonPath: currentKey,
          originalJsonPath: currentKey,
          key: currentKey,
          value: jsonValue,
          icon: getIconByType(value),
          title: (
            <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
              <KanbanText className={styles['node-key']}>{key}</KanbanText>
              {jsonValue !== '' && <KanbanText className={styles['node-value']} size='sm' lineClamp={4}>{` ${jsonValue}`}</KanbanText>}
            </div>
          ),
          style: { padding: '5px', marginBottom: '3px' },
          children: isObjectValue ? traverse(value, currentKey) : undefined,
        };
      });
    }
    return [];
  };

  const dataNodes = traverse(json, parentKey);
  return { dataNodes, keys };
};

const escapeRegExp = (value: string) => {
  return value.replace(/[^\w\s]/g, '\\$&');
};

const TextHighlighter: React.FC<TextHighlighterProps> = ({ searchValue, text }) => {
  if (!searchValue) {
    return <span>{text}</span>;
  }
  const safeSearchValue = escapeRegExp(searchValue);
  const parts = text.split(new RegExp(`(${safeSearchValue})`, 'gi'));

  return (
    <>
      {parts.map((part, index) =>
        part.toLowerCase() === searchValue.toLowerCase() ? (
          <span key={index} style={{ backgroundColor: 'yellow', fontWeight: 'bold' }}>
            {part}
          </span>
        ) : (
          <span key={index}>{part}</span>
        ),
      )}
    </>
  );
};

export const convertJsonToDataNodeWithSearch = (json: JSONNode, parentKey: string = '', searchValue: string = ''): JsonToDataNodeResult => {
  const keys: string[] = [];

  const traverse = (json: JSONNode, parentKey: string = ''): CustomDataNode[] => {
    if (Array.isArray(json)) {
      return json
        .map((value, index) => {
          const currentKey = parentKey ? `${parentKey}[${index}]` : `[${index}]`;
          keys.push(currentKey);

          const isObjectValue = typeof value === 'object' && value !== null;
          const jsonValue = isObjectValue ? (Object.keys(value).length === 0 ? '{}' : '') : value;
          const displayKey = currentKey.split('.').pop() ?? currentKey;
          const isMatchValue = isMatch(displayKey, jsonValue, searchValue);

          const children = isObjectValue ? traverse(value, currentKey) : undefined;

          if (isMatchValue || (children && children.length > 0)) {
            return {
              id: currentKey,
              jsonPath: currentKey,
              originalJsonPath: currentKey,
              key: currentKey,
              value: jsonValue,
              icon: getIconByType(value),
              title: (
                <Flex gap='lg' justify='flex-start' align='center'>
                  <div className={styles['node-key']}>
                    <TextHighlighter text={displayKey} searchValue={searchValue} />
                  </div>
                  {jsonValue !== '' && (
                    <span className={styles['node-value']}>
                      <TextHighlighter text={String(jsonValue)} searchValue={searchValue} />
                    </span>
                  )}
                </Flex>
              ),
              style: { padding: '5px', marginBottom: '3px' },
              children,
            };
          }

          return null;
        })
        .filter((node) => node !== null);
    } else if (typeof json === 'object' && json !== null) {
      return Object.entries(json)
        .map(([key, value]) => {
          const currentKey = parentKey ? `${parentKey}.${key}` : key;
          keys.push(currentKey);

          const isObjectValue = typeof value === 'object' && value !== null;
          const jsonValue = isObjectValue ? (Object.keys(value).length === 0 ? '{}' : '') : value;
          const isMatchValue = isMatch(key, jsonValue, searchValue);
          const children = isObjectValue ? traverse(value, currentKey) : undefined;

          if (isMatchValue || (children && children.length > 0)) {
            return {
              id: currentKey,
              jsonPath: currentKey,
              originalJsonPath: currentKey,
              key: currentKey,
              value: jsonValue,
              icon: getIconByType(value),
              title: (
                <Flex gap='lg' justify='flex-start' align='center'>
                  <div className={styles['node-key']}>
                    <TextHighlighter text={key} searchValue={searchValue} />
                  </div>
                  {jsonValue !== '' && (
                    <span className={styles['node-value']}>
                      <TextHighlighter text={String(jsonValue)} searchValue={searchValue} />
                    </span>
                  )}
                </Flex>
              ),
              style: { padding: '5px', marginBottom: '3px' },
              children,
            };
          }

          return null;
        })
        .filter((node) => node !== null);
    }
    return [];
  };

  const dataNodes = traverse(json, parentKey);
  return { dataNodes, keys };
};

export const getIconByType = (value: unknown) => {
  if (Array.isArray(value)) {
    return <IconList size={16} color='blue' />;
  }

  switch (typeof value) {
    case 'string':
      return <IconFileText size={16} color='green' />;
    case 'boolean':
      return <IconCheck size={16} color='orange' />;
    case 'object':
      return value ? <IconFolder size={16} color='purple' /> : <IconAlertCircle size={16} color='red' />;
    default:
      return <IconAlertCircle size={16} color='gray' />;
  }
};
