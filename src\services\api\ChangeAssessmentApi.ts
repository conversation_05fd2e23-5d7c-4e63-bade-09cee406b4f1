import { BaseApi } from '@core/api/BaseApi';
import type {
  ChangeAssessmentDTO,
  ChangeAssessmentRequestDto,
  CiImpactedByRelationshipInformationResponseDto,
  CiImpactedByRelationshipRequestDto,
  ImpactedCiParameters,
} from '@models/ChangeAssessment';
import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
import type { ConfigItemResponse } from './ConfigItemApi';
import type { ChangeAssessmentDraftStatus } from '@models/ChangeAssessment';
import type { EntityUserInfoResponse, EntityUserInfoResponsePagingResponse } from './systems/UsersApi';
import type { ChangeResponseDto } from '@models/ChangeSdp';
import { BaseUrl } from '@core/api/BaseUrl';

export type ChangeAssessmentDTOResponse = PaginationResponseModel<ChangeAssessmentDTO>;
export type CiImpactedByRelationshipInformationResponsePagination = PaginationResponseModel<CiImpactedByRelationshipInformationResponseDto>;

export type MapAssessmentWithChangeResponse = {
  [key: string]: ChangeResponseDto;
};
export class ChangeAssessmentApi extends BaseApi {
  static baseUrl = BaseUrl.changeAssessments;

  static getAll(pagination: PaginationRequestModel<ChangeAssessmentDTO>) {
    return BaseApi.postData<ChangeAssessmentDTOResponse>(
      `${this.baseUrl}/filter`,
      pagination,
      {},
      {},
      { useLoading: false, useErrorNotification: true },
    );
  }

  static getById(id: number) {
    return BaseApi.getData<ChangeAssessmentDTO>(`${this.baseUrl}/${id}`);
  }

  static createNewChange(data: ChangeAssessmentRequestDto) {
    return BaseApi.postData<boolean>(`${this.baseUrl}`, data);
  }

  static calculateImpactedServices(impactedCiParameters: ImpactedCiParameters) {
    return BaseApi.getData<ConfigItemResponse[]>(`${this.baseUrl}/impacted-services/calculation`, impactedCiParameters);
  }

  static calculateImpactedServicesByRelationship(impactedCiParameters: CiImpactedByRelationshipRequestDto) {
    return BaseApi.postData<CiImpactedByRelationshipInformationResponsePagination>(
      `${this.baseUrl}/impacted-services/relationship/calculation`,
      impactedCiParameters,
    );
  }

  static deleteByIds(ids: number[]) {
    return BaseApi.deleteData<MapAssessmentWithChangeResponse>(`${this.baseUrl}/batch`, {
      ids,
    });
  }

  static updateChange(data: ChangeAssessmentRequestDto, id: number) {
    return BaseApi.putData<boolean>(`${this.baseUrl}/${id}`, data);
  }

  static updateDraftStatus(data: ChangeAssessmentDraftStatus, id: number) {
    return BaseApi.putData<boolean>(`${this.baseUrl}/${id}?status=${data}`);
  }

  static getAllOwnerUsers(pagination: PaginationRequestModel<EntityUserInfoResponse>) {
    return BaseApi.getData<EntityUserInfoResponsePagingResponse>(
      `${this.baseUrl}/owner-users`,
      pagination,
      {},
      { useLoading: false, useErrorNotification: true },
    );
  }

  static assignOwnerById(assignUsername: string, id: number) {
    return BaseApi.putData<boolean>(`${this.baseUrl}/${id}/users/${assignUsername}`);
  }
}
