{
    "env": {
        "browser": true,
        "es2021": true
    },
    "extends": [
        "eslint:recommended",
        "plugin:@typescript-eslint/recommended",
        "plugin:react/recommended",
        "prettier"
    ],
    "parser": "@typescript-eslint/parser",
    "parserOptions": {
        "ecmaVersion": "latest",
        "sourceType": "module"
    },
    "plugins": [
        "@typescript-eslint",
        "react",
        "react-hooks",
        "sort-destructure-keys",
        "unused-imports",
        "filenames-simple",
        "check-file",
        "prettier"
    ],
    "ignorePatterns": [
        "**/*.js",
        "*.js",
        "",
        "src/common/libs/**"
    ],
    "rules": {
        "camelcase": [
            "error"
        ],
        "constructor-super": [
            "error"
        ],
        "no-debugger": [
            "error"
        ],
        "no-dupe-else-if": [
            "error"
        ],
        "no-duplicate-case": [
            "error"
        ],
        "no-fallthrough": [
            "error"
        ],
        "no-prototype-builtins": [
            "error"
        ],
        "no-this-before-super": [
            "error"
        ],
        "no-unexpected-multiline": [
            "error"
        ],
        "react/prop-types": "off",
        "no-unneeded-ternary": [
            "error"
        ], //Hmm, should we?
        "prettier/prettier": [
            "error",
            {
                "semi": true,
                "tabWidth": 2,
                "printWidth": 150,
                "singleQuote": true,
                "trailingComma": "all",
                "jsxBracketSameLine": true,
                "endOfLine": "auto",
                "bracketSpacing": true,
                "bracketSameLine": true,
                "arrowParens": "always",
                "jsxSingleQuote": true,
                "singleAttributePerLine": false
            }
        ],
        "curly": "error",
        "no-alert": "error",
        "no-empty": "error",
        "no-var": "error",
        "prefer-template": "error",
        "no-console": [
            "error",
            {
                "allow": [
                    "warn",
                    "error",
                    "debug",
                    "trace",
                    "info"
                ]
            }
        ],
        "check-file/filename-naming-convention": [
            "error",
            {
                "**/!(index|store|use).*": "PASCAL_CASE"
            },
            {
                "ignoreMiddleExtensions": true
            }
        ],
        "check-file/folder-naming-convention": [
            "error",
            {
                "src/**/": "CAMEL_CASE"
            }
        ],
        "filenames-simple/extension": "error",
        // "filenames-simple/naming-convention": [
        //     "error",
        //     {
        //         "rule": "PascalCase",
        //         "excepts": [
        //             "index",
        //             "store",
        //             "^use(.*)$"
        //         ]
        //     }
        // ],
        "@typescript-eslint/no-explicit-any": 0,
        "react-hooks/rules-of-hooks": "error",
        "sort-destructure-keys/sort-destructure-keys": [
            "warn",
            {
                "caseSensitive": false
            }
        ],
        "@typescript-eslint/no-unused-vars": [
            "warn",
            {
                "argsIgnorePattern": "^_",
                "varsIgnorePattern": "^_"
            }
        ],
        "no-unused-vars": "off",
        "unused-imports/no-unused-imports": "error",
        "@typescript-eslint/ban-types": "warn",
        "unused-imports/no-unused-vars": [
            "warn",
            {
                "vars": "all",
                "varsIgnorePattern": "^_",
                "args": "after-used",
                "argsIgnorePattern": "^_"
            }
        ],
        // "indent": [
        //     "error",
        //     4
        // ],
        "linebreak-style": [
            "error",
            "windows"
        ],
        // "quotes": [
        //     "error",
        //     "single"
        // ],
        // "semi": [
        //     "error",
        //     "always"
        // ],
        "react-hooks/exhaustive-deps": "error",
        "eqeqeq": [
            "error",
            "always"
        ],
        "@typescript-eslint/no-non-null-assertion": "error"
    }
}