import { KanbanTabs } from 'kanban-design-system';
import styled from '@emotion/styled';
import React, { useEffect, useState } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import ChangeRequestTable from './subtabRequest/ChangeRequestTableTab';
import IncidentTableTab from './subtabRequest/IncidentTableTab';
import ProblemRequestTableTab from './subtabRequest/ProblemRequestTableTab';
import ServiceRequestTableTab from './subtabRequest/ServiceRequestTableTab';
import { CiDetailScreenType, CiDetailSubTabType } from '@common/constants/CiDetail';

export type CiRequestProps = {
  ciId: number;
  ciTypeId: number;
  isViewPage: boolean;
};
export const CustomLink = styled(Link)`
  text-decoration: none;
  color: blue;

  &:hover {
    text-decoration: underline;
  }
`;
export const CiRequest = ({ ciId, isViewPage }: CiRequestProps) => {
  const [currentTab, setCurrentTab] = useState<string>(CiDetailSubTabType.CHANGEREQUEST);
  const [searchParams, setSearchParams] = useSearchParams();
  useEffect(() => {
    if (isViewPage) {
      const subTab = searchParams.get('subTab');
      if (subTab) {
        setCurrentTab(subTab);
      }
    }
  }, [isViewPage, searchParams]);
  return (
    <>
      <KanbanTabs
        configs={{
          defaultValue: CiDetailSubTabType.CHANGEREQUEST,
          value: currentTab,
          onChange: (value) => {
            if (isViewPage) {
              // preview-discovery :make ci detail screen -> pop up: when change tab =>  change param instead of redirect url
              // navigate(buildCiUrl(ciTypeId, ciId, CiDetailScreenType.REQUEST, value || CiDetailSubTabType.CHANGEREQUEST));
              const params = new URLSearchParams();
              params.set('tab', CiDetailScreenType.REQUEST);
              params.set('subtab', value || CiDetailSubTabType.CHANGEREQUEST);
              setSearchParams(params);
            }
            setCurrentTab(value as CiDetailScreenType);
          },
        }}
        tabs={{
          SERVICEREQUEST: {
            title: 'Service request',
            content: (
              <>
                <ServiceRequestTableTab />
              </>
            ),
          },
          INCIDENT: {
            title: 'Incident',
            content: (
              <>
                <IncidentTableTab ciId={ciId} />
              </>
            ),
          },
          CHANGEREQUEST: {
            title: 'Change request',
            content: (
              <>
                <ChangeRequestTable ciId={ciId} />
              </>
            ),
          },
          PROBLEM: {
            title: 'Problem',
            content: (
              <>
                <ProblemRequestTableTab />
              </>
            ),
          },
        }}
      />
    </>
  );
};
CiRequest.whyDidYouRender = true;

export default CiRequest;
