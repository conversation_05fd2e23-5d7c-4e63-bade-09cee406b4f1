import { Divider } from '@mantine/core';
import { KanbanText } from 'kanban-design-system';
import React from 'react';
function BaseErrorMessage(message?: string, clientMessageId?: string) {
  return (
    <>
      <KanbanText>ClientMessageId: {clientMessageId ?? ''}</KanbanText>
      <Divider />
      <KanbanText>{message && message.trim().length > 0 ? message : 'A system error has occurred. Please contact the admin.'}</KanbanText>
    </>
  );
}
export default BaseErrorMessage;
