import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { Stack } from '@mantine/core';
import { AuthenticationType, IntegrateMethodEnum as Method } from '@common/constants/DiscoverySourceConfigEnum';
import {
  defaultDiscoverySourceConfigDetail,
  DiscoverySourceConfigDetail,
  SourceConfigSnmpInfo,
  defaultSnmpInfo,
} from '@models/discovery/DiscoverySourceConfig';
import BasicInfo, { BasicInfoRef } from './components/BasicInfo';
import ApiDetails, { ApiDetailsRef } from './components/ApiDetails';
import DbDetails, { DbDetailsRef } from './components/DbDetails';
import SnmpDetails, { SnmpDetailsRef } from './components/SnmpDetails';
import { KanbanButton } from 'kanban-design-system';
import { BreadcrumbComponent } from '@pages/admins/breadcrumb/BreadcrumbComponent';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import GuardComponent from '@components/GuardComponent';
import { IconDeviceFloppy, IconEdit, IconPlus } from '@tabler/icons-react';
import { IconX } from '@tabler/icons-react';
import styles from './SourceConfig.module.scss';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { DiscoverySourceApi } from '@api/discovery/DiscoverySourceApi';
import { PASSWORD_PLACEHOLDER } from '@common/constants/OutgoingMailConstants';
import { buildSourceDataConfigUrl, dataSourceConfigPath } from '@common/utils/RouterUtils';
import { SourceDataAction } from '@common/constants/SourceDataActionEnum';
import { NotificationSuccess } from '@common/utils/NotificationUtils';

export const getEnumKeyByValue = <T extends Record<string, string>>(enumObj: T, value: string): keyof T | undefined => {
  return Object.keys(enumObj).find((key) => enumObj[key] === value);
};

const maskSensitiveFields = (data: DiscoverySourceConfigDetail): DiscoverySourceConfigDetail => {
  return {
    ...data,
    apiInfo: data.apiInfo
      ? {
          ...data.apiInfo,
          password: AuthenticationType.BASIC === data.apiInfo.authenticationType ? PASSWORD_PLACEHOLDER : '',
          tokenKey: AuthenticationType.API_TOKEN === data.apiInfo.authenticationType ? PASSWORD_PLACEHOLDER : '',
        }
      : undefined,
    databaseInfo: data.databaseInfo
      ? {
          ...data.databaseInfo,
          password: PASSWORD_PLACEHOLDER,
        }
      : undefined,
    snmpInfos: data.snmpInfos?.map((item) => ({
      ...item,
      password: PASSWORD_PLACEHOLDER,
      encryptionKey: PASSWORD_PLACEHOLDER,
      community: PASSWORD_PLACEHOLDER,
    })),
  };
};
const getTitlePage = (sourceDataId: number, action: SourceDataAction): string => {
  if (sourceDataId > 0) {
    return action === SourceDataAction.VIEW ? 'View setting config source' : 'Update setting config source';
  }
  return 'Setting config source';
};
const DiscoverySourceConfigDetailPage: React.FC = () => {
  const { id } = useParams();
  const sourceDataId = Number(id);
  const navigate = useNavigate();
  const [sourceDetail, setSourceDetail] = useState<DiscoverySourceConfigDetail>(defaultDiscoverySourceConfigDetail);
  const basicInfoRef = useRef<BasicInfoRef>(null);
  const apiDetailsRef = useRef<ApiDetailsRef>(null);
  const dbDetailsRef = useRef<DbDetailsRef>(null);
  const [selectedMethod, setSelectedMethod] = useState<Method | undefined>(undefined);
  const snmpDetailsRefs = useRef<{ [id: string]: SnmpDetailsRef | null }>({});
  const [selectedVersion, setSelectedVersion] = useState<string | undefined>(undefined);
  const [searchParams] = useSearchParams();
  const actionParam = searchParams.get('action');
  const action = Object.values(SourceDataAction).includes(actionParam as SourceDataAction)
    ? (actionParam as SourceDataAction)
    : SourceDataAction.VIEW;
  const isViewAction = SourceDataAction.VIEW === action;
  const { control, reset } = useForm<DiscoverySourceConfigDetail>({
    defaultValues: defaultDiscoverySourceConfigDetail,
  });
  const { append, fields, remove } = useFieldArray({
    control,
    name: 'snmpInfos',
  });

  const handleMethodChange = (method: Method | undefined) => {
    setSelectedMethod(method);

    // Nếu chọn SNMP và chưa có snmp info nào, thêm một item mặc định
    if (Method.SNMP === method && fields.length === 0) {
      handleAddSnmp();
    }
  };
  const handleSubmit = async () => {
    const [basicInfo, apiInfo, dbInfo] = await Promise.all([
      basicInfoRef.current?.getData(),
      apiDetailsRef.current?.getData(),
      dbDetailsRef.current?.getData(),
    ]);
    if (!basicInfo) {
      return;
    }

    const finalData: DiscoverySourceConfigDetail = {
      basicInfo,
    };

    if (Method.API === basicInfo.method) {
      if (!apiInfo || Object.keys(apiInfo).length === 0) {
        return;
      }
      finalData.apiInfo = apiInfo;
    }

    if (Method.DB_CONNECT === basicInfo.method) {
      if (!dbInfo || Object.keys(dbInfo).length === 0) {
        return;
      }
      finalData.databaseInfo = dbInfo;
    }

    if (Method.SNMP === basicInfo.method) {
      const snmpInfo = await Promise.all(
        fields.map((field) => {
          const ref = snmpDetailsRefs.current[field.id];
          return ref ? ref.getData() : Promise.resolve(null);
        }),
      );
      const validSnmpInfo = snmpInfo.filter((item): item is SourceConfigSnmpInfo => !!item && Object.keys(item).length > 0);
      if (validSnmpInfo.length === 0) {
        return;
      }
      finalData.snmpInfos = validSnmpInfo;
    }

    DiscoverySourceApi.saveOrUpdate(finalData)
      .then((response) => {
        if (response.status === 200) {
          NotificationSuccess({
            message: sourceDataId > 0 ? 'Update source config success.' : 'Create source config success.',
          });
          setTimeout(() => {
            navigate(dataSourceConfigPath);
          }, 500);
        }
      })
      .catch(() => {
        // NotificationError
      });
  };
  // Hàm thêm card SNMP
  const handleAddSnmp = () => {
    append({ ...defaultSnmpInfo, id: String(crypto.randomUUID()) });
  };
  // Hàm xóa card SNMP
  const handleRemoveSnmp = (index: number) => {
    const fieldToRemove = fields[index];
    if (fieldToRemove) {
      // Xóa ref tương ứng
      delete snmpDetailsRefs.current[fieldToRemove.id];
    }
    remove(index);
  };
  const getSourceDataById = useCallback(() => {
    DiscoverySourceApi.getById(sourceDataId)
      .then((response) => {
        if (response.status === 200) {
          const maskedData = maskSensitiveFields(response.data);
          setSourceDetail(maskedData);
          // Reset form với dữ liệu mới
          reset(maskedData);
        }
      })
      .catch(() => {});
  }, [sourceDataId, reset]);

  useEffect(() => {
    if (sourceDataId > 0) {
      getSourceDataById();
    }
  }, [getSourceDataById, sourceDataId]);

  const titlePage = getTitlePage(sourceDataId, action);

  return (
    <>
      <BreadcrumbComponent />

      <div className={styles.stickyHeader}>
        <HeaderTitleComponent
          title={titlePage}
          rightSection={
            <GuardComponent requirePermissions={[]} hiddenOnUnSatisfy>
              {/* <KanbanButton leftSection={<IconPlugConnected size={16} />} color='blue' mr={10} onClick={handleSubmit}>
                Test connection
              </KanbanButton> */}
              {isViewAction && (
                <KanbanButton
                  leftSection={<IconEdit size={16} />}
                  mr={10}
                  onClick={() => navigate(buildSourceDataConfigUrl(Number(id), SourceDataAction.UPDATE))}>
                  Edit
                </KanbanButton>
              )}
              {!isViewAction && (
                <KanbanButton leftSection={<IconDeviceFloppy size={16} />} color='green' mr={10} onClick={handleSubmit}>
                  Save
                </KanbanButton>
              )}
              <KanbanButton
                leftSection={<IconX size={16} />}
                color='gray'
                onClick={() => {
                  navigate(dataSourceConfigPath);
                }}>
                Close
              </KanbanButton>
            </GuardComponent>
          }
        />
      </div>

      <div className={styles.scrollableContent}>
        <Stack gap='lg' className={styles.pageContainer}>
          <BasicInfo
            ref={basicInfoRef}
            onMethodChange={handleMethodChange}
            onVersionChange={setSelectedVersion}
            defaultData={sourceDetail.basicInfo}
            action={action}
          />
          {Method.API === selectedMethod && <ApiDetails ref={apiDetailsRef} defaultData={sourceDetail.apiInfo} action={action} />}
          {Method.DB_CONNECT === selectedMethod && <DbDetails ref={dbDetailsRef} defaultData={sourceDetail.databaseInfo} action={action} />}
          {Method.SNMP === selectedMethod && (
            <>
              {fields.map((field, index) => (
                <div key={field.id} style={{ position: 'relative' }}>
                  <SnmpDetails
                    ref={(el) => (snmpDetailsRefs.current[field.id] = el)}
                    defaultData={field}
                    action={action}
                    snmpVersion={selectedVersion}
                    index={index}
                    onRemove={handleRemoveSnmp}
                  />
                </div>
              ))}
            </>
          )}
          {Method.SNMP === selectedMethod && action !== SourceDataAction.VIEW && (
            <div>
              <KanbanButton leftSection={<IconPlus size={16} />} mr={10} onClick={handleAddSnmp}>
                Add
              </KanbanButton>
            </div>
          )}
        </Stack>
      </div>
    </>
  );
};

export default DiscoverySourceConfigDetailPage;
