import React from 'react';

type ProtectedRouteProps = {
  children: React.ReactNode;
  //One of those role
  requirePermissions: string[];
  userPermissions: string[];
  errorElement: React.ReactNode;
  isSuperAdmin: boolean;
};

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, errorElement, isSuperAdmin, requirePermissions, userPermissions }) => {
  const isAuthorized = () => requirePermissions.some((permissions) => userPermissions.includes(permissions));

  return <>{isSuperAdmin || isAuthorized() ? children : errorElement}</>;
};
