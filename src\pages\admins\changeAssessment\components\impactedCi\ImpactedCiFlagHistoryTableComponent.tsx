import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { KanbanIconButton, KanbanTable, renderDateTime, type ColumnType, type KanbanTableProps } from 'kanban-design-system';
import { CiImpactedHistoryDto, ImpactedCiHistoryApi } from '@api/ImpactedCiHistoryApi';
import type { ImpactedCiHistoryProps } from '@models/ChangeAssessment';
import { IconFlag } from '@tabler/icons-react';

export const ImpactedCiFlagHistoryTableComponent: React.FC<ImpactedCiHistoryProps> = ({ ciImpactedHistoryId }) => {
  const [listHistory, setListHistory] = useState<CiImpactedHistoryDto[]>([]);
  const columns: ColumnType<CiImpactedHistoryDto>[] = useMemo(() => {
    return [
      {
        title: 'Impacted CI name',
        sortable: false,
        name: 'ciImpactedName',
        width: '30%',
      },
      {
        title: 'CI Names in change plan',
        sortable: false,
        name: 'ciChangeName',
        width: '30%',
      },
      {
        title: 'Level',
        sortable: false,
        name: 'relationshipLevel',
        width: '5%',
      },
      {
        title: 'Relationship',
        sortable: false,
        name: 'relationshipName',
        width: '15%',
        //not take relationshipId as input, stricly get from SQL cause function filter all cannot filter by relationShipId
      },
      {
        title: 'Modified date',
        sortable: false,
        name: 'modifiedDate',
        width: '30%',
        customRender: renderDateTime,
      },
      {
        title: 'Modified by',
        sortable: false,
        name: 'modifiedBy',
        width: '30%',
      },
      {
        title: 'Status',
        sortable: false,
        name: 'deleted',
        width: '30%',
        customRender: (data: boolean) => {
          return (
            <KanbanIconButton variant={data ? 'outline' : 'filled'} size='sm'>
              <IconFlag />
            </KanbanIconButton>
          );
        },
      },
    ];
  }, []);

  const tableProps: KanbanTableProps<CiImpactedHistoryDto> = useMemo(() => {
    return {
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 300,
      },
      sortable: {
        enable: true,
      },
      columns: columns,
      data: listHistory,
      key: 1,
    };
  }, [columns, listHistory]);

  const fetchHistory = useCallback(() => {
    if (ciImpactedHistoryId && ciImpactedHistoryId !== 0) {
      ImpactedCiHistoryApi.findCiImpactedHistoryWhenFlag(ciImpactedHistoryId)
        .then((res) => {
          if (res.data && res.data.length > 0) {
            setListHistory(res.data);
            return;
          }
        })
        .catch(() => {});
    }
  }, [ciImpactedHistoryId]);
  useEffect(() => {
    fetchHistory();
  }, [fetchHistory]);
  return <KanbanTable {...tableProps} />;
};
