import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import {
  KanbanAccordion,
  KanbanAccordionData,
  KanbanButton,
  KanbanCheckbox,
  KanbanConfirmModal,
  KanbanInput,
  KanbanSelect,
  KanbanSwitch,
  KanbanTextarea,
  KanbanTitle,
} from 'kanban-design-system';
import { useGetRelationshipTypes } from '@slices/CiRelationshipTypesSlice';
import { SetFieldValueFn } from '@common/utils/CommonUtils';
import { Flex, ScrollArea } from '@mantine/core';
import styled from '../ImpactedRule.module.scss';
import { ImpactedRuleApi } from '@api/ImpactedRuleApi';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { IconCopy } from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { IconTrash } from '@tabler/icons-react';
import { IconEdit } from '@tabler/icons-react';
import { buildImpactedRuleDetailUrl, impactedRulesPath, navigateTo } from '@common/utils/RouterUtils';
import { DATE_FORMAT_2, dateToString } from '@common/utils/DateUtils';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { CiRelationshipDirectionEnum, ImpactedRuleModel, RuleAction } from '@models/ImpactedRule';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import ForbiddenPage from '@pages/base/ForbiddenPage';
import { BreadcrumbComponent, UrlBaseCrumbData } from '@pages/admins/breadcrumb/BreadcrumbComponent';
import { formatStandardName } from '@common/utils/StringUtils';

const copyRule = (srcRule: ImpactedRuleModel) => {
  return { ...srcRule, id: 0, ruleName: `${srcRule.ruleName}-[Copy]-${dateToString(new Date(), DATE_FORMAT_2)}`, active: false };
};

const listAllowEditScreen = [RuleAction.EDIT, RuleAction.COPY, RuleAction.ADD];
export const ImpactedRuleDetailPage = () => {
  const ciRelationshipTypes = useGetRelationshipTypes();
  const navigate = useNavigate();
  const [currentRule, setCurrentRule] = useState<ImpactedRuleModel>({ id: 0, relationshipDirection: CiRelationshipDirectionEnum.OUT });

  const { id } = useParams();
  const ruleIdParam = Number(id);

  const [searchParams] = useSearchParams();
  const action = searchParams.get('action');

  const [openedConfirmModal, { close: closeConfirmModal, open: openConfirmModal }] = useDisclosure(false);

  useEffect(() => {
    //avoid call api when already not have permission

    if (ruleIdParam && isCurrentUserMatchPermissions([AclPermission.viewDetailImpactedRule])) {
      ImpactedRuleApi.getRuleById(ruleIdParam)
        .then((res) => {
          const resRule = res.data;
          if (RuleAction.COPY === action) {
            setCurrentRule(copyRule(resRule));
          } else {
            setCurrentRule(resRule);
          }
        })
        .catch(() => {
          navigateTo(impactedRulesPath);
        });
    }
  }, [action, ruleIdParam]);

  useEffect(() => {
    if (action && !Object.values(RuleAction).includes(action as RuleAction)) {
      navigate(buildImpactedRuleDetailUrl(ruleIdParam, RuleAction.VIEW));
      return;
    }
  }, [action, navigate, ruleIdParam]);
  useEffect(() => {
    if (RuleAction.ADD === action) {
      setCurrentRule((prev) => ({ ...prev, id: 0, relationshipDirection: CiRelationshipDirectionEnum.OUT, active: true }));
      return;
    }
  }, [action]);
  const listRelationshipCombobox = useMemo(() => {
    return ciRelationshipTypes.data.map((item) => {
      return {
        value: `${item.id}`,
        label: `${item.type}-${item.inverseType}`,
      };
    });
  }, [ciRelationshipTypes.data]);
  const handleChange: SetFieldValueFn<ImpactedRuleModel> = useCallback(
    (field, value) => {
      if (!listAllowEditScreen.includes(action as RuleAction)) {
        return;
      }
      setCurrentRule((prev) => {
        return {
          ...prev,
          [field]: value,
        };
      });
    },
    [action],
  );

  // const MODIFY_ACTION_LST = [RULE_ACTION.ADD, RULE_ACTION.EDIT, RULE_ACTION.COPY];
  const validateRule = useCallback(
    (data: ImpactedRuleModel) => {
      //051224 fix bug not check delete rel
      const isExistRel = ciRelationshipTypes.data.some((it) => it.id === data.relationshipTypeId);

      if ((data.ruleName && data.ruleName.trim() === '') || !data.ruleName || !data.relationshipTypeId || !isExistRel) {
        return false;
      }
      return true;
    },
    [ciRelationshipTypes.data],
  );
  const handleSaveRule = useCallback(() => {
    //save rule
    if (!currentRule) {
      return;
    }

    if (RuleAction.EDIT === action) {
      ImpactedRuleApi.updateRuleImpacted(currentRule.id, currentRule)
        .then(() => {
          NotificationSuccess({
            message: 'Update rule successfully',
          });
          navigateTo(impactedRulesPath);
        })
        .catch(() => {});
    }
    if (RuleAction.ADD === action || RuleAction.COPY === action) {
      ImpactedRuleApi.createRuleImpacted(currentRule)
        .then(() => {
          NotificationSuccess({
            message: 'Create rule successfully',
          });
          navigateTo(impactedRulesPath);
        })
        .catch(() => {});
    }
  }, [action, currentRule]);
  const accordionItems: KanbanAccordionData[] = useMemo(
    () => [
      {
        key: 'info',
        content: (
          <>
            <KanbanInput
              label='Rule name'
              disabled={RuleAction.VIEW === action}
              value={currentRule?.ruleName}
              maxLength={255}
              withAsterisk
              required
              onChange={(event) => {
                handleChange('ruleName', event.target.value);
              }}
            />
            <KanbanTextarea
              label='Description'
              value={currentRule?.description}
              maxLength={2000}
              //2477 height des
              className={styled.textArea}
              disabled={RuleAction.VIEW === action}
              onChange={(event) => {
                handleChange('description', event.target.value);
              }}
            />
            <Flex justify={'flex-start'} align={'center'}>
              <KanbanSwitch
                size='sm'
                mr='xs'
                color='green'
                checked={currentRule?.active ?? false}
                //Not allow to change status when edit cause fail api update rule
                // disabled={RuleAction.VIEW === action}
                onChange={(e) => {
                  if (RuleAction.VIEW !== action) {
                    handleChange('active', e.target.checked);
                  }
                }}
              />
              {currentRule?.active ? 'Active' : 'Inactive'}
            </Flex>
          </>
        ),
        title: (
          <KanbanTitle order={4} c={'var(--mantine-color-blue-8)'}>
            Information
          </KanbanTitle>
        ),
      },
      {
        key: 'relation',
        title: (
          <KanbanTitle order={4} c={'var(--mantine-color-blue-8)'}>
            Condition
          </KanbanTitle>
        ),
        content: (
          <>
            <KanbanSelect
              comboboxProps={{ position: 'top', middlewares: { flip: false, shift: false }, offset: 0, withinPortal: false }}
              key={'relationcb'}
              searchable
              label='Relationship'
              disabled={RuleAction.VIEW === action}
              withAsterisk
              required
              value={`${currentRule?.relationshipTypeId}`}
              data={listRelationshipCombobox}
              onChange={(value) => {
                //0 case deselect
                if (value) {
                  handleChange('relationshipTypeId', Number.parseInt(value));
                }
              }}
            />

            <KanbanCheckbox
              disabled={RuleAction.VIEW === action}
              label='Invert relationship'
              checked={CiRelationshipDirectionEnum.IN === currentRule?.relationshipDirection}
              onChange={(event) => {
                handleChange('relationshipDirection', event.target.checked ? CiRelationshipDirectionEnum.IN : CiRelationshipDirectionEnum.OUT);
              }}
            />
          </>
        ),
      },
    ],
    [
      action,
      currentRule?.active,
      currentRule?.description,
      currentRule?.relationshipDirection,
      currentRule?.relationshipTypeId,
      currentRule?.ruleName,
      handleChange,
      listRelationshipCombobox,
    ],
  );
  const leftSection = useCallback(() => {
    switch (action) {
      case RuleAction.VIEW:
        return (
          <Flex justify={'space-between'} align={'center'} gap={'xs'} className={styled.titleModal}>
            <Flex gap={'xs'}>
              <>
                <KanbanButton
                  variant='outline'
                  onClick={() => {
                    navigateTo(impactedRulesPath);
                  }}>
                  Cancel
                </KanbanButton>
                <GuardComponent requirePermissions={[AclPermission.createImpactedRule]} hiddenOnUnSatisfy>
                  <KanbanButton
                    leftSection={<IconCopy />}
                    color={'cyan'}
                    variant='light'
                    onClick={() => {
                      navigate(buildImpactedRuleDetailUrl(ruleIdParam, RuleAction.COPY));
                    }}>
                    Copy
                  </KanbanButton>
                </GuardComponent>
                <GuardComponent requirePermissions={[AclPermission.deleteImpactedRule]} hiddenOnUnSatisfy>
                  <KanbanButton
                    key='deletebtn'
                    leftSection={<IconTrash />}
                    color={'red'}
                    onClick={() => {
                      openConfirmModal();
                    }}>
                    Delete
                  </KanbanButton>
                </GuardComponent>
                <GuardComponent requirePermissions={[AclPermission.updateImpactedRule]} hiddenOnUnSatisfy>
                  <KanbanButton
                    leftSection={<IconEdit />}
                    onClick={() => {
                      navigate(buildImpactedRuleDetailUrl(ruleIdParam, RuleAction.EDIT));
                    }}>
                    Edit
                  </KanbanButton>
                </GuardComponent>
              </>
            </Flex>
          </Flex>
        );

      case RuleAction.ADD:
      case RuleAction.EDIT:
      case RuleAction.COPY:
      default:
        return (
          <Flex gap={'xs'}>
            <KanbanButton
              variant='outline'
              onClick={() => {
                navigateTo(impactedRulesPath);
              }}>
              Cancel
            </KanbanButton>
            {/* {isCurrentUserHasAnyPermissions([AclPermission.updateImpactedRule, AclPermission.createImpactedRule]) && ( */}
            <KanbanButton key='savebtn' disabled={!validateRule(currentRule)} onClick={handleSaveRule}>
              Save
            </KanbanButton>
            {/* )} */}
          </Flex>
        );
    }
  }, [action, currentRule, handleSaveRule, navigate, openConfirmModal, ruleIdParam, validateRule]);

  const onConfirmConfirmModal = useCallback(() => {
    ImpactedRuleApi.deleteRuleImpacteds([currentRule?.id ?? 0])
      .then(() => {
        NotificationSuccess({ message: 'Delete rule successfully' });
        navigateTo(impactedRulesPath);
      })
      .catch(() => {});

    closeConfirmModal();
  }, [closeConfirmModal, currentRule]);

  const locationCustomPaths = useMemo((): UrlBaseCrumbData => {
    const originPath = buildImpactedRuleDetailUrl(ruleIdParam, RuleAction.VIEW);
    let detailBread = '';

    if (ruleIdParam) {
      detailBread = `${formatStandardName(action || '')} ${currentRule.ruleName}`;
    } else if (RuleAction.ADD === action) {
      detailBread = `${formatStandardName(action)}`;
    } else if (RuleAction.COPY === action) {
      detailBread = `${formatStandardName(action)} ${currentRule.ruleName}`;
    }

    return {
      [`/${ruleIdParam}`]: {
        title: detailBread,
        href: originPath,
      },
    };
  }, [action, currentRule.ruleName, ruleIdParam]);
  const renderHeader = useCallback(() => {
    const actionMessages: { [key in RuleAction]?: string } = {
      [RuleAction.ADD]: 'Add new rule',
      [RuleAction.COPY]: 'Copy rule',
      [RuleAction.EDIT]: 'Update rule',
      [RuleAction.VIEW]: 'View rule',
    };
    return actionMessages[action as RuleAction] || ''; // Ensure `action` is a valid key
  }, [action]);

  if (
    (RuleAction.ADD === action && !isCurrentUserMatchPermissions([AclPermission.createImpactedRule])) ||
    (RuleAction.EDIT === action && !isCurrentUserMatchPermissions([AclPermission.updateImpactedRule]))
  ) {
    return <ForbiddenPage />;
  }

  return (
    <ScrollArea.Autosize>
      <BreadcrumbComponent locationCustomPaths={locationCustomPaths} />
      <KanbanConfirmModal
        title={`Confirm delete item`}
        onClose={closeConfirmModal}
        onConfirm={onConfirmConfirmModal}
        textConfirm='Confirm'
        opened={openedConfirmModal}
        modalProps={{
          size: '30%',
        }}>
        Are you sure to delete rule {currentRule?.ruleName}?
      </KanbanConfirmModal>
      <Flex justify={'center'}>
        <Flex justify={'center'} direction={'column'} w={'50%'}>
          <HeaderTitleComponent title={renderHeader()} rightSection={<>{leftSection()}</>} />
          <KanbanAccordion
            variant='separated'
            chevronPosition={'left'}
            transitionDuration={200}
            defaultValue={['info', 'relation']}
            multiple
            data={accordionItems}
          />
        </Flex>
      </Flex>
    </ScrollArea.Autosize>
  );
};
