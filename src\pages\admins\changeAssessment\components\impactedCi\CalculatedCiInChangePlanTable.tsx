import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { KanbanButton, KanbanText } from 'kanban-design-system';
import { KanbanTable, type ColumnType, type KanbanTableProps } from 'kanban-design-system';
import { useNavigate } from 'react-router-dom';
import { renderDateTime } from 'kanban-design-system';
import { IconClipboardList } from '@tabler/icons-react';
import CiUpdateSole, { CiUpdateSoleMethods } from '@pages/cis/ci/CiUpdateSole';
import { KanbanConfirmModal } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import { KanbanIconButton } from 'kanban-design-system';
import { CiManagementApi, type CiManagementResponse } from '@api/CiManagementApi';
import CiManagementDetailViewPopup, { CiManagementDetailViewPopupMethods } from '@pages/cis/ciManagement/modal/CiManagementDetailViewPopup';

import { ScrollArea, Tooltip } from '@mantine/core';
import { IconEye } from '@tabler/icons-react';
import { IconAffiliate } from '@tabler/icons-react';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { buildCiTypeUrl } from '@common/utils/RouterUtils';
import type { ConfigItemResponse } from '@api/ConfigItemApi';
import ViewServiceMappingDetail, { ViewServiceMappingDetailMethods } from '../ViewServiceMappingDetail';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import type { CiImpactedByRelationshipInformationResponseDto } from '@models/ChangeAssessment';
import { NumberMapEntry } from './ImpactedCiFlagTableComponent';
import { ConfigItemTypeModel } from '@models/ConfigItemType';
import CiRelationship from '@pages/cis/ci/relationship/CiRelationship';

type CalculatedCiInChangePlanProps = {
  allowEdit: boolean;
  currentFlagImpactedCi: CiImpactedByRelationshipInformationResponseDto;
  setCurrentListCiInChangeNew: (vals: ConfigItemResponse[]) => void;
  setCurrentFlagImpactedCi: (vals: CiImpactedByRelationshipInformationResponseDto) => void;
};

export const CalculatedCiInChangePlanTable: React.FC<CalculatedCiInChangePlanProps> = ({
  allowEdit,
  currentFlagImpactedCi,
  setCurrentListCiInChangeNew,
}) => {
  const [listCiDraft, setListCiDraft] = useState<CiManagementResponse[]>([]);
  const allCiTypeMap = useGetCiTypes().data.reduce((acc: NumberMapEntry<ConfigItemTypeModel>, item) => {
    acc[item.id] = item; // Use the id as the key and the item as the value
    return acc;
  }, {});
  const navigate = useNavigate();

  const listCiChange = useMemo(() => currentFlagImpactedCi?.ciChangePlans || [], [currentFlagImpactedCi?.ciChangePlans]);

  const [openedModalViewCi, { close: closeModalViewCi, open: openModalViewCi }] = useDisclosure(false);
  const [openedModalRelationship, { close: closeModalRelationship, open: openModalRelationship }] = useDisclosure(false);
  const [currentCiInfo, setCurrentCiInfo] = useState<
    | {
        ciTypeId: number;
        ciId: number;
      }
    | undefined
  >();

  const [currentCiTemp, setCurrentCiTemp] = useState<CiManagementResponse>();

  const onClickViewDraft = useCallback(
    (ciId: number) => {
      const ciTempData = listCiDraft.find((x) => x.ciId === ciId);
      setCurrentCiTemp(ciTempData);
      childRefViewDetail.current?.openPopupView();
    },
    [listCiDraft],
  );
  // useEffect(() => {
  //   setCurrentListCiInChangeNew(currentFlagImpactedCi?.ciChangePlans);
  // }, [currentFlagImpactedCi?.ciChangePlans]);

  const columns: ColumnType<ConfigItemResponse>[] = useMemo(() => {
    return [
      {
        title: 'Id',
        name: 'id',
        hidden: true,
      },
      {
        title: 'Name',
        name: 'name',
        width: '10%',
      },
      {
        title: 'Description',
        name: 'description',
        customRender: (data) => {
          return <KanbanText lineClamp={2}>{data}</KanbanText>;
        },
        width: '40%',
      },
      {
        title: 'Ci Type',
        name: 'ciTypeId',
        customRender: (data: number) => {
          const ciType = allCiTypeMap[data];
          if (!ciType) {
            return <></>;
          }
          return (
            <KanbanButton
              size='compact-xs'
              radius={'lg'}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(buildCiTypeUrl(ciType.id));
              }}>
              {ciType.name}
            </KanbanButton>
          );
        },
        width: '10%',
      },
      {
        title: 'Created by',
        name: 'createdBy',
        width: '10%',
      },
      {
        title: 'Created date',
        name: 'createdDate',
        customRender: renderDateTime,
        width: '10%',
      },
    ];
  }, [allCiTypeMap, navigate]);

  const handleRemoveCisInchange = useCallback(
    (toRemoveCis: ConfigItemResponse[]) => {
      setCurrentListCiInChangeNew(toRemoveCis);
    },
    [setCurrentListCiInChangeNew],
  );
  const tableProps: KanbanTableProps<ConfigItemResponse> = useMemo(() => {
    return {
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
      },
      sortable: {
        enable: true,
      },
      columns: columns,
      data: listCiChange,
      pagination: {
        enable: true,
      },

      selectableRows: {
        enable: true,
        customAction: (toRemoveCis) => {
          handleRemoveCisInchange(toRemoveCis);
          return <></>;
        },
      },
      actions: {
        customAction: (data) => {
          return (
            <>
              <Tooltip label='View change'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  disabled={listCiDraft.every((x) => x.ciId !== data.id)}
                  onClick={() => {
                    onClickViewDraft(data.id);
                  }}>
                  <IconClipboardList />
                </KanbanIconButton>
              </Tooltip>
              <GuardComponent requirePermissions={AclPermission.createViewCiPermissions(data.id, data.ciTypeId)} hiddenOnUnSatisfy>
                <Tooltip label='View info'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setCurrentCiInfo({
                        ciId: data.id,
                        ciTypeId: data.ciTypeId,
                      });
                      openModalViewCi();
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                </Tooltip>
              </GuardComponent>

              <Tooltip label='View relationship'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    setCurrentCiInfo({
                      ciId: data.id,
                      ciTypeId: data.ciTypeId,
                    });
                    openModalRelationship();
                  }}>
                  <IconAffiliate />
                </KanbanIconButton>
              </Tooltip>
            </>
          );
        },
      },
    };
  }, [columns, handleRemoveCisInchange, listCiChange, listCiDraft, onClickViewDraft, openModalRelationship, openModalViewCi]);

  const customTableProps = useMemo(() => {
    const tablePropsUpdate = { ...tableProps };
    if (!allowEdit) {
      const { actions, selectableRows, ...rest } = tablePropsUpdate;
      if (selectableRows) {
        selectableRows.enable = false;
      }
      if (actions && actions.deletable) {
        delete actions.deletable;
      }

      return { ...rest, selectableRows, actions };
    }

    return tablePropsUpdate;
  }, [allowEdit, tableProps]);

  const fetchDataDraft = useCallback(() => {
    const listIds = listCiChange.map((x) => x.id);
    if (listIds.length > 0) {
      CiManagementApi.getCiTempByCiIdIn(listIds)
        .then((res) => {
          if (res.data && res.data.length > 0) {
            setListCiDraft(res.data);
            return;
          }
        })
        .catch(() => {});
    }
  }, [listCiChange]);

  useEffect(() => {
    fetchDataDraft();
  }, [fetchDataDraft]);

  const childRef = useRef<CiUpdateSoleMethods | null>(null);
  const childRefViewDetail = useRef<CiManagementDetailViewPopupMethods | null>(null);
  const childRefViewServiceMapping = useRef<ViewServiceMappingDetailMethods | null>(null);

  return (
    <>
      {/* modal view detail info of CI management draft */}
      <CiManagementDetailViewPopup ref={childRefViewDetail} initData={currentCiTemp} />

      {/* Modal view service mapping */}
      <ViewServiceMappingDetail ref={childRefViewServiceMapping} />

      <KanbanConfirmModal
        title={'CI Detail'}
        onConfirm={undefined}
        onClose={closeModalViewCi}
        opened={openedModalViewCi}
        modalProps={{
          size: '80%',
        }}>
        {currentCiInfo && (
          <ScrollArea.Autosize mah={1000} type='scroll'>
            <CiUpdateSole readOnly={true} ciId={currentCiInfo.ciId} ciTypeId={currentCiInfo.ciTypeId} forwardedRef={childRef} />
          </ScrollArea.Autosize>
        )}
      </KanbanConfirmModal>

      <KanbanConfirmModal
        title={'Relationship'}
        onConfirm={undefined}
        onClose={closeModalRelationship}
        opened={openedModalRelationship}
        modalProps={{
          size: '80%',
        }}>
        {currentCiInfo && <CiRelationship ciId={currentCiInfo.ciId} ciTypeId={currentCiInfo.ciTypeId} isView isFromBusinessView={true} />}
      </KanbanConfirmModal>
      <KanbanTable {...customTableProps} />
    </>
  );
};

CalculatedCiInChangePlanTable.whyDidYouRender = true;
