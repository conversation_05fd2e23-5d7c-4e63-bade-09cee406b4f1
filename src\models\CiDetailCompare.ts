export type AttributeValue = {
  oldValue: string | null;
  newValue: string | number | null;
  sourceChange?: string;
};

export type Attribute = {
  [key: string]: AttributeValue;
};

export type Attributes = {
  [key: string]: Attribute | AttributeValue;
};

export type DataCiCompare = {
  ci?: Attribute;
  attributes?: Attributes;
  attributesCustom?: Attributes;
  //4243: show info ciRelationShip added/ removed from a CI
  relationships?: Attributes;
};

export function isAttribute(attribute: Attribute | AttributeValue): attribute is Attribute {
  return typeof attribute === 'object' && attribute !== null && Object.values(attribute).every((val) => 'oldValue' in val && 'newValue' in val);
}
