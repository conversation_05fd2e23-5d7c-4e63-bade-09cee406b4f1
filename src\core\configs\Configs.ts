export type Service = 'server' | 'discovery';

export const getConfigs = () => {
  const apiBaseUrl = process.env.REACT_APP_ITCMDB_API_URL || '';
  const apiBaseUrlRemote = process.env.REACT_APP_ITCMDB_API_URL_REMOTE || apiBaseUrl;

  const apiServiceRemote = (process.env.REACT_APP_ITCMDB_API_SERVICE_REMOTE || '').split(',');

  const isDevMode = process.env.NODE_ENV === 'development';

  return {
    publicAssetsPath: process.env.ASSET_PREFIX || '',
    name: process.env.REACT_APP_NAME || '',
    fullname: process.env.REACT_APP_FULLNAME || '',
    description: process.env.REACT_APP_DESCRIPTION || '',
    deployUrl: process.env.REACT_APP_DEPLOY_URL || '',
    apiBaseUrl: apiBaseUrl || '',
    showDevelopmentGuide: process.env.REACT_APP_SHOW_DEVELOPMENT_GUIDE === 'true',
    apiBaseUrlRemote: apiBaseUrlRemote,
    buildApiBaseUrl: (service: Service = 'server') => {
      const map: Record<Service, string> = {
        server: '/api',
        discovery: '/api/discovery',
      };
      const baseUrl = map[service];
      if (!isDevMode || !apiServiceRemote.includes(service)) {
        return apiBaseUrl + baseUrl;
      }
      return apiBaseUrlRemote + baseUrl;
    },
    apiWebHookCollect: process.env.REACT_APP_MBMONITOR_API_WEBHOOK_COLLECT || '',

    keycloak: {
      enable: process.env.REACT_APP_KEYCLOAK_ENABLED === 'true',
      url: process.env.REACT_APP_KEYCLOAK_URL || '',
      realm: process.env.REACT_APP_KEYCLOAK_REALM || '',
      clientId: process.env.REACT_APP_KEYCLOAK_CLIENT_ID || '',
    },
    features: {
      cmdbDemo: process.env.REACT_APP_FEATURE_CMDB_DEMO === 'true',
      // default true (show feature), set file ENV process.env.[key] === false -> disable feature
      ciTypeReferenceAttribute: process.env.REACT_APP_FEATURE_CI_TYPE_REFERENCE_ATTRIBUTE !== 'false',
      impactedService: process.env.REACT_APP_FEATURE_CHANGE_IMPACTED_SERVICE !== 'false',
      discoverySourceData: process.env.REACT_APP_FEATURE_DISCOVERY_SOURCE_DATA !== 'false',
    },
  };
};
