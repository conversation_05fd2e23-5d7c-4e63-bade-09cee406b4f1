import type { GoJs } from '@common/libs';
import {
  changeDiagramLinkStyle,
  GRAPH_VIEW_EXPORT_TYPE,
  GRAPH_VIEW_LINK_STYPES,
  GRAPH_VIEW_TYPE,
  handleConnectToNode,
  handleDownloadImage,
  scrollAndZoomToItem,
  settingGraphViewType,
} from '@common/utils/GoJsHelper';
import { ComboboxItem, Divider, NavLink, Popover, ThemeIcon } from '@mantine/core';
import {
  IconArrowNarrowRight,
  IconArrowsRightLeft,
  IconArrowsUpDown,
  IconCircleDashed,
  IconCircleDot,
  IconCornerUpRight,
  IconDownload,
  IconEyeShare,
  IconHierarchy,
  IconJpg,
  IconLayout,
  IconPng,
} from '@tabler/icons-react';
import { KanbanCheckbox, KanbanRadio, KanbanSelect, KanbanText, KanbanTooltip } from 'kanban-design-system';
import React, { useCallback, useState } from 'react';
import type { ShowActionDiagramConfigProps } from '../../relationship/CiRelationship';
import stylesCss from '../CiRelationshipGraph.module.scss';
import FilterCiRelationship from './FilterCiRelationship';
import type { ToolbarActionProps } from '../CiRelationshipGraph';
import SaveGraphView from './SaveGraphView';
import type { CIBusinessViews } from '@models/CIBusinessViews';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';
import go from '@common/libs/gojs/go';
import { ImpactTypeInput } from '@models/CiRelationship';
import { handleDownloadImageWithVitualized } from '@common/utils/GoJsVirtualizeHelper';

type GraphToolbarActionViewProps = {
  dataViewGraph: CIBusinessViews | undefined;
  currentLevel: string | undefined;
  ciId: number;
  goDiagram: GoJs.Diagram | undefined;
  toolBarConfig: ShowActionDiagramConfigProps;
  toolbarAction: ToolbarActionProps;
  setToolbarAction: (toolbarAction: ToolbarActionProps) => void;
  isDiagramVirtual?: boolean;
};

const GraphToolbarActionView = (props: GraphToolbarActionViewProps) => {
  const { goDiagram, setToolbarAction, toolbarAction, toolBarConfig } = props;
  const [locateCis, setLocateCis] = useState<ComboboxItem[]>([]);
  const [upStream, setUpStream] = useState<boolean>(true);
  const [downStream, setDownStream] = useState<boolean>(true);
  const [typeInput, setTypeInput] = useState(`${ImpactTypeInput.IMPACTTO}`);

  const updateLayout = useCallback(
    (typeGraph: GRAPH_VIEW_TYPE) => {
      settingGraphViewType(goDiagram, props.ciId, typeGraph);
      setToolbarAction({ ...toolbarAction, graphViewStyle: typeGraph });
    },
    [goDiagram, setToolbarAction, props.ciId, toolbarAction],
  );

  const updateLinkStyle = useCallback(
    (linkStyle: GRAPH_VIEW_LINK_STYPES) => {
      changeDiagramLinkStyle(goDiagram, linkStyle);
      setToolbarAction({ ...toolbarAction, graphViewLinkStyle: linkStyle });
    },
    [goDiagram, setToolbarAction, toolbarAction],
  );

  const handleUpStreamOrDownStream = (isUpStream: boolean, checked: boolean) => {
    const handleUpStreamOrDownStreamTransaction = 'handleUpStreamOrDownStreamTransaction';
    if (!goDiagram) {
      return;
    }
    const nodeRoot = goDiagram.findNodesByExample({ isRoot: true }).first();
    if (!nodeRoot) {
      return;
    }

    goDiagram.startTransaction(handleUpStreamOrDownStreamTransaction);

    const toggleLinkVisibility = (link: go.Link) => {
      link.visible = checked;
    };

    // Hide/show all links to the root node
    if (isUpStream) {
      nodeRoot.findLinksInto().each(toggleLinkVisibility);
    }

    // Hide/show all links from the root node
    if (!isUpStream) {
      nodeRoot.findLinksOutOf().each(toggleLinkVisibility);
    }

    // Update the visibility of nodes based on whether they are connected to the root node
    handleConnectToNode(goDiagram, nodeRoot);

    goDiagram.commitTransaction(handleUpStreamOrDownStreamTransaction);
  };

  /**
   * get status checked setting upstream/downStream
   * @param isUpStream
   */
  const openedUpStreamAndDownStream = () => {
    if (!goDiagram) {
      return;
    }
    const nodeRoot = goDiagram.findNodesByExample({ isRoot: true }).first();
    if (!nodeRoot) {
      return;
    }
    const visibleUpstreamLinks = nodeRoot.findLinksInto().filter((link: go.Link) => link.visible).count;
    setUpStream(visibleUpstreamLinks > 0);
    const visibleDownstreamLinks = nodeRoot.findLinksOutOf().filter((link: go.Link) => link.visible).count;
    setDownStream(visibleDownstreamLinks > 0);
  };
  /**
   * set new data locate when open toolbar locate CI
   * @returns
   */
  const openedLocateCi = () => {
    const selectCis: ComboboxItem[] = [];
    if (!goDiagram) {
      setLocateCis([]);
      return;
    }
    goDiagram.nodes.each((node: GoJs.Node) => {
      const newItem = { value: `${node.data.key}`, label: node.data.label };
      selectCis.push(newItem);
    });
    setLocateCis(selectCis);
  };

  const handleDownloadDiagram = (isDiagramVirtual: boolean | undefined, imageType: GRAPH_VIEW_EXPORT_TYPE) => {
    if (isDiagramVirtual) {
      handleDownloadImageWithVitualized(goDiagram, imageType);
      return;
    }
    handleDownloadImage(goDiagram, imageType);
  };

  return (
    <div className={stylesCss['toolbar']}>
      {toolBarConfig.toolBar?.isShowLocate && (
        <div className={stylesCss['icon']}>
          <Popover
            withinPortal={false}
            closeOnClickOutside={true}
            clickOutsideEvents={['mouseup', 'touchend']}
            position='right'
            shadow='md'
            width={200}
            onOpen={openedLocateCi}>
            <Popover.Target>
              <KanbanTooltip
                withinPortal={false}
                label={props.isDiagramVirtual ? 'Locate CI (You need to scroll the entire graph before performing locating)' : 'Locate CI'}>
                <ThemeIcon variant='outline' color={'primary'} size={30}>
                  <IconCircleDot size='3rem' stroke={2} />
                </ThemeIcon>
              </KanbanTooltip>
            </Popover.Target>
            <Popover.Dropdown>
              Select CI
              <KanbanSelect
                comboboxProps={{ withinPortal: false }}
                searchable={true}
                data={locateCis}
                onChange={(e) => {
                  scrollAndZoomToItem(goDiagram, e);
                }}></KanbanSelect>
              <Divider />
            </Popover.Dropdown>
          </Popover>
        </div>
      )}

      {toolBarConfig.toolBar?.isShowFilter && (
        <div className={stylesCss['icon']}>
          <FilterCiRelationship toolbarAction={toolbarAction} goDiagram={goDiagram} setToolbarAction={setToolbarAction} />
        </div>
      )}

      {/* SAVE TOOLBAR */}
      {toolBarConfig.toolBar?.isShowSave && isCurrentUserMatchPermissions([AclPermission.createBusinessView, AclPermission.updateBusinessView]) && (
        <div className={stylesCss['icon']}>
          <SaveGraphView
            toolbarAction={toolbarAction}
            ciId={props.ciId}
            goDiagram={goDiagram}
            cIBusinessViews={props.dataViewGraph}
            currentLevel={props.currentLevel}></SaveGraphView>
        </div>
      )}

      {toolBarConfig.toolBar?.isShowDownload && (
        <div className={stylesCss['icon']}>
          <Popover
            withinPortal={false}
            closeOnClickOutside={true}
            clickOutsideEvents={['mouseup', 'touchend']}
            position='right'
            shadow='md'
            width={200}>
            <Popover.Target>
              <KanbanTooltip withinPortal={false} label='Download Diagram'>
                <ThemeIcon variant='outline' color={'primary'} size={30}>
                  <IconDownload size='3rem' stroke={2} />
                </ThemeIcon>
              </KanbanTooltip>
            </Popover.Target>

            <Popover.Dropdown>
              <KanbanText>Export Diagram</KanbanText>
              <Divider />
              <NavLink
                onClick={() => handleDownloadDiagram(props.isDiagramVirtual, GRAPH_VIEW_EXPORT_TYPE.JPG)}
                leftSection={<IconJpg />}
                label='Image jpg'
              />
              <NavLink
                onClick={() => handleDownloadDiagram(props.isDiagramVirtual, GRAPH_VIEW_EXPORT_TYPE.PNG)}
                leftSection={<IconPng />}
                label='Image png'
              />

              <Divider />
            </Popover.Dropdown>
          </Popover>
        </div>
      )}

      {toolBarConfig.toolBar?.isShowUpStreamAndDownStream && (
        <div className={stylesCss['icon']}>
          <Popover
            withinPortal={false}
            closeOnClickOutside={true}
            clickOutsideEvents={['mouseup', 'touchend']}
            position='right'
            shadow='md'
            width={200}
            onOpen={openedUpStreamAndDownStream}>
            <Popover.Target>
              <KanbanTooltip withinPortal={false} label='Up Stream/ Down Stream'>
                <ThemeIcon variant='outline' color={'primary'} size={30}>
                  <IconEyeShare size='3rem' stroke={2} />
                </ThemeIcon>
              </KanbanTooltip>
            </Popover.Target>

            <Popover.Dropdown>
              <KanbanText>Up Stream/ Down Stream</KanbanText>
              <Divider p={10} />
              <KanbanCheckbox
                checked={upStream}
                onChange={(e) => {
                  const value = e.target.checked;
                  setUpStream(value);
                  handleUpStreamOrDownStream(true, value);
                }}
                label='Enable Up stream'></KanbanCheckbox>
              <KanbanCheckbox
                checked={downStream}
                onChange={(e) => {
                  const value = e.target.checked;
                  setDownStream(value);
                  handleUpStreamOrDownStream(false, value);
                }}
                label='Enable Down stream'></KanbanCheckbox>
              <Divider />
            </Popover.Dropdown>
          </Popover>
        </div>
      )}

      {toolBarConfig.toolBar?.isShowChangeStyle && !props.isDiagramVirtual && (
        <div className={stylesCss['icon']}>
          <Popover
            withinPortal={false}
            closeOnClickOutside={true}
            clickOutsideEvents={['mouseup', 'touchend']}
            position='right'
            shadow='md'
            width={200}>
            <Popover.Target>
              <KanbanTooltip withinPortal={false} label='Change Style'>
                <ThemeIcon variant='outline' color={'primary'} size={30}>
                  <IconLayout size='3rem' stroke={2} />
                </ThemeIcon>
              </KanbanTooltip>
            </Popover.Target>

            <Popover.Dropdown>
              <KanbanText>View Style</KanbanText>
              <Divider p={10} />

              <NavLink
                onClick={() => {
                  updateLayout(GRAPH_VIEW_TYPE.TOP_DOWN);
                }}
                leftSection={<IconArrowsUpDown />}
                label='Up/down'
              />

              <NavLink
                onClick={() => {
                  updateLayout(GRAPH_VIEW_TYPE.LEFT_RIGHT);
                }}
                leftSection={<IconArrowsRightLeft />}
                label='Left/Right'
              />
              <NavLink
                onClick={() => {
                  updateLayout(GRAPH_VIEW_TYPE.CIRCLE);
                }}
                leftSection={<IconCircleDashed />}
                label='Circle'
              />

              <Divider />
            </Popover.Dropdown>
          </Popover>
        </div>
      )}
      {/* change style link  */}
      {toolBarConfig.toolBar?.isShowChangeLinkStyle && (
        <div className={stylesCss['icon']}>
          <Popover
            withinPortal={false}
            closeOnClickOutside={true}
            clickOutsideEvents={['mouseup', 'touchend']}
            position='right'
            shadow='md'
            width={200}>
            <Popover.Target>
              <KanbanTooltip withinPortal={false} label='Change Relationship Style'>
                <ThemeIcon variant='outline' color={'primary'} size={30}>
                  <IconHierarchy size='3rem' stroke={2} />
                </ThemeIcon>
              </KanbanTooltip>
            </Popover.Target>

            <Popover.Dropdown>
              <KanbanText>View Relationship Style</KanbanText>
              <Divider p={10} />

              <NavLink
                onClick={() => {
                  updateLinkStyle(GRAPH_VIEW_LINK_STYPES.NORMAL_JUMP_OVER);
                }}
                leftSection={<IconArrowNarrowRight />}
                label='Normal Jump Over'
              />

              <NavLink
                onClick={() => {
                  updateLinkStyle(GRAPH_VIEW_LINK_STYPES.ORTHOGONAL_ROUTING);
                }}
                leftSection={<IconCornerUpRight />}
                label='Orthogonal Routing'
              />
              <Divider />
            </Popover.Dropdown>
          </Popover>
        </div>
      )}
      {/* Impact to / Impact by */}
      {toolBarConfig.toolBar?.isShowImpactToAndImpactBy && (
        <div className={stylesCss['icon']}>
          <Popover
            withinPortal={false}
            closeOnClickOutside={true}
            clickOutsideEvents={['mouseup', 'touchend']}
            position='right'
            shadow='md'
            width={200}>
            <Popover.Target>
              <KanbanTooltip withinPortal={false} label='Impact to / Impact by'>
                <ThemeIcon variant='outline' color={'primary'} size={30}>
                  <IconArrowsUpDown size='3rem' stroke={2} />
                </ThemeIcon>
              </KanbanTooltip>
            </Popover.Target>

            <Popover.Dropdown>
              <KanbanText>Impact to / Impact by</KanbanText>
              <Divider p={5} />
              <KanbanRadio
                group={{
                  name: 'typeSelect',
                  label: '',
                  mb: 'lg',
                  withAsterisk: false,
                  value: typeInput,
                  onChange: (value) => {
                    setTypeInput(value);
                    setToolbarAction({ ...toolbarAction, isImpactTo: value === ImpactTypeInput.IMPACTTO });
                  },
                }}
                radios={[
                  {
                    value: ImpactTypeInput.IMPACTTO,
                    label: 'Impact to',
                    checked: true,
                  },
                  {
                    value: ImpactTypeInput.IMPACTBY,
                    label: 'Impact by',
                  },
                ]}
              />{' '}
              <Divider />
            </Popover.Dropdown>
          </Popover>
        </div>
      )}
    </div>
  );
};
export default GraphToolbarActionView;
