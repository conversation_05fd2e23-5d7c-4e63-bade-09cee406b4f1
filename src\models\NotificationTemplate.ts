import type { SuggestionOptions } from '@tiptap/suggestion';
import type { MentionNodeAttrs } from '@tiptap/extension-mention';

export type NotificationTemplateDto = {
  id?: number;
  active?: boolean;
  message?: string;
  subject?: string;
  description?: string;
  params?: NotificationTemplateParamDto[];
};
export type NotificationTemplateParamDto = {
  value?: string;
  description?: string;
};

export type NotificationTemplateDetailPageProps = {
  id?: number;
  openedModalEditTemplate: boolean;
  closeModalEditTemplate: () => void;
};
export type MentionQueryProps = {
  query: string;
};

export type SuggestionType = Omit<SuggestionOptions<any, MentionNodeAttrs>, 'editor'>;

export interface MentionListProps {
  items: string[];
  command: (params: { id: string }) => void;
}
export interface MentionListHandle {
  onKeyDown: (params: { event: KeyboardEvent }) => boolean;
}
