import { BaseApi } from '@core/api/BaseApi';
import type { ChangeAssessmentAction, ChangeAssessmentDTO, ChangeAssessmentRequestDto } from '@models/ChangeAssessment';
import type { IncidentRequestResponseDto } from '@models/IncidentSdp';
import type { ChangeAssessmentDTOResponse } from './ChangeAssessmentApi';
import type { PaginationRequestModel } from '@models/EntityModelBase';
import type { CiSdpRequestModel } from '@models/ChangeSdp';
import { BaseUrl } from '@core/api/BaseUrl';

export class IncidentSdpApi extends BaseApi {
  static baseUrl = BaseUrl.incidentRequests;

  static getAll(pagination: PaginationRequestModel<ChangeAssessmentDTO>) {
    return BaseApi.postData<ChangeAssessmentDTOResponse>(
      `${this.baseUrl}/filter`,
      pagination,
      {},
      {},
      { useLoading: false, useErrorNotification: true },
    );
  }

  static getIncidentRequestById(id: number, action: ChangeAssessmentAction) {
    return BaseApi.getData<IncidentRequestResponseDto>(`${this.baseUrl}/sdp-requests?sdpId=${id}&action=${action}`);
  }

  static getByRequestId(requestId: number) {
    return BaseApi.getData<ChangeAssessmentDTO>(`${this.baseUrl}?requestId=${requestId}`);
  }

  static createNewIncident(data: ChangeAssessmentRequestDto) {
    return BaseApi.postData<boolean>(`${this.baseUrl}`, data);
  }

  static updateIncident(data: ChangeAssessmentRequestDto, id: number) {
    return BaseApi.putData<boolean>(`${this.baseUrl}/${id}`, data);
  }

  static deleteByIds(ids: number[]) {
    return BaseApi.deleteData<number[]>(`${this.baseUrl}/batch`, {
      ids,
    });
  }

  static getStatusByRequestIdIn(requestIds: number[], useLoading: boolean) {
    return BaseApi.getData<CiSdpRequestModel[]>(
      `${this.baseUrl}/sdp-requests/status`,
      { requestIds },
      {},
      {
        useLoading: useLoading,
        useErrorNotification: true,
      },
    );
  }
}
