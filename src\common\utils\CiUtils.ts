import type { ConfigItemTypeAttrResponse } from '@api/ConfigItemTypeAttrApi';
import { ActionType } from '@common/constants/CiManagement';
import type { HistoryDescription } from '@models/CiHistory';

export const historyDescriptionToString = (historyDescription: HistoryDescription): string => {
  let result = '';

  for (const item of historyDescription.info) {
    result += `\n- [${item.name}] changed to: [${item.toValue || '-'}]`;
  }

  for (const item of historyDescription.attributes) {
    result += `\n- [${item.name}] changed to: [${item.toValue || '-'}]`;
  }
  for (const item of historyDescription.attributeCustoms) {
    result += `\n- [${item.name}] changed to: [${item.toValue || '-'}]`;
  }

  return result;
};

export const configItemTypeAttrSorted = (data: ConfigItemTypeAttrResponse[]) => {
  return data.sort((a, b) => {
    const aOrder = a.order || 0;
    const bOrder = b.order || 0;

    if (a.id === 0 || b.id === 0) {
      return a.id === b.id ? aOrder - bOrder : a.id - b.id;
    }
    return aOrder - bOrder;
  });
};

export const messageApprovalByAction = (action: ActionType) => {
  const actionMessages = {
    [ActionType.SEND]: 'submitted for approval',
    [ActionType.APPROVE]: 'approved',
    [ActionType.REJECT]: 'rejected',
  };

  const msgAction = actionMessages[action];
  return `The CI(s) record has been ${msgAction} successfully`;
};

export default 1;
