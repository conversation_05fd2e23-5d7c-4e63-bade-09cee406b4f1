import type { CiImpactedHistoryDto } from '@api/ImpactedCiHistoryApi';
import { ServiceAttachedTypeEnum } from '@common/constants/CiDetail';
import type { ChangeAffectedCiDto, ChangeAssessmentRequestDto, CiImpactedByRelationshipInformationResponseDto } from '@models/ChangeAssessment';
import { ImpactedTypeTable } from '@models/ChangeAssessment';
import { CHANGE_STAGES } from '@common/constants/ChangeAssessmentConstants';
import { NotificationSuccess } from './NotificationUtils';
import type { MapAssessmentWithChangeResponse } from '@api/ChangeAssessmentApi';

export const checkIsChangeCorThenAuthor = (
  changeCor: string | undefined,
  currentUser: string | undefined,
  currentAuthor: string,
  // isSuperAdmin: boolean,
) => {
  // if (isSuperAdmin) {
  //   return true;
  // }
  return changeCor && changeCor !== '' ? changeCor === currentUser && currentUser === currentAuthor : currentUser === currentAuthor;
};

export enum ChangeErrorCodeEnum {
  CHANGE_COR_INVALID = 'CHANGE_COR_INVALID',
  CHANGE_INCORRECT_STAGE_WHEN_CMDB_LINK_NEW_CHANGE_OR_DELETE = 'CHANGE_INCORRECT_STAGE_WHEN_CMDB_LINK_NEW_CHANGE_OR_DELETE',
  CHANGE_INVALID_STATUS = 'CHANGE_INVALID_STATUS',
}

export enum ChangeFieldEnum {
  COORDINATOR = 'COORDINATOR',
  STAGE = 'STAGE',
  STATUS = 'STATUS',
}
const createChangeAffectedList = (ids: number[]): ChangeAffectedCiDto[] => {
  return ids.map((id) => ({
    ciId: id,
    type: ImpactedTypeTable.CI,
  }));
};
const createChangeManualImpactedCiList = (ids: number[]): ChangeAffectedCiDto[] => {
  return ids.map((id) => ({
    ciId: id,
    type: ImpactedTypeTable.CI_IMPACTED_FLAG,
  }));
};

const createChangeImpactedList = (items: ChangeAffectedCiDto[]): ChangeAffectedCiDto[] => {
  return items.map((item) => ({
    ciId: item.ciId,
    type: ImpactedTypeTable.SERVICE,
    attachedType: ServiceAttachedTypeEnum.MANUAL === item.attachedType ? ServiceAttachedTypeEnum.MANUAL : ServiceAttachedTypeEnum.CALCULATED,
  }));
};
/**
 *
 * @param changeId : Id o change
 * @param listCiImpactNew : list id of affected ci for save new
 * @param listServiceImpactNew : list of impacted service for save new
 * @param listCiImpactRemove : list id of affected ci for delete relation
 * @param listServiceImpactRemove : list id impacted service for delete relation
 * @returns request payload for update/save ChangeAssessment
 */
export const createIncidentAssessmentRequest = (
  changeId: number | undefined,
  listCiImpactNew: number[],
  listServiceImpactNew: ChangeAffectedCiDto[],
  listCiImpactRemove: number[],
  listServiceImpactRemove: ChangeAffectedCiDto[],
): ChangeAssessmentRequestDto => {
  const listIdsCiNew = createChangeAffectedList(listCiImpactNew);
  const listIdsCiRemove = createChangeAffectedList(listCiImpactRemove);

  const listIdsServiceNew = createChangeImpactedList(listServiceImpactNew);
  const listIdsServiceRemove = createChangeImpactedList(listServiceImpactRemove);

  return {
    changeId: changeId || 0,
    changeAffectedCisNew: [...new Set<ChangeAffectedCiDto>([...listIdsCiNew, ...listIdsServiceNew])],
    changeAffectedCisRemove: [...new Set<ChangeAffectedCiDto>([...listIdsCiRemove, ...listIdsServiceRemove])],
  };
};

export const createChangeAssessmentRequest = (
  changeId: number | undefined,
  listCiImpactNew: number[],
  listServiceImpactNew: ChangeAffectedCiDto[],
  listManualImpactedCiNew: number[],
  listCiImpactRemove: number[],
  listServiceImpactRemove: ChangeAffectedCiDto[],
  ciImpactedHistoryEntities: CiImpactedHistoryDto[],
): ChangeAssessmentRequestDto => {
  const listIdsCiNew = createChangeAffectedList(listCiImpactNew);
  const listIdsCiRemove = createChangeAffectedList(listCiImpactRemove);

  const listIdsServiceNew = createChangeImpactedList(listServiceImpactNew);
  const listIdsServiceRemove = createChangeImpactedList(listServiceImpactRemove);

  const listIdsManualImpactedCiNew = createChangeManualImpactedCiList(listManualImpactedCiNew);

  return {
    changeId: changeId || 0,
    changeAffectedCisNew: [...new Set<ChangeAffectedCiDto>([...listIdsCiNew, ...listIdsServiceNew, ...listIdsManualImpactedCiNew])],
    changeAffectedCisRemove: [...new Set<ChangeAffectedCiDto>([...listIdsCiRemove, ...listIdsServiceRemove])],
    //3849: save reverse -> BE save oldest change record first, latest change record at the end.
    ciImpactedHistoryEntities: ciImpactedHistoryEntities.reverse(),
  };
};

//list ci in change plan , ci impacted - attacted type
export const keyImpactedCiAndMergedCisInChangeAndAttType = (item: CiImpactedByRelationshipInformationResponseDto) => {
  return `${item.ciChangePlans
    ?.map((it) => it.id)
    .toSorted()
    .join('###')}###${item.ciImpactedId}###${item.attachedType}`;
};

export const keyPairImpactedCiAndAttType = (item: CiImpactedByRelationshipInformationResponseDto) => {
  return `${item.ciImpactedId}###${item.attachedType}`;
};
//3849: use when
export const keyPairImpactedCiAndCiInChange = (ciInChangeId?: number, impactedCiId?: number) => {
  return `${ciInChangeId}###${impactedCiId}`;
};

export const checkChangeStagesWhenDelete = (mapDeletedAssessmentWithChange: MapAssessmentWithChangeResponse) => {
  const listChangeIdForWarning = [];
  for (const assessmentId in mapDeletedAssessmentWithChange) {
    const changeRes = mapDeletedAssessmentWithChange[assessmentId];
    if (!changeRes || !(CHANGE_STAGES.APPROVAL === changeRes.changeStage || CHANGE_STAGES.PLANNING === changeRes.changeStage)) {
      continue;
    }
    listChangeIdForWarning.push(changeRes.changeId);
  }
  const warningIdsStr = listChangeIdForWarning.join(', ');
  NotificationSuccess({
    message: `Deleted successfully ${warningIdsStr ? `You need to remove ITCMDB URL at Change ID(s):${warningIdsStr} on HOTROCNTT!` : ''}`,
  });
};
