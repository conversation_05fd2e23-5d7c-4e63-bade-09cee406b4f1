import { Flex } from '@mantine/core';
import React, { DragEvent } from 'react';

export type DragAndDropCustomResult = {
  e: DragEvent<HTMLDivElement>;
  data: unknown;
};
interface CustomDragProps {
  children: React.ReactElement;
  onDrag?: (result: DragAndDropCustomResult) => void;
  onDrop?: (result: DragAndDropCustomResult) => void;
  customDragStart?: (e: DragEvent<HTMLDivElement>, data: unknown) => void;
  data?: unknown;
}

export const CustomDrag: React.FC<CustomDragProps> = ({ children, customDragStart, data, onDrag, onDrop }) => {
  const handleDragStart = (e: DragEvent<HTMLDivElement>) => {
    if (customDragStart) {
      customDragStart(e, data);
    }
    if (onDrag) {
      const result: DragAndDropCustomResult = {
        e: e,
        data: data,
      };
      onDrag(result);
    }
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const result: DragAndDropCustomResult = {
      e: e,
      data: data,
    };
    if (onDrop) {
      onDrop(result);
    }
  };

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  return (
    <Flex w={'100%'} h='100%' onDragOver={handleDragOver} onDrop={handleDrop}>
      <Flex flex={1} direction={'column'} onDragStart={handleDragStart} draggable>
        {children}
      </Flex>
    </Flex>
  );
};
