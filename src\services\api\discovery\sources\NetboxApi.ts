import { DeviceRoleEnum } from '@common/constants/DeviceRoleEnum';
import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export class <PERSON>box<PERSON>pi extends BaseApi {
  static baseUrl = BaseUrl.netboxs;

  static findVirtualMachineById(id: string) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/virtual-machines/${id}`);
  }

  static findVirtualMachines() {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/virtual-machines`);
  }

  static findVirtualDisks() {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/virtual-disks`);
  }

  static findVirtualClusters() {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/clusters`);
  }

  static findDevices(role?: <PERSON><PERSON>RoleEnum) {
    const params = role ? { role } : {};
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/devices`, params);
  }
}
