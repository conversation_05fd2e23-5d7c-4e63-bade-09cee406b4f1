import { ConfigParamModel } from '@api/systems/ConfigParamApi';
import { PayloadAction, createSelector, createSlice } from '@reduxjs/toolkit';
import type { RootStoreType } from '@store';
import { useFetchForEmpty } from './CommonSlice';
import { SystemConfigParamKey } from '@common/constants/SystemConfigParam';

export type SystemParamState = {
  isFetching: boolean;
  isFetched: boolean;
  data: ConfigParamModel[];
};
const initialState: SystemParamState = {
  isFetching: false,
  isFetched: false,
  data: [],
};
export const systemParameterSlice = createSlice({
  name: 'configParam',
  initialState,
  reducers: {
    fetchData() {},
    fetchForEmpty() {},
    setValue(_state, action: PayloadAction<SystemParamState>) {
      return action.payload;
    },
  },
});

export const getSystemParams = (store: RootStoreType) => store.systemParams;

export const getSystemParamByKey = (key: SystemConfigParamKey) =>
  createSelector(getSystemParams, (data) => {
    return data.data.find((x) => x.key === key);
  });

export const useGetSystemParams = () => {
  const systemParams = useFetchForEmpty(getSystemParams, systemParameterSlice);
  return systemParams;
};
