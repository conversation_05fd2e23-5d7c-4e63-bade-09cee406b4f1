import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { ColumnType, KanbanTable } from 'kanban-design-system';
import { Flex, rem, Box, Grid, Divider, Alert } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IconFileImport, IconFileSpreadsheet, IconDownload, IconLocationCancel } from '@tabler/icons-react';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
// import { useParams } from 'react-router-dom';
import { NotificationError, NotificationSuccess } from '@common/utils/NotificationUtils';
import { KanbanText } from 'kanban-design-system';
import { KanbanModal } from 'kanban-design-system';
import { KanbanFileInput } from 'kanban-design-system';
import { KanbanSelect } from 'kanban-design-system';
import classes from './CiRelationShipImport.module.scss';
import type { ImportFileResponse, DetailInfoRowImportDTO, FileUploadRequest } from '@models/ImportFileResponse';
import { validateUploadedFile, exportFileFromBase64, getColorTypeError, addPrefixToFileName, sortedDetailErrors } from 'common/utils/ExcelUtils';
import GuardComponent from '@components/GuardComponent';
import { FileImportMessage } from '@models/FileImportMessage';
import { KanbanConfirmModal } from 'kanban-design-system';
import { useNavigate } from 'react-router-dom';
import { IconAlertTriangle } from '@tabler/icons-react';
import { CiRelationshipApi } from '@api/CiRelationshipApi';
import { buildCiTypeUrl } from '@common/utils/RouterUtils';

interface LoadFileInfoState {
  excelFields: string[];
  show?: boolean;
  mappingField?: string;
  totalRowValue: number;
  isTemplateFile: boolean;
}

interface FileUploadState {
  file: File | null;
}

interface MappingFieldDto {
  id: number;
  name: string;
  mandatory: boolean | undefined;
  value: string | null;
}

const defaultLoadFileInfoState: LoadFileInfoState = {
  excelFields: [],
  show: false,
  totalRowValue: 0,
  isTemplateFile: false,
};

const defaultImportFileInfoState: ImportFileResponse = {
  totalRecordSuccess: 0,
  totalRecordFail: 0,
  detailErrors: [],
  urlFile: '',
};

const defaultFileUploadState: FileUploadState = {
  file: null,
};

export type ImportRelationshipRequest = Pick<FileUploadRequest, 'file' | 'mappingFields'>;

export const CiRelationShipImportPage = () => {
  const navigate = useNavigate();
  const limitDefault = 5000;
  const valueResetMapping = '-------';
  const [fileState, setFileState] = useState<FileUploadState>(defaultFileUploadState);
  const [loadFileInfoState, setLoadFileInfoState] = useState<LoadFileInfoState>(defaultLoadFileInfoState);
  const [importFileInfoState, setImportFileInfoState] = useState<ImportFileResponse>(defaultImportFileInfoState);

  const [mappingFields, setMappingFields] = useState<MappingFieldDto[]>([]);

  const messageWarning = (totalRow: number) => {
    return `Your file has ${totalRow}, the system can only process the first 5000 lines, the rest will be ignored, do you agree?`;
  };

  const [openedModalViewLog, { close: closeModalViewLog, open: openModalViewLog }] = useDisclosure(false);
  const [isOpenConfirmImport, { close: closeConfirmImport, open: openConfirmImport }] = useDisclosure(false);
  const iconImportFile = <IconFileSpreadsheet style={{ width: rem(18), height: rem(18) }} stroke={1.5} />;
  const [tableImportErrorData, setTableImportErrorData] = useState<DetailInfoRowImportDTO[]>([]);
  const [initMappingFields, setInitMappingFields] = useState<Map<number, number | null>>(new Map());

  const openPopupConfirmImport = () => {
    const mappingObject = validateWhenImportFile();
    if (!mappingObject) {
      return;
    }
    if (loadFileInfoState.totalRowValue > limitDefault) {
      openConfirmImport();
    } else {
      importFile();
    }
  };

  useEffect(() => {
    const responseData = { ...importFileInfoState };
    if (Object.keys(responseData).length > 0) {
      const errorDetailsArray: DetailInfoRowImportDTO[] = responseData.detailErrors;
      setTableImportErrorData(errorDetailsArray);
    }
  }, [importFileInfoState]);

  const attributes = useMemo(() => {
    const attributeCIDefault: MappingFieldDto[] = [
      { id: 999999999, name: 'Ci Type 1', mandatory: true, value: null }, //9
      { id: 9999999999, name: 'Ci Name 1', mandatory: true, value: null },
      { id: 99999999999, name: 'Relationship Type', mandatory: true, value: null },
      { id: 999999999999, name: 'Ci Type 2', mandatory: true, value: null },
      { id: 9999999999999, name: 'Ci Name 2', mandatory: true, value: null },
    ];

    return attributeCIDefault;
  }, []);

  const updateAttributeValue = (idToUpdate: number, newValue: string | null) => {
    setMappingFields((prevAttributes) =>
      prevAttributes.map((attribute) => (attribute.id === idToUpdate ? { ...attribute, value: newValue } : attribute)),
    );
  };

  const resetAllAttributeValues = () => {
    setMappingFields((prevAttributes) => prevAttributes.map((attribute) => ({ ...attribute, value: null })));
  };

  useEffect(() => {
    setMappingFields([...attributes]);
  }, [attributes]);

  const initializeMappingFields = useCallback((attributes: MappingFieldDto[]) => {
    const mappingField = new Map();
    attributes.forEach((attribute) => {
      mappingField.set(attribute.id, null);
    });
    setInitMappingFields(mappingField);
  }, []);

  useEffect(() => {
    initializeMappingFields(attributes);
  }, [attributes, initializeMappingFields]);

  const validateWhenImportFile = () => {
    const missingMappingValues = attributes
      .filter((attribute) => {
        const value = initMappingFields.get(attribute.id);
        return attribute.mandatory && (value === null || value === undefined || value < 0);
      })
      .map((attribute) => attribute.name);
    if (missingMappingValues.length > 0) {
      NotificationError({
        message: `${missingMappingValues.join(', ')}: ${FileImportMessage.MISSING_REQUIRED_FIELD}`,
      });
      return null;
    }
    const mappingObject: { [key: string]: number | null } = {};
    initMappingFields.forEach((value, key) => {
      mappingObject[key.toString()] = value;
    });
    return mappingObject;
  };

  const executeWhenImportFileFail = () => {
    closeConfirmImport();
    resetAllAttributeValues();
    setFileState((prevState) => ({
      ...prevState,
      file: null,
    }));
    initializeMappingFields(attributes);
  };
  const importFile = () => {
    const mappingObject = validateWhenImportFile();
    if (!mappingObject) {
      return;
    }
    if (fileState.file) {
      const request: ImportRelationshipRequest = {
        file: fileState.file,
        mappingFields: JSON.stringify(mappingObject),
      };
      CiRelationshipApi.importFileCI(request)
        .then((res) => {
          setImportFileInfoState(res.data);
          NotificationSuccess({
            message: FileImportMessage.IMPORT_FILE_SUCCESS,
          });
          // openModalViewLog();
          closeConfirmImport();
          openModalViewLog();
          initializeMappingFields(attributes);
        })
        .catch(() => {
          executeWhenImportFileFail();
          NotificationError({
            message: FileImportMessage.IMPORT_FILE_FAIL,
          });
        });
    }
  };

  const handleFileChange = (payload: File | null) => {
    if (!payload) {
      setFileState((prevState) => ({
        ...prevState,
        file: null,
      }));
      setLoadFileInfoState((prevState) => ({
        ...prevState,
        totalRowValue: 0,
        show: false,
      }));
      resetAllAttributeValues();
      initializeMappingFields(attributes);
      return;
    }

    if (!validateUploadedFile(payload)) {
      NotificationError({
        message: FileImportMessage.INVALID_EXCEL_FORMAT,
      });
    } else {
      CiRelationshipApi.loadFileImportCiRelationship(payload)
        .then((res) => {
          setFileState((prevState) => ({
            ...prevState,
            file: payload,
          }));
          setLoadFileInfoState((prevState) => ({
            ...prevState,
            excelFields: [valueResetMapping, ...res.data.excelFields],
            show: true,
            totalRowValue: res.data.totalRowValue,
            isTemplateFile: res.data.templateFile,
          }));

          if (res.data.templateFile) {
            const newInitMappingFields = new Map();
            const attributeDTOs: MappingFieldDto[] = [];
            const attributes = [...mappingFields];
            attributes.forEach((attribute) => {
              const modifiedValue = attribute.mandatory ? `${attribute.name} *` : attribute.name;
              const valueAttribute = modifiedValue || '';
              const namesHeader = res.data.excelFields;
              if (namesHeader.includes(valueAttribute)) {
                const indexInArray = namesHeader.indexOf(valueAttribute);
                attribute.value = modifiedValue;
                newInitMappingFields.set(attribute.id, indexInArray);
              }
              attributeDTOs.push(attribute);
            });
            setMappingFields(attributeDTOs);
            setInitMappingFields(newInitMappingFields);
          } else {
            resetAllAttributeValues();
            initializeMappingFields(attributes);
          }
        })
        .catch(() => {
          setFileState((prevState) => ({
            ...prevState,
            file: payload,
          }));
          setLoadFileInfoState(defaultLoadFileInfoState);
          resetAllAttributeValues();
          initializeMappingFields(attributes);
          // NotificationError({
          //   message: FileImportMessage.LOAD_FILE_FAIL,
          // });
        });
    }
  };

  const checkExistFieldMappingWithValue = (key: number, value: number | null) => {
    let found = false;
    initMappingFields.forEach((currentValue, currentKey) => {
      if (currentKey !== key && currentValue === value) {
        found = true;
      }
    });
    return found;
  };

  const onChangeSelectField = (val: string | null, index: number) => {
    const excelFields = loadFileInfoState.excelFields;

    const attribute = attributes[index];
    const indexMapping = excelFields.indexOf(val || '');
    if (val && val === valueResetMapping) {
      initMappingFields.set(attribute.id, null);
      updateAttributeValue(attribute.id, null);
    } else {
      if (checkExistFieldMappingWithValue(attribute.id, indexMapping - 1)) {
        NotificationError({
          message: FileImportMessage.KEY_EXIST_MAPPING,
        });
      } else {
        initMappingFields.set(attribute.id, indexMapping - 1);
        updateAttributeValue(attribute.id, val || null);
      }
    }
  };

  const excuteWhenCloseImportFileModal = () => {
    setFileState(defaultFileUploadState);
    setLoadFileInfoState(defaultLoadFileInfoState);
    setImportFileInfoState(defaultImportFileInfoState);
    resetAllAttributeValues();
  };

  const excuteWhenCloseLogImportFileModal = () => {
    excuteWhenCloseImportFileModal();
    closeModalViewLog();
    closeConfirmImport();
  };

  const downloadFileLog = () => {
    const nameFile = fileState.file ? fileState.file.name : 'file';
    const nameFile_ = addPrefixToFileName(nameFile, '_log');
    exportFileFromBase64(importFileInfoState.urlFile, nameFile_);
  };

  const downloadTemplateFile = () => {
    CiRelationshipApi.downloadTemplateImportFile()
      .then((res) => {
        exportFileFromBase64(res.data, 'ci_relationships_template.xlsx');
      })
      .catch((ex) => {
        NotificationError({
          message: ex,
        });
      });
  };

  const onCancel = () => {
    const path = buildCiTypeUrl(0);
    navigate(path);
  };

  // 27/2/2024

  const baseColumns: ColumnType<DetailInfoRowImportDTO>[] = useMemo(() => {
    return [
      {
        name: 'type',
        title: 'Type',
        customRender: (data) => {
          return (
            <KanbanText lineClamp={2} c={getColorTypeError(data)}>
              {data}
            </KanbanText>
          );
        },
      },
      {
        name: 'line',
        title: 'Line',
      },
      {
        name: 'detail',
        title: 'Description',
      },
    ];
  }, []);

  return (
    <>
      <KanbanConfirmModal
        opened={isOpenConfirmImport}
        onClose={closeConfirmImport}
        title='Confirm Import'
        onConfirm={() => {
          importFile();
        }}>
        {messageWarning(loadFileInfoState.totalRowValue)}
      </KanbanConfirmModal>

      <KanbanModal
        size={'70%'}
        opened={openedModalViewLog}
        onClose={excuteWhenCloseLogImportFileModal}
        title={'Log Detail When Import File'}
        closeOnClickOutside={false}
        actions={
          <KanbanButton leftSection={<IconDownload size={14} />} onClick={downloadFileLog}>
            Download
          </KanbanButton>
        }>
        <KanbanText mt='sm' size='sm' fw={500}>
          Total number of imported rows: {importFileInfoState.totalRecordSuccess + importFileInfoState.totalRecordFail}
        </KanbanText>
        <KanbanText mt='sm' size='sm' fw={500}>
          Total record import success: {importFileInfoState.totalRecordSuccess}
        </KanbanText>
        <KanbanText mt='sm' size='sm' fw={500}>
          Total record import fail: {importFileInfoState.totalRecordFail}
        </KanbanText>
        <Divider my='md' />
        <Box mt='sm'>
          <KanbanTable
            columns={baseColumns}
            pagination={{
              enable: true,
            }}
            data={sortedDetailErrors(tableImportErrorData)}
          />
        </Box>
      </KanbanModal>

      <HeaderTitleComponent
        title={'Import Ci relationship'}
        rightSection={
          //TODO Permission
          <GuardComponent requirePermissions={[]} hiddenOnUnSatisfy>
            <Flex gap='xs'>
              <KanbanButton leftSection={<IconDownload />} onClick={downloadTemplateFile}>
                Download Template
              </KanbanButton>
            </Flex>
          </GuardComponent>
        }
      />
      <Box>
        <KanbanFileInput
          leftSection={iconImportFile}
          accept='.xlsx'
          label='Choose file'
          placeholder='No file selected'
          // rightSectionPointerEvents="none"
          required
          mt='lg'
          w='20%'
          value={fileState.file}
          onChange={handleFileChange}
          clearable
        />

        {loadFileInfoState.totalRowValue > limitDefault && (
          <Alert mt={'md'} variant='light' color='red' title={messageWarning(loadFileInfoState.totalRowValue)} icon={<IconAlertTriangle />}></Alert>
        )}
      </Box>

      {loadFileInfoState.show && (
        <Box mt='sm'>
          <Grid justify='space-between'>
            <Grid.Col span={6}>
              <KanbanText fw={500} size='sm'>
                Customize Mapping
              </KanbanText>
            </Grid.Col>
            <Grid.Col span={6}>
              <Flex justify='flex-end' align='center'>
                <KanbanText fw={500} size='sm'>
                  <span className={classes.required}>*</span> Mandatory Field
                </KanbanText>
              </Flex>
            </Grid.Col>
          </Grid>

          <Divider mt='sm' />

          <Box mt='md'>
            <Box>
              <KanbanText size='sm'>
                {' '}
                The column headers in the excel file are displayed in the drop down boxes. Kindly choose the appropriate column header for the fields
                below.
              </KanbanText>
            </Box>
          </Box>

          <Box mt='sm' w='50%'>
            {mappingFields.map((attribute, index) => (
              <Grid justify='space-between' align='stretch' mb='sm' key={index}>
                <Grid.Col span={6}>
                  <KanbanText size='sm'>
                    {attribute.name} {attribute.mandatory && <span className={classes.required}>*</span>}
                  </KanbanText>
                </Grid.Col>
                <Grid.Col span={6}>
                  <KanbanSelect
                    label='Select a field from Excel data'
                    placeholder='Pick value'
                    value={attribute.value}
                    // data={importFileState.loadFileInfo.excelFields}
                    data={loadFileInfoState.excelFields}
                    onChange={(e) => {
                      onChangeSelectField(e, index);
                    }}
                  />
                </Grid.Col>
              </Grid>
            ))}

            <Box>
              <Flex gap='xs' justify='center' align='center' direction='row' wrap='wrap'>
                <KanbanButton leftSection={<IconFileImport />} onClick={openPopupConfirmImport} disabled={!fileState.file}>
                  Import
                </KanbanButton>
                <KanbanButton leftSection={<IconLocationCancel />} onClick={onCancel} variant='default'>
                  Cancel
                </KanbanButton>
              </Flex>
            </Box>
          </Box>
        </Box>
      )}
    </>
  );
};
export default CiRelationShipImportPage;
