import React, { ReactNode, useMemo, useState } from 'react';
import 'rc-tree/assets/index.css';
// import { convertJsonToDataNodeWithKeys, jsonData } from '../helper/JsonTransformHelper';
import { Divider, Flex } from '@mantine/core';
import { KanbanInput, KanbanTabs, KanbanText, KanbanTitle, useDebounceCallback } from 'kanban-design-system';
import { JsonViewEnum } from '@common/constants/JsonViewEnum';
import { filterJsonByKeyValue } from '../helper/JsonTransformHelper';
import JsonTableView from './JsonTableView';
import styles from '../helper/RcTree.module.scss';
import { IconSearch } from '@tabler/icons-react';
import styled from 'styled-components';
import { useDataTransform } from '@context/DataTransformContext';
import JsonOutputView from './JsonOutputView';
// import { getDataTransform } from '@slices/DataTransformSlice';

const Container = styled.div`
  justify-content: center;
  align-items: center;
  display: flex;
`;
const Wrapper = styled.div`
  text-align: center;
`;

const JsonTransformOutputPage: React.FC = () => {
  const [searchValue, setSearchValue] = useState<string>('');
  const { state: dataTransform } = useDataTransform();
  const isExecuting = dataTransform?.isExecuting;
  // const jsonData = dataTransform?.jsonData;
  const columnDatas = dataTransform?.columnDatasForOutput;
  const jsonTransform = dataTransform?.jsonTransform;

  const datasForJsonViewFilter = useMemo(() => {
    return filterJsonByKeyValue(jsonTransform, searchValue);
  }, [jsonTransform, searchValue]);

  const datasForTableViewFilter = useMemo(() => {
    return filterJsonByKeyValue(jsonTransform, searchValue);
  }, [jsonTransform, searchValue]);

  const debounced = useDebounceCallback((e: string) => {
    setSearchValue(e);
  }, 400);

  const renderContent = (): ReactNode => {
    if (isExecuting) {
      return (
        <Container className={styles['rc-tree-scroll-container']}>
          <Wrapper>
            <KanbanText>Wating for executing...</KanbanText>
          </Wrapper>
        </Container>
      );
    }

    if (columnDatas.length > 0) {
      return (
        <KanbanTabs
          configs={{
            defaultValue: JsonViewEnum.TABLE,
            variant: 'outline',
            keepMounted: true,
            flex: 1,
            className: styles['custom-tabs'],
          }}
          moreConfigs={{
            tabPanels: {
              className: styles['mantine-Tabs-panel'],
            },
          }}
          tabs={{
            TABLE: {
              title: 'TABLE',
              content: <JsonTableView tableMaxHeight={'calc(100% - 50px)'} jsonTransform={datasForTableViewFilter} />,
            },
            JSON: {
              title: 'JSON',
              content: (
                <div style={{ maxHeight: 'calc(100% - 50px)', overflow: 'auto' }}>
                  <JsonOutputView jsonTransform={datasForJsonViewFilter} />
                </div>
              ),
            },
          }}
        />
      );
    }

    return (
      <Container className={styles['rc-tree-scroll-container']}>
        <Wrapper>
          <KanbanText>
            Execute this node to view data
            <br />
            or set mock data
          </KanbanText>
        </Wrapper>
      </Container>
    );
  };

  return (
    <Flex p={'xs'} h={'100%'} direction={'column'}>
      <KanbanTitle order={4}>OUTPUT</KanbanTitle>
      <Flex gap={10} pt={'50px'} justify={'end'}>
        <KanbanInput placeholder='Search here' leftSection={<IconSearch size='1rem' />} onChange={(e) => debounced(e.target.value)}></KanbanInput>
      </Flex>
      <Divider my='sm' />
      <>{renderContent()}</>
    </Flex>
  );
};

export default JsonTransformOutputPage;
