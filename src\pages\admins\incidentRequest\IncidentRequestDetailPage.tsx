import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { ViewAdvancedSearchDataChange } from './components/ViewAdvancedSearchData';
import { KanbanAccordion, KanbanAccordionData, KanbanTabs } from 'kanban-design-system';
import { AffectedCiComponent } from './components/ChangeAffectedCiComponent';
import { ImpactedServiceComponent } from './components/ImpactedServiceComponent';
import { Box, Flex } from '@mantine/core';
import type { ConfigItemResponse } from '@api/ConfigItemApi';
import { ChangeAssessmentAction, ChangeAssessmentRequestDto, ImpactedCiParameters, ImpactedTypeTable } from '@models/ChangeAssessment';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { buildIncidentDetailUrl, incidentRequestPath, navigateTo } from '@common/utils/RouterUtils';
import { ChangeAssessmentApi } from '@api/ChangeAssessmentApi';
import { NotificationError, NotificationSuccess } from '@common/utils/NotificationUtils';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { KanbanTitle } from 'kanban-design-system';
import { IconEdit, IconTrash } from '@tabler/icons-react';
import { IncidentComponent } from './components/IncidentComponent';
import { KanbanConfirmModal } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import ImpactDataGraphViewComponent from './components/ImpactDataGraphViewComponent';
import { IncidentSdpApi } from '@api/IncidentSdpApi';
import { createIncidentAssessmentRequest } from '@common/utils/ChangeAssessmentUtils';
import styled from 'styled-components';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { BreadcrumbComponent, UrlBaseCrumbData } from '../breadcrumb/BreadcrumbComponent';
import { formatStandardName } from '@common/utils/StringUtils';

const ScrollableDiv = styled.div`
  overflow-y: auto;
  flex: 1;
`;
const WrapperContentDiv = styled.div`
  display: flex;
  flex-direction: column;
  max-height: var(--kanban-appshell-maxheight-content);
`;

const titleView = (idNumber: number, action: string | null) => {
  if (idNumber > 0) {
    switch (action) {
      case ChangeAssessmentAction.UPDATE:
        return 'Update incident request detail';
      case ChangeAssessmentAction.COPY:
        return 'Copy incident request detail';
      case ChangeAssessmentAction.VIEW:
        return 'View incident request detail';
      default:
        return 'Incident request detail';
    }
  } else {
    return 'Add new incident request';
  }
};

export const IncidentRequestDetailPage = () => {
  const navigate = useNavigate();
  const { incidentId } = useParams();
  const [searchParams] = useSearchParams();
  const action = searchParams.get('action');
  const incidentIdNumber = Number(incidentId);

  const [listCiImpact, setListCiImpact] = useState<ConfigItemResponse[]>([]);
  const [listServiceImpact, setListServiceImpact] = useState<ConfigItemResponse[]>([]);

  const [listCiImpactNew, setListCiImpactNew] = useState<number[]>([]);
  const [listServiceImpactNew, setListServiceImpactNew] = useState<number[]>([]);
  const [listCiImpactRemove, setListCiImpactRemove] = useState<number[]>([]);
  const [listServiceImpactRemove, setListServiceImpactRemove] = useState<number[]>([]);

  const [onProcessAddDataImpact, setOnProcessAddDataImpact] = useState(false);
  const [activeTab, setActiveTab] = useState<ImpactedTypeTable>(ImpactedTypeTable.CI);
  const [changeId, setChangeId] = useState<number | undefined>();
  const [allowEdit, setAllowEdit] = useState(true);
  const [isUpdateData, setUpdateData] = useState(true);
  const [allowViewImpactData, setAllowViewImpactData] = useState(true);
  const [idNumber, setIdNumber] = useState<number>(-1);
  const [openedModalDelete, { close: closeModalDelete, open: openModalDelete }] = useDisclosure(false);

  useEffect(() => {
    setUpdateData(ChangeAssessmentAction.UPDATE === action);
    setAllowEdit(ChangeAssessmentAction.VIEW !== action);
  }, [action]);

  const fetchChangeDetail = useCallback(() => {
    if (incidentIdNumber > 0) {
      IncidentSdpApi.getByRequestId(incidentIdNumber)
        .then((res) => {
          const resData = res.data;
          if (resData && resData.id) {
            setChangeId(resData.changeId);
            setIdNumber(resData.id);
            setListCiImpact(resData.affectedCis);
            setListServiceImpact(resData.impactedServices);
          } else {
            setChangeId(incidentIdNumber);
            setIdNumber(0);
            setListCiImpact([]);
            setListServiceImpact([]);
          }
        })
        .catch(() => {});
    }
  }, [incidentIdNumber]);

  useEffect(() => {
    fetchChangeDetail();
  }, [fetchChangeDetail]);

  useEffect(() => {
    if (ChangeAssessmentAction.SDP_UPDATE === action && incidentIdNumber && changeId) {
      if (idNumber) {
        navigate(buildIncidentDetailUrl(incidentIdNumber, ChangeAssessmentAction.UPDATE));
      } else {
        navigate(buildIncidentDetailUrl(incidentIdNumber, ChangeAssessmentAction.CREATE));
      }
    }
  }, [action, changeId, idNumber, incidentIdNumber, navigate]);

  useEffect(() => {
    if (activeTab) {
      setOnProcessAddDataImpact(false);
    }
  }, [activeTab]);

  const addDataImpact = useCallback(
    (cis: ConfigItemResponse[]) => {
      if (ImpactedTypeTable.CI === activeTab) {
        const listOldData = [...listCiImpact];
        const listOldIds = listOldData.map((item) => item.id);

        const newItems = cis.filter((item) => !listOldIds.includes(item.id));

        // add list new CI
        const newIds = new Set(newItems.map((obj) => obj.id));
        setListCiImpactNew((prev) => [...prev, ...newIds]);
        // Update list CI Impact
        setListCiImpact([...listOldData, ...newItems]);
      } else {
        const listOldData = [...listServiceImpact];
        const listOldIds = listOldData.map((item) => item.id);

        const newItems = cis.filter((item) => !listOldIds.includes(item.id));

        // add list new Service
        const newIds = new Set(newItems.map((obj) => obj.id));
        setListServiceImpactNew((prev) => [...prev, ...newIds]);
        // Update list Service Impact
        setListServiceImpact([...listOldData, ...newItems]);
      }
    },
    [activeTab, listCiImpact, listServiceImpact],
  );

  const removeDataImpact = useCallback(
    (listIds: number[]) => {
      if (ImpactedTypeTable.CI === activeTab) {
        const listOldData = [...listCiImpact];
        const updatedList = listOldData.filter((item) => !listIds.includes(item.id));

        // add list remove CI
        setListCiImpactRemove((prev) => [...prev, ...listIds]);
        // Update list CI Impact
        setListCiImpact(updatedList);
      } else {
        const listOldData = [...listServiceImpact];
        const updatedList = listOldData.filter((item) => !listIds.includes(item.id));

        // add list remove Service
        setListServiceImpactRemove((prev) => [...prev, ...listIds]);
        // Update list Service Impact
        setListServiceImpact(updatedList);
      }
    },
    [activeTab, listCiImpact, listServiceImpact],
  );

  const updateIncidentRequest = () => {
    if (idNumber > 0) {
      const listServiceImpactNewRes = listServiceImpactNew.map((id) => ({ ciId: id, type: ImpactedTypeTable.SERVICE }));
      const listServiceImpactRemoveRes = listServiceImpactRemove.map((id) => ({ ciId: id, type: ImpactedTypeTable.SERVICE }));
      const dataRequest: ChangeAssessmentRequestDto = createIncidentAssessmentRequest(
        changeId,
        listCiImpactNew,
        listServiceImpactNewRes,
        listCiImpactRemove,
        listServiceImpactRemoveRes,
      );

      IncidentSdpApi.updateIncident(dataRequest, idNumber)
        .then((res) => {
          if (res.data) {
            NotificationSuccess({
              message: 'Update successfully.',
            });
            navigateTo(incidentRequestPath);
          } else {
            NotificationError({
              message: 'Error when update incident request.',
            });
          }
        })
        .catch(() => {
          NotificationError({
            message: 'Error when update incident request.',
          });
        });
    }
  };

  const addNewIncidentRequest = () => {
    if (!changeId) {
      NotificationError({
        message: 'Incident ID cannot be empty',
      });
      return;
    }
    const listServiceImpactNewRes = listServiceImpactNew.map((id) => ({ ciId: id, type: ImpactedTypeTable.SERVICE }));
    const listServiceImpactRemoveRes = listServiceImpactRemove.map((id) => ({ ciId: id, type: ImpactedTypeTable.SERVICE }));

    const dataRequest: ChangeAssessmentRequestDto = createIncidentAssessmentRequest(
      changeId,
      listCiImpactNew,
      listServiceImpactNewRes,
      listCiImpactRemove,
      listServiceImpactRemoveRes,
    );
    IncidentSdpApi.createNewIncident(dataRequest)
      .then((res) => {
        if (res.data) {
          NotificationSuccess({
            message: 'Created successfully.',
          });
          navigateTo(incidentRequestPath);
        } else {
          NotificationError({
            message: 'Error when create new incident request.',
          });
        }
      })
      .catch(() => {});
  };

  const calculateImpactedServices = useCallback(() => {
    const affectedCiIds: number[] = listCiImpact.map((entity) => entity.id);
    const impactedCiParameters: ImpactedCiParameters = {
      affectedCiIds: affectedCiIds,
    };
    if (affectedCiIds.length > 0) {
      ChangeAssessmentApi.calculateImpactedServices(impactedCiParameters)
        .then((res) => {
          if (res.data.length > 0) {
            addDataImpact(res.data);
            NotificationSuccess({
              message: 'Calculate successfully',
            });
          } else {
            NotificationSuccess({
              title: 'Calculate successfully',
              message: 'Not found impacted service',
            });
          }
        })
        .catch(() => {});
    } else {
      NotificationError({
        message: 'Please choose affected cis.',
      });
    }
  }, [listCiImpact, addDataImpact]);

  const accordionItems: KanbanAccordionData[] = useMemo(
    () => [
      {
        key: 'linkSdp',
        content: (
          <IncidentComponent
            changeId={changeId}
            setChangeId={setChangeId}
            action={action as ChangeAssessmentAction}
            setAllowViewImpactData={setAllowViewImpactData}
          />
        ),
        title: (
          <KanbanTitle order={4} c={'var(--mantine-color-blue-8)'}>
            Link to a incident in SDP
          </KanbanTitle>
        ),
      },
      {
        key: 'impactData',
        content: (
          <Box mt='sm'>
            <KanbanTabs
              configs={{
                defaultValue: activeTab,
                onChange: (val) => {
                  setActiveTab(ImpactedTypeTable.SERVICE === val ? ImpactedTypeTable.SERVICE : ImpactedTypeTable.CI);
                },
              }}
              tabs={{
                CI: {
                  content: (
                    <AffectedCiComponent
                      cis={listCiImpact}
                      onProcessAdd={onProcessAddDataImpact}
                      onAddCi={setOnProcessAddDataImpact}
                      onDeleteCi={removeDataImpact}
                      allowEdit={allowEdit}
                    />
                  ),
                  title: 'AFFECTED CIS',
                },
                SERVICE: {
                  content: (
                    <ImpactedServiceComponent
                      cis={listServiceImpact}
                      onProcessAdd={onProcessAddDataImpact}
                      onAddCi={setOnProcessAddDataImpact}
                      onDeleteCi={removeDataImpact}
                      calculateImpactedServices={calculateImpactedServices}
                      allowEdit={allowEdit}
                    />
                  ),
                  title: 'IMPACTED SERVICE/CIS',
                },
                GRAPH: {
                  content: <ImpactDataGraphViewComponent listCiImpact={listCiImpact} listServiceImpact={listServiceImpact} />,
                  title: 'PREVIEW GRAPH CIS/SERVICES',
                },
              }}
            />
          </Box>
        ),
        title: (
          <KanbanTitle order={4} c={'var(--mantine-color-blue-8)'}>
            List affected CIs & impacted services
          </KanbanTitle>
        ),
      },
      {
        key: 'addImpact',
        content: (
          <ViewAdvancedSearchDataChange
            title={'LIST CIS'}
            cis={activeTab === ImpactedTypeTable.CI ? listCiImpact : listServiceImpact}
            activeTab={activeTab}
            onAddChange={addDataImpact}
            onClose={setOnProcessAddDataImpact}
          />
        ),
        title: (
          <KanbanTitle order={4} c={'var(--mantine-color-blue-8)'}>
            AFFECTED SERVICE/CIS
          </KanbanTitle>
        ),
      },
    ],
    [
      action,
      activeTab,
      addDataImpact,
      allowEdit,
      calculateImpactedServices,
      changeId,
      listCiImpact,
      listServiceImpact,
      onProcessAddDataImpact,
      removeDataImpact,
    ],
  );

  const customAccordionItems = useMemo(() => {
    const accordionItemsUpdate = [...accordionItems];
    let updateItems = accordionItemsUpdate;
    if (!allowViewImpactData) {
      updateItems = updateItems.filter((x) => x.key !== 'impactData');
    }

    if (!onProcessAddDataImpact) {
      updateItems = updateItems.filter((x) => x.key !== 'addImpact');
    }

    return updateItems;
  }, [accordionItems, allowViewImpactData, onProcessAddDataImpact]);

  const onDeleteIncident = () => {
    if (idNumber) {
      IncidentSdpApi.deleteByIds([idNumber])
        .then(() => {
          NotificationSuccess({
            message: 'Deleted successfully',
          });
          navigateTo(incidentRequestPath);
        })
        .catch(() => {})
        .finally(() => {});
    }
  };

  const locationCustomPaths = useMemo((): UrlBaseCrumbData => {
    const originPath = buildIncidentDetailUrl(Number(incidentId), ChangeAssessmentAction.VIEW);

    return {
      [`/${incidentId}`]: {
        title: action
          ? ChangeAssessmentAction.CREATE === action
            ? `${formatStandardName(action)}`
            : `${formatStandardName(action)} ${incidentId}`
          : '',
        href: originPath,
      },
    };
  }, [action, incidentId]);
  return (
    <>
      {/* 4763  incident detail*/}
      <BreadcrumbComponent locationCustomPaths={locationCustomPaths} />

      <KanbanConfirmModal
        title='Delete Incident'
        onConfirm={onDeleteIncident}
        textConfirm='Delete'
        onClose={closeModalDelete}
        opened={openedModalDelete}>
        {'Are you sure to delete this request?'}
      </KanbanConfirmModal>
      <WrapperContentDiv>
        <div>
          <HeaderTitleComponent
            title={titleView(idNumber, action)}
            rightSection={
              <Flex gap={10}>
                <KanbanButton
                  variant='outline'
                  onClick={() => {
                    navigateTo(incidentRequestPath);
                  }}>
                  Cancel
                </KanbanButton>
                {!allowEdit && (
                  <>
                    <GuardComponent requirePermissions={[AclPermission.deleteIncidentRequest]} hiddenOnUnSatisfy>
                      <KanbanButton leftSection={<IconTrash />} color={'red'} onClick={openModalDelete}>
                        Delete
                      </KanbanButton>
                    </GuardComponent>
                    <GuardComponent requirePermissions={[AclPermission.updateIncidentRequest]} hiddenOnUnSatisfy>
                      <KanbanButton
                        leftSection={<IconEdit />}
                        onClick={() => {
                          navigate(buildIncidentDetailUrl(incidentIdNumber, ChangeAssessmentAction.UPDATE));
                        }}>
                        Edit
                      </KanbanButton>
                    </GuardComponent>
                  </>
                )}
                {allowEdit && (
                  <KanbanButton
                    onClick={() => {
                      if (isUpdateData) {
                        updateIncidentRequest();
                      } else {
                        addNewIncidentRequest();
                      }
                    }}>
                    Save
                  </KanbanButton>
                )}
              </Flex>
            }
          />
        </div>
        <ScrollableDiv>
          <KanbanAccordion
            variant='separated'
            chevronPosition={'left'}
            transitionDuration={200}
            defaultValue={['linkSdp', 'impactData', 'addImpact']}
            multiple
            data={customAccordionItems}
          />
        </ScrollableDiv>
      </WrapperContentDiv>
    </>
  );
};
export default IncidentRequestDetailPage;
