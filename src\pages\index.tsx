import React, { useEffect } from 'react';
import { Route, RouterProps, Routes, useNavigate } from 'react-router-dom';
import { RoutePropsType, ciRuleLinkConfigs, discoveryLinkConfigs, headerLinkConfigs, navbarConfigs, routeConfigs } from 'routes';
import HomePage from './base/HomePage';
import ErrorPage from './base/ErrorPage';
import ForbiddenPage from './base/ForbiddenPage';
import AppShell from '@components/appShell';
import { useDispatch, useSelector } from 'react-redux';
import UserNotFoundPage from './base/UserNotFoundPage';
import { currentUserSlice, getCurrentUser } from '@slices/CurrentUserSlice';
import { ciTypesSlice, getCiTypes } from '@slices/CiTypesSlice';
import { HistoryRouter } from '@common/utils/RouterUtils';
import { systemParameterSlice } from '@slices/SystemParameterSlice';

function Index() {
  const dispatch = useDispatch();
  const currentUser = useSelector(getCurrentUser);
  const ciTypes = useSelector(getCiTypes);
  useEffect(() => {
    dispatch(currentUserSlice.actions.fetchData());
    dispatch(ciTypesSlice.actions.fetchForEmpty());
    dispatch(systemParameterSlice.actions.fetchForEmpty());
  }, [dispatch]);
  HistoryRouter.navigate = useNavigate();

  const generateRoute = (routeConfig: RoutePropsType, index: number) => {
    const { children, ...rest } = routeConfig;
    const routerProps = rest as RouterProps;
    return (
      <Route key={index} {...routerProps}>
        {children &&
          children.map((x, key) => {
            return generateRoute(x, key);
          })}
      </Route>
    );
  };

  if ((!currentUser.isFetching && !currentUser.data) || !currentUser.data?.isActive) {
    return <UserNotFoundPage />;
  }
  return (
    <>
      <AppShell
        routers={navbarConfigs}
        ciTypes={ciTypes.data}
        headerLinks={headerLinkConfigs}
        discoveryLinks={discoveryLinkConfigs}
        ciRuleLinks={ciRuleLinkConfigs}>
        <Routes>
          <Route path='/' element={<HomePage />} />
          <Route path='/403' element={<ForbiddenPage />} />
          <Route path='*' element={<ErrorPage />} />
          {routeConfigs.map((x, key) => generateRoute(x, key))}
        </Routes>
      </AppShell>
    </>
  );
}
export default Index;
