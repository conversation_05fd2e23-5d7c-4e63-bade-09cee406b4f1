import type { ComboboxItem } from '@mantine/core';

export enum OperatorEnum {
  IS = '=',
  IS_NOT = '!=',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'doesNotContain',
  BEGIN_WITH = 'beginsWith',
  END_WITH = 'endsWith',
  EMPTY = 'empty',
  NOT_EMPTY = 'notEmpty',
  LESS_THAN = '<',
  GREATER_THAN = '>',
  LESS_THAN_OR_EQUAL = '<=',
  GREATER_THAN_OR_EQUAL = '>=',
  BETWEEN = 'between',
}

export const getTextOperatorEnum = (value: OperatorEnum): string => {
  const map: Record<OperatorEnum, string> = {
    [OperatorEnum.IS]: 'Is',
    [OperatorEnum.IS_NOT]: 'Is not',
    [OperatorEnum.CONTAINS]: 'Contains',
    [OperatorEnum.NOT_CONTAINS]: 'Not contains',
    [OperatorEnum.BEGIN_WITH]: 'Begin with',
    [OperatorEnum.END_WITH]: 'End with',
    [OperatorEnum.EMPTY]: 'Empty',
    [OperatorEnum.NOT_EMPTY]: 'Not Empty',
    [OperatorEnum.LESS_THAN]: 'Less than',
    [OperatorEnum.LESS_THAN_OR_EQUAL]: 'Less than or equal',
    [OperatorEnum.GREATER_THAN]: 'Greater than',
    [OperatorEnum.GREATER_THAN_OR_EQUAL]: 'Greater than or equal',
    [OperatorEnum.BETWEEN]: 'Between',
  };
  return map[value] || '';
};

export const getComboboxOperatorEnum = (values: OperatorEnum[]): ComboboxItem[] => {
  const operatorComboboxItems: ComboboxItem[] = [];
  values.forEach((value) => {
    operatorComboboxItems.push({
      value: value,
      label: getTextOperatorEnum(value),
    });
  });
  return operatorComboboxItems;
};
