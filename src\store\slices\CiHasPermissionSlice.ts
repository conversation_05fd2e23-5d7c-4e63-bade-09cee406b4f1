import { createSlice } from '@reduxjs/toolkit';
import type { RootStoreType } from '@store';

export interface CiHasPermissionState {
  ciHasPermissions: boolean;
}

const initialState: CiHasPermissionState = {
  ciHasPermissions: false,
};

export const ciHasPermissionsSlice = createSlice({
  name: 'ciHasPermissionsState',
  initialState,
  reducers: {
    updateCiHasPermissions(state, action) {
      state.ciHasPermissions = action.payload;
    },
  },
});

export const getCiHasPermissions = (store: RootStoreType) => store.ciHasPermissions;
