import React, { useState } from 'react';
import { DemoContext } from './DemoContext';
import Child1 from './Child1';
import Child2 from './Child2';

export const DemoUseContextPage = () => {
  const [value, setValue] = useState<number>(0);
  return (
    <>
      Use provider page: {value}
      <br />
      <br />
      <DemoContext.Provider value={{ value, setValue }}>
        <div style={{ display: 'flex' }}>
          <div style={{ flex: 1 }}>
            <Child1></Child1>
          </div>
          <div style={{ flex: 1 }}>
            <Child2></Child2>
          </div>
        </div>
      </DemoContext.Provider>
      <br />
      Note: Use context in cases where you need to pass props to many child components or communicate between components at the same level or
      centrally manage data in the parent component.
      <br />
      For example, when data is stored in multiple tabs and popups on the same page.
      <br />
      In other cases, use store
    </>
  );
};

export default DemoUseContextPage;
