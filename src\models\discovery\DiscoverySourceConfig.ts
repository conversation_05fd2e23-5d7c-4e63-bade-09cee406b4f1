import {
  ApiType,
  AuthenticationType,
  DbType,
  LastDiscoveryStatusEnum,
  IntegrateMethodEnum as Method,
  SnmpType,
  SnmpTypeOid,
  SnmpVersion,
} from '@common/constants/DiscoverySourceConfigEnum';
import type { EntityModelBase } from '@models/EntityModelBase';
import { DiscoverySourceResponse } from './DiscoverySource';

export type DiscoverySourceConfigModel = EntityModelBase & {
  id: number;
  sourceId: number;
  tokenKey: string;
  usernameAuthen: string;
  passwordAuthen: string;
  limit: number;
};

export type DiscoverySourceConfigResponse = DiscoverySourceConfigModel;

export type Option = {
  name: string;
  value: string;
};

export type Param = Option & {
  type: string;
};
export type SnmpOidInfo = {
  type: SnmpTypeOid;
  oid: string;
  label: string;
};
export const defaultSnmpOidInfo: SnmpOidInfo = {
  type: SnmpTypeOid.SCALAR,
  oid: '',
  label: '',
};

export type SourceConfigBasicInfo = {
  name: string;
  method: Method | undefined;
  type: ApiType | DbType | SnmpType | undefined;
  description?: string;
  snmpVersion?: SnmpVersion;
};

export const defaultBasicInfo: SourceConfigBasicInfo = {
  name: '',
  method: undefined,
  type: undefined,
  snmpVersion: undefined,
};

export type SourceConfigApiInfo = {
  apiUrl?: string;
  authenticationType?: AuthenticationType;
  username?: string;
  password?: string;
  tokenKey?: string;
  params?: Param[];
};

export const defaultApiInfo: SourceConfigApiInfo = {
  apiUrl: '',
  authenticationType: undefined,
  username: '',
  password: '',
  tokenKey: '',
  params: [],
};

export type SourceConfigDatabaseInfo = {
  server?: string;
  port?: string;
  databaseName?: string;
  username?: string;
  password?: string;
  // extraOptions?: Option[];
};

export const defaultDatabaseInfo: SourceConfigDatabaseInfo = {
  server: '',
  port: '',
  databaseName: '',
  username: '',
  password: '',
  // extraOptions: [],
};
export type SourceConfigSnmpInfo = {
  id: string;
  hostName: string;
  version?: SnmpType;
  username?: string;
  password?: string;
  community?: string;
  authentication?: string;
  encryption?: string;
  encryptionKey?: string;
  oidsInfo: SnmpOidInfo[];
};

export const defaultSnmpInfo: SourceConfigSnmpInfo = {
  id: '',
  hostName: '',
  version: undefined,
  username: undefined,
  password: '',
  community: '',
  authentication: undefined,
  encryption: undefined,
  encryptionKey: '',
  oidsInfo: [defaultSnmpOidInfo],
};
export type DiscoverySourceConfigDetail = {
  basicInfo: SourceConfigBasicInfo;
  apiInfo?: SourceConfigApiInfo;
  databaseInfo?: SourceConfigDatabaseInfo;
  snmpInfos?: SourceConfigSnmpInfo[];
};

export const defaultDiscoverySourceConfigDetail: DiscoverySourceConfigDetail = {
  basicInfo: defaultBasicInfo,
};

export const discoverySourceConfigDetailDefault: DiscoverySourceResponse = {
  id: 0,
  name: '',
  method: Method.API,
  type: undefined,
  lastDiscoveryStatus: LastDiscoveryStatusEnum.SUCCESS,
  lastDiscoveryTime: new Date(),
};

export const mockDiscoveryConfigs: DiscoverySourceResponse[] = [
  {
    id: 0,
    name: 'Netbox API',
    method: Method.API,
    type: undefined,
    lastDiscoveryStatus: LastDiscoveryStatusEnum.SUCCESS,
    lastDiscoveryTime: new Date(),
  },
  {
    id: 0,
    name: 'Rancher DB',
    method: Method.DB_CONNECT,
    type: undefined,
    lastDiscoveryStatus: LastDiscoveryStatusEnum.SUCCESS,
    lastDiscoveryTime: new Date(),
  },
];
