import { useDisclosure } from '@mantine/hooks';
import { DiscoveryTransformMapDetailModel, DiscoveryTransformMapModel } from '@models/DiscoveryTransformMap';
import {
  ColumnType,
  KanbanButton,
  KanbanConfirmModal,
  KanbanInput,
  KanbanModal,
  KanbanSelect,
  KanbanTable,
  KanbanTableProps,
  KanbanText,
  CustomColumnRenderValueType,
} from 'kanban-design-system';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { TransfromMappingJson } from './TransfromMappingJson';
import { ComboboxItem, Flex, Pill } from '@mantine/core';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import { DiscoveryStagingApi } from '@api/DiscoveryStagingApi';
import { DiscoveryTransformMapApi } from '@api/DiscoveryTransformMapApi';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { useNavigate, useParams } from 'react-router-dom';
import { buildCreateOrUpdateDiscoveryTransfromMapUrl, transformMapPath } from '@common/utils/RouterUtils';
import { IconArrowBack } from '@tabler/icons-react';
import { DEFAULT_ID_FOR_CI_DESCRIPTION, DEFAULT_ID_FOR_CI_NAME } from '@common/constants/CommonConstants';
import { BreadcrumbComponent, UrlBaseCrumbData } from '../breadcrumb/BreadcrumbComponent';
/**
 * check valid data transfrom map.
 * @param data data map
 * @returns true/false
 */
const isValidData = (data: DiscoveryTransformMapModel): boolean => {
  if (data.name && data.ciTypeId && data.stagingTableId && data.details?.length) {
    return true;
  }
  return false;
};

/**
 * Get new attribue with name and system attribute (Name, description...)
 * @param data data
 * @returns new attribute
 */
export const getNewAttribute = (data: DiscoveryTransformMapDetailModel): DiscoveryTransformMapDetailModel => {
  if (data.attributeId === DEFAULT_ID_FOR_CI_NAME) {
    data.attributeName = 'Name';
  }
  if (data.attributeId === DEFAULT_ID_FOR_CI_DESCRIPTION) {
    data.attributeName = 'Description';
  }
  return data;
};

// Tách hàm render ra ngoài component hoặc trong cùng file

const renderSourceFieldCell = (_: any, row: DiscoveryTransformMapDetailModel): CustomColumnRenderValueType => {
  let pillLabel = 'staging Table';
  let pillColor = '#40c057'; // green
  let textColor: string | undefined = undefined;

  const text = row.isStatic ? `"${row.staticValue}"` : row.columnName;
  if (row.isStatic) {
    pillLabel = 'static';
    pillColor = '#0066CC'; // blue
    textColor = '#67BEFF';
  }

  return {
    value: (
      <Flex align='center' justify='space-between' gap='xs'>
        <KanbanText c={textColor}>{text}</KanbanText>
        <Pill style={{ backgroundColor: pillColor, color: 'white' }}>{pillLabel}</Pill>
      </Flex>
    ),
  };
};

const renderTargetFieldCell = (_: any, row: DiscoveryTransformMapDetailModel): CustomColumnRenderValueType => {
  return {
    value: (
      <Flex align='center' justify='space-between' gap='xs'>
        <KanbanText>{row.attributeName}</KanbanText>
        {row.attributeDeleted && (
          <Pill bg='#FF3333' c='white'>
            removed
          </Pill>
        )}
      </Flex>
    ),
    customProps: {
      rowSpan: row._targetRowSpan,
      ...((row._targetRowSpan ?? 1) > 1 && {
        td: {
          style: {
            borderLeft: '1px solid var(--mantine-color-gray-3)',
          },
        },
      }),
    },
  };
};

export const CreateOrUpdateDiscoveryTransfromMap = () => {
  const { id } = useParams();
  const transFormMapId = Number(id);
  const navigate = useNavigate();
  const [openedModalMapJson, { close: closeModalMapJson, open: openModalMapJson }] = useDisclosure(false);
  const [openedModalConfirmMap, { close: closeModalConfirmMap, open: openModalConfirmMap }] = useDisclosure(false);
  const [dataMap, setDataMap] = useState<DiscoveryTransformMapModel>({
    name: '',
    stagingTableId: 0,
    stagingTableName: '',
    ciTypeId: 0,
    ciTypeName: '',
    details: [],
  });
  const [mapName, setMapName] = useState<string>('');
  const [listDataMapping, setListDataMapping] = useState<DiscoveryTransformMapDetailModel[]>([]);
  const [ciTypeComboxItem, setCiTypeComboxItem] = useState<ComboboxItem[]>([]);
  const [stagingComboxItem, setStagingComboxItem] = useState<ComboboxItem[]>([]);
  const allCiTypes = useGetCiTypes();

  useEffect(() => {
    const citypes = allCiTypes.data || [];
    setCiTypeComboxItem(
      citypes.map((obj) => {
        return {
          value: `${obj.id}`,
          label: obj.name,
        };
      }),
    );
  }, [allCiTypes.data]);

  const getAllStagingTable = useCallback(() => {
    DiscoveryStagingApi.getAll()
      .then((res) => {
        if (res.data) {
          const datas = res.data || [];
          const comboboxs = datas.map((obj) => {
            return {
              value: `${obj.id}`,
              label: obj.name,
            };
          });
          setStagingComboxItem(comboboxs);
        }
      })
      .catch(() => {});
  }, []);

  const getMapDetails = useCallback(() => {
    if (transFormMapId) {
      DiscoveryTransformMapApi.getById(transFormMapId)
        .then((res) => {
          if (res.data) {
            setDataMap(res.data);
            setListDataMapping(res.data.details || []);
            setMapName(res.data.name);
          }
        })
        .catch(() => {});
    }
  }, [transFormMapId]);

  const runJob = useCallback(() => {
    if (transFormMapId) {
      DiscoveryTransformMapApi.runJob(transFormMapId)
        .then((res) => {
          if (res.data) {
            NotificationSuccess({ title: 'Success', message: 'Run job successfully' });
          }
        })
        .catch(() => {});
    }
  }, [transFormMapId]);

  const saveMap = useCallback(
    (data: DiscoveryTransformMapModel) => {
      DiscoveryTransformMapApi.save(data)
        .then((res) => {
          if (res.data) {
            NotificationSuccess({ title: 'Success', message: 'Save map successfully' });
            navigate(buildCreateOrUpdateDiscoveryTransfromMapUrl(res.data.id || 0));
          }
        })
        .catch(() => {});
    },
    [navigate],
  );

  useEffect(() => {
    getAllStagingTable();
    getMapDetails();
  }, [getAllStagingTable, getMapDetails]);

  const columns: ColumnType<DiscoveryTransformMapDetailModel>[] = useMemo(() => {
    return [
      {
        title: 'Source Fields',
        name: 'columnName',
        customRender: renderSourceFieldCell,
        sortable: false,
      },
      {
        title: 'Target Fields',
        name: 'attributeName',
        customRender: renderTargetFieldCell,
      },
    ];
  }, []);

  const dataMapDetails = useMemo(() => {
    // trải phẳng dữ liệu
    const result: DiscoveryTransformMapDetailModel[] = [];

    for (const item of dataMap?.details ?? []) {
      const children = Array.isArray(item.listChildren) ? item.listChildren : [];
      const hasChild = children.length > 0;

      result.push({
        ...item,
        _targetRowSpan: hasChild ? children.length + 1 : 1,
      });

      if (hasChild) {
        for (const child of children) {
          result.push({
            ...child,
            _targetRowSpan: 0,
          });
        }
      }
    }

    if (dataMap?.details) {
      return result.map((obj) => getNewAttribute(obj));
    }
    return [];
  }, [dataMap.details]);
  /**
   * custom search.
   */
  const onSearched = useCallback((datas: DiscoveryTransformMapDetailModel[], search: string): DiscoveryTransformMapDetailModel[] => {
    const lowerCaseSearch = search.toLowerCase();
    return datas.filter((item) => {
      const searchFields = [item.columnName, item.attributeName, item.staticValue];
      return searchFields.some((field) => (field || '').toLowerCase().includes(lowerCaseSearch));
    });
  }, []);

  const tableViewProps: KanbanTableProps<DiscoveryTransformMapDetailModel> = useMemo(() => {
    return {
      columns: columns,
      key: 1,
      data: dataMapDetails,
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 300,
        onSearched: onSearched,
      },
      customRowProps: (data) => {
        if (![DEFAULT_ID_FOR_CI_DESCRIPTION, DEFAULT_ID_FOR_CI_NAME].includes(data.attributeId) && !data.attributeName) {
          return {
            bg: 'red',
            c: 'White',
          };
        }
        return {};
      },
      sortable: {
        enable: true,
      },
      actions: {},
      selectableRows: {
        enable: false,
      },
      pagination: {
        enable: true,
      },
    };
  }, [columns, dataMapDetails, onSearched]);

  const locationCustomPaths = useMemo((): UrlBaseCrumbData => {
    const originPath = buildCreateOrUpdateDiscoveryTransfromMapUrl(Number(id));

    return {
      [`/${id}`]: {
        title: dataMap.id ? `Update transform map ${mapName}` : 'Create transform map',
        href: originPath,
      },
    };
  }, [dataMap.id, id, mapName]);

  return (
    <>
      <BreadcrumbComponent locationCustomPaths={locationCustomPaths} />
      <HeaderTitleComponent
        title={dataMap.id ? `Update transform map ${mapName}` : 'Create transform map'}
        rightSection={
          <Flex gap={10}>
            <KanbanButton
              leftSection={<IconArrowBack />}
              variant='outline'
              onClick={() => {
                navigate(transformMapPath);
              }}>
              Cancel
            </KanbanButton>
            <KanbanButton disabled={!transFormMapId} onClick={runJob}>
              Run Job
            </KanbanButton>
            <KanbanButton
              disabled={!isValidData(dataMap)}
              onClick={() => {
                saveMap(dataMap);
              }}>
              Save
            </KanbanButton>
          </Flex>
        }
      />
      <KanbanInput
        required
        label='Name'
        maxLength={100}
        value={dataMap.name}
        onChange={(value) => {
          setDataMap((prev) => ({ ...prev, name: value.target.value }));
        }}
        onBlur={() => {
          setDataMap((prev) => ({ ...prev, name: prev.name.trim() }));
        }}
      />
      <KanbanSelect
        required
        searchable
        value={`${dataMap.stagingTableId}`}
        data={stagingComboxItem}
        label='Staging table (source)'
        onChange={(value, data) => {
          if (!value) {
            return;
          }
          if (Number(value || 0) === dataMap.stagingTableId) {
            setDataMap((prev) => ({ ...prev, stagingTableId: Number(value || 0), stagingTableName: data.label }));
          } else {
            setDataMap((prev) => ({ ...prev, stagingTableId: Number(value || 0), stagingTableName: data.label, details: [] }));
          }
        }}
      />
      <KanbanSelect
        required
        value={`${dataMap.ciTypeId}`}
        data={ciTypeComboxItem}
        label='CI Type (target)'
        searchable
        onChange={(value, data) => {
          if (!value) {
            return;
          }
          if (Number(value || 0) === dataMap.ciTypeId) {
            setDataMap((prev) => ({ ...prev, ciTypeId: Number(value || 0), ciTypeName: data.label }));
          } else {
            setDataMap((prev) => ({ ...prev, ciTypeId: Number(value || 0), ciTypeName: data.label, details: [] }));
          }
        }}
      />

      <HeaderTitleComponent
        title={''}
        rightSection={
          <Flex gap={10}>
            <KanbanButton disabled={!dataMap.ciTypeId || !dataMap.stagingTableId} onClick={openModalMapJson}>
              Config Map
            </KanbanButton>
          </Flex>
        }
      />

      <KanbanTable {...tableViewProps} />

      <KanbanModal
        size={'80%'}
        opened={openedModalMapJson}
        onClose={() => {
          closeModalMapJson();
        }}
        centered
        title={
          <KanbanText size='xl' fw={600}>
            Transfrom map setting
          </KanbanText>
        }
        actions={
          <>
            <KanbanConfirmModal
              title={'Map error'}
              onClose={closeModalConfirmMap}
              opened={openedModalConfirmMap}
              modalProps={{
                size: 'sm',
              }}>
              <KanbanText>The data map has one or more attributes that have been deleted. Please check it again.!</KanbanText>
            </KanbanConfirmModal>
            <KanbanButton
              disabled={!listDataMapping?.length}
              onClick={() => {
                const isMappingDeleted = listDataMapping.some((obj) => obj.attributeDeleted);
                if (isMappingDeleted) {
                  openModalConfirmMap();
                  return;
                }
                setDataMap((prev) => ({ ...prev, details: listDataMapping }));
                closeModalMapJson();
              }}>
              Submit
            </KanbanButton>
          </>
        }>
        <TransfromMappingJson discoveryTransformMap={dataMap} setDetails={setListDataMapping} />
      </KanbanModal>
    </>
  );
};
