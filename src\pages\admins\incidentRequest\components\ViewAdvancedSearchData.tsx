import { Box, Group, Tooltip } from '@mantine/core';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ColumnType, KanbanTable, KanbanTableProps, TableAffactedSafeType } from 'kanban-design-system';
import { KanbanText } from 'kanban-design-system';
import { KanbanButton } from 'kanban-design-system';
import { renderDateTime } from 'kanban-design-system';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import equal from 'fast-deep-equal';
import { buildCiTypeUrl, buildCiUrl } from '@common/utils/RouterUtils';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { getCiTypes } from '@slices/CiTypesSlice';
import { KanbanIconButton } from 'kanban-design-system';
import { IconAffiliate, IconClipboardList, IconEye, IconEyeUp, IconLayoutList, IconPlus } from '@tabler/icons-react';
import { KanbanConfirmModal } from 'kanban-design-system';
import CiUpdateSole, { CiUpdateSoleMethods } from '@pages/cis/ci/CiUpdateSole';
import { useDisclosure } from '@mantine/hooks';
import CiRelationship from '@pages/cis/ci/relationship/CiRelationship';
import AdvanceSearchCi, { AdvanceSearchCiMethod } from '@components/commonCi/advanceSearch/AdvanceSearchCi';
import type { AdvanceSearchData } from '@components/commonCi/advanceSearch/AdvanceSearchComponent';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanCheckbox } from 'kanban-design-system';
import { ImpactedTypeTable } from '@models/ChangeAssessment';
import type { RuleGroupType } from 'react-querybuilder';
import ViewServiceMappingDetail, { ViewServiceMappingDetailMethods } from './ViewServiceMappingDetail';
import type { ConfigItemResponse } from '@api/ConfigItemApi';
import { CI_TYPE_ID_OF_SERVICE_MAP } from '@common/constants/CommonConstants';
import { CiManagementApi, CiManagementResponse } from '@api/CiManagementApi';
import CiManagementDetailViewPopup, { CiManagementDetailViewPopupMethods } from '@pages/cis/ciManagement/modal/CiManagementDetailViewPopup';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { tableAffectedToPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import type { CiInfoModel } from '@models/ConfigItem';

interface ViewAdvancedSearchDataChangeProps {
  title: string;
  cis: ConfigItemResponse[];
  activeTab: ImpactedTypeTable;
  onAddChange: (val: ConfigItemResponse[]) => void;
  onClose: (val: boolean) => void;
}

const initialSearchCi: RuleGroupType = {
  combinator: 'and',
  rules: [
    {
      rules: [
        {
          field: 'ciTypeId',
          operator: '!=',
          value: '-1',
        },
      ],
      combinator: 'and',
      not: false,
    },
  ],
};

const initialSearchService: RuleGroupType = {
  combinator: 'and',
  rules: [
    {
      rules: [
        {
          field: 'ciTypeId',
          operator: '=',
          value: '-1',
        },
      ],
      combinator: 'and',
      not: false,
    },
  ],
};

export const ViewAdvancedSearchDataChange: React.FC<ViewAdvancedSearchDataChangeProps> = ({ activeTab, cis, onAddChange, onClose, title }) => {
  const [advanceSearch, setAdvanceSearch] = useState<AdvanceSearchData | undefined>(undefined);
  const exportComponentRef = useRef<AdvanceSearchCiMethod | null>(null);

  const [listCi, setListCi] = useState<ConfigItemResponse[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const allCiTypes = useSelector(getCiTypes);
  const navigate = useNavigate();

  const [openedModalViewCi, { close: closeModalViewCi, open: openModalViewCi }] = useDisclosure(false);
  const [openedModalRelationship, { close: closeModalRelationship, open: openModalRelationship }] = useDisclosure(false);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);

  const [selectedCis, setSelectedCis] = useState<ConfigItemResponse[]>([]);
  const [listCiDraft, setListCiDraft] = useState<CiManagementResponse[]>([]);
  const [currentCiInfo, setCurrentCiInfo] = useState<CiInfoModel | undefined>();
  const [currentCiTemp, setCurrentCiTemp] = useState<CiManagementResponse>();

  const onClickViewDraft = useCallback(
    (ciId: number) => {
      const ciTempData = listCiDraft.find((x) => x.ciId === ciId);
      setCurrentCiTemp(ciTempData);
      childRefViewDetail.current?.openPopupView();
    },
    [listCiDraft],
  );

  const isCheckAll = useMemo(() => {
    const listCiIds = selectedCis.map((item) => item.id);
    const rowIdsNotSelect = new Set(cis ? cis.map((x) => x.id) : []);
    const listCiAllowSelect = listCi.filter((x) => !rowIdsNotSelect.has(x.id));
    return listCiAllowSelect.every((item) => listCiIds.includes(item.id));
  }, [cis, listCi, selectedCis]);

  const columns: ColumnType<ConfigItemResponse>[] = useMemo(() => {
    return [
      {
        name: 'checkbox',
        title: '',
        sortable: false,
        width: '3%',
        customRenderHeader: () => {
          return (
            <KanbanCheckbox
              checked={isCheckAll}
              onChange={(e) => {
                const checked = e.target.checked;
                if (checked) {
                  const selectedCisOld = [...selectedCis];
                  const listOldIds = new Set(selectedCis.map((item) => item.id));
                  const rowIdsNotSelect = new Set(cis ? cis.map((x) => x.id) : []);

                  const newItems = listCi.filter((item) => !listOldIds.has(item.id) && !rowIdsNotSelect.has(item.id));

                  // Update list selectedCi
                  setSelectedCis([...selectedCisOld, ...newItems]);
                } else {
                  const selectedCisOld = [...selectedCis];
                  const listCiIds = listCi.map((item) => item.id);
                  const updatedList = selectedCisOld.filter((item) => !listCiIds.includes(item.id));
                  // Update list CI Impact
                  setSelectedCis(updatedList);
                }
              }}
            />
          );
        },
        customRender: (_, row) => {
          return (
            <>
              {!(cis && cis.some((x) => x.id === row.id)) && (
                <KanbanCheckbox
                  checked={selectedCis.some((x) => x.id === row.id)}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  onChange={(e) => {
                    const checked = e.target.checked;
                    let selected = [...selectedCis];
                    if (checked) {
                      selected.push(row);
                    } else {
                      selected = selected.filter((x) => x.id !== row.id);
                    }
                    setSelectedCis(selected);
                  }}
                />
              )}
            </>
          );
        },
      },
      {
        title: 'Id',
        name: 'id',
        hidden: true,
      },
      {
        title: 'Name',
        name: 'name',
        width: '15%',
      },
      {
        title: 'Description',
        name: 'description',
        customRender: (data) => {
          return <KanbanText lineClamp={2}>{data}</KanbanText>;
        },
        width: '45%',
      },
      {
        title: 'Ci Type',
        name: 'ciTypeId',
        customRender: (data: number) => {
          const ciType = allCiTypes.data.find((x) => x.id === data);
          if (!ciType) {
            return <></>;
          }
          return (
            <KanbanButton
              size='compact-xs'
              radius={'lg'}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(buildCiTypeUrl(ciType.id));
              }}>
              {ciType.name}
            </KanbanButton>
          );
        },
        width: '10%',
      },
      {
        title: 'Created by',
        name: 'author',
        width: '10%',
      },
      {
        title: 'Created date',
        name: 'createdDate',
        customRender: renderDateTime,
        width: '10%',
      },
    ];
  }, [isCheckAll, selectedCis, listCi, cis, allCiTypes.data, navigate]);

  const tableProps: KanbanTableProps<ConfigItemResponse> = useMemo(() => {
    return {
      title: 'List CI',
      searchable: {
        enable: true,
        debounceTime: 800,
      },
      sortable: {
        enable: true,
      },

      columns: columns,
      data: listCi,

      pagination: {
        enable: true,
      },
      serverside: {
        totalRows: totalRecords,
        onTableAffected(dataSet) {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
      actions: {
        customAction: (data) => {
          return (
            <>
              <Tooltip label='View change'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  disabled={listCiDraft.every((x) => x.ciId !== data.id)}
                  onClick={() => {
                    onClickViewDraft(data.id);
                  }}>
                  <IconClipboardList />
                </KanbanIconButton>
              </Tooltip>
              <GuardComponent requirePermissions={AclPermission.createViewCiPermissions(data.id, data.ciTypeId)} hiddenOnUnSatisfy>
                <Tooltip label='Go to detail'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      window.open(buildCiUrl(Number(data.ciTypeId), Number(data.id)), '_blank');
                    }}>
                    <IconEyeUp />
                  </KanbanIconButton>
                </Tooltip>
              </GuardComponent>
              <GuardComponent requirePermissions={AclPermission.createViewCiPermissions(data.id, data.ciTypeId)} hiddenOnUnSatisfy>
                <Tooltip label='View info'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setCurrentCiInfo({
                        ciId: data.id,
                        ciTypeId: data.ciTypeId,
                        serviceMapId: 0,
                      });
                      openModalViewCi();
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                </Tooltip>
              </GuardComponent>

              <Tooltip label='View relationship'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    setCurrentCiInfo({
                      ciId: data.id,
                      ciTypeId: data.ciTypeId,
                      serviceMapId: 0,
                    });
                    openModalRelationship();
                  }}>
                  <IconAffiliate />
                </KanbanIconButton>
              </Tooltip>
              {data.ciTypeId === CI_TYPE_ID_OF_SERVICE_MAP && (
                <Tooltip label='View service info'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      childRefViewServiceMapping.current?.viewServiceMappingDetail(data.id);
                    }}>
                    <IconLayoutList />
                  </KanbanIconButton>
                </Tooltip>
              )}
            </>
          );
        },
      },
    };
  }, [columns, listCi, totalRecords, tableAffected, listCiDraft, onClickViewDraft, openModalViewCi, openModalRelationship]);

  const [isLoadingTable, setIsLoadingTable] = useState(false);

  const fetchCis = useCallback(() => {
    if (!tableAffected) {
      return;
    }
    setIsLoadingTable(true);
    const pagination = tableAffectedToPaginationRequestModel(tableAffected);
    ConfigItemTypeApi.getAllCisWithChildrenPaging(0, pagination, advanceSearch, false)
      .then((res) => {
        setTotalRecords(res.data.totalElements);
        setListCi(res.data.content);
      })
      .catch(() => {})
      .finally(() => {
        setIsLoadingTable(false);
      });
  }, [tableAffected, advanceSearch]);

  useEffect(() => {
    fetchCis();
  }, [fetchCis]);

  const fetchDataDraft = useCallback(() => {
    const listIds = listCi.map((x) => x.id);
    if (listIds.length > 0) {
      CiManagementApi.getCiTempByCiIdIn(listIds)
        .then((res) => {
          if (res.data && res.data.length > 0) {
            setListCiDraft(res.data);
            return;
          }
        })
        .catch(() => {});
    }
  }, [listCi]);

  useEffect(() => {
    fetchDataDraft();
  }, [fetchDataDraft]);

  const actionAddDataChange = () => {
    onAddChange(selectedCis);
    setSelectedCis([]);
  };

  const childRef = useRef<CiUpdateSoleMethods | null>(null);
  const childRefViewDetail = useRef<CiManagementDetailViewPopupMethods | null>(null);
  const childRefViewServiceMapping = useRef<ViewServiceMappingDetailMethods | null>(null);

  return (
    <>
      {/* modal view detail info of CI management draft */}
      <CiManagementDetailViewPopup ref={childRefViewDetail} initData={currentCiTemp} />

      {/* Modal view service mapping */}
      <ViewServiceMappingDetail ref={childRefViewServiceMapping} />

      <KanbanConfirmModal
        title={'CI Detail'}
        onConfirm={undefined}
        onClose={closeModalViewCi}
        opened={openedModalViewCi}
        modalProps={{
          size: '80%',
        }}>
        {currentCiInfo && <CiUpdateSole readOnly={true} ciId={currentCiInfo.ciId} ciTypeId={currentCiInfo.ciTypeId} forwardedRef={childRef} />}
      </KanbanConfirmModal>

      <KanbanConfirmModal
        title={'Relationship'}
        onConfirm={undefined}
        onClose={closeModalRelationship}
        opened={openedModalRelationship}
        modalProps={{
          size: '80%',
        }}>
        {currentCiInfo && <CiRelationship ciId={currentCiInfo.ciId} ciTypeId={currentCiInfo.ciTypeId} isView isFromBusinessView />}
      </KanbanConfirmModal>

      <Box>
        <AdvanceSearchCi
          onSearch={(val) => {
            setAdvanceSearch(val);
          }}
          ref={exportComponentRef}
          initialQuery={activeTab === ImpactedTypeTable.CI ? initialSearchCi : initialSearchService}
        />

        <HeaderTitleComponent
          title={title}
          rightSection={
            <KanbanButton
              disabled={selectedCis.length <= 0}
              leftSection={<IconPlus />}
              onClick={() => {
                actionAddDataChange();
              }}>
              Add to incident
            </KanbanButton>
          }
        />
        <KanbanText>
          Selected record ({selectedCis.length || 0}) - Total record ({totalRecords || 0})
        </KanbanText>
        <KanbanTable isLoading={isLoadingTable} {...tableProps} />
        <Group justify='flex-end'>
          <KanbanButton
            onClick={() => {
              onClose(false);
            }}>
            Close
          </KanbanButton>
        </Group>
      </Box>
    </>
  );
};

ViewAdvancedSearchDataChange.whyDidYouRender = true;
ViewAdvancedSearchDataChange.displayName = 'ViewAdvancedSearchDataChange';
export default ViewAdvancedSearchDataChange;
