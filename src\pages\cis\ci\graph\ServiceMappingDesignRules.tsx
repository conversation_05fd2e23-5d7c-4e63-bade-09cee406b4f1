import { OperatorEnum, getComboboxOperatorEnum } from '@common/constants/OperatorEnum';
import type { GoJs } from '@common/libs';
import { safetyParseObjectFromJson } from '@common/utils/Helpers';
import { SelectGroupProps, SelectGroups, SelectItemProps } from '@components/commonCi/advanceSearch/SelectGroups';
import { KanbanInput } from 'kanban-design-system';
import { KanbanSelect } from 'kanban-design-system';
import { ComboboxItem, SimpleGrid } from '@mantine/core';
import { CiTypeAttributeDataType } from '@models/CiType';
import type { ServiceMapRuleModel } from '@models/ServiceMapping';
import type { ConfigItemOptionPickerModel } from '@pages/admins/ciType/CiTypeAttributeDetail';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import type { ConfigItemTypeAttrModel } from '@models/ConfigItemTypeAttr';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import { ServiceMapFilterTypeEnum } from '@common/constants/ServiceMapEnum';

type ServiceMappingDesignRuleProps = {
  setRule: (rule: ServiceMapRuleModel) => void;
  node: GoJs.Node | undefined;
};

// data atribute common
const attributeCommons: SelectGroupProps = {
  label: 'Attribute',
  items: [
    {
      name: 'ci-name',
      value: 'ci-name',
    },
  ],
};

export const ServiceMappingDesignRules = (props: ServiceMappingDesignRuleProps) => {
  const { node, setRule: setRuleProps } = props;
  const [ciTypeAttributes, setCiTypeAttributes] = useState<ConfigItemTypeAttrModel[]>([]);
  const [ciTypeId, setCiTypeId] = useState<number | undefined>(node?.data?.ciTypeId);
  const ciTypesData = useGetCiTypes()?.data || [];
  const ciTypeComboboxs = ciTypesData.map((item) => ({
    value: item.id.toString(),
    label: item.name,
  }));
  const [pickListOptions, setPickListOptions] = useState<ConfigItemOptionPickerModel[]>([]);
  const [rule, setRule] = useState<ServiceMapRuleModel>(node?.data?.rules || {});
  const [operatorComboboxs, setOperatorComboboxs] = useState<ComboboxItem[]>([]);

  const initRef = useRef(true);
  useEffect(() => {
    if (ciTypeId) {
      ConfigItemTypeApi.getAllAttributes(ciTypeId)
        .then((res) => {
          if (res.data) {
            setCiTypeAttributes(res.data);

            if (!initRef.current) {
              setRule({
                ciTypeId: ciTypeId,
              });
              setPickListOptions([]);
            } else {
              setRule((prev) => ({ ...prev, ciTypeId: ciTypeId }));
            }
            initRef.current = false;
          }
        })
        .catch(() => {});
    }
  }, [ciTypeId]);

  useEffect(() => {
    setRuleProps(rule);
  }, [setRuleProps, rule]);

  useEffect(() => {
    if (rule.type === CiTypeAttributeDataType.TEXT) {
      setOperatorComboboxs(getComboboxOperatorEnum([OperatorEnum.IS, OperatorEnum.IS_NOT, OperatorEnum.CONTAINS, OperatorEnum.NOT_CONTAINS]));
    } else {
      setOperatorComboboxs(getComboboxOperatorEnum([OperatorEnum.IS, OperatorEnum.IS_NOT]));
    }
  }, [rule]);
  /**
   * set data selectbox choise attribute,
   */
  const groupedOptions: SelectGroupProps[] = useMemo(() => {
    if (!ciTypeAttributes) {
      return [];
    }
    const items = ciTypeAttributes
      .filter((item) => item.type === CiTypeAttributeDataType.TEXT || CiTypeAttributeDataType.PICK_LIST === item.type)
      .map((attr): SelectItemProps => {
        return {
          name: attr.name,
          value: `${attr.id}`,
          description: attr.type,
        };
      });
    return [
      //TODO thangnv9 update on next sprint
      //attributeCommons,
      {
        label: 'Custom Attribute',
        items: items,
      },
    ];
  }, [ciTypeAttributes]);

  const handleOnChangeAttribute = useCallback(
    (value: string, isFetchValue = true) => {
      const valueCommons: string[] = attributeCommons.items.map((item) => item.name);
      const ciTypeAttribute = ciTypeAttributes.find((item) => item.id === Number(value));
      if (ciTypeAttribute && CiTypeAttributeDataType.PICK_LIST === ciTypeAttribute.type && ciTypeAttribute.options) {
        const options = (safetyParseObjectFromJson(ciTypeAttribute.options) || []) as ConfigItemOptionPickerModel[];
        setPickListOptions(options);
      } else if (valueCommons.includes(value) && ciTypeId) {
        ConfigItemTypeApi.getAllCis(ciTypeId)
          .then((res) => {
            if (res.data) {
              const listCis: ConfigItemOptionPickerModel[] = res.data.map((item) => {
                return {
                  label: item.name,
                  value: `${item.id}`,
                };
              });
              setPickListOptions(listCis);
            }
          })
          .catch(() => {});
      } else {
        setPickListOptions([]);
      }

      if (valueCommons.includes(value)) {
        setRule((prev) => ({
          ...prev,
          ciTypeAttrId: value,
          attributeType: ServiceMapFilterTypeEnum.ATTRIBUTE_COMMON,
          ciTypeAttrName: value,
          type: ciTypeAttribute?.type,
          operator: !isFetchValue ? prev.operator : undefined,
          value: !isFetchValue ? prev.value : undefined,
        }));
      } else {
        setRule((prev) => ({
          ...prev,
          ciTypeAttrId: Number(value),
          attributeType: ServiceMapFilterTypeEnum.ATTRIBUTE_CUSTOM,
          ciTypeAttrName: ciTypeAttribute?.name,
          type: ciTypeAttribute?.type,
          operator: !isFetchValue ? prev.operator : undefined,
          value: !isFetchValue ? prev.value : undefined,
        }));
      }
    },
    [ciTypeAttributes, ciTypeId],
  );

  useEffect(() => {
    if (rule.ciTypeAttrId && ciTypeAttributes.length > 0) {
      handleOnChangeAttribute(`${rule.ciTypeAttrId}`, false);
    }
  }, [rule.ciTypeAttrId, handleOnChangeAttribute, ciTypeAttributes]);
  return (
    <>
      <SimpleGrid cols={3}>
        <KanbanSelect
          label='CI Type'
          searchable={true}
          data={ciTypeComboboxs}
          defaultValue={props ? `${props.node?.data?.ciTypeId}` : ''}
          onChange={(value) => {
            setCiTypeId(Number(value));
          }}></KanbanSelect>
      </SimpleGrid>

      <SimpleGrid cols={3}>
        <SelectGroups
          label='Attribute'
          placeholder='Attribute'
          datas={groupedOptions}
          value={`${rule.ciTypeAttrId}`}
          onChange={handleOnChangeAttribute}></SelectGroups>
        <KanbanSelect
          placeholder='Operator'
          label='Operator'
          data={operatorComboboxs}
          value={rule.operator?.toString() || null}
          onChange={(value) => {
            if (value) {
              setRule((prev) => ({ ...prev, operator: value as OperatorEnum }));
            }
          }}></KanbanSelect>
        {pickListOptions.length === 0 && (
          <KanbanInput
            placeholder='Value'
            label='Value'
            maxLength={200}
            value={rule.value || ''}
            onChange={(e) => {
              if (e) {
                setRule((prev) => ({ ...prev, value: e.target.value }));
              }
            }}></KanbanInput>
        )}
        {pickListOptions.length !== 0 && (
          <KanbanSelect
            label='Value'
            value={rule.value || null}
            onChange={(_, item) => {
              if (item.value) {
                setRule((prev) => ({ ...prev, value: item.value, label: item.label }));
              }
            }}
            data={pickListOptions}></KanbanSelect>
        )}
      </SimpleGrid>
    </>
  );
};
ServiceMappingDesignRules.displayName = 'ServiceMappingDesignRules';
