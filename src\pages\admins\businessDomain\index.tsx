import React, { useEffect, useState, useRef, useCallback } from 'react';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import {
  SimpleGrid,
  // Flex, Group, ScrollArea
} from '@mantine/core';
import type { BusinessDomainModel } from '@models/BusinessDomain';
import { IconSearch } from '@tabler/icons-react';
import { KanbanInput, KanbanButton } from 'kanban-design-system';
import { BusinessDomainItemComponent } from './component/BusinessDomainItemComponent';
import styles from './BusinessDomain.module.scss';
import type { PaginationRequestModel } from '@models/EntityModelBase';
import { BusinessDomainApi } from '@api/BusinessDomainApi';
import { useNavigate } from 'react-router-dom';
import { buildBusinessDomainAggregateUrl } from '@common/utils/RouterUtils';
import { BreadcrumbComponent } from '../breadcrumb/BreadcrumbComponent';

export const BusinessDomainPage = () => {
  const navigate = useNavigate();
  const viewRef = useRef<HTMLDivElement>(null);
  const [businessDomainList, setBusinessDomainList] = useState<BusinessDomainModel[]>([]);
  const [search, setSearch] = useState('');

  const [isLastPage, setIsLastPage] = useState<boolean>(false);
  const [pageAffected, setPageAffected] = useState<PaginationRequestModel<BusinessDomainModel>>({
    page: 0,
    size: 50,
    search: '',
    sortBy: 'name' as keyof BusinessDomainModel,
    isReverse: false,
  });

  // CI RELATION
  const fetchDomain = useCallback(() => {
    if (!isLastPage) {
      BusinessDomainApi.getAllWithPaging(pageAffected)
        .then((res) => {
          setIsLastPage(res?.data?.last);
          setBusinessDomainList((prev) => {
            return [...prev, ...(res.data?.content || [])];
          });
        })
        .catch(() => {});
    }
  }, [pageAffected, isLastPage]);

  useEffect(() => {
    const handleScroll = (event: Event) => {
      const target = event.target as HTMLDivElement;
      const heightNearBottomScroll = 10;
      if (target) {
        // fix-bug CMDB-4767
        if (target.scrollHeight !== 0 && target.scrollHeight <= heightNearBottomScroll + target.scrollTop + target.clientHeight) {
          setPageAffected((prev) => {
            return { ...prev, page: prev.page + 1 };
          });
        }
      }
    };

    const element = viewRef.current;
    if (element) {
      element.addEventListener('scroll', handleScroll);
    }
    return () => {
      if (element) {
        element.removeEventListener('scroll', handleScroll);
      }
    };
  }, [viewRef]);

  useEffect(() => {
    fetchDomain();
  }, [fetchDomain]);

  return (
    <div className={styles['wrapper']}>
      {/* 4736 bussiness domains */}
      <BreadcrumbComponent />
      <HeaderTitleComponent
        title={`Business Architecture Management`}
        rightSection={
          <KanbanInput
            placeholder='Enter CI Name'
            value={search}
            onChange={(e) => {
              const value = e.target.value;
              setSearch(value);
            }}
            onKeyUp={(element) => {
              if (element.key === 'Enter') {
                navigate(buildBusinessDomainAggregateUrl(search?.trim() || ''));
              }
            }}
            leftSection={
              <KanbanButton
                size='compact-xs'
                variant='outline'
                bd={0}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  navigate(buildBusinessDomainAggregateUrl(search?.trim() || ''));
                }}>
                <IconSearch />
              </KanbanButton>
            }
          />
        }
      />
      <div className={styles['content']} ref={viewRef}>
        <SimpleGrid className={styles['grid-auto-fit-w']}>
          {businessDomainList.map((itemDomain, indexDomain) => {
            return <BusinessDomainItemComponent key={indexDomain} data={itemDomain}></BusinessDomainItemComponent>;
          })}
        </SimpleGrid>
      </div>
    </div>
  );
};

export default BusinessDomainPage;
