import { ConfigItemApi } from '@api/ConfigItemApi';
import { GoJs } from '@common/libs';
import type { CiRelationshipCountModel, CiRelationshipInfoModel, CiRelationshipInfoWithImpact, LinkDataModel } from '@models/CiRelationship';
import type { CiRelationshipTypeModel } from '@models/CiRelationshipType';
import type { ConfigItemModel } from '@models/ConfigItem';
import type { ConfigItemTypeModel } from '@models/ConfigItemType';
import FileSaver from 'file-saver';
import { TablerIconKeys, getIconPublicPathFromIconName } from './IconsUtils';
import { CiRelationshipApi } from '@api/CiRelationshipApi';
import type { ServiceMapRuleModel } from '@models/ServiceMapping';
import { type DiagramEventName } from '@common/libs/gojs/go';
import { RelationshipTypeConstants } from '@common/constants/RelationshipTypeConstants';
import { isCiPermissionAction } from './AclPermissionUtils';
import { PermissionAction } from '@common/constants/PermissionAction';
import { CiRelationshipDirectionEnum } from '@models/ImpactedRule';
import { FlaggedImpactedCiAttachedType } from '@common/constants/CiManagement';
import { ServiceMapFilterTypeEnum } from '@common/constants/ServiceMapEnum';
import { getTextOperatorEnum } from '@common/constants/OperatorEnum';
// import { TreeLayout } from '@common/libs/gojs/go';

const go = GoJs;
const $ = go.GraphObject.make;
const duration = 400;
export enum DIAGRAM_EVENT_TYPE {
  INITIAL_LAYOUT_COMPLETED = 'InitialLayoutCompleted',
  LAYOUT_COMPLETED = 'LayoutCompleted',
}

export enum GRAPH_VIEW_TYPE {
  TOP_DOWN = 'TOP_DOWN',
  LEFT_RIGHT = 'LEFT_RIGHT',
  CIRCLE = 'CIRCLE',
}

export enum GRAPH_VIEW_EXPORT_TYPE {
  JPG = 'JPG',
  PNG = 'PNG',
}

export const GRAPH_STYLES = {
  DASH: [2, 5], // dash pattern: 2px line, 5px gap
  DOT: [1, 3], // dot pattern: 1px line, 3px gap
  SOLID: null, // solid line (no dash pattern)
  DASH_DOT: [4, 2, 1, 2], // dash-dot pattern: 4px line, 2px gap, 1px line, 2px gap
  DASH_DOT_HIGHT_LIGHT: [2, 0.5],
};
export type GraphViewLinkStyle = {
  routing: GoJs.EnumValue;
  corner: number;
};

export enum GRAPH_VIEW_LINK_STYPES {
  NORMAL_JUMP_OVER = 'NORMAL_JUMP_OVER',
  ORTHOGONAL_ROUTING = 'ORTHOGONAL_ROUTING',
}

export const GRAPH_VIEW_LINK_STYPES_LIST: Record<GRAPH_VIEW_LINK_STYPES, GraphViewLinkStyle> = {
  [GRAPH_VIEW_LINK_STYPES.NORMAL_JUMP_OVER]: { routing: go.Link.Normal, corner: 0 },
  [GRAPH_VIEW_LINK_STYPES.ORTHOGONAL_ROUTING]: { routing: go.Link.AvoidsNodes, corner: 10 },
};

export interface PalleteTemplate {
  key: any;
  icon?: TablerIconKeys;
  label: string;
  data?: Record<string, unknown>;
  isRoot?: boolean;
  totalFrom?: number;
  totalTo?: number;
  group?: number;
  rules?: ServiceMapRuleModel;
  ciTypeId?: number;
  borderColor?: string;
  opacity?: number;
  backgroundColor?: string;
  bounds?: GoJs.Rect; // attr of virualization
  showChildLine?: boolean;
  showParentLine?: boolean;
}
export interface PalleteTemplateFull extends PalleteTemplate {
  loc?: string;
}
export interface LinkDataTemplate {
  relationshipId: number;
  from: any;
  to: any;
  text: string;
  style?: number[];
  color?: string;
  isHightlight?: boolean;
  opacity?: number;
  direction?: string[];
}

export interface GraphData {
  template: PalleteTemplateFull[];
  links: LinkDataTemplate[];
}

export type IsVirtualizableType = {
  isVirtualized?: boolean;
};

export type MakePallateTemplateStyle = IsVirtualizableType & {
  isShowTotalChild?: boolean;
};
export type MakeLinkTemplateStyleOptions = IsVirtualizableType & {
  isEdit?: boolean;
  styleEnum?: GRAPH_VIEW_LINK_STYPES;
};

export type AppendNodeProps = IsVirtualizableType & {
  goDiagram: go.Diagram | undefined;
  datas: CiRelationshipInfoModel[];
  ciTypes: ConfigItemTypeModel[];
  getRelationshipTypeById: (id: number) => CiRelationshipTypeModel | undefined;
  ciAddNewRelationship?: number;
  isAddNew?: boolean;
  isUseTransaction?: boolean;
};

export type DiagramConfig = IsVirtualizableType & {
  dom: HTMLDivElement | unknown;
  showLayout: boolean;
  isEdit?: boolean;
  isShowSelectionAdornment?: boolean;
  isShowTotalChild?: boolean;
  showOnlyInfo?: boolean;
  isVirtualized?: boolean;
};

export const removeAllChildLinks = (
  ciId: number,
  links: LinkDataTemplate[],
  isUpStream: boolean,
  checkedNodes: Set<number> = new Set(),
): LinkDataTemplate[] => {
  const linkChilds: LinkDataTemplate[] = [];

  function findChilds(currentCiId: number) {
    // case node connect to itseft or all nodes connect to circle => break recursive
    if (checkedNodes.has(currentCiId)) {
      return;
    }
    checkedNodes.add(currentCiId);
    const nodeChilds = links.filter((link) => (isUpStream ? link.from === currentCiId : link.to === currentCiId));
    linkChilds.push(...nodeChilds);

    nodeChilds.forEach((child) => {
      findChilds(isUpStream ? child.to : child.from);
    });
  }

  findChilds(ciId);
  return linkChilds;
};

export const generateTemplateAndLinks = (
  relationships: CiRelationshipInfoWithImpact[],
  ciInfo: ConfigItemModel,
  ciTypes: ConfigItemTypeModel[],
  getRelationshipTypeById: (id: number) => CiRelationshipTypeModel | undefined,
  totalCis?: CiRelationshipCountModel[],
): GraphData => {
  const template: PalleteTemplateFull[] = [];
  const links: LinkDataTemplate[] = [];
  const sortedCiRelationships = relationships ?? [];
  sortedCiRelationships.sort(function (a, b) {
    const fromCiTypeIdA = a.fromCiTypeId !== undefined ? a.fromCiTypeId : Number.POSITIVE_INFINITY;
    const fromCiTypeIdB = b.fromCiTypeId !== undefined ? b.fromCiTypeId : Number.POSITIVE_INFINITY;
    return fromCiTypeIdA - fromCiTypeIdB;
  });
  for (const item of sortedCiRelationships) {
    const totalNodeLinkFrom = totalCis?.find((x) => x.ciId === item.fromCi);
    const fromCiItem: PalleteTemplateFull = {
      key: item.fromCi,
      icon: (ciTypes.find((x) => x.id === item.fromCiTypeId)?.icon || 'IconHome') as TablerIconKeys,
      label: `${item.fromCiName}`,
      isRoot: ciInfo.id === item.fromCi,
      data: {
        ciId: item.fromCi,
        ciTypeId: item.fromCiTypeId,
        ciTypeName: item.fromCiTypeName,
        isCiChangePlan: item.ciChangePlans?.includes(item.fromCi) ?? false,
        isImpact: !!item.impactedCi,
      },
      totalFrom: totalNodeLinkFrom?.totalFrom || 0,
      totalTo: totalNodeLinkFrom?.totalTo || 0,
      backgroundColor: handleBackgroundColor(true, item.ciChangePlans, item.impactedCi, item.fromCi),
      showChildLine: false,
      showParentLine: false,
    };

    const totalNodeLinkTo = totalCis?.find((x) => x.ciId === item.toCi);

    if (item.toCi) {
      const toCiItem: PalleteTemplateFull = {
        key: item.toCi,
        icon: (ciTypes.find((x) => x.id === item.toCiTypeId)?.icon || 'IconHome') as TablerIconKeys,
        label: `${item.toCiName}`,
        isRoot: ciInfo.id === item.toCi,
        data: {
          ciId: item.toCi,
          ciTypeId: item.toCiTypeId,
          ciTypeName: item.toCiTypeName,
          isCiChangePlan: item.ciChangePlans?.includes(item.toCi) ?? false,
          isImpact: !!item.impactedCi,
        },
        totalFrom: totalNodeLinkTo?.totalFrom || 0,
        totalTo: totalNodeLinkTo?.totalTo || 0,
        backgroundColor: handleBackgroundColor(false, item.ciChangePlans, item.impactedCi, item.toCi),
        showChildLine: false,
        showParentLine: false,
      };
      if (!template.some((x) => x.key === item.toCi)) {
        template.push(toCiItem);
      }
    }
    if (!template.some((x) => x.key === item.fromCi)) {
      template.push(fromCiItem);
    }
    const relationship = getRelationshipTypeById(item.relationshipId);

    links.push({
      relationshipId: item.relationshipId,
      from: item.fromCi,
      to: item.toCi ? item.toCi : '',
      text: getRelationshipName(!!item.impactedCi, item, relationship),
      color:
        item.attachedType === FlaggedImpactedCiAttachedType.MANUAL
          ? 'black'
          : item.impactedCi
            ? item.ciRuleRelationships
              ? '#CC0099'
              : 'black'
            : 'black',
      direction: item.ciRuleRelationships?.map((relationship) => relationship.impactedRuleRelationDirection ?? '') ?? [],
    });
  }
  if (template.length === 0 && links.length === 0) {
    const rootItem: PalleteTemplateFull = {
      key: ciInfo.id,
      icon: (ciTypes.find((x) => x.id === ciInfo.ciTypeId)?.icon || 'IconHome') as TablerIconKeys,
      label: ciInfo.name,
      isRoot: true,
      data: {
        ciId: ciInfo.id,
        ciTypeId: ciInfo.ciTypeId,
      },
      showChildLine: false,
      showParentLine: false,
    };
    template.push(rootItem);
  }

  return { template, links };
};

export function makeLinkTemplateStyle({
  isEdit = false,
  isVirtualized = false,
  styleEnum = GRAPH_VIEW_LINK_STYPES.ORTHOGONAL_ROUTING,
}: MakeLinkTemplateStyleOptions) {
  const style = GRAPH_VIEW_LINK_STYPES_LIST[styleEnum];
  const $ = go.GraphObject.make;

  const linkSelectionAdornmentTemplate = $(
    go.Adornment,
    'Link',
    { layerName: 'Background' },
    $(go.Shape, { isPanelMain: true, fill: null, stroke: 'deepskyblue', strokeWidth: 0 }),
  );

  return $(
    go.Link,
    {
      selectable: true,
      movable: true,
      selectionAdornmentTemplate: isEdit ? linkSelectionAdornmentTemplateWithCaseEdit : linkSelectionAdornmentTemplate,
      mouseEnter: (e: any, obj: any) => {
        const link = obj.part;
        settingVisibleTextOfLink(link, true);
      },
      mouseLeave: (e: any, obj: any) => {
        if (obj && obj.link) {
          const link = obj.part;
          const isShow = !!(link.isHighlighted || link.data?.isHighlighted);
          settingVisibleTextOfLink(link, isShow);
        }
      },
    },
    {
      routing: style.routing,
      corner: style.corner,
      ...(isVirtualized ? {} : { curve: go.Link.JumpOver }),
      reshapable: isEdit,
      resegmentable: isEdit,
      layerName: 'Background',
    },
    $(
      go.Shape,
      { isPanelMain: true, strokeWidth: 1 },
      new go.Binding('opacity', '', (data) => data.opacity ?? 1),
      new go.Binding('stroke', 'color', (c) => c || 'black').ofObject(),
      new go.Binding('stroke', 'isHighlighted', (h, obj) => {
        if (h) {
          return obj.part.data.color || obj.part.stroke || 'black';
        }
        if (obj.part.data.isHighlighted) {
          return 'red';
        }
        return obj.part.data.color || obj.part.stroke || 'black';
      }).ofObject(),
      new go.Binding('strokeWidth', 'isHighlighted', (h, obj) => (h || obj.part.data.isHighlighted ? 3 : 1)).ofObject(),
      new go.Binding('strokeDashArray', '', (data: LinkDataTemplate) => {
        return data.style || (data.relationshipId === RelationshipTypeConstants.ANY.key ? GRAPH_STYLES.DASH : null);
      }),
    ),
    $(
      go.Shape,
      { toArrow: 'Standard', stroke: null },
      new go.Binding('fill', 'isHighlighted', (h, obj) => {
        const color = obj.part.data.color || 'black';
        return h || obj.part.data.isHighlighted ? color : color;
      }).ofObject(),
    ),
    $(
      go.Panel,
      'Auto',
      { alignmentFocus: go.Spot.Bottom },
      $(go.Shape, 'Rectangle', { fill: 'white', stroke: null }),
      $(
        go.TextBlock,
        {
          name: 'LINK_TEXT',
          textAlign: 'center',
          font: 'bold 10px helvetica, arial, sans-serif',
          stroke: '#000000',
          margin: 2,
          minSize: new go.Size(10, NaN),
          maxSize: new go.Size(100, 100),
          editable: false,
          overflow: go.TextBlock.OverflowEllipsis,
          wrap: go.TextBlock.WrapFit,
          segmentIndex: 0,
          segmentFraction: 0.2,
          segmentOffset: new go.Point(0, -10),
          visible: false,
        },
        new go.Binding('text', '', (data: LinkDataTemplate) => data.text),
      ),
    ),
  );
}

const dragComputation = (part: go.Part, newLoc: go.Point): go.Point => {
  const diagram = part.diagram;
  if (!diagram) {
    return newLoc;
  } // fallback nếu diagram null

  const vb = diagram.viewportBounds;
  const width = part.actualBounds.width;
  const height = part.actualBounds.height;

  const x = Math.max(vb.x, Math.min(newLoc.x, vb.right - width));
  const y = Math.max(vb.y, Math.min(newLoc.y, vb.bottom - height));

  return new go.Point(x, y);
};

const maxLength = 20;
const formatText = (text: string): string => {
  if (!text) {
    return '';
  }
  return text.length > maxLength ? `${text.slice(0, maxLength - 3)}...` : text;
};

export const makePallateTemplateStyle = ({ isShowTotalChild = false, isVirtualized = false }: MakePallateTemplateStyle) => {
  const renderIcon = (data: PalleteTemplate) => getIconPublicPathFromIconName(data.icon);

  const renderLabel = (data: any): string => {
    const label = formatText(data?.label || '');
    const startCiName = formatText(data?.data?.startCiName || '');

    if (data?.data?.startCiId) {
      return `${label}\n(${startCiName})`;
    }

    return label;
  };

  const nodeSelectionAdornmentTemplate = $(
    go.Adornment,
    'Auto',
    $(go.Shape, {
      fill: null,
      stroke: 'deepskyblue',
      strokeWidth: 1.5,
      strokeDashArray: [4, 2],
    }),
    $(go.Placeholder),
  );

  const nodeBindings = [new go.Binding('opacity', '', (data: PalleteTemplate) => data.opacity ?? 1), new go.Binding('angle').makeTwoWay()];

  if (isVirtualized) {
    nodeBindings.push(
      new go.Binding('position', 'bounds', (b) => b.position).makeTwoWay((p, d) => new go.Rect(p.x, p.y, d.bounds.width, d.bounds.height)),
    );
  } else {
    nodeBindings.push(new go.Binding('location', 'loc', go.Point.parse).makeTwoWay(go.Point.stringify));
  }

  return $(
    go.Node,
    'Auto',
    {
      ...(isVirtualized ? { dragComputation } : {}),
    },
    {
      opacity: 0,
      ...(isVirtualized
        ? {
            isLayoutPositioned: false,
            width: 90,
            height: 100,
            toolTip: $('ToolTip', $(go.TextBlock, { margin: 3 }, new go.Binding('text', '', (d) => `Name: ${d.label}`))),
          }
        : {
            width: 90,
            height: 100,
          }),
    },
    {
      selectionChanged: (node: go.Part) => {
        node.layerName = node.isSelected ? 'Foreground' : '';
      },
    },
    ...nodeBindings,
    { selectionAdornmentTemplate: nodeSelectionAdornmentTemplate },
    $(
      go.Panel,
      'Auto',
      $(
        go.Shape,
        'RoundedRectangle',
        {
          fill: 'white',
          stroke: '#eeeeee',
          strokeWidth: 2,
          cursor: 'pointer',
          portId: '',
          fromMaxLinks: NaN,
          toMaxLinks: NaN,
          name: 'SHAPE',
          fromLinkable: true,
          toLinkable: true,
        },
        new go.Binding('stroke', '', (data: PalleteTemplate) =>
          data.backgroundColor ? '#c1c0c0' : data.isRoot ? '#FF9A9A' : data.borderColor || '#eeeeee',
        ),
        new go.Binding('fill', '', (data: PalleteTemplate) => (data.backgroundColor ? data.backgroundColor : data.isRoot ? '#FFCECE' : 'white')),
      ),
      $(
        go.Panel,
        'Vertical',
        {
          width: 90,
          height: 100,
          padding: new go.Margin(5, 5, 5, 5),
        },
        $(
          go.Panel,
          'Spot',
          {
            width: 75,
            height: 20,
            alignment: go.Spot.Top,
          },
          $(go.Shape, 'Circle', {
            name: 'totalChild',
            width: 15,
            height: 15,
            alignment: go.Spot.Right,
            fill: 'red',
            stroke: null,
            strokeWidth: 0,
            visible: isShowTotalChild,
          }),
          $(
            go.TextBlock,
            {
              visible: isShowTotalChild,
              alignment: go.Spot.Center,
              font: 'bold 10px Helvetica, Arial, sans-serif',
              stroke: 'white',
            },
            new go.Binding('text', '', (data) => `${(data.totalFrom || 0) + (data.totalTo || 0)}`),
          ),
        ),

        $(go.Picture, { width: 20, height: 20 }, new go.Binding('source', '', renderIcon)),

        $(
          go.TextBlock,
          {
            font: 'bold 11px Helvetica, Arial, sans-serif',
            stroke: 'black',
            textAlign: 'center',
            margin: new go.Margin(0, 0, 15, 0),
            wrap: go.TextBlock.WrapFit,
            verticalAlignment: go.Spot.Center,
            desiredSize: new go.Size(80, 40),
            maxSize: new go.Size(80, 40),
          },
          new go.Binding('text', '', renderLabel),
        ),

        $(
          go.Panel,
          'Auto',
          {
            alignment: go.Spot.BottomRight,
            alignmentFocus: go.Spot.BottomRight,
          },
          $(
            go.Picture,
            {
              name: 'filter',
              width: 20,
              height: 20,
              cursor: 'pointer',
              source: getIconPublicPathFromIconName('IconFilter'),
              toolTip: $(
                go.Adornment,
                'Auto',
                $(go.Shape, { fill: '#FFFFCC' }),
                $(
                  go.TextBlock,
                  {
                    margin: 4,
                    font: 'bold 12px Helvetica, Arial, sans-serif',
                  },
                  new go.Binding('text', '', (data) => {
                    return `Attribute: ${data.rules.ciTypeAttrName} \n 
                    operator: ${getTextOperatorEnum(data.rules.operator)} \n 
                    value: ${data.rules.attributeType === ServiceMapFilterTypeEnum.ATTRIBUTE_COMMON ? `${data.rules.label}-${data.rules.value}` : data.rules.value}`;
                  }),
                ),
              ),
            },
            new go.Binding('visible', '', (data) => !!data.rules),
          ),
        ),
      ),
    ),
    {
      click: (_e: any, node: go.Node) => handleHighlightForNode(node),
      doubleClick: () => {
        // Logic xử lý khi double click nếu cần
      },
    },
  );
};

export const makeDiagram = (props: DiagramConfig) => {
  const { dom, isEdit, isShowSelectionAdornment, isShowTotalChild, isVirtualized = false, showLayout, showOnlyInfo } = props;

  const myDiagram = $(go.Diagram, dom, {
    initialContentAlignment: go.Spot.Center,
    initialDocumentSpot: go.Spot.Center,
    initialViewportSpot: go.Spot.Center,
    'undoManager.isEnabled': true,
    'animationManager.isEnabled': !isVirtualized,
    'linkingTool.isEnabled': false,
    'linkingTool.archetypeLinkData': { category: 'Link' },
    layout: $(go.Layout, { isInitial: false, isOngoing: false }),
    ...(isVirtualized ? {} : { scrollMargin: 200 }),
  });

  if (isEdit) {
    myDiagram.toolManager.linkingTool.linkValidation = validateLink;
  }

  myDiagram.allowUndo = true;
  myDiagram.allowClipboard = true;
  myDiagram.undoManager.isEnabled = true;

  myDiagram.nodeTemplate = makePallateTemplateStyle({ isShowTotalChild, isVirtualized });

  myDiagram.linkTemplate = makeLinkTemplateStyle({ isEdit: isEdit, isVirtualized: isVirtualized });

  myDiagram.click = (e) => {
    e.diagram.commit((d) => {
      d.clearHighlighteds();
      d.links.each((l) => {
        if (!l.data?.isHighlighted) {
          settingVisibleTextOfLink(l, false);
        }
      });
    }, 'no highlighteds');
  };

  if (showLayout) {
    myDiagram.layout = $(go.LayeredDigraphLayout, {
      layerSpacing: 150,
      columnSpacing: 30,
      direction: 90,
    });
  }

  if (isShowSelectionAdornment) {
    myDiagram.nodeTemplate.selectionAdornmentTemplate = nodeHoverAdornmentRelations(myDiagram, isEdit, showOnlyInfo);
  }

  if (isVirtualized) {
    myDiagram.updateAllTargetBindings();
  }

  return myDiagram;
};

export const scrollAndZoomToItem = (goDiagram: go.Diagram | undefined, id: string | number | null) => {
  if (!goDiagram || !id) {
    return;
  }
  // Find the specified item
  const part = goDiagram.findPartForKey(id);
  if (part !== null) {
    //const initScalePart = part.scale;
    // Start a transaction for multiple changes
    goDiagram.startTransaction('highlightTransaction');
    goDiagram.select(part);
    // Move to the specified part
    goDiagram.commandHandler.scrollToPart(part);
    goDiagram.commitTransaction('highlightTransaction');
    goDiagram.updateAllTargetBindings();
  } else {
    console.warn(`Item with ID ${id} not found.`);
  }
};

export const callBackSaveFile = (blob: Blob, fileName: string) => {
  FileSaver.saveAs(blob, fileName);
};
export const handleDownloadImage = (goDiagram: go.Diagram | undefined, type: GRAPH_VIEW_EXPORT_TYPE) => {
  if (!goDiagram) {
    return;
  }
  if (type === GRAPH_VIEW_EXPORT_TYPE.PNG) {
    goDiagram.makeImageData({
      scale: 1,
      background: 'white',
      type: 'blob',
      maxSize: new go.Size(Infinity, Infinity),

      callback: (blob) => callBackSaveFile(blob, 'diagram.png'),
    });
  } else if (type === GRAPH_VIEW_EXPORT_TYPE.JPG) {
    goDiagram.makeImageData({
      scale: 1,
      background: 'white',
      type: 'blob',
      maxSize: new go.Size(Infinity, Infinity),
      callback: (blob) => callBackSaveFile(blob, 'diagram.jpg'),
    });
  }
};

// Zoom In
export const zoomIn = (goDiagram: go.Diagram | undefined) => {
  if (goDiagram) {
    goDiagram.commandHandler.increaseZoom();
  }
};

// Zoom Out
export const zoomOut = (goDiagram: go.Diagram | undefined) => {
  if (goDiagram) {
    goDiagram.commandHandler.decreaseZoom();
  }
};

/**
 * Get all nodes key when you want to hide a CI it does not include the nodeHide ci input
 * @param goDiagram
 * @param nodeHide
 * @param isHideFrom
 */
export const getNodeKeyRemoves = (goDiagram: go.Diagram | undefined, nodeHide: go.Node | null, isHideFrom: boolean): number[] => {
  if (!goDiagram || !nodeHide) {
    return [];
  }

  const nodeHideSearch = goDiagram.findNodeForKey(nodeHide.data.key);
  const nodeRoot = goDiagram.findNodesByExample({ isRoot: true }).first();
  if (!nodeRoot || !nodeHideSearch) {
    return [];
  }

  //get all from and to node
  const allChildNodes = isHideFrom ? nodeHideSearch.findNodesOutOf() : nodeHideSearch.findNodesInto();
  const dataConnects =
    (isHideFrom ? getNodesOnPath(goDiagram, nodeHideSearch, nodeRoot) : getNodesOnPath(goDiagram, nodeRoot, nodeHideSearch))?.map(
      (item) => item.data.key,
    ) || [];
  let nodeRemoves: number[] = [];

  allChildNodes.each((childNode) => {
    if (!dataConnects?.includes(childNode.data.key)) {
      //When a node connects to the root but does not pass through the current node, then it should be kept
      const allNodeKeyConnectToRoot = getNodesOnPath(goDiagram, childNode, nodeRoot, true).map((item) => item.data.key);
      if (allNodeKeyConnectToRoot?.includes(nodeHide.data.key)) {
        nodeRemoves.push(childNode.data.key);
      }
    }
  });
  // keep all node when it's has a connect to other node
  //example: A has children (B, C, D). F has children (G,H , D) need keep D
  goDiagram.links.each((link) => {
    if (link.toNode && link.fromNode && nodeHideSearch.data.key !== link.fromNode.data.key && nodeRemoves.includes(link.toNode.data.key)) {
      nodeRemoves = nodeRemoves.filter((nodeId) => nodeId !== link.toNode?.data.key);
    }
  });
  return nodeRemoves || [];
};

/**
 * Hide node when click icon show/hide relationship
 * @param goDiagram
 * @param nodeHide
 * @param isHideFrom
 */
export const hideNodeRelationships = (
  goDiagram: go.Diagram | undefined,
  nodeHide: go.Node | null,
  isHideFrom: boolean,
  isUseTransaction: boolean = true,
) => {
  if (!goDiagram || !nodeHide) {
    return;
  }
  const nodeRoot = goDiagram.findNodesByExample({ isRoot: true }).first();
  if (!nodeRoot) {
    return;
  }

  const relationshipsTransaction = 'hideNodeRelationshipsTransaction';

  //delete all node into nodeRemoves and all node not connected to root
  if (isUseTransaction) {
    goDiagram.startTransaction(relationshipsTransaction);
  }
  if (!isHideFrom && nodeHide.data.key === nodeRoot.data.key) {
    nodeHide.findNodesInto().each((node) => {
      if (node) {
        goDiagram.remove(node);
      }
    });
  }

  if (isHideFrom && nodeHide.data.key === nodeRoot.data.key) {
    nodeHide.findNodesOutOf().each((node) => {
      if (node) {
        goDiagram.remove(node);
      }
    });
  }
  if (nodeHide.data.key !== nodeRoot.data.key) {
    const nodeRemoves = getNodeKeyRemoves(goDiagram, nodeHide, isHideFrom);
    goDiagram.nodes.each((item) => {
      if (nodeRemoves.includes(item.data.key)) {
        goDiagram.remove(item);
      }
    });

    // case upstream delete all link from current node to all childen
    //JIRA: CMDB-456
    const nodeHideSearch = goDiagram.findNodeForKey(nodeHide.data.key);
    const linksToRemove: go.Link[] = [];

    if (isHideFrom && nodeHideSearch) {
      nodeHideSearch.findLinksOutOf().each((link) => {
        const isConnectToRoot = link.toNode ? isConnected(goDiagram, link.toNode, nodeRoot) : false;
        let isRemoveLink: boolean = false;
        const nodesIterator = goDiagram.nodes.iterator;
        while (nodesIterator.next()) {
          const node = nodesIterator.value;
          if (!node.data.isRoot && link.toNode && node.data.key === link.toNode.key && !isConnectToRoot) {
            isRemoveLink = true;
            break;
          }
        }
        if (isRemoveLink) {
          linksToRemove.push(link);
        }
      });
      goDiagram.removeParts(linksToRemove, true);
    }
  }
  //remove all node not connected to root
  goDiagram.nodes.each((item) => {
    if (!item.data.data.isCiChangePlan) {
      if (!isConnected(goDiagram, item, nodeRoot)) {
        goDiagram.remove(item);
      }
    }
  });

  if (goDiagram.nodeTemplate.selectionAdornmentTemplate) {
    goDiagram.nodeTemplate.selectionAdornmentTemplate = nodeHoverAdornmentRelations(goDiagram);
  }
  //add animation
  goDiagram.animationManager.isEnabled = true;
  goDiagram.animationManager.duration = duration;
  if (isUseTransaction) {
    goDiagram.commitTransaction(relationshipsTransaction);
  }
};

/**
 * Hide one CI select with all node From and To
 * @param goDiagram
 * @param node
 */

export const hiddenNodeTransaction = 'hiddenNode';
export const hiddenNode = (
  goDiagram: go.Diagram | undefined,
  node: go.Node | undefined,
  isUseTransaction: boolean = true,
  isStaticView: boolean = true,
) => {
  if (!goDiagram || !node) {
    return;
  }
  if (isUseTransaction) {
    goDiagram.startTransaction(hiddenNodeTransaction);
  }
  if (isStaticView) {
    // remove layout fix static view when delete node
    goDiagram.layout.isOngoing = false;
  }

  const nodeRoot = goDiagram.findNodesByExample({ isRoot: true }).first();
  if (!nodeRoot) {
    return;
  }
  const itemNodes = goDiagram.nodes.iterator;
  while (itemNodes.next()) {
    const itemNode = itemNodes.value;
    if (itemNode.data.key === node.data.key) {
      goDiagram.remove(itemNode);
    }
  }

  //remove all node not connected to root
  const itemConnectNodes = goDiagram.nodes.iterator;
  while (itemConnectNodes.next()) {
    const itemConnectNode = itemConnectNodes.value;
    if (!isConnected(goDiagram, itemConnectNode, nodeRoot)) {
      goDiagram.remove(itemConnectNode);
    }
  }

  goDiagram.animationManager.isEnabled = true;
  goDiagram.animationManager.duration = duration;
  if (isUseTransaction) {
    goDiagram.commitTransaction(hiddenNodeTransaction);
  }
  if (isStaticView) {
    goDiagram.layout.isOngoing = true;
  }
};

/**
 * Remove by link relationship
 * @param goDiagram
 * @param link
 */
export const removeLink = (
  goDiagram: go.Diagram | undefined,
  links: go.Link[] | undefined,
  isUseTransaction: boolean = true,
  isStaticView: boolean = true,
) => {
  if (!goDiagram || !links) {
    return;
  }

  if (isStaticView) {
    // remove layout fix static view when delete node
    goDiagram.layout.isOngoing = false;
  }
  const nodeRoot = goDiagram.findNodesByExample({ isRoot: true }).first();
  if (!nodeRoot) {
    return;
  }
  const removeLinkTransaction = 'removeLinkTransaction';
  if (isUseTransaction) {
    goDiagram.startTransaction(removeLinkTransaction);
  }
  links.forEach((link) => {
    goDiagram.remove(link);
  });

  //remove all node not connected to root
  goDiagram.nodes.each((item) => {
    if (!isConnected(goDiagram, item, nodeRoot)) {
      goDiagram.remove(item);
    }
  });
  if (isUseTransaction) {
    goDiagram.commitTransaction(removeLinkTransaction);
  }

  if (isStaticView) {
    goDiagram.layout.isOngoing = true;
  }
};

/**
 * check node A connected node B
 * @param goDiagram
 * @param nodeA
 * @param nodeB
 */
export const isConnected = (goDiagram: go.Diagram, nodeA: go.Node, nodeB: go.Node): boolean => {
  if (!goDiagram || !nodeA || !nodeB) {
    return false;
  }

  const visited: Set<string> = new Set(); // Set to store visited nodes
  const queue: go.Node[] = []; // Queue for BFS

  // Add node A to the queue
  queue.push(nodeA);

  while (queue.length > 0) {
    const currentNode = queue.shift();
    if (!currentNode) {
      continue;
    } // Skip if currentNode is null

    const currentNodeKey = currentNode.data.key;
    if (!currentNodeKey) {
      continue;
    } // Skip if the key of currentNode is null

    // Check if the current node has been visited
    if (!visited.has(currentNodeKey)) {
      visited.add(currentNodeKey); // Mark the current node as visited

      // Check if the current node is node B
      if (currentNode === nodeB) {
        return true; // Return true if node B is found
      }

      // Iterate through all links out of the current node
      currentNode.findLinksOutOf().each((link) => {
        if (link.visible) {
          const targetNode = link.toNode;
          if (targetNode) {
            queue.push(targetNode); // Add the next node to the queue
          }
        }
      });

      // Iterate through all links into the current node
      currentNode.findLinksInto().each((link) => {
        if (link.visible) {
          const sourceNode = link.fromNode;
          if (sourceNode) {
            queue.push(sourceNode); // Add the next node to the queue
          }
        }
      });
    }
  }

  return false; // Return false if node B is not found
};

/**
 * Get all nodes on the path from nodeA to nodeB, optionally ignoring direction.
 * @param goDiagram The GoJS diagram.
 * @param nodeA The starting node.
 * @param nodeB The ending node.
 * @param ignoreDirection If true, ignore the direction of the connections.
 */
export const getNodesOnPath = (goDiagram: go.Diagram, nodeA: go.Node, nodeB: go.Node, ignoreDirection: boolean = false): go.Node[] => {
  if (!goDiagram || !nodeA || !nodeB) {
    return [];
  }

  const visitedNodes: Set<string> = new Set(); // Set to store visited nodes
  const paths: go.Node[][] = []; // Array to store all found paths
  const queue: { node: go.Node; path: go.Node[] }[] = []; // Queue for BFS, storing both node and its path

  // Add node A and its path to the queue
  queue.push({ node: nodeA, path: [nodeA] });

  while (queue.length > 0) {
    const queueItem = queue.shift();
    if (!queueItem) {
      continue;
    }

    const { node, path } = queueItem;

    const nodeKey = node.data.key;
    if (nodeKey === null) {
      continue;
    } // Skip if the key of currentNode is null

    // Check if the current node has been visited
    if (!visitedNodes.has(nodeKey)) {
      visitedNodes.add(nodeKey); // Mark the current node as visited

      // Check if the current node is node B
      if (node === nodeB) {
        paths.push(path.slice()); // Save the found path
      }

      // Iterate through all links out of or into the current node based on ignoreDirection flag
      if (ignoreDirection) {
        node.findNodesConnected().each((nextNode) => {
          if (nextNode !== null) {
            const newPath = path.concat(nextNode); // Extend the path
            queue.push({ node: nextNode, path: newPath }); // Add the next node and its path to the queue
          }
        });
      } else {
        node.findNodesConnected().each((nextNode) => {
          if (nextNode !== null) {
            const newPath = path.concat(nextNode); // Extend the path
            queue.push({ node: nextNode, path: newPath }); // Add the next node and its path to the queue
          }
        });
        node.findNodesInto().each((prevNode) => {
          if (prevNode !== null) {
            const newPath = path.concat(prevNode); // Extend the path
            queue.push({ node: prevNode, path: newPath }); // Add the previous node and its path to the queue
          }
        });
      }
    }
  }

  // Remove duplicate nodes in paths
  const uniquePaths: go.Node[][] = paths.map((path) => {
    const uniqueNodes = new Set<string>();
    return path.filter((node) => {
      const nodeKey = node.data.key;
      if (nodeKey !== null && !uniqueNodes.has(nodeKey)) {
        uniqueNodes.add(nodeKey);
        return true;
      }
      return false;
    });
  });

  // Flatten the unique paths into a single array
  const flattenPaths: go.Node[] = uniquePaths.reduce((acc, val) => acc.concat(val), []);

  return flattenPaths;
};

/**
 * append new node into graph view
 * @param goDiagram
 * @param data
 * @param getRelationshipTypeById
 */
export const appendNodeTransaction = 'appendNodeTransaction';
export const appendNode = async (props: AppendNodeProps) => {
  const {
    ciAddNewRelationship,
    ciTypes,
    datas,
    getRelationshipTypeById,
    goDiagram,
    isAddNew = false,
    isUseTransaction = true,
    isVirtualized = false,
  } = props;

  if (!goDiagram) {
    return Promise.resolve(false);
  }
  const position = goDiagram.position.copy();
  const scale = goDiagram.scale;
  const model = goDiagram.model as go.GraphLinksModel;
  const template: PalleteTemplateFull[] = [];
  const links: LinkDataTemplate[] = [];
  const ciIds = datas.flatMap((item) => [item.fromCi, item.toCi]);
  const updateCountTransaction = 'updateCountTransaction';

  if (!ciIds.length) {
    return Promise.resolve(false);
  }
  const res = await ConfigItemApi.ciCountRelationships(ciIds);
  for (const item of datas) {
    const totalNodeLinkFrom = res?.data?.find((x) => x.ciId === item.fromCi);
    const fromCiItem: PalleteTemplateFull = {
      key: item.fromCi,
      icon: (ciTypes.find((x) => x.id === item.fromCiTypeId)?.icon || 'IconHome') as TablerIconKeys,
      label: `${item.fromCiName}`,
      isRoot: false,
      data: {
        ciId: item.fromCi,
        ciTypeId: item.fromCiTypeId,
        ciTypeName: item.fromCiTypeName,
      },
      totalFrom: totalNodeLinkFrom?.totalFrom || 0,
      totalTo: totalNodeLinkFrom?.totalTo || 0,
    };

    const totalNodeLinkTo = res?.data?.find((x) => x.ciId === item.toCi);
    const toCiItem: PalleteTemplateFull = {
      key: item.toCi,
      icon: (ciTypes.find((x) => x.id === item.toCiTypeId)?.icon || 'IconHome') as TablerIconKeys,
      label: `${item.toCiName}`,
      isRoot: false,
      data: {
        ciId: item.toCi,
        ciTypeId: item.toCiTypeId,
        ciTypeName: item.toCiTypeName,
      },
      totalFrom: totalNodeLinkTo?.totalFrom || 0,
      totalTo: totalNodeLinkTo?.totalTo || 0,
    };

    if (!template.some((x) => x.key === item.fromCi)) {
      template.push(fromCiItem);
    }

    if (!template.some((x) => x.key === item.toCi)) {
      template.push(toCiItem);
    }

    const relationship = getRelationshipTypeById(item.relationshipId);

    links.push({
      relationshipId: item.relationshipId,
      from: item.fromCi,
      to: item.toCi,
      text: relationship ? relationship.type : '',
      direction: item.ciRuleRelationships?.map((relationship) => relationship.impactedRuleRelationDirection ?? '') ?? [],
    });
  }

  if (isUseTransaction) {
    goDiagram.startTransaction(appendNodeTransaction);
  }

  if (isVirtualized) {
    //  check if item not exists then append node to diagram
    template.forEach((item) => {
      if (!model.nodeDataArray.some((node) => node.key === item.key)) {
        // add bound for node
        const index = goDiagram.model.nodeDataArray.length;
        const sqrt = Math.floor(Math.sqrt(index + 1)); // +1 vì thêm node mới
        const col = index % sqrt;
        const row = Math.floor(index / sqrt);
        const newNodeBounds = new go.Rect(col * 180, row * 180, 80, 100);
        item.bounds = newNodeBounds;
        goDiagram.model.addNodeData(item);
      }
    });
  } else {
    template.forEach((item) => {
      if (!model.nodeDataArray.some((node) => node.key === item.key)) {
        goDiagram.model.addNodeData(item);
      }
    });
  }

  //append node to diagram
  links.forEach((item) => {
    if (!model.linkDataArray.some((link) => link.from === item.from && link.to === item.to)) {
      goDiagram.model.addLinkData(item);
      // update count relationship case add new relationship
      if (isAddNew) {
        const nodeFind = goDiagram.model.findNodeDataForKey(item.from);
        if (nodeFind && item.from === ciAddNewRelationship) {
          model.startTransaction(updateCountTransaction);
          goDiagram.model.set(nodeFind, 'totalFrom', (nodeFind.totalFrom || 0) + 1);
          model.commitTransaction(updateCountTransaction);
          goDiagram.model.updateTargetBindings(nodeFind);
        }

        const nodeFindTo = goDiagram.model.findNodeDataForKey(item.to);
        if (nodeFindTo && item.to === ciAddNewRelationship) {
          model.startTransaction(updateCountTransaction);
          goDiagram.model.set(nodeFindTo, 'totalTo', (nodeFindTo.totalTo || 0) + 1);
          model.commitTransaction(updateCountTransaction);
          goDiagram.model.updateTargetBindings(nodeFindTo);
        }
      }
    }
  });
  //add animation add node
  goDiagram.animationManager.isEnabled = true;
  goDiagram.animationManager.duration = duration;
  goDiagram.position = position;
  goDiagram.scale = scale;
  if (isUseTransaction) {
    goDiagram.commitTransaction(appendNodeTransaction);
  }
  return res;
};

export const appendNodeWithDataNode = (
  goDiagram: go.Diagram | undefined,
  templates: PalleteTemplateFull[] | GoJs.ObjectData[],
  links: LinkDataTemplate[],
  isUseTransaction: boolean = true,
  isStaticView: boolean = false,
) => {
  if (!goDiagram) {
    return;
  }
  if (isStaticView) {
    // remove layout keep static view
    goDiagram.layout.isOngoing = false;
  }
  const appendNodeTransaction = 'appendNodeWithDataNode';
  const model = goDiagram.model as go.GraphLinksModel;
  if (isUseTransaction) {
    goDiagram.startTransaction(appendNodeTransaction);
  }

  //check if item not exists then append node to diagram
  templates.forEach((item) => {
    if (!model.nodeDataArray.some((node) => node.key === item.key)) {
      goDiagram.model.addNodeData(item);
    }
  });
  //append node to diagram
  links.forEach((item) => {
    if (!model.linkDataArray.some((link) => link.from === item.from && link.to === item.to)) {
      goDiagram.model.addLinkData(item);
    }
  });
  if (isUseTransaction) {
    goDiagram.commitTransaction(appendNodeTransaction);
  }

  if (isStaticView) {
    goDiagram.layout.isOngoing = true;
  }
};

export const initSettingDiagram = (goDiagram: go.Diagram, ciId: number) => {
  const isForceDirected = goDiagram.layout instanceof go.ForceDirectedLayout;
  if (isForceDirected) {
    // rebuild diagram circle
    settingGraphViewType(goDiagram, ciId, GRAPH_VIEW_TYPE.CIRCLE);
  } else {
    //add event scroll to root node when complete change layout
    callBackEventDiagram(goDiagram, DIAGRAM_EVENT_TYPE.LAYOUT_COMPLETED, () => scrollAndZoomToItem(goDiagram, ciId));
  }
};
/**
 * event callback
 */
export const callBackEventDiagram = (goDiagram: go.Diagram, eventType: DiagramEventName, externalHandler: () => void) => {
  const layoutCompletedHandler = () => {
    // Execute the external handler if provided
    externalHandler();
    // Remove the event listener to avoid multiple calls
    goDiagram.removeDiagramListener(eventType, layoutCompletedHandler);
  };
  goDiagram.addDiagramListener(eventType, layoutCompletedHandler);
};

export const settingGraphViewType = (goDiagram: go.Diagram | undefined, ciId: number, type: GRAPH_VIEW_TYPE | null) => {
  if (goDiagram) {
    const node = goDiagram.findPartForKey(ciId);
    if (!node) {
      return;
    }
    //add event scroll to root node when complete change layout
    callBackEventDiagram(goDiagram, DIAGRAM_EVENT_TYPE.LAYOUT_COMPLETED, () => scrollAndZoomToItem(goDiagram, ciId));

    if (type === GRAPH_VIEW_TYPE.TOP_DOWN) {
      goDiagram.layout = go.GraphObject.make(go.LayeredDigraphLayout, {
        layerSpacing: 150,
        columnSpacing: 30,
        direction: 90,
      });
    } else if (type === GRAPH_VIEW_TYPE.LEFT_RIGHT) {
      goDiagram.layout = go.GraphObject.make(go.LayeredDigraphLayout, {
        layerSpacing: 150,
        columnSpacing: 30,
        direction: 0,
      });
    } else if (type === GRAPH_VIEW_TYPE.CIRCLE) {
      goDiagram.layout = go.GraphObject.make(go.ForceDirectedLayout, {
        maxIterations: 3000,
        defaultSpringStiffness: 0.05,
      });
    }
  }
};

export const makeGridTemplateStyle = () => {
  return $(
    go.Panel,
    'Grid',
    $(go.Shape, 'LineH', { stroke: 'lightgray', strokeWidth: 0.5 }),
    $(go.Shape, 'LineH', { stroke: 'gray', strokeWidth: 0.5, interval: 10 }),
    $(go.Shape, 'LineV', { stroke: 'lightgray', strokeWidth: 0.5 }),
    $(go.Shape, 'LineV', { stroke: 'gray', strokeWidth: 0.5, interval: 10 }),
  );
};

//ChangeStyleLink
export const changeDiagramLinkStyle = (goDiagram: go.Diagram | undefined, style: GRAPH_VIEW_LINK_STYPES) => {
  if (!goDiagram || !style) {
    return;
  }
  goDiagram.linkTemplate = makeLinkTemplateStyle({ isEdit: false, isVirtualized: false, styleEnum: style });
};

export const fetchDiagram = async (goDiagram: go.Diagram | undefined) => {
  if (!goDiagram) {
    return;
  }
  resetHightLight(goDiagram);
  const linkDataModels: LinkDataModel[] = [];
  goDiagram.links.each((link) => {
    linkDataModels.push({ from: link.data.from, to: link.data.to });
  });

  // when fetch diagram but diagram have  a only root node
  const nodeRoot = goDiagram.findNodesByExample({ isRoot: true }).first();
  if (!linkDataModels.length && nodeRoot) {
    linkDataModels.push({ from: nodeRoot.data.key, to: nodeRoot.data.key });
  }
  const res = await CiRelationshipApi.getDetails(linkDataModels);
  try {
    const cisDetail = res.data.cisDetail;
    const ciRelationships = res.data.ciRelationships;
    goDiagram.nodes.each((node) => {
      const ciDetail = cisDetail.find((item) => item.ciId === node.data.key);
      if (ciDetail) {
        goDiagram.model.setDataProperty(node.data, 'totalFrom', ciDetail.totalFrom | 0);
        goDiagram.model.setDataProperty(node.data, 'totalTo', ciDetail.totalTo | 0);
        goDiagram.model.setDataProperty(node.data, 'label', ciDetail.ciName as any);
        goDiagram.model.setDataProperty(node.data.data, 'ciTypeName', ciDetail.ciTypeName as any);
        goDiagram.model.setDataProperty(node.data.data, 'ciTypeId', ciDetail.ciTypeId as any);
      } else {
        hiddenNode(goDiagram, node, false, true);
      }
    });
    goDiagram.links.each((link) => {
      if (!ciRelationships.some((item) => item.fromCi === link.data.from && item.toCi === link.data.to)) {
        removeLink(goDiagram, [link], false, true);
      }
    });
    goDiagram.undoManager.clear();
  } catch (error) {
    console.error(error);
  }
};

function drawLink(e: go.InputEvent, button: go.GraphObject) {
  const part = button.part;
  if (!part) {
    return;
  }

  const node = e.diagram.findNodeForKey(part.data.key);
  if (!node) {
    return;
  }

  const diagram = e.diagram;
  const tool = diagram.toolManager.linkingTool;
  tool.startObject = node.port;
  diagram.currentTool = tool;
  tool.doActivate();

  // Listen for the "LinkDrawn" event to set default text
  diagram.addDiagramListener('LinkDrawn', function (event: go.DiagramEvent) {
    const link = event.subject;
    if (link instanceof go.Link) {
      // Set default text for the link
      diagram.model.setDataProperty(link.data, 'relationshipId', RelationshipTypeConstants.ANY.key);
      diagram.model.setDataProperty(link.data, 'text', RelationshipTypeConstants.ANY.value);
    }
  });
}

function deleteAllOutgoingLinks(e: go.InputEvent, button: go.GraphObject) {
  const part = button.part;
  if (!part) {
    return;
  }
  const node = e.diagram.findNodeForKey(part.data.key);
  if (!node) {
    return;
  }
  // Find all links connected to the node
  const links = node.findLinksOutOf();
  // Remove each link
  links.each((link) => {
    e.diagram.remove(link);
  });
}

export const nodeHoverAdornmentRelations = (goDiagram: go.Diagram | undefined, isEdit?: boolean, showOnlyInfo?: boolean) => {
  if (isEdit) {
    return $(
      go.Adornment,
      'Spot',
      $(go.Panel, 'Auto', $(go.Shape, { stroke: 'dodgerblue', strokeWidth: 2, fill: null }), $(go.Placeholder)),
      $(
        go.Panel,
        'Horizontal',
        { alignment: go.Spot.Top, alignmentFocus: go.Spot.Bottom },
        $(
          'Button',
          {
            click: drawLink, // click on Button and then click on target node
            actionMove: drawLink, // drag from Button to the target node
            _dragData: { text: 'a Node', color: 'lightgray' }, // node data to copy
          },
          $(go.Shape, { geometryString: 'M0 0 L3 0 3 10 6 10 x F1 M6 6 L14 6 14 14 6 14z', fill: 'gray' }),
        ),
        $(
          'Button',
          {
            _dragData: { text: 'a Node', color: 'lightgray' }, // node data to copy
            click: deleteAllOutgoingLinks,
          },
          $(go.Shape, { geometryString: 'M0 0 L3 0 3 10 6 10 x F1 M6 6 L14 6 14 14 6 14z M0 0 L14 14 M14 0 L0 14', fill: 'gray' }),
        ),
      ),
    );
  }

  if (showOnlyInfo) {
    return $(
      go.Adornment,
      'Spot',
      $(go.Panel, 'Auto', $(go.Shape, { stroke: 'dodgerblue', strokeWidth: 1, fill: null, visible: true }), $(go.Placeholder)),
      $(
        go.Panel,
        'Horizontal',
        { alignment: go.Spot.BottomCenter, alignmentFocus: go.Spot.BottomCenter },

        $(
          'Button',
          { name: 'showInfoNode' },
          $(
            go.Picture,
            { width: 20, height: 20 },
            new go.Binding('source', '', () => {
              return getIconPublicPathFromIconName('IconInfoCircle');
            }),
          ),
          new go.Binding('visible', '', (data) => {
            // apply permission show info graph
            if (data?.data?.ciTypeId && data?.data?.ciId) {
              return isCiPermissionAction(Number(data.data.ciTypeId), Number(data.data.ciId), [
                PermissionAction.CI__VIEW_BASIC,
                PermissionAction.CI__VIEW_ADVANCED,
              ]);
            }
            return false;
          }),
        ),
      ),
    );
  }

  return $(
    go.Adornment,
    'Spot',
    $(go.Panel, 'Auto', $(go.Shape, { stroke: 'dodgerblue', strokeWidth: 1, fill: null, visible: true }), $(go.Placeholder)),
    $(
      go.Panel,
      'Horizontal',
      { alignment: go.Spot.BottomCenter, alignmentFocus: go.Spot.BottomCenter },

      $(
        go.Panel,
        $(
          'Button',
          { name: 'showChildNode' },
          $(
            go.Picture,
            { width: 20, height: 20 },
            {
              source: getIconPublicPathFromIconName('IconArrowNarrowDown'),
            },
          ),
        ),
        $(
          go.Panel,
          { background: 'black' },
          { height: 1, width: 35, position: new go.Point(0, 0), angle: 45 },
          new go.Binding('visible', 'showChildLine'), // fix bug icon up/down realtime
          // new go.Binding('visible', '', (data, obj) => {
          //   if (!goDiagram) {
          //     return true;
          //   }
          //   const node = goDiagram.findNodeForKey(obj.part.data.key);
          //   if (!node) {
          //     return true;
          //   }
          //   const connectedLinksCount = node.findLinksOutOf().filter((link: go.Link) => link.visible).count;
          //   return data.totalFrom > connectedLinksCount;
          // }),
        ),
      ),
      $(
        go.Panel,
        $(
          'Button',
          { name: 'showParentNode' },
          $(
            go.Picture,
            { width: 20, height: 20 },
            {
              source: getIconPublicPathFromIconName('IconArrowNarrowUp'),
            },
          ),
        ),
        $(
          go.Panel,
          { background: 'black' },
          { height: 1, width: 35, position: new go.Point(0, 0), angle: 45 },
          new go.Binding('visible', 'showParentLine'),
          // new go.Binding('visible', '', (data, obj) => {
          //   if (!goDiagram) {
          //     return true;
          //   }
          //   const node = goDiagram.findNodeForKey(obj.part.data.key);
          //   if (!node) {
          //     return true;
          //   }
          //   const connectedLinksCount = node.findLinksInto().filter((link: go.Link) => link.visible).count;
          //   return data.totalTo > connectedLinksCount;
          // }),
        ),
      ),
      $(
        'Button',
        { name: 'showInfoNode' },
        $(
          go.Picture,
          { width: 20, height: 20 },
          new go.Binding('source', '', () => {
            return getIconPublicPathFromIconName('IconInfoCircle');
          }),
        ),
        new go.Binding('visible', '', (data) => {
          // apply permission show info graph
          if (data?.data?.ciTypeId && data?.data?.ciId) {
            return isCiPermissionAction(Number(data.data.ciTypeId), Number(data.data.ciId), [
              PermissionAction.CI__VIEW_BASIC,
              PermissionAction.CI__VIEW_ADVANCED,
            ]);
          }
          return false;
        }),
      ),
    ),
  );
};

/**
 * validate Link when create service mapping
 * When A connect to B then block B connect to A
 * @param fromNode
 * @param fromPort
 * @param toNode
 * @param toPort
 * @returns
 */
export function validateLink(fromNode: go.Node, fromPort: go.GraphObject, toNode: go.Node, toPort: go.GraphObject): boolean {
  const diagram = fromNode.diagram;
  if (!diagram || !toPort) {
    return true;
  }
  // Iterate over all links in the diagram
  const links = diagram.links;
  while (links.next()) {
    const link = links.value;

    // Check if there is already a link from 'toNode' to 'fromNode'
    if (link.fromNode === toNode && link.toNode === fromNode) {
      return false; // Do not allow the link to be created
    }
  }

  return true; // Allow the link to be created
}

/**
 * use when enable edit diagram: service mapping
 */
export const linkSelectionAdornmentTemplateWithCaseEdit = $(
  go.Adornment,
  'Link',
  $(go.Shape, { isPanelMain: true, fill: null, stroke: 'deepskyblue', strokeWidth: 0 }),
  $(
    go.Panel,
    'Vertical', // Use Vertical panel to place buttons vertically
    { alignment: go.Spot.Center, alignmentFocus: go.Spot.Center, margin: new go.Margin(0, 10, 0, 0) },
    $(
      go.Panel,
      'Auto',
      { alignment: go.Spot.Top, alignmentFocus: go.Spot.Top, margin: new go.Margin(5) }, // Top-aligned with margin
      $(
        'Button',
        {
          'ButtonBorder.figure': 'Circle',
          width: 20,
          'ButtonBorder.stroke': 'cyan',
          'ButtonBorder.strokeWidth': 1,
          click: (e: GoJs.InputEvent, obj: go.GraphObject) => {
            const adornment = obj.part as go.Adornment;
            const link = adornment.adornedPart as go.Link;
            if (link && link.diagram && e) {
              link.diagram.remove(link);
            }
          },
        },
        $(go.Shape, 'XLine', {
          // Use 'XLine' for delete icon
          fill: 'red',
          stroke: 'darkred',
          width: 12,
          height: 12,
        }),
      ),
    ),
    $(
      go.Panel,
      'Auto',
      { alignment: go.Spot.Bottom, alignmentFocus: go.Spot.Bottom, margin: new go.Margin(5) }, // Bottom-aligned with margin
      $(
        'Button',
        {
          name: 'addCiType',
          'ButtonBorder.figure': 'Circle',
          width: 20,
          'ButtonBorder.stroke': 'cyan',
          'ButtonBorder.strokeWidth': 1,
        },
        $(go.Shape, 'PlusLine', {
          // Use 'PlusLine' for add icon
          fill: 'green',
          stroke: 'darkgreen',
          width: 12,
          height: 12,
        }),
      ),
    ),
  ),
);

/**
 * higtlight all child node when user click menu show highlight node in node
 * //TODO not use transaction because logic recursive and update big node and link.
 * If use transaction it will overfolow memory Undomanage and impact when use other transaction of logic other
 * Unnecessary use transaction because HightLight not use undo/undo but it's not best choice
 *
 */
export const drawHightLightFromNode = (
  goDiagram: GoJs.Diagram | undefined,
  nodeId?: number,
  isUpStream: boolean = true,
  listLinkHighLighted: Set<string> = new Set(),
  isResetAll: boolean = true,
  isFirstRecursive: boolean = true,
) => {
  if (!nodeId || !goDiagram) {
    return;
  }

  const nodeRootHightLight = goDiagram.findNodeForKey(nodeId);
  if (!nodeRootHightLight) {
    resetHightLight(goDiagram);
    return;
  }

  const model = goDiagram.model;
  if (isResetAll) {
    resetHightLight(goDiagram);
  }

  if (isFirstRecursive) {
    goDiagram.undoManager.isEnabled = false;
  }

  model.setDataProperty(nodeRootHightLight.data, 'borderColor', 'red');
  model.setDataProperty(nodeRootHightLight.data, 'opacity', 1);

  const processLinks = (links: go.Iterator<go.Link>) => {
    while (links.next()) {
      const link = links.value;
      const linkKey = `${link.data.from}-${link.data.to}`;
      if (!listLinkHighLighted.has(linkKey)) {
        listLinkHighLighted.add(linkKey);
        model.setDataProperty(link.data, 'style', GRAPH_STYLES.DASH_DOT_HIGHT_LIGHT);
        model.setDataProperty(link.data, 'isHighlighted', true);
        model.setDataProperty(link.data, 'opacity', 1);
        settingVisibleTextOfLink(link, true);

        if (link.fromNode) {
          model.setDataProperty(link.fromNode.data, 'borderColor', 'red');
          model.setDataProperty(link.fromNode.data, 'opacity', 1);
        }

        if (link.toNode) {
          model.setDataProperty(link.toNode.data, 'borderColor', 'red');
          model.setDataProperty(link.toNode.data, 'opacity', 1);
        }

        drawHightLightFromNode(
          goDiagram,
          isUpStream ? link.fromNode?.data.key : link.toNode?.data.key,
          isUpStream,
          listLinkHighLighted,
          false,
          false,
        );
      }
    }
  };

  if (isUpStream) {
    processLinks(nodeRootHightLight.findLinksInto().iterator);
  } else {
    processLinks(nodeRootHightLight.findLinksOutOf().iterator);
  }

  if (isFirstRecursive) {
    goDiagram.links.each((link) => {
      if (!link.data.isHighlighted) {
        model.setDataProperty(link.data, 'opacity', link.data.opacity ?? 0.5);
        if (link.fromNode) {
          model.setDataProperty(link.fromNode.data, 'opacity', link.fromNode.data.opacity ?? 0.5);
        }
        if (link.toNode) {
          model.setDataProperty(link.toNode.data, 'opacity', link.toNode.data.opacity ?? 0.5);
        }
      }
    });
    //goDiagram.commitTransaction(transactionName);
    goDiagram.undoManager.isEnabled = true;
  }
};

/**
 * Rest hightLight when user click button unhightlight
 * @param goDiagram goDiagram
 * @returns
 */
export const resetHightLight = (goDiagram: go.Diagram | undefined) => {
  if (!goDiagram) {
    return;
  }
  console.warn('reset hight');
  goDiagram.undoManager.isEnabled = false;
  const model = goDiagram.model;
  const links = goDiagram.links.iterator;
  while (links.next()) {
    const link = links.value;
    model.setDataProperty(link.data, 'style', undefined);
    // 4244: comment set color
    // model.setDataProperty(link.data, 'color', undefined);
    model.setDataProperty(link.data, 'isHighlighted', false);
    model.setDataProperty(link.data, 'opacity', undefined);
    if (link.fromNode) {
      model.setDataProperty(link.fromNode.data, 'borderColor', undefined);
      model.setDataProperty(link.fromNode.data, 'opacity', undefined);
    }

    if (link.toNode) {
      model.setDataProperty(link.toNode.data, 'borderColor', undefined);
      model.setDataProperty(link.toNode.data, 'opacity', undefined);
    }
  }

  goDiagram.undoManager.isEnabled = true;
};

export const handleHighlightForNode = (node: GoJs.Node) => {
  // highlight all Links and Nodes coming out of a given Node
  const diagram = node.diagram;
  if (!diagram) {
    return;
  }
  const transactionName = 'highlight';
  diagram.startTransaction(transactionName);
  // remove any previous highlighting
  diagram.clearHighlighteds();

  // for each Link coming out of the Node, set Link.isHighlighted
  node.findLinksOutOf().each((l: go.Link) => {
    if (!l.data?.isHighlighted) {
      l.isHighlighted = true;
    }
    settingVisibleTextOfLink(l, true);
  });
  // for each Node destination for the Node, set Node.isHighlighted
  // node.findNodesOutOf().each((n: Record<string, any>) => (n.isHighlighted = true));
  node.findLinksInto().each((n: go.Link) => {
    if (!n.data?.isHighlighted) {
      n.isHighlighted = true;
    }
    settingVisibleTextOfLink(n, true);
  });
  diagram.commitTransaction(transactionName);
};

export const settingVisibleTextOfLink = (link: go.Link, isShow: boolean) => {
  const textBlock = link.findObject('LINK_TEXT');
  if (textBlock) {
    textBlock.visible = isShow;
  }
};

/**
 * Updates the visibility of nodes based on whether they are connected to the root node.
 *
 * - If a node has the `isCiChangePlan` property set to true, it will be visible.
 * - Otherwise, its visibility depends on whether it's connected to the root node.
 *
 * @param goDiagram go.Diagram - The GoJS diagram containing the nodes.
 * @param nodeRoot go.Node - The root node to check connections against.
 * @returns void - The function modifies the visibility of nodes but does not return a value.
 */
export const handleConnectToNode = (goDiagram: go.Diagram, nodeRoot: go.Node) => {
  goDiagram.nodes.each((item) => {
    if (item.data.data.isCiChangePlan) {
      item.visible = true;
    } else {
      item.visible = isConnected(goDiagram, item, nodeRoot);
    }
  });
};

/**
 * Determines the background color of a node based on CI change plans and impacted CI.
 *
 * isNodeFrom = true -> nodeFrom
 * isNodeFrom = false -> nodeTo
 *
 * Colors:
 * - `#e79242fa` for nodes in `ciChangePlans`.
 * - `#FFCECE` for the impacted CI.
 * - `'white'` for other nodes.
 *
 * @param {boolean} isNodeFrom - Whether the node is "from".
 * @param {number[] | undefined} ciChangePlans - List of CI change plans.
 * @param {number | undefined} impactedCi - The impacted CI.
 * @param {number} ciId - The ciId to check.
 * @returns {string | undefined} - The background color.
 */
export const handleBackgroundColor = (isNodeFrom: boolean, ciChangePlans: number[] | undefined, impactedCi: number | undefined, ciId: number) => {
  if (isNodeFrom) {
    return ciChangePlans // Check if ciChangePlans exists
      ? ciChangePlans.includes(ciId) // If ciChangePlans includes ciId, return a specific color : '#e79242fa'
        ? '#e79242fa'
        : impactedCi // If ciChangePlans does not include ciId, check for impactedCi
          ? ciId === impactedCi // If ciId matches impactedCi, return a different color: '#FFCECE'
            ? '#FFCECE'
            : 'white' // Default color for non-matching nodes
          : undefined // If no impactedCi exists, return undefined
      : undefined; // If no ciChangePlans exist, return undefined
  } else {
    return impactedCi // Check if impactedCi exists
      ? ciId === impactedCi // If ciId matches impactedCi, return the color for impacted node: '#FFCECE'
        ? '#FFCECE'
        : ciChangePlans // If ciId does not match impactedCi, check for ciChangePlans
          ? ciChangePlans.includes(ciId) // If ciChangePlans includes ciId, return a different color: '#e79242fa'
            ? '#e79242fa'
            : 'white' // Default color for non-matching nodes
          : undefined // If no ciChangePlans exist, return undefined
      : undefined; // If no impactedCi exists, return undefined
  }
};

/**
 * Returns the appropriate link text based on the CI relationship.
 *
 * If `impactedCi` is defined and a matching rule with direction "IN" is found in `ciRuleRelationships`,
 * it returns the `inverseType`. Otherwise, it returns the `type`.
 *
 * @param isimpact - A boolean indicating whether the relationship is impacted
 * @param data - The relationship data containing `fromCi`, `toCi`, and `ciRuleRelationships`.
 * @param relationShip - The relationship object containing `type` and `inverseType`.
 *
 * @returns {string} - The resulting link text, either the `inverseType` or the `type`.
 */
export const getRelationshipName = (
  isimpact: boolean | undefined,
  data: CiRelationshipInfoWithImpact,
  relationShip: CiRelationshipTypeModel | undefined,
): string => {
  // If the relationship type is not defined, return an empty string.
  if (!relationShip) {
    return ``;
  }

  // If no impactedCi, return the default type
  if (!isimpact) {
    return `${relationShip.type}`;
  }

  // If the relationship is impacted and it's inverse, return the inverse type.
  if (data.inverse) {
    return `${relationShip.inverseType}`;
  }

  // Find the first rule that matches the fromCi, toCi, and direction "IN"
  const matchingRule = data.ciRuleRelationships?.find(
    (rule) => data.fromCi === rule.fromCi && data.toCi === rule.toCi && rule.impactedRuleRelationDirection === CiRelationshipDirectionEnum.IN,
  );

  // If a matching rule is found, return the inverseType, otherwise return the type
  return matchingRule ? `${relationShip.inverseType}` : `${relationShip.type}`;
};
