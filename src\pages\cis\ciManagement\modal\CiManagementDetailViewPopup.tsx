import { KanbanConfirmModal } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import React from 'react';
import { forwardRef, useImperativeHandle } from 'react';
import type { CiManagementResponse } from '@api/CiManagementApi';
import CiManagementDetailPage from '../detail/CiManagementDetailPage';

type CiManagementDetailViewPopupProps = {
  initData?: CiManagementResponse;
};

export type CiManagementDetailViewPopupMethods = {
  openPopupView: () => void;
  closePopupView: () => void;
};

export const CiManagementDetailViewPopup = forwardRef<CiManagementDetailViewPopupMethods, CiManagementDetailViewPopupProps>((props, ref) => {
  const [openedModalView, { close: closeModalView, open: openModalView }] = useDisclosure(false);

  useImperativeHandle<any, CiManagementDetailViewPopupMethods>(
    ref,
    () => ({
      openPopupView: openModalView,
      closePopupView: closeModalView,
    }),
    [openModalView, closeModalView],
  );

  return (
    <>
      <KanbanConfirmModal
        title={''}
        onClose={closeModalView}
        opened={openedModalView}
        modalProps={{
          size: '100%',
        }}>
        <CiManagementDetailPage isView={true} initData={props.initData} />
      </KanbanConfirmModal>
    </>
  );
});
CiManagementDetailViewPopup.displayName = 'CiManagementDetailViewPopup';
export default CiManagementDetailViewPopup;
