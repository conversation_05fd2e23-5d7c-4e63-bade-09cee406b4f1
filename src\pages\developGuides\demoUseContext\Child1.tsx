import React, { useContext } from 'react';
import { DemoContext } from './DemoContext';
import { KanbanButton } from 'kanban-design-system';

const Child1 = () => {
  const context = useContext(DemoContext);

  return (
    <>
      Child1 Render: <br /> {context.value}
      <br />
      <KanbanButton
        onClick={() => {
          context.setValue((prev) => prev + 1);
        }}>
        Increase
      </KanbanButton>
    </>
  );
};
export default Child1;
