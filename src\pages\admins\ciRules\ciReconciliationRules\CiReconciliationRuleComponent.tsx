import { Badge } from '@mantine/core';
import { IconEyeShare } from '@tabler/icons-react';
import { KanbanText, KanbanTooltip, KanbanIconButton } from 'kanban-design-system';
import { SourceDataAction } from '@common/constants/SourceDataActionEnum';
import { buildSourceDataUrl, createOrUpdateDataSourcePath } from '@common/utils/RouterUtils';
import { DiscoverySourceDataModel } from '@models/discovery/DiscoverySourceData';
import React from 'react';
import { DATA_SOURCE_ANY, DATA_SOURCE_MANUAL, RuleStatus } from '@models/CiReconciliationRule';

export const renderDiscoverySourceDataName = (data: string, discoverySourceData: DiscoverySourceDataModel[]): React.ReactNode => {
  if (!data) {
    return (
      <Badge variant='light' color='indigo' radius='sm' mr='5'>
        <KanbanText fw={500} size='sm' tt='initial'>
          {DATA_SOURCE_ANY.name}
        </KanbanText>
      </Badge>
    );
  }

  const discoverySource = discoverySourceData.find((item) => item.id === Number(data));

  if (!discoverySource) {
    return '';
  }

  const badgeColor = discoverySource.deleted ? 'red' : 'indigo';
  const badgeText = discoverySource.deleted ? `${discoverySource.name} - (Deleted)` : discoverySource.name;

  return (
    <>
      <Badge variant='light' color={badgeColor} radius='sm' mr='5'>
        <KanbanText fw={500} size='sm' tt='initial'>
          {badgeText}
        </KanbanText>
      </Badge>

      {!discoverySource.deleted && discoverySource.id !== DATA_SOURCE_MANUAL.id && (
        <KanbanTooltip label='View Data Sources'>
          <KanbanIconButton
            variant='transparent'
            size='sm'
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              window.open(buildSourceDataUrl(discoverySource.id || 0, createOrUpdateDataSourcePath, SourceDataAction.VIEW), '_blank');
            }}>
            <IconEyeShare />
          </KanbanIconButton>
        </KanbanTooltip>
      )}
    </>
  );
};

export const renderEntryName = (defaultEntry: boolean, nameEntry: string): React.ReactNode => {
  if (defaultEntry) {
    return (
      <Badge variant='light' color='grape' radius='sm' mr='5'>
        <KanbanText fw={500} size='sm' tt='initial'>
          {nameEntry}
        </KanbanText>
      </Badge>
    );
  }

  return nameEntry;
};

export const getDiscoverySourceDataName = (data: number, discoverySourceData: DiscoverySourceDataModel[]): string => {
  if (!data) {
    return 'Any';
  }
  const discoverySource = discoverySourceData.find((item) => item.id === data);
  return discoverySource ? (discoverySource.deleted ? `${discoverySource.name} - (Deleted)` : discoverySource.name) : '';
};

export const renderActive = (value: any): string => (RuleStatus.ENABLE === value ? 'Active' : 'Inactive');

export const renderValueOrNone = (value: any) => (value !== null && value !== undefined && value !== '' ? value : '(none)');
