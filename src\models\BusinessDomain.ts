export type BusinessDomainModel = BusinessDomainItem & {
  listSubDomain?: BusinessDomainItem[];
};
export type BusinessDomainItem = {
  id: number;
  name?: string;
  description?: string;
  levelLeaf?: number;
  parentId?: number;
  approvedBy?: string;
  approvedDate?: Date;
  createdBy?: string;
  author?: string;
  createdDate?: Date;
};

export type BusinessDomainDetailModel = {
  treeContents?: BusinessDomainItem[];
  content: BusinessDomainItem & {
    isLastChild: boolean;
    parents?: BusinessDomainItem[];
  };
};

export type BusinessApplicationModel = {
  id: number;
  name?: string;
  businessAttributeValue?: string;
};

export type BusinessFunctionModel = {
  contents: BusinessApplicationModel[];
};

export type BusinessDomainAggregateItem = {
  id: number;
  name?: string;
  description?: string;
  ciTypeName?: string;
  ciTypeEnum?: string;
  parentId?: number;
  parentName?: string;
};
