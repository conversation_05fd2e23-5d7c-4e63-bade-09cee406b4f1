import { FormatTypeEnum } from '@common/constants/FormatTypeEnum';
import type { EntityModelBase } from '@models/EntityModelBase';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export type DiscoverySourceDataModel = EntityModelBase & {
  name: string;
  formatType: FormatTypeEnum;
  sourceId: number;
  discoveryStagingId: number;
  selectedFields?: string[];
  discoveryStagingStructureJson?: string;
  jsonStatic?: JSONNode;
  deleted?: boolean;
};

export type DiscoverySourceDataResponse = DiscoverySourceDataModel & {
  discoveryStagingName?: string;
  discoverySourceName?: string;
};
