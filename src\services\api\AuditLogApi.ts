import { BaseApi } from '@core/api/BaseApi';
import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
import { AuditLogModel, ColumnFilterStateWithType, EntityAuditLogTypeEnum } from '@models/AuditLog';
import { BaseUrl } from '@core/api/BaseUrl';

export type PaginationRequestMultiModel<T = unknown> = PaginationRequestModel<T> & {
  columnFilters?: ColumnFilterStateWithType[];
};
export class AuditLogApi extends BaseApi {
  static baseUrl = BaseUrl.auditLogs;
  static findByTypeAndEntityId(type: EntityAuditLogTypeEnum, entityId: number, pagination: PaginationRequestMultiModel<AuditLogModel>) {
    return BaseApi.postData<PaginationResponseModel<AuditLogModel>>(`${this.baseUrl}?type=${type}&entityId=${entityId}`, pagination);
  }
  //delete

  //add
  static createIndex() {
    return BaseApi.postData<boolean>(`${this.baseUrl}/index`);
  }

  //update

  //find by id
}
