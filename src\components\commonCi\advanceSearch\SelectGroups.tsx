import { useEffect, useMemo, useState } from 'react';
import { <PERSON>ge, CloseButton, Combobox, Container, Flex, InputBase, ScrollArea, useCombobox } from '@mantine/core';
import React from 'react';
import { KanbanText, KanbanTooltip } from 'kanban-design-system';

export type SelectItemProps = {
  name: string;
  value: string;
  description?: string;
  tag?: string;
};

export type SelectGroupProps = {
  label: string;
  items: SelectItemProps[];
};

type SelectGroupListProps = {
  datas: SelectGroupProps[];
  value?: string;
  placeholder?: string;
  label?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
};

export const SelectGroups = (props: SelectGroupListProps) => {
  const { datas, disabled, value } = props;
  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
  });

  const allItems: SelectItemProps[] = useMemo(() => {
    return datas.reduce<SelectItemProps[]>((acc, group) => {
      return [...acc, ...group.items];
    }, []);
  }, [datas]);

  const [search, setSearch] = useState('');

  useEffect(() => {
    const itemSelect = allItems.find((x) => x.value === value);
    setSearch(itemSelect?.name || '');
  }, [allItems, value]);

  const filteredGroups = useMemo(() => {
    return datas.map((group) => {
      const filteredOptions = group.items.filter((item) => item.name.toLowerCase().includes(search.toLowerCase().trim()));

      return { ...group, items: filteredOptions };
    });
  }, [datas, search]);

  const totalOptions = useMemo(() => {
    return filteredGroups.reduce((acc, group) => acc + group.items.length, 0);
  }, [filteredGroups]);

  const groups = useMemo(() => {
    return filteredGroups.map((group) => {
      const options = group.items.map((item) => (
        <Combobox.Option value={item.value} key={item.value}>
          <>
            <Container>
              <Flex justify='space-between' align='center'>
                <KanbanTooltip
                  bd={'1px solid rgba(0, 119, 255, 0.8)'}
                  bg={'white'}
                  fs={'italic'}
                  c={'var(--mantine-color-blue-4)'}
                  label={item.name}
                  style={{ flexShrink: 0, wordBreak: 'break-all' }}
                  maw={'450px'}
                  multiline>
                  <KanbanText size='sm' tt='initial' lineClamp={2} maw={'250px'}>
                    {item.name}
                  </KanbanText>
                </KanbanTooltip>

                {item.tag && (
                  <KanbanTooltip
                    bd='1px solid rgba(114, 174, 243, 0.8)'
                    bg='white'
                    fs='italic'
                    c='var(--mantine-color-blue-4)'
                    multiline
                    label={item.tag}>
                    <Badge variant='light' color='blue' radius='sm' mr={5}>
                      <KanbanText truncate fw={500} size='sm' tt='initial' style={{ maxWidth: '120px' }}>
                        {item.tag}
                      </KanbanText>
                    </Badge>
                  </KanbanTooltip>
                )}
              </Flex>

              <KanbanText c='dimmed' size='xs'>
                {item.description}
              </KanbanText>
            </Container>
          </>
        </Combobox.Option>
      ));

      return (
        <Combobox.Group label={group.label} key={group.label}>
          {options}
        </Combobox.Group>
      );
    });
  }, [filteredGroups]);

  return (
    <Combobox
      store={combobox}
      withinPortal={true}
      onOptionSubmit={(val) => {
        if (props.onChange) {
          props.onChange(val);
        }
        const itemSelect = allItems.find((x) => x.value === val);
        setSearch(itemSelect?.name || '');
        combobox.closeDropdown();
      }}>
      <Combobox.Target>
        <InputBase
          disabled={disabled}
          // rightSection={<Combobox.Chevron />}
          rightSection={
            !value || disabled ? (
              <Combobox.Chevron />
            ) : (
              <CloseButton
                size='sm'
                onMouseDown={(event) => event.preventDefault()}
                onClick={() => {
                  if (props.onChange) {
                    props.onChange('');
                  }
                  setSearch('');
                }}
                title='Clear value'
              />
            )
          }
          value={search}
          onChange={(event) => {
            combobox.openDropdown();
            combobox.updateSelectedOptionIndex();
            setSearch(event.currentTarget.value);
          }}
          onClick={() => combobox.openDropdown()}
          onFocus={() => combobox.openDropdown()}
          onBlur={() => {
            combobox.closeDropdown();
            const itemSelect = allItems.find((x) => x.value === value);
            setSearch(itemSelect?.name || '');
            // setSearch(value || '');
          }}
          placeholder={props.placeholder || 'Search value'}
          label={props.label}
          rightSectionPointerEvents={'all'}
        />
      </Combobox.Target>

      <Combobox.Dropdown>
        <Combobox.Options>
          <ScrollArea.Autosize type='scroll' mah={200}>
            {totalOptions > 0 ? groups : <Combobox.Empty>Nothing found</Combobox.Empty>}
          </ScrollArea.Autosize>
        </Combobox.Options>
      </Combobox.Dropdown>
    </Combobox>
  );
};
