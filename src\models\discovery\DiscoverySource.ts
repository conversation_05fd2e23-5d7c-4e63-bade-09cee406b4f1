import { LastDiscoveryStatusEnum } from '@common/constants/DiscoverySourceConfigEnum';
import type { EntityModelBase } from '@models/EntityModelBase';
import { SourceConfigBasicInfo } from './DiscoverySourceConfig';

export type DiscoverySourceModel = EntityModelBase &
  SourceConfigBasicInfo & {
    id: number;
    parentId?: number;
    configJson?: string;
    lastDiscoveryTime?: Date;
    lastDiscoveryStatus?: LastDiscoveryStatusEnum;
  };

export type DiscoverySourceResponse = DiscoverySourceModel;
