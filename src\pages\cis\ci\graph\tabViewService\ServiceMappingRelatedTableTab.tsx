import type { ServiceMapCiImpactModel } from '@models/ServiceMapping';
import React, { useMemo } from 'react';
import { KanbanText } from 'kanban-design-system';
import { renderDateTime } from 'kanban-design-system';
import { ColumnType, KanbanTable } from 'kanban-design-system';

type ViewServiceMappingProps = {
  listImpact?: ServiceMapCiImpactModel[];
};

export const ServiceMappingRelatedTableTab = (props: ViewServiceMappingProps) => {
  const { listImpact } = props;
  const columns: ColumnType<ServiceMapCiImpactModel>[] = useMemo(() => {
    return [
      {
        title: 'Name',
        name: 'ciName',
        customRender: (data) => {
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {data}
            </KanbanText>
          );
        },
        width: '20%',
      },

      {
        title: 'Description',
        name: 'description',
        customRender: (data) => {
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {data}
            </KanbanText>
          );
        },
        width: '30%',
      },
      {
        title: 'Ci Type',
        name: 'ciTypeName',
        customRender: (data) => {
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {data}
            </KanbanText>
          );
        },
      },
      {
        title: 'Create By',
        name: 'createdBy',
        customRender: (data) => {
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {data}
            </KanbanText>
          );
        },
      },
      {
        title: 'Create Date',
        name: 'createdDate',
        customRender: renderDateTime,
      },
    ];
  }, []);

  return (
    <>
      <div style={{ flex: 2 }}>
        {listImpact && (
          <KanbanTable
            columns={columns}
            searchable={{
              enable: true,
            }}
            key={1}
            data={listImpact}
            pagination={{
              enable: true,
            }}
          />
        )}
      </div>
    </>
  );
};

ServiceMappingRelatedTableTab.displayName = 'ServiceMappingRelatedTableTab';
export default ServiceMappingRelatedTableTab;
