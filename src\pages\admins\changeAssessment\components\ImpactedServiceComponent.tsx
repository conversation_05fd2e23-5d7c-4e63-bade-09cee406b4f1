import { Box, Flex } from '@mantine/core';
import { ImpactedChangeTableComponent } from './ImpactedChangeTableComponent';
import React from 'react';
import { ImpactedChangeProps, ImpactedTypeTable } from '@models/ChangeAssessment';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { IconPlus, IconSettingsAutomation } from '@tabler/icons-react';
import type { ConfigItemResponse } from '@api/ConfigItemApi';

export type ImpactedServiceComponentProps = ImpactedChangeProps & {
  onProcessAdd: boolean;
  onAddCi: (val: boolean) => void;
  onDeleteCi: (val: ConfigItemResponse[]) => void;
  calculateImpactedServices: () => void;
  allowEdit: boolean;
};

export const ImpactedServiceComponent: React.FC<ImpactedServiceComponentProps> = ({
  allowEdit,
  calculateImpactedServices,
  cis,
  onAddCi,
  onDeleteCi,
  onProcessAdd,
}) => {
  return (
    <Box>
      <Box mt='sm'>
        <HeaderTitleComponent
          title={'List Impacted Services'}
          rightSection={
            <>
              {allowEdit && (
                <Flex gap='xs'>
                  <KanbanButton onClick={calculateImpactedServices} variant={'light'} leftSection={<IconSettingsAutomation />}>
                    Calculate Impacted Services
                  </KanbanButton>
                  <KanbanButton
                    leftSection={<IconPlus />}
                    disabled={onProcessAdd}
                    onClick={() => {
                      onAddCi(true);
                    }}>
                    Add Impacted Services
                  </KanbanButton>
                </Flex>
              )}
            </>
          }
        />
      </Box>
      <ImpactedChangeTableComponent
        typeTable={ImpactedTypeTable.SERVICE}
        cis={cis}
        onDelete={onDeleteCi}
        allowEdit={allowEdit}></ImpactedChangeTableComponent>
    </Box>
  );
};
