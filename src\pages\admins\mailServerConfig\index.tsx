import React, { useCallback, useEffect, useState } from 'react';
import { KanbanRadio, KanbanButton, KanbanSelect, KanbanInput, KanbanLoading, KanbanText } from 'kanban-design-system';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { OutgoingMailConfigApi } from '@api/OutgoingMailConfigApi';
import { NotificationError, NotificationSuccess } from '@common/utils/NotificationUtils';
// import type { OutgoingMailConfigDto, ProxyDto } from '@models/OutgoingMail';
import type { OutgoingMailConfigDto } from '@models/OutgoingMail';

import {
  AUTHENTICATION_TYPE_ENUM,
  AUTHENTYPE_LIST,
  CONNECTION_PROTOCOL_ENUM,
  CONNECTION_PROTOCOL_LIST,
} from '@common/constants/OutgoingMailConstants';
import { isBlank } from '@common/utils/StringUtils';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { SimpleGrid } from '@mantine/core';
import { MailServiceBasicComponent } from './components/MailServiceBasicComponent';
import { BreadcrumbComponent } from '../breadcrumb/BreadcrumbComponent';
// import GuardComponent from '@components/GuardComponent';

function checkRequiredFieldsMailService(config: OutgoingMailConfigDto): boolean {
  // Check if required fields are not empty or null
  if (isBlank(config.serverName)) {
    return false;
  }

  // If authentication is enabled, check mailUsername and mailPassword
  if (config.authenticationEnabled) {
    //case update : enable authen + type ******** -> bug still enable SAVE  button
    // case update dont change any thing => password =  ******** , when save BE catch this
    if (isBlank(config.mailUsername) || isBlank(config.mailPassword)) {
      return false;
    }
  }

  return true;
}

const validateRequireConfigData = (input: OutgoingMailConfigDto) => {
  if (!input.authenticationType) {
    return false;
  }

  if (CONNECTION_PROTOCOL_ENUM.MAIL_SERVICE === input.connectionProtocol && AUTHENTICATION_TYPE_ENUM.BASIC === input.authenticationType) {
    return checkRequiredFieldsMailService(input);
  }
  return true;
};

const OutgoingMailPage: React.FC = () => {
  const [testEmailAddress, setTestEmailAddress] = useState('');
  //null hold data that not yet have res from send test mail
  const [testEmailResult, setTestEmailResult] = useState<boolean | null>(null);
  const [isSendingTest, setIsSendingTest] = useState<boolean>(false);
  const [enableSaveBtn, setEnableSaveBtn] = useState(false);

  const [config, setConfig] = useState<OutgoingMailConfigDto>({});
  const fetchConfig = useCallback(() => {
    OutgoingMailConfigApi.getOutgoingMailConfig()
      .then((res) => {
        const data = res.data;
        if (data.id) {
          setConfig(data);
        }
      })
      .catch(() => {});
  }, []);

  //handle reset on connection protocol change
  useEffect(() => {
    fetchConfig();
  }, [fetchConfig]);

  const handleSave = () => {
    OutgoingMailConfigApi.saveOrUpdateOutgoingMailConfig(config)
      .then(() => {
        NotificationSuccess({
          message: 'Saved successfully',
        });
        fetchConfig();
        setTestEmailResult(null);
      })
      .catch(() => {});
  };
  const handleOnChangeConnectionProtocol = (val: string) => {
    //reset data when change connection protocol
    const resetData: OutgoingMailConfigDto = { connectionProtocol: val };
    setConfig({ ...resetData });
  };
  const handleChangeAuthenticationType = (val?: string | null) => {
    //reset data when change connection protocol

    const resetData: OutgoingMailConfigDto = { connectionProtocol: config.connectionProtocol, authenticationType: val ? val : undefined };
    setConfig((prev) => ({ ...prev, ...resetData }));
  };

  const handleSendTestEmail = async () => {
    setIsSendingTest(true);
    setTestEmailResult(null);
    if (!testEmailAddress || testEmailAddress.trim() === '') {
      NotificationError({
        title: 'Error',
        message: 'Please input mail receiver address',
      });
      return;
    }
    await OutgoingMailConfigApi.sendTest(testEmailAddress, config)
      .then((res) => {
        if (res.data) {
          setTestEmailResult(true); // or 'Fail' based on the result
        }
      })
      .catch(() => {
        setTestEmailResult(false);
      })
      .finally(() => {
        setIsSendingTest((prev) => !prev);
      });
  };

  useEffect(() => {
    setEnableSaveBtn(validateRequireConfigData(config));
  }, [config]);
  //
  return (
    <div>
      {/* 4736 outgoing mails page */}
      <BreadcrumbComponent />
      <HeaderTitleComponent
        title='Outgoing Mail Configurations'
        rightSection={
          <GuardComponent requirePermissions={[AclPermission.saveOutGoingMailConfig]}>
            <KanbanButton onClick={handleSave} disabled={!enableSaveBtn}>
              SAVE
            </KanbanButton>
          </GuardComponent>
        }></HeaderTitleComponent>
      <KanbanRadio
        group={{
          name: 'connection',
          label: 'Connection Protocol',
          mb: 'lg',
          withAsterisk: false,
          value: config.connectionProtocol,
          onChange: (val) => {
            handleOnChangeConnectionProtocol(val);
          },
          required: true,
        }}
        radios={CONNECTION_PROTOCOL_LIST}
      />
      {config.connectionProtocol && (
        <>
          <KanbanSelect
            label='Authentication Type'
            placeholder='Choose authentication type'
            w={'250px'}
            allowDeselect={false}
            value={config.authenticationType}
            mb={0}
            data={AUTHENTYPE_LIST}
            onChange={(val) => {
              handleChangeAuthenticationType(val);
            }}
            required={true}
          />

          <br />
          {config.authenticationType && (
            <>
              {CONNECTION_PROTOCOL_ENUM.MAIL_SERVICE === config.connectionProtocol &&
                AUTHENTICATION_TYPE_ENUM.BASIC === config.authenticationType && <MailServiceBasicComponent info={config} setInfo={setConfig} />}
              <SimpleGrid cols={2}>
                <KanbanInput
                  type='email'
                  placeholder='Receiver mail address'
                  label='Check mail server connectivity'
                  value={testEmailAddress}
                  onChange={(e) => setTestEmailAddress(e.target.value)}
                />
              </SimpleGrid>
              <GuardComponent requirePermissions={[AclPermission.testOutGoingMailConfig]}>
                <KanbanButton
                  disabled={!testEmailAddress || testEmailAddress.trim() === ''}
                  variant={'outline'}
                  onClick={handleSendTestEmail}
                  leftSection={isSendingTest && <KanbanLoading color='primary' type='dots' />}>
                  Send a test mail
                </KanbanButton>
              </GuardComponent>
              {testEmailResult && <KanbanText c={'green'}>Success - Send test mail successfully with input config!</KanbanText>}
              {testEmailResult !== null && testEmailResult === false && (
                <KanbanText c={'red'}>Fail - send test mail failed with input config!</KanbanText>
              )}
            </>
          )}
        </>
      )}
    </div>
  );
};

export default OutgoingMailPage;
