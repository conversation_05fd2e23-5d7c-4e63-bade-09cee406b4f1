// This JSON file configures the eslint plugin. It supports comments as well as per the JSON5 spec
{
    "root": true,
    "parser": "@typescript-eslint/parser",
    "plugins": ["react", "@typescript-eslint", "prettier", "react-hooks","sort-destructure-keys", "cypress"],
    "extends": [
      "plugin:react/recommended", // Uses the recommended rules from @eslint-plugin-react
      "plugin:@typescript-eslint/recommended",
      "plugin:cypress/recommended",
      // Note: Please keep this as the last config to make sure that this (and by extension our .prettierrc file) overrides all configuration above it
      // https://www.npmjs.com/package/eslint-plugin-prettier#recommended-configuration
      "plugin:prettier/recommended"
    ],
    "parserOptions": {
      "ecmaVersion": 2020, // Allows for the parsing of modern ECMAScript features
      "sourceType": "module", // Allows for the use of imports
      "ecmaFeatures": {
        "jsx": true // Allows for the parsing of JSX
      }
    },
    "rules": {
      "@typescript-eslint/no-empty-function": 0,
      "@typescript-eslint/no-explicit-any": 0,
      "react-hooks/rules-of-hooks": "error",
      "@typescript-eslint/no-use-before-define": 0,
      "@typescript-eslint/no-var-requires": 0,
      "import/no-webpack-loader-syntax": 0,
      "no-undef": 0,
      "react/prop-types": 0,
      "react/display-name": 0,
      "@typescript-eslint/explicit-module-boundary-types": 0,
      "cypress/no-unnecessary-waiting": 0,
      "cypress/no-assigning-return-values": 0,
      "react/jsx-boolean-value": "off", //error
      "react/self-closing-comp": "off", //error
      "react/jsx-sort-props": "off", //error
      "react/jsx-fragments": "off", //error
      "@typescript-eslint/no-inferrable-types": "off",
      "react/no-unknown-property": 0,
      "react/jsx-no-useless-fragment": "off",
      "sort-destructure-keys/sort-destructure-keys": ["warn", {"caseSensitive": false}],
      "no-console": "warn",
      "no-debugger": "warn",
      // "no-unused-vars": ["warn", {
      //   "argsIgnorePattern": "^_",
      //   "varsIgnorePattern": "^_"
      // }],
      // "@typescript-eslint/no-unused-vars": ["warn", {
      //   "argsIgnorePattern": "^_",
      //   "varsIgnorePattern": "^_"
      // }],
  
      //"prettier/prettier": ["error", { "endOfLine": "auto" }]
      "prettier/prettier": 0
    },
    "settings": {
      "import/resolver": {
        "babel-module": {}
      },
      "react": {
        "pragma": "React",
        // Tells eslint-plugin-react to automatically detect the version of React to use
        "version": "detect"
      }
    },
    "env": {
      "browser": true,
      "node": true,
      "cypress/globals": true,
      "worker": true
    }
  }
  