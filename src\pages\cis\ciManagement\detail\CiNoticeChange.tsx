import type { ConfigItemTypeAttrResponse } from '@api/ConfigItemTypeAttrApi';
import { KanbanText } from 'kanban-design-system';
import { Alert, Group, Mark } from '@mantine/core';
import type { DataCiCompare } from '@models/CiDetailCompare';
import { IconInfoSquare } from '@tabler/icons-react';
import React, { useMemo } from 'react';
import { CiTypeAttributeDataType } from '@models/CiType';

type CiNoticeChangeProps = {
  differenceObject: DataCiCompare;
  listCiTypeAttribute: ConfigItemTypeAttrResponse[];
};

export type DataChangeObj = {
  key: string;
  oldValue?: string;
  newValue?: string | number;
};

export const formatValueCiAttribute = (value: string | number | null, objAttribute: ConfigItemTypeAttrResponse | undefined): string => {
  if (objAttribute && CiTypeAttributeDataType.REFERENCE === objAttribute.type) {
    const objRefer = objAttribute.ciTypeReferenceData?.find((x) => `${x.ciId}` === `${value}`);
    return objRefer ? `${objRefer.ciName}[${objRefer.ciAttributeValue || 'Empty value'}]` : 'null';
  }
  return String(value);
};

export const CiNoticeChange = (props: CiNoticeChangeProps) => {
  const { differenceObject, listCiTypeAttribute } = props;
  const generateKeyValueString = useMemo(() => {
    const result: DataChangeObj[] = [];
    const data = { ...differenceObject };

    // Iterate through "ci" object
    for (const key in data.ci) {
      const oldValue = data.ci[key].oldValue || 'null';
      const newValue = data.ci[key].newValue || 'null';
      result.push({ key: key, oldValue: oldValue, newValue: newValue });
    }

    // Iterate through "attributes" object
    for (const key in data.attributes) {
      const attribute = data.attributes[key];
      const objAttribute = listCiTypeAttribute.find((x) => String(x.id) === key);
      const keyName = objAttribute ? objAttribute.name : key;

      const oldValue = String(attribute.oldValue);
      const valueStrOld = formatValueCiAttribute(oldValue, objAttribute);
      const newValue = String(attribute.newValue);
      const valueStrNew = formatValueCiAttribute(newValue, objAttribute);
      result.push({ key: keyName, oldValue: valueStrOld, newValue: valueStrNew });
    }

    // Iterate through "attributes custom" object
    for (const key in data.attributesCustom) {
      const attribute = data.attributesCustom[key];
      const keyName = key;

      const valueStr = String(attribute.oldValue);
      const newValue = String(attribute.newValue);
      result.push({ key: keyName, oldValue: valueStr, newValue: newValue });
    }

    return result;
  }, [differenceObject, listCiTypeAttribute]);

  return (
    <>
      <Alert my={'20'} variant='light' color='yellow' title={'CI data change'} icon={<IconInfoSquare />}>
        <KanbanText ml={8} style={{ whiteSpace: 'break-spaces' }}>
          {generateKeyValueString.length > 0 ? (
            generateKeyValueString.map((item) => (
              <Group key={item.key}>
                -{' '}
                <KanbanText fw={700} w={'200px'} truncate='end'>
                  {item.key}
                </KanbanText>
                :{' '}
                <KanbanText td={'line-through'} lineClamp={2} maw={'35%'}>
                  <Mark color='red'>{item.oldValue}</Mark>
                </KanbanText>
                {item.newValue && (
                  <>
                    {` -> `}
                    <KanbanText lineClamp={2} maw={'35%'}>
                      <Mark color='green'>{item.newValue}</Mark>
                    </KanbanText>
                  </>
                )}
                <br />
              </Group>
            ))
          ) : (
            <KanbanText>No data change</KanbanText>
          )}
        </KanbanText>
      </Alert>
    </>
  );
};

export default CiNoticeChange;
