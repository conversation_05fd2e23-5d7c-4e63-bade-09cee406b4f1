import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import type { ChangeAssessmentAction } from '@models/ChangeAssessment';
import type { ChangeResponseDto, CiSdpRequestModel } from '@models/ChangeSdp';
import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
export type ChangeRequestsPagingResponse = PaginationResponseModel<CiSdpRequestModel>;

export class ChangeSdpApi extends BaseApi {
  static baseUrl = BaseUrl.changes;

  /**
   *
   * @param id Id change
   * @param action
   * @param sdpRequestMappingId : case check change of existed sdpRequestMappingId
   * @param showInvalid : input true: for case press btn SDP link, get change data despite of validate failed, false: for other case
   * @returns Change data
   */
  static getById(id: number, action: ChangeAssessmentAction, sdpRequestMappingId: number | undefined, showInvalid: boolean) {
    return BaseApi.getData<ChangeResponseDto>(
      `${this.baseUrl}/${id}?action=${action}&sdpRequestMappingId=${sdpRequestMappingId}&showInvalid=${showInvalid}`,
    );
  }
  static getAllByCiId(id: number, pagination: PaginationRequestModel<CiSdpRequestModel>, useLoading: boolean) {
    return BaseApi.getData<ChangeRequestsPagingResponse>(
      `${this.baseUrl}`,
      { id, ...pagination },
      {},
      {
        useLoading: useLoading,
        useErrorNotification: true,
      },
    );
  }
  static getStatusByChangeIdIn(changeIds: number[], useLoading: boolean) {
    return BaseApi.getData<CiSdpRequestModel[]>(
      `${this.baseUrl}/status`,
      { changeIds },
      {},
      {
        useLoading: useLoading,
        useErrorNotification: true,
      },
    );
  }
}
