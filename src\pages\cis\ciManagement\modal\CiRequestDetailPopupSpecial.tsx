import { useDisclosure } from '@mantine/hooks';
import React, { useCallback, useEffect, useState } from 'react';
import { forwardRef, useImperativeHandle } from 'react';
import { ActionType } from '@common/constants/CiManagement';
import CiRequestDetail from './CiRequestDetail';
import { CiManagementApi, type CiManagementResponse } from '@api/CiManagementApi';
import { KanbanConfirmModal } from 'kanban-design-system';

type CiRequestDetailPopupSpecialProps = {
  viewId: number;
  screenAction: ActionType;
  isFromDetail?: boolean;
  onCloseModal?: () => void;
};

export type CiRequestDetailPopupSpecialMethods = {
  openPopupReview: () => void;
  closePopupReview: () => void;
};

export const CiRequestDetailPopupSpecial = forwardRef<CiRequestDetailPopupSpecialMethods, CiRequestDetailPopupSpecialProps>((props, ref) => {
  const { isFromDetail, onCloseModal, screenAction, viewId } = props;
  const [openedModalReview, { close: closeModalReview, open: openModalReview }] = useDisclosure(false);
  const [listData, setListData] = useState<CiManagementResponse[]>([]);
  const [actionType, setActionType] = useState<ActionType>(ActionType.SEND);

  useImperativeHandle<any, CiRequestDetailPopupSpecialMethods>(
    ref,
    () => ({
      openPopupReview: openModalReview,
      closePopupReview: closeModalReview,
    }),
    [openModalReview, closeModalReview],
  );

  const fetchDataView = useCallback(() => {
    if (viewId) {
      CiManagementApi.getById(Number(viewId))
        .then((res) => {
          setListData([res.data]);
        })
        .catch(() => {});
    }
  }, [viewId]);

  useEffect(() => {
    fetchDataView();
  }, [fetchDataView]);

  const executeLogicWhenCloseModalReview = () => {
    closeModalReview();
    if (onCloseModal) {
      onCloseModal();
    }
  };

  const titleModalReview = screenAction === ActionType.SEND ? 'Send request for approval' : 'CI(s) approval information';

  return (
    <>
      <KanbanConfirmModal
        title={titleModalReview}
        textConfirm=''
        onClose={closeModalReview}
        opened={openedModalReview}
        modalProps={{
          size: '100%',
        }}>
        <CiRequestDetail
          listData={listData}
          screenAction={screenAction}
          onCloseModal={executeLogicWhenCloseModalReview}
          isFromDetail={isFromDetail}
          actionType={actionType}
          setActionType={setActionType}
        />
      </KanbanConfirmModal>
    </>
  );
});
CiRequestDetailPopupSpecial.displayName = 'CiRequestDetailPopupSpecial';
export default CiRequestDetailPopupSpecial;
