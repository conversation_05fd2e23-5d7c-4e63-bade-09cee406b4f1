import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import equal from 'fast-deep-equal';
import { Box, Chip, Flex, Grid } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanAccordion, KanbanAccordionData } from 'kanban-design-system';
import { SysPermissionOthersApi } from '@api/SysPermissionOthersApi';
import { PermissionActionType } from '@common/constants/PermissionActionType';
import { KanbanTable, ColumnType, TableAffactedSafeType } from 'kanban-design-system';
import type { PermissionOtherModel, RoleModel } from '@models/Role';
import { RolesApi } from '@api/systems/RolesApi';
import { PermissionCiOrCiTypeRoleResponse, SysPermissionCisApi } from '@api/SysPermissionCisApi';
import { tableAffectedToPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import GuardComponent from '@components/GuardComponent';
import { IconCopy, IconEdit, IconTrash } from '@tabler/icons-react';
import { buildCreateOrUpdateRoleUrl, buildRoleUrl, navigateTo, roleDetailPath, roleViewListPath } from '@common/utils/RouterUtils';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { KanbanTitle, KanbanText, KanbanButton, KanbanConfirmModal } from 'kanban-design-system';
import { AclPermission } from '@models/AclPermission';
import { RoleAction } from '@common/constants/RoleActionEnum';
import { BreadcrumbComponent, UrlBaseCrumbData } from '../breadcrumb/BreadcrumbComponent';

type ActionGroupProps = {
  mainAction?: string;
  subActions: string[];
};

type PermissionGrouped = {
  [key: string]: {
    [key: string]: string[];
  };
};

const groupByTypeAndAction = (permissions: PermissionOtherModel[]): PermissionGrouped => {
  return permissions.reduce((acc, { action, type }) => {
    if (!acc[type]) {
      acc[type] = {};
    }
    const mainAction = action.split('__')[0].replace('_', ' ');
    if (!acc[type][mainAction]) {
      acc[type][mainAction] = [];
    }
    acc[type][mainAction].push(action);
    return acc;
  }, {} as PermissionGrouped);
};

const ActionGroup: React.FC<ActionGroupProps> = ({ mainAction, subActions }) => (
  <Box key={mainAction}>
    <KanbanTitle order={5} c={TITLE_COLOR}>
      {mainAction}
    </KanbanTitle>
    <SubActionGroup subActions={subActions} />
  </Box>
);
const SubActionGroup: React.FC<ActionGroupProps> = ({ subActions }) => (
  <Flex gap='xs' wrap='wrap'>
    {subActions.map((subAction) => (
      <Chip key={subAction} checked>
        {subAction}
      </Chip>
    ))}
  </Flex>
);
// const BasicInformationRole: React.FC<RoleModel> = ({ description, name }) => (
//   <Box>
//     <Flex gap='xl'>
//       <KanbanText w={'15%'} fw={500}>
//         {'Role name'}
//       </KanbanText>
//       <KanbanText>{name}</KanbanText>
//     </Flex>
//     <Flex gap='xl'>
//       <KanbanText w={'15%'} fw={500}>
//         {'Role description'}
//       </KanbanText>
//       <KanbanText>{description}</KanbanText>
//     </Flex>
//   </Box>
// );

const BasicInformationRole: React.FC<RoleModel> = ({ description, name }) => (
  <Box>
    <Grid>
      <Grid.Col span={1}>
        <KanbanText fw={500}>Role name</KanbanText>
      </Grid.Col>
      <Grid.Col span='auto'>
        <KanbanText>{name}</KanbanText>
      </Grid.Col>
    </Grid>

    <Grid>
      <Grid.Col span={1}>
        <KanbanText fw={500}>Role description</KanbanText>
      </Grid.Col>
      <Grid.Col span='auto'>
        <KanbanText>{description}</KanbanText>
      </Grid.Col>
    </Grid>
  </Box>
);

const TITLE_COLOR = 'var(--mantine-color-primary-5)';
const RoleDetailPage: React.FC = () => {
  useDisclosure(false);
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [openedModalDelete, { close: closeModalDelete, open: openModalDelete }] = useDisclosure(false);
  const [tablePermissionCiTypeAffected, setTablePermissionCiTypeAffected] = useState<TableAffactedSafeType>();
  const [tablePermissionCiAffected, setPermissionCiTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);
  const [permissionOther, setPermissionOther] = useState<PermissionGrouped>({});
  const [permissionCis, setPermissionCis] = useState<PermissionCiOrCiTypeRoleResponse[]>([]);
  const [permissionCiTypes, setPermissionCiTypes] = useState<PermissionCiOrCiTypeRoleResponse[]>([]);
  const [roleDetail, setRoleDetail] = useState<RoleModel>({ id: 0, name: '' });
  const [totalPermissionCiRecords, setTotalPermissionCiRecords] = useState(0);
  const [totalPermissionCiTypeRecords, setTotalPermissionCiTypeRecords] = useState(0);
  const columnForPermissionCis: ColumnType<PermissionCiOrCiTypeRoleResponse>[] = useMemo(
    () => [
      { title: 'Ci Name', name: 'name' },
      { title: 'Ci Description', name: 'description' },
      { title: 'Ci Type Name', name: 'ciTypeName' },
      {
        title: 'Permissions',
        name: 'action',
        customRender: (_, row) => {
          return (
            <>
              <SubActionGroup subActions={row.action} />
            </>
          );
        },
      },
    ],
    [],
  );
  const columnForPermissionCiTypes: ColumnType<PermissionCiOrCiTypeRoleResponse>[] = useMemo(
    () => [
      { title: 'Ci Type Name', name: 'name' },
      { title: 'Ci Type Description', name: 'description' },
      {
        title: 'Permissions',
        name: 'action',
        customRender: (_, row) => {
          return (
            <>
              <SubActionGroup subActions={row.action} />
            </>
          );
        },
      },
    ],
    [],
  );

  const accordionItems: KanbanAccordionData[] = useMemo(
    () => [
      {
        key: 'infoRole',
        content: (
          <Flex direction='column' gap='sm'>
            {/*devsec-978536: roleDetail?.description*/}
            <BasicInformationRole name={roleDetail.name} description={roleDetail.description} id={roleDetail.id} />
          </Flex>
        ),
        title: (
          <KanbanTitle order={5} c={TITLE_COLOR}>
            Basic infomation role
          </KanbanTitle>
        ),
      },
      {
        key: 'application',
        content: (
          <Flex direction='column' gap='sm'>
            {permissionOther[PermissionActionType.APPLICATIONS] &&
              Object.keys(permissionOther[PermissionActionType.APPLICATIONS]).map((mainAction) => (
                <ActionGroup key={mainAction} mainAction={mainAction} subActions={permissionOther[PermissionActionType.APPLICATIONS][mainAction]} />
              ))}
          </Flex>
        ),
        title: (
          <KanbanTitle order={4} c={TITLE_COLOR}>
            APPLICATION
          </KanbanTitle>
        ),
      },
      {
        key: 'admin',
        content: (
          <Flex direction='column' gap='sm'>
            {permissionOther[PermissionActionType.ADMIN] &&
              Object.keys(permissionOther[PermissionActionType.ADMIN]).map((mainAction) => (
                <ActionGroup key={mainAction} mainAction={mainAction} subActions={permissionOther[PermissionActionType.ADMIN][mainAction]} />
              ))}
          </Flex>
        ),
        title: (
          <KanbanTitle order={4} c={TITLE_COLOR}>
            ADMIN
          </KanbanTitle>
        ),
      },
      {
        key: 'ciType',
        content: (
          <KanbanTable
            columns={columnForPermissionCiTypes}
            key={1}
            data={permissionCiTypes}
            showNumericalOrderColumn
            searchable={{
              enable: true,
              debounceTime: 300,
            }}
            pagination={{
              enable: true,
            }}
            serverside={{
              totalRows: totalPermissionCiTypeRecords,
              onTableAffected: (dataSet) => {
                if (!equal(tablePermissionCiTypeAffected, dataSet)) {
                  setTablePermissionCiTypeAffected(dataSet);
                }
              },
            }}
          />
        ),
        title: (
          <KanbanTitle order={4} c={TITLE_COLOR}>
            CI TYPE
          </KanbanTitle>
        ),
      },
      {
        key: 'ci',
        content: (
          <KanbanTable
            columns={columnForPermissionCis}
            key={1}
            data={permissionCis}
            showNumericalOrderColumn
            searchable={{
              enable: true,
              debounceTime: 300,
            }}
            pagination={{
              enable: true,
            }}
            serverside={{
              totalRows: totalPermissionCiRecords,
              onTableAffected: (dataSet) => {
                if (!equal(tablePermissionCiAffected, dataSet)) {
                  setPermissionCiTableAffected(dataSet);
                }
              },
            }}
          />
        ),
        title: (
          <KanbanTitle order={4} c={TITLE_COLOR}>
            CI
          </KanbanTitle>
        ),
      },
    ],
    [
      roleDetail.name,
      //dev-sec 978537
      roleDetail.description,
      roleDetail.id,
      permissionOther,
      columnForPermissionCiTypes,
      permissionCiTypes,
      totalPermissionCiTypeRecords,
      columnForPermissionCis,
      permissionCis,
      totalPermissionCiRecords,
      tablePermissionCiTypeAffected,
      tablePermissionCiAffected,
    ],
  );
  const onDeleteRole = () => {
    if (id) {
      RolesApi.deleteByIds([Number(id)])
        .then(() => {
          navigateTo(roleViewListPath);
          NotificationSuccess({
            message: 'Deleted successfully',
          });
        })
        .catch(() => {});
    }
  };
  const fetchAllPermissionCisByRoleId = useCallback(() => {
    if (!tablePermissionCiAffected || roleDetail.id === 0) {
      return;
    }

    SysPermissionCisApi.getAllPermissionCisByRoleId(Number(id), tableAffectedToPaginationRequestModel(tablePermissionCiAffected))
      .then((response) => {
        if (response.status === 200) {
          setPermissionCis(response.data.content);
          setTotalPermissionCiRecords(response.data.totalElements);
        }
      })
      .catch(() => {});
  }, [id, roleDetail.id, tablePermissionCiAffected]);
  const fetchAllPermissionCiTypesByRoleId = useCallback(() => {
    if (!tablePermissionCiTypeAffected || roleDetail.id === 0) {
      return;
    }

    SysPermissionCisApi.getAllPermissionCiTypesByRoleId(Number(id), tableAffectedToPaginationRequestModel(tablePermissionCiTypeAffected))
      .then((response) => {
        if (response.status === 200) {
          setPermissionCiTypes(response.data.content);
          setTotalPermissionCiTypeRecords(response.data.totalElements);
        }
      })
      .catch(() => {});
  }, [id, roleDetail.id, tablePermissionCiTypeAffected]);
  const fetchAllPermissionOthersByRoleId = useCallback(() => {
    SysPermissionOthersApi.getAllPermissionOtherByRoleId(Number(id))
      .then((response) => {
        if (response.status === 200) {
          const permissionOtherMapping = groupByTypeAndAction(response.data);
          setPermissionOther(permissionOtherMapping);
        }
      })
      .catch(() => {});
  }, [id]);
  const fetchRoleInfoByRoleId = useCallback(() => {
    RolesApi.getById(Number(id))
      .then((response) => {
        if (response.status === 200) {
          fetchAllPermissionOthersByRoleId();
          setRoleDetail(response.data);
        }
      })
      .catch(() => {});
  }, [fetchAllPermissionOthersByRoleId, id]);

  useEffect(() => {
    fetchRoleInfoByRoleId();
  }, [fetchRoleInfoByRoleId]);
  useEffect(() => {
    fetchAllPermissionCiTypesByRoleId();
  }, [fetchAllPermissionCiTypesByRoleId]);
  useEffect(() => {
    fetchAllPermissionCisByRoleId();
  }, [fetchAllPermissionCisByRoleId]);

  const orderFullCustomPaths = useMemo((): UrlBaseCrumbData => {
    const originPath = buildRoleUrl(Number(id), RoleAction.VIEW);

    return {
      ['admins']: {
        title: 'Admins',
        href: '/admins',
      },
      ['roles']: {
        title: 'Roles',
        href: roleViewListPath,
      },
      [`${roleDetailPath}`]: {
        title: `View ${roleDetail.name}`,
        href: originPath,
      },
    };
  }, [id, roleDetail.name]);
  return (
    <div>
      {/* 4746  role view page*/}
      <BreadcrumbComponent orderFullCustomPaths={orderFullCustomPaths} />

      <HeaderTitleComponent
        title='Role detail'
        rightSection={
          <Flex gap={10}>
            <GuardComponent requirePermissions={[]} hiddenOnUnSatisfy>
              <KanbanButton leftSection={<IconCopy />} variant='outline' onClick={() => navigateTo(roleViewListPath)}>
                Cancel
              </KanbanButton>
            </GuardComponent>
            <GuardComponent requirePermissions={[AclPermission.createRole]} hiddenOnUnSatisfy>
              <KanbanButton
                leftSection={<IconCopy />}
                color={'cyan'}
                variant='light'
                onClick={() => {
                  navigate(buildRoleUrl(Number(id), RoleAction.COPY));
                }}>
                Copy
              </KanbanButton>
            </GuardComponent>
            <GuardComponent requirePermissions={[AclPermission.deleteRole]} hiddenOnUnSatisfy>
              <KanbanButton leftSection={<IconTrash />} color={'red'} onClick={openModalDelete}>
                Delete
              </KanbanButton>
            </GuardComponent>
            <GuardComponent requirePermissions={[AclPermission.updateRole]} hiddenOnUnSatisfy>
              <KanbanButton leftSection={<IconEdit />} onClick={() => navigate(buildCreateOrUpdateRoleUrl(Number(id)))}>
                Edit
              </KanbanButton>
            </GuardComponent>
          </Flex>
        }
      />
      <KanbanConfirmModal title='Delete role' onConfirm={onDeleteRole} textConfirm='Delete' onClose={closeModalDelete} opened={openedModalDelete}>
        {'Are you sure to delete this role?'}
      </KanbanConfirmModal>
      <KanbanAccordion data={accordionItems} defaultValue={['infoRole', 'application', 'admin', 'ciType', 'ci']} />
    </div>
  );
};

export default RoleDetailPage;
