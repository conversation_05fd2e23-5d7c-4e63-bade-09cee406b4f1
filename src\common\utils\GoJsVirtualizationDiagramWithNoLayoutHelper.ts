import { GoJs } from '@common/libs';
import { Key, ObjectData } from '@common/libs/gojs/go';
import { Quadtree } from '@common/libs/gojs/Quadtree';
import { CiRelationshipInfoModel } from '@models/CiRelationship';
import { CiRelationshipTypeModel } from '@models/CiRelationshipType';
import { ConfigItemTypeModel } from '@models/ConfigItemType';
import { appendNodeTransaction, LinkDataTemplate, PalleteTemplateFull } from './GoJsHelper';
import { ConfigItemApi } from '@api/ConfigItemApi';
import { TablerIconKeys } from './IconsUtils';

const go = GoJs;
const duration = 400;
/**
 * Tính toán và trả về tổng thể vùng bao (bounding box) của tất cả các node
 * trong model dựa trên thuộc tính `bounds` được lưu trong mỗi node.
 *
 * Hàm này được sử dụng khi bạn có một model toàn cục (myWholeModel) và muốn xác định
 * kích thước của toàn bộ sơ đồ (diagram) — kể cả các node không được hiển thị trong viewport hiện tại.
 *
 * @param myWholeModel - Mô hình chứa toàn bộ dữ liệu node và link (GraphLinksModel).
 *                       Mỗi node cần có thuộc tính `bounds` là một đối tượng go.Rect.
 * @returns Một `go.Rect` đại diện cho vùng bao phủ toàn bộ các node trong model.
 *          Nếu không node nào có bounds, trả về một Rect rỗng.
 */

export const computeDocumentBounds = (myWholeModel: go.GraphLinksModel) => {
  const b = new go.Rect();
  const ndata = myWholeModel.nodeDataArray;
  for (let i = 0; i < ndata.length; i++) {
    const d = ndata[i];
    if (!d.bounds) {
      continue;
    }
    if (b.isEmpty()) {
      b.set(d.bounds);
    } else {
      b.unionRect(d.bounds);
    }
  }
  return b;
};

export const addNode = (diagram: go.Diagram, wholeModel: go.GraphLinksModel, data: any) => {
  const model = diagram.model;
  if (model.containsNodeData(data)) {
    return;
  }
  model.addNodeData(data);
  const n = diagram.findNodeForData(data);
  if (n !== null) {
    n.ensureBounds();
  }
};

export const onViewportChanged = (e: go.DiagramEvent, myWholeQuadtree: Quadtree<any>, myWholeModel: go.GraphLinksModel) => {
  const diagram = e.diagram;
  // make sure there are Nodes for each node data that is in the viewport
  // and each Link that is connected to such a Node
  const viewb = diagram.viewportBounds; // the new viewportBounds
  const model = diagram.model;

  const oldskips = diagram.skipsUndoManager;
  diagram.skipsUndoManager = true;

  //?? this does NOT remove Nodes or Links that are outside of the viewport

  const b = new go.Rect();
  const ndata = myWholeQuadtree.intersecting(viewb); // myWholeModel.nodeDataArray;
  for (let i = 0; i < ndata.length; i++) {
    const n = ndata[i];
    if (model.containsNodeData(n)) {
      continue;
    }
    if (!n.bounds) {
      continue;
    }
    if (n.bounds.intersectsRect(viewb)) {
      addNode(diagram, myWholeModel, n);
    }
  }

  //?? not considering TreeModel
  if (model instanceof go.GraphLinksModel) {
    // this could be much more efficient if we kept track of links in a Quadtree;
    // but then we would need to be able to update the Quadtree efficiently as nodes moved
    const ldata = myWholeModel.linkDataArray;
    for (let i = 0; i < ldata.length; i++) {
      const l = ldata[i];
      if (model.containsLinkData(l)) {
        continue;
      }

      const fromkey = myWholeModel.getFromKeyForLinkData(l);
      if (fromkey === undefined) {
        continue;
      }
      const from = myWholeModel.findNodeDataForKey(fromkey);
      if (from === null || !from.bounds) {
        continue;
      }

      const tokey = myWholeModel.getToKeyForLinkData(l);
      if (tokey === undefined) {
        continue;
      }
      const to = myWholeModel.findNodeDataForKey(tokey);
      if (to === null || !to.bounds) {
        continue;
      }

      b.set(from.bounds);
      b.unionRect(to.bounds);
      if (b.intersectsRect(viewb)) {
        // also make sure both connected nodes are present,
        // so that link routing is authentic
        addNode(diagram, myWholeModel, from);
        addNode(diagram, myWholeModel, to);
        model.addLinkData(l);
        const link = diagram.findLinkForData(l);
        if (link !== null) {
          // do this now to avoid delayed routing outside of transaction
          link.updateRoute();
        }
      }
    }
  }

  diagram.skipsUndoManager = oldskips;
};

export const onModelChanged = (e: go.ChangedEvent, myWholeQuadtree: Quadtree<any>, myWholeModel: go.GraphLinksModel) => {
  // handle moves and insertions and removals
  if (!e.model) {
    return;
  }
  if (e.model.skipsUndoManager) {
    return;
  }
  if (e.change === go.ChangedEvent.Property) {
    if (e.propertyName === 'bounds') {
      myWholeQuadtree.move(e.object, e.newValue.bounds.x, e.newValue.bounds.y);
    }
  } else if (e.change === go.ChangedEvent.Insert) {
    if (e.propertyName === 'nodeDataArray') {
      myWholeModel.addNodeData(e.newValue);
      myWholeQuadtree.add(e.newValue, e.newValue.bounds);
    } else if (e.propertyName === 'linkDataArray') {
      myWholeModel.addLinkData(e.newValue);
    }
  } else if (e.change === go.ChangedEvent.Remove) {
    if (e.propertyName === 'nodeDataArray') {
      myWholeModel.removeNodeData(e.oldValue);
      myWholeQuadtree.remove(e.oldValue);
    } else if (e.propertyName === 'linkDataArray') {
      myWholeModel.removeLinkData(e.oldValue);
    }
  }
};

export const virtualUniqueKey = (model: go.Model, myWholeModel: go.GraphLinksModel, data: ObjectData): Key => {
  myWholeModel.makeNodeDataKeyUnique(data);
  return myWholeModel.getKeyForNodeData(data);
};

export const appendNodeWithVitualized = async (
  goDiagram: go.Diagram | undefined,
  datas: CiRelationshipInfoModel[],
  ciTypes: ConfigItemTypeModel[],
  getRelationshipTypeById: (id: number) => CiRelationshipTypeModel | undefined,
  ciAddNewRelationship?: number,
  isAddNew: boolean = false,
  isUseTransaction: boolean = true,
) => {
  if (!goDiagram) {
    return Promise.resolve(false);
  }
  const position = goDiagram.position.copy();
  const scale = goDiagram.scale;
  const model = goDiagram.model as go.GraphLinksModel;
  const template: PalleteTemplateFull[] = [];
  const links: LinkDataTemplate[] = [];
  const ciIds = datas.flatMap((item) => [item.fromCi, item.toCi]);
  const updateCountTransaction = 'updateCountTransaction';

  if (!ciIds.length) {
    return Promise.resolve(false);
  }
  const res = await ConfigItemApi.ciCountRelationships(ciIds);
  for (const item of datas) {
    const totalNodeLinkFrom = res?.data?.find((x) => x.ciId === item.fromCi);
    const fromCiItem: PalleteTemplateFull = {
      key: item.fromCi,
      icon: (ciTypes.find((x) => x.id === item.fromCiTypeId)?.icon || 'IconHome') as TablerIconKeys,
      label: `${item.fromCiName}`,
      isRoot: false,
      data: {
        ciId: item.fromCi,
        ciTypeId: item.fromCiTypeId,
        ciTypeName: item.fromCiTypeName,
      },
      totalFrom: totalNodeLinkFrom?.totalFrom || 0,
      totalTo: totalNodeLinkFrom?.totalTo || 0,
    };

    const totalNodeLinkTo = res?.data?.find((x) => x.ciId === item.toCi);
    const toCiItem: PalleteTemplateFull = {
      key: item.toCi,
      icon: (ciTypes.find((x) => x.id === item.toCiTypeId)?.icon || 'IconHome') as TablerIconKeys,
      label: `${item.toCiName}`,
      isRoot: false,
      data: {
        ciId: item.toCi,
        ciTypeId: item.toCiTypeId,
        ciTypeName: item.toCiTypeName,
      },
      totalFrom: totalNodeLinkTo?.totalFrom || 0,
      totalTo: totalNodeLinkTo?.totalTo || 0,
    };

    if (!template.some((x) => x.key === item.fromCi)) {
      template.push(fromCiItem);
    }

    if (!template.some((x) => x.key === item.toCi)) {
      template.push(toCiItem);
    }

    const relationship = getRelationshipTypeById(item.relationshipId);

    links.push({
      relationshipId: item.relationshipId,
      from: item.fromCi,
      to: item.toCi,
      text: relationship ? relationship.type : '',
      direction: item.ciRuleRelationships?.map((relationship) => relationship.impactedRuleRelationDirection ?? '') ?? [],
    });
  }

  if (isUseTransaction) {
    goDiagram.startTransaction(appendNodeTransaction);
  }
  //check if item not exists then append node to diagram
  template.forEach((item) => {
    if (!model.nodeDataArray.some((node) => node.key === item.key)) {
      // add bound for node
      const index = goDiagram.model.nodeDataArray.length;
      const sqrt = Math.floor(Math.sqrt(index + 1)); // +1 vì thêm node mới
      const col = index % sqrt;
      const row = Math.floor(index / sqrt);
      const newNodeBounds = new go.Rect(col * 180, row * 180, 80, 100);
      item.bounds = newNodeBounds;
      goDiagram.model.addNodeData(item);
    }
  });

  //append node to diagram
  links.forEach((item) => {
    if (!model.linkDataArray.some((link) => link.from === item.from && link.to === item.to)) {
      goDiagram.model.addLinkData(item);
      // update count relationship case add new relationship
      if (isAddNew) {
        const nodeFind = goDiagram.model.findNodeDataForKey(item.from);
        if (nodeFind && item.from === ciAddNewRelationship) {
          model.startTransaction(updateCountTransaction);
          goDiagram.model.set(nodeFind, 'totalFrom', (nodeFind.totalFrom || 0) + 1);
          model.commitTransaction(updateCountTransaction);
        }

        const nodeFindTo = goDiagram.model.findNodeDataForKey(item.to);
        if (nodeFindTo && item.to === ciAddNewRelationship) {
          model.startTransaction(updateCountTransaction);
          goDiagram.model.set(nodeFindTo, 'totalTo', (nodeFindTo.totalTo || 0) + 1);
          model.commitTransaction(updateCountTransaction);
        }
      }
    }
  });
  //add animation add node
  goDiagram.animationManager.isEnabled = true;
  goDiagram.animationManager.duration = duration;
  goDiagram.position = position;
  goDiagram.scale = scale;
  if (isUseTransaction) {
    goDiagram.commitTransaction(appendNodeTransaction);
  }
  return res;
};
