import React, { create<PERSON>ontext, useReducer, useContext, ReactNode, Dispatch, useMemo } from 'react';
import { produce } from 'immer';
import { CustomDataNode, JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';
import { DataTransformActionType } from '@common/constants/DataTransformActionType';

export interface DataTransformState {
  jsonData: JSONNode;
  jsonTransform: JSONNode[];
  isExecuting: boolean;
  columnDatasForOutput: CustomDataNode[];
  columnDatasForInput: CustomDataNode[];
  isJsonStatic: boolean;
}

const initialState: DataTransformState = {
  jsonData: {},
  jsonTransform: [],
  isExecuting: false,
  columnDatasForOutput: [],
  columnDatasForInput: [],
  isJsonStatic: false,
};

type DataTransformActions =
  | { type: DataTransformActionType.UPDATE_JSON_DATA; payload: JSONNode }
  | { type: DataTransformActionType.UPDATE_JSON_TRANSFORM; payload: JSONNode[] }
  | { type: DataTransformActionType.UPDATE_IS_EXECUTING; payload: boolean }
  | { type: DataTransformActionType.UPDATE_COLUMN_DATAS_FOR_OUTPUT; payload: CustomDataNode[] }
  | { type: DataTransformActionType.UPDATE_COLUMN_DATAS_FOR_INPUT; payload: CustomDataNode[] }
  | { type: DataTransformActionType.UPDATE_IS_JSON_STATIC; payload: boolean }
  | { type: DataTransformActionType.RESET }
  | { type: DataTransformActionType.REFRESH_JSON_DATA_AND_OUTPUT };

const dataTransformReducer = (state: DataTransformState, action: DataTransformActions): DataTransformState => {
  return produce(state, (draft) => {
    switch (action.type) {
      case DataTransformActionType.UPDATE_JSON_DATA:
        draft.jsonData = action.payload;
        break;
      case DataTransformActionType.UPDATE_JSON_TRANSFORM:
        draft.jsonTransform = action.payload;
        break;
      case DataTransformActionType.UPDATE_IS_EXECUTING:
        draft.isExecuting = action.payload;
        break;
      case DataTransformActionType.UPDATE_COLUMN_DATAS_FOR_OUTPUT:
        draft.columnDatasForOutput = action.payload;
        break;
      case DataTransformActionType.UPDATE_COLUMN_DATAS_FOR_INPUT:
        draft.columnDatasForInput = action.payload;
        break;
      case DataTransformActionType.UPDATE_IS_JSON_STATIC:
        draft.isJsonStatic = action.payload;
        break;
      case DataTransformActionType.RESET:
        return initialState;
      case DataTransformActionType.REFRESH_JSON_DATA_AND_OUTPUT:
        draft.jsonData = {};
        draft.columnDatasForOutput = [];
        draft.isJsonStatic = false;
        break;
      default:
        throw new Error(`Unhandled action type`);
    }
  });
};

const DataTransformContext = createContext<{ state: DataTransformState; dispatch: Dispatch<DataTransformActions> } | undefined>(undefined);

export const DataTransformProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(dataTransformReducer, initialState);

  const memoizedValue = useMemo(() => ({ state, dispatch }), [state, dispatch]);

  return <DataTransformContext.Provider value={memoizedValue}>{children}</DataTransformContext.Provider>;
};

export const useDataTransform = () => {
  const context = useContext(DataTransformContext);
  if (context === undefined) {
    throw new Error('useDataTransform must be used within a DataTransform');
  }
  return context;
};
