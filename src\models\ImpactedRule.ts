import { EntityModelBase } from './EntityModelBase';

export enum CiRelationshipDirectionEnum {
  IN = 'IN',
  OUT = 'OUT',
}

export enum RuleAction {
  ADD = 'ADD',
  EDIT = 'EDIT',
  VIEW = 'VIEW',
  COPY = 'COPY',
  DELETE = 'DELETE',
  APPLY = 'APPLY',
  CHANGE_STATUS = 'CHANGE_STATUS',
  INIT = 'INIT',
}

export type ImpactedRuleModel = EntityModelBase & {
  //add
  ruleName?: string;
  relationshipTypeId?: number;
  //add
  relationshipTypeName?: string;
  relationshipDirection?: string;
  //4243: show relation ship related to direction of impacted rule
  relationshipDirectionImpacted?: string;
  active?: boolean;
  description?: string;
};

export type ImpactedCiRuleRelationModel = EntityModelBase & {
  fromCi?: number;
  toCi?: number;
  impactedRuleId?: number;
  impactedRuleRelationDirection?: string;
  impactedRuleName?: string;
  ciRelationshipId?: number;
};
