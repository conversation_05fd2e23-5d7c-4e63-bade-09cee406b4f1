import { SimpleGrid } from '@mantine/core';
import { KanbanCheckbox, KanbanInput } from 'kanban-design-system';
import React from 'react';
import type { OutgoingMailConfigDto, OutGoingMailProps } from '@models/OutgoingMail';
import type { SetFieldValueFn } from '@common/utils/CommonUtils';
export const MailServiceBasicComponent: React.FC<OutGoingMailProps> = ({ info, setInfo }) => {
  const handleChange: SetFieldValueFn<OutgoingMailConfigDto> = (field, value) => {
    let updateData: OutgoingMailConfigDto = {
      ...info,
      [field]: value,
    };
    if ('authenticationEnabled' === field) {
      updateData = {
        ...updateData,
        ['mailUsername']: '',
        ['mailPassword']: '',
      };
    }
    setInfo(updateData);
  };

  return (
    <>
      <SimpleGrid cols={2}>
        <KanbanInput
          maxLength={255}
          label='Server Name / IP Address'
          value={info.serverName ?? ''}
          onChange={(event) => handleChange('serverName', event.target.value)}
          required
        />
      </SimpleGrid>
      <SimpleGrid cols={2}>
        <KanbanInput
          label='Sender Name'
          value={info.senderName ?? ''}
          maxLength={100}
          onChange={(event) => handleChange('senderName', event.target.value)}
        />
      </SimpleGrid>
      <KanbanCheckbox
        label='Requires Authentication'
        checked={info.authenticationEnabled ?? false}
        onChange={(e) => handleChange('authenticationEnabled', e.target.checked)}
      />
      {info.authenticationEnabled && (
        <>
          <SimpleGrid cols={2}>
            <KanbanInput
              label='Username'
              value={info.mailUsername ?? ''}
              maxLength={255}
              onChange={(e) => handleChange('mailUsername', e.target.value)}
              required
            />
          </SimpleGrid>
          <SimpleGrid cols={2}>
            <KanbanInput
              label='Password'
              type='password'
              maxLength={255}
              value={info.mailPassword ?? ''}
              onChange={(e) => handleChange('mailPassword', e.target.value)}
              required
            />
          </SimpleGrid>
        </>
      )}
    </>
  );
};
