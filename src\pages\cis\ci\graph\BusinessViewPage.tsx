import React, { useCallback, useEffect, useMemo, useState } from 'react';
import CiDetailPage from '../CiDetailPage';
import { CIBussinessViewsApi } from '@api/CIBussinessViewsApi';
import type { CIBusinessViewResponseDTO } from '@models/CIBusinessViews';
import { useParams } from 'react-router-dom';
import CiRelationship from '../relationship/CiRelationship';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import GuardComponent from '@components/GuardComponent';
import { Flex, Space } from '@mantine/core';
import { KanbanButton } from 'kanban-design-system';
import { IconTrash } from '@tabler/icons-react';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { buildViewCreateGraphUrl, businessViewsPath, navigateTo } from '@common/utils/RouterUtils';
import { KanbanConfirmModal } from 'kanban-design-system';
import { KanbanText } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import { AclPermission } from '@models/AclPermission';
import { BreadcrumbComponent, UrlBaseCrumbData } from '@pages/admins/breadcrumb/BreadcrumbComponent';

export type BusinessViewPageProps = {
  ciId: number;
  ciTypeId: number;
  bussinessViewId?: number;
};

export const BusinessViewPage = () => {
  const { ciId: ciIdParam, ciTypeId: ciTypeIdParam, id: idParam } = useParams();
  const [bussinessView, setBusinessView] = useState<CIBusinessViewResponseDTO | undefined>(undefined);
  const bussinessViewId = idParam ? Number(idParam) : undefined;
  const ciTypeId = Number(ciTypeIdParam);
  const ciId = Number(ciIdParam);
  const [openedModalDelete, { close: closeModalDelete, open: openModalDelete }] = useDisclosure(false);

  // load bussiness graphView
  const fetchBusinessView = useCallback(() => {
    if (bussinessViewId) {
      // if bussiness view then get bussiness view

      CIBussinessViewsApi.getById(bussinessViewId)
        .then((res) => {
          setBusinessView(res.data);
        })
        .catch(() => {});
    }
  }, [bussinessViewId]);

  useEffect(() => {
    fetchBusinessView();
  }, [fetchBusinessView]);

  const onDeleteBusinessView = () => {
    if (bussinessViewId) {
      CIBussinessViewsApi.deleteById(bussinessViewId)
        .then((res) => {
          if (res.data) {
            NotificationSuccess({
              message: 'Deleted successfully',
            });
            navigateTo(businessViewsPath);
          }
        })
        .catch(() => {});
    }
  };
  const locationCustomPaths = useMemo((): UrlBaseCrumbData => {
    let detailBread = '';
    let originPath = '';
    if (bussinessViewId) {
      detailBread = `View ${bussinessView?.entity.viewName}`;
      originPath = buildViewCreateGraphUrl(bussinessViewId);
    } else {
      detailBread = 'Create';
    }
    return {
      [`/${bussinessViewId}`]: {
        title: detailBread,
        href: originPath,
      },
    };
  }, [bussinessView?.entity.viewName, bussinessViewId]);
  return (
    <>
      {bussinessViewId && bussinessView?.ciInfo.ciTypeId && bussinessView?.ciInfo.id && (
        <>
          <BreadcrumbComponent locationCustomPaths={locationCustomPaths} />
          <HeaderTitleComponent
            title={`CI: ${bussinessView?.ciInfo?.name || ''} - GraphView Name: ${bussinessView?.entity?.viewName}`}
            rightSection={
              <>
                {/* //TODO Permission */}
                <GuardComponent requirePermissions={[AclPermission.deleteBusinessView]} hiddenOnUnSatisfy>
                  <Flex>
                    <Space w={'sm'} />
                    <KanbanButton onClick={openModalDelete} leftSection={<IconTrash />} color='red'>
                      Delete
                    </KanbanButton>
                  </Flex>
                </GuardComponent>
              </>
            }
          />
          <CiRelationship
            ciId={bussinessView?.ciInfo?.id}
            ciTypeId={bussinessView?.ciInfo?.ciTypeId}
            cIBusinessViews={bussinessView?.entity}
            isFromBusinessView={true}
          />

          <KanbanConfirmModal
            title='Delete CI'
            onConfirm={onDeleteBusinessView}
            textConfirm='Delete'
            onClose={closeModalDelete}
            opened={openedModalDelete}>
            <KanbanText>Are you sure to delete this business view?</KanbanText>
          </KanbanConfirmModal>
        </>
      )}

      {/* case create new businessView */}
      {!bussinessViewId && ciTypeId && ciId && <CiDetailPage ciTypeId={ciTypeId} ciId={ciId} isFromBusinessView={true} />}
    </>
  );
};

BusinessViewPage.displayName = 'BusinessViewPage';
export default BusinessViewPage;
