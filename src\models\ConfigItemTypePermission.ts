import type { PermissionAction } from '@common/constants/PermissionAction';
import type { PermissionActionType } from '@common/constants/PermissionActionType';

export type ConfigItemTypePermissionModel = {
  id: number;
  name: string;
  description?: string;
  icon?: string;
  parentId?: number;
  type?: string;
  action?: string[];
  isSetting?: boolean;
};

export type ConfigItemPermissionOtherModel = {
  actions: PermissionAction[];
  type: PermissionActionType;
};

export type ConfigItemPermissionOtherDto = {
  action: PermissionAction;
  meaning: string;
  type: PermissionActionType;
  isChecked?: boolean;
};

export default 1;
