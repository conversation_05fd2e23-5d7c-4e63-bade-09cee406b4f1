import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import type { OutgoingMailConfigDto } from '@models/OutgoingMail';

export type OutgoingMailConfigRequest = OutgoingMailConfigDto & {
  hasAuthentication?: boolean;
  hasTls?: boolean;
  oauthClientValue?: string;
  hasProxy?: boolean;
};
export class OutgoingMailConfigApi extends BaseApi {
  static baseUrl = BaseUrl.outgoingMailConfigs;

  static convertDtoToRequest(dto: OutgoingMailConfigDto): OutgoingMailConfigRequest {
    return {
      ...dto,
      hasAuthentication: dto.authenticationEnabled,
      hasTls: dto.tlsEnabled,
      hasProxy: dto.proxyEnabled,
      oauthClientValue: dto.oauthClientSecret,
    };
  }

  static sendTest(receiver: string, dto: OutgoingMailConfigDto) {
    const payload = OutgoingMailConfigApi.convertDtoToRequest(dto);
    return BaseApi.postData<boolean>(
      `${this.baseUrl}/send-test`,
      payload,
      { receiver: receiver },
      {},
      { useLoading: false, useErrorNotification: true },
    );
  }

  static saveOrUpdateOutgoingMailConfig(dto: OutgoingMailConfigDto) {
    const payload = OutgoingMailConfigApi.convertDtoToRequest(dto);
    return BaseApi.postData<OutgoingMailConfigDto>(`${this.baseUrl}`, payload);
  }

  static getOutgoingMailConfig() {
    return BaseApi.getData<OutgoingMailConfigDto>(`${this.baseUrl}`);
  }
}
