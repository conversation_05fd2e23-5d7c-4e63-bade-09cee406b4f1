import type { PermissionAction } from '@common/constants/PermissionAction';
import type { PermissionActionType } from '@common/constants/PermissionActionType';
import type { ApiResponseDataBase } from '@core/api/ApiResponse';
import { BaseApi } from '@core/api/BaseApi';
import type { ConfigItemPermissionOtherModel, ConfigItemTypePermissionModel } from '@models/ConfigItemTypePermission';
import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
import type { UserResponse } from './UsersApi';
import { BaseUrl } from '@core/api/BaseUrl';

export type GroupResponse = ApiResponseDataBase & GroupModel;
export type GroupResponsePagingResponse = PaginationResponseModel<UserSettingGroupModel>;

export type GroupModel = {
  id: number;
  name: string;
  active: boolean;
  description?: string;
};

export type PermissionCiModel = {
  id: number;
  name: string;
  description: string;
  actions: string[];
};
export type UserSettingGroupModel = GroupResponse & {
  // isSetting?: boolean;
  groupActiveId?: number;
};

export type GroupSettingUsersModel = {
  groupId: number;
  userId: number;
  userName: string;
  isSetting: boolean;
};
export type GroupSettingRolesModel = {
  groupId: number;
  roleId: number;
  roleName?: string;
  groupName?: string;
};

export type SysPermissionCiModel = {
  id: number;
  ciId: number;
  ciTypeId: number;
  type?: string;
  name: string;
  description: string;
  action?: string;
};
export type SysPermissionOtherModel = {
  id?: number;
  type: PermissionActionType;
  description?: string;
  action: PermissionAction;
};
export enum ViewTypeEnum {
  ROLE_VIEW = 'ROLE_VIEW',
  USER_VIEW = 'USER_VIEW',
}

export class GroupsApi extends BaseApi {
  static baseGroupsUrl = BaseUrl.groups;

  static getAllGroupWithPaging(pagination: PaginationRequestModel<GroupResponse>) {
    const url = `${GroupsApi.baseGroupsUrl}/filter`;
    return BaseApi.postData<GroupResponsePagingResponse>(url, pagination);
  }

  static getAllGroupInUserPageToView(pagination: PaginationRequestModel<GroupResponse>, username: string) {
    const url = `${GroupsApi.baseGroupsUrl}/view?username=${username}&viewType=${ViewTypeEnum.USER_VIEW}`;
    return BaseApi.getData<GroupResponsePagingResponse>(url, pagination);
  }

  static getAllGroupInRolePageToView(pagination: PaginationRequestModel<GroupResponse>, roleId: number) {
    const url = `${GroupsApi.baseGroupsUrl}/view?roleId=${roleId}&viewType=${ViewTypeEnum.ROLE_VIEW}`;
    return BaseApi.getData<GroupResponsePagingResponse>(url, pagination);
  }

  static findAllGroupActivesInRoleToView(roleId: number) {
    const url = `${GroupsApi.baseGroupsUrl}/roles/${roleId}/active`;
    return BaseApi.getData<GroupResponse[]>(url);
  }

  // static getAllGroupByUserName(userName: string) {
  //   return BaseApi.getData<GroupResponse[]>(`${GroupsApi.baseGroupsUrl}/users/${userName}`);
  // }

  static getAllGroupSettingByUserName(userName: string) {
    return BaseApi.getData<UserSettingGroupModel[]>(`${GroupsApi.baseGroupsUrl}/users/${userName}/settings`);
  }

  // static getAllCisByGroupAndCitypeId(groupId: number, citypeId: number) {
  //   return BaseApi.getData<PermissionCiModel[]>(`${GroupsApi.baseGroupsUrl}/${groupId}/citype/${citypeId}/cis`);
  // }

  static addGroupForUser(datas: number[], userName: string) {
    return BaseApi.postData<UserSettingGroupModel[]>(`${GroupsApi.baseGroupsUrl}/users/${userName}/settings`, datas);
  }
  static deleteGroupInUser(groupIds: number[], userName: string) {
    return BaseApi.deleteData<UserSettingGroupModel[]>(`${GroupsApi.baseGroupsUrl}/users/${userName}/settings`, { groupIds });
  }
  static createOrUpdateGroup(data: GroupModel) {
    return BaseApi.postData<GroupResponse>(GroupsApi.baseGroupsUrl, data);
  }
  static deleteGroup(id: number) {
    return BaseApi.deleteData<boolean>(`${GroupsApi.baseGroupsUrl}/${id}`);
  }

  static settingPermissions(data: ConfigItemTypePermissionModel[], groupId: number) {
    return BaseApi.postData<GroupResponse>(`${GroupsApi.baseGroupsUrl}/${groupId}/permission`, data);
  }

  // static getPermissions(groupId: number) {
  //   return BaseApi.getData<SysPermissionCiModel[]>(`${GroupsApi.baseGroupsUrl}/${groupId}/tree`);
  // }

  static activeGroups(groupIds: number[]) {
    return BaseApi.postData<ApiResponseDataBase>(`${GroupsApi.baseGroupsUrl}/active`, groupIds);
  }

  static inActiveGroups(groupIds: number[]) {
    return BaseApi.postData<ApiResponseDataBase>(`${GroupsApi.baseGroupsUrl}/inactive`, groupIds);
  }

  static deleteGroups(ids: number[]) {
    return BaseApi.delete<ApiResponseDataBase>(`${GroupsApi.baseGroupsUrl}/batch`, {
      ids,
    });
  }

  static findAllUserWithGroupId(groupId: number) {
    return BaseApi.getData<UserResponse[]>(`${GroupsApi.baseGroupsUrl}/${groupId}/users`);
  }
  static findAllRoleWithGroupId(groupId: number) {
    return BaseApi.getData<number[]>(`${GroupsApi.baseGroupsUrl}/${groupId}/roles`);
  }

  static addUsersIntoGroup(groupId: number, userNames: string[]) {
    return BaseApi.postData<GroupSettingUsersModel[]>(`${GroupsApi.baseGroupsUrl}/${groupId}/users`, userNames);
  }
  static updateRolesIntoGroup(groupId: number, roleId: number[]) {
    return BaseApi.putData<GroupSettingRolesModel[]>(`${GroupsApi.baseGroupsUrl}/${groupId}/roles`, roleId);
  }
  static removeRolesFromGroup(groupId: number, roleIds: number[]) {
    return BaseApi.deleteData<GroupSettingUsersModel[]>(`${GroupsApi.baseGroupsUrl}/${groupId}/roles`, { roleIds });
  }

  static removeUsersIntoGroup(groupId: number, userNames: string[]) {
    return BaseApi.deleteData<GroupSettingUsersModel[]>(`${GroupsApi.baseGroupsUrl}/${groupId}/users`, { userNames });
  }
  static createOrUpdateSysPermissionOtherGroup(data: ConfigItemPermissionOtherModel, groupId: number) {
    return BaseApi.postData<GroupResponse>(`${GroupsApi.baseGroupsUrl}/${groupId}/permission-other`, data);
  }
}
