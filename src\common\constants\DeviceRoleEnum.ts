export enum DeviceRoleEnum {
  AED = 'aed',
  APPLIANCE = 'appliance',
  ATS = 'ats',
  BLADE = 'blade',
  BYPASS_SW = 'bypass-sw',
  F5 = 'f5',
  FIREWALL = 'firewall',
  LOADBALANCER = 'loadbalancer',
  NAS = 'nas',
  NETWORK = 'network-',
  OTHER = 'other',
  POLYCOM = 'polycom',
  ROUTER = 'router',
  SECURITY = 'security',
  SENSOR = 'sensor',
  SERVER = 'server',
  SEVER = 'sever',
  STG = 'stg',
  STORAGE = 'storage',
  SVR = 'svr',
  SW = 'sw',
  SWITCH = 'switch',
}
