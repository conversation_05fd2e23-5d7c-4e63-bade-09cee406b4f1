export enum StatusCiManagement {
  DRAFT = 'DRAFT',
  WAITING = 'WAITING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

export enum ScreenTypeManagement {
  DRAFT = 'DRAFT',
  SENT = 'SENT',
  WAITING = 'WAITING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

export enum ActionType {
  SEND = 'SEND',
  APPROVE = 'APPROVE',
  REJECT = 'REJECT',
}

export enum FlaggedImpactedCiAttachedType {
  MANUAL = 'MANUAL',
  CALCULATED = 'CALCULATED',
}
