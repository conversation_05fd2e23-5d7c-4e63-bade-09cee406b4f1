import type { CiTypeRelationAttrModel, CiTypeRelationModel } from './CiTypeRelation';
import type { CiTypeReferenceModel, ConfigItemTypeAttrModel } from './ConfigItemTypeAttr';

export type ConfigItemTypeInfoModel = {
  attributesUpdate?: ConfigItemTypeAttrModel[];
  attributesDelete?: ConfigItemTypeAttrModel[];
  relationshipsUpdate?: CiTypeRelationModel[];
  relationshipsDelete?: CiTypeRelationModel[];
  relationshipAttributeUpdate?: CiTypeRelationAttrModel[];
  relationshipAttributeDelete?: CiTypeRelationAttrModel[];
  attributesReferenceUpdate?: CiTypeReferenceModel[];
  attributesReferenceDelete?: CiTypeReferenceModel[];
};

export default 1;
