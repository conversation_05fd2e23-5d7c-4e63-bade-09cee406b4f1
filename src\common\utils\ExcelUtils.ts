import { DetailInfoRowImportDTO, DetailInfoType } from '@models/ImportFileResponse';
import dayjs from 'dayjs';
import * as FileSaver from 'file-saver';
import { Xlsx } from '@common/libs';

export function generateFileExportName(): string {
  const currentDate = dayjs();
  return `${currentDate.format('YYYY_MM_DD-HH_mm_ss')}.xlsx`;
}

export const addPrefixToFileName = (originalFileName: string, prefix: string): string => {
  const fileNameParts = originalFileName.split('.');
  const fileExtension = fileNameParts.pop();
  const modifiedFileName = `${fileNameParts.join('_') + prefix}.${fileExtension}`;
  return modifiedFileName;
};

export const exportFile = (csvData: any[], fileName: string, sheetName: string): void => {
  const fileType: string = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
  const fileExtension: string = '.xlsx';
  const ws: Xlsx.WorkSheet = Xlsx.utils.json_to_sheet(csvData);
  const wb: Xlsx.WorkBook = { Sheets: { [sheetName]: ws }, SheetNames: [sheetName] };
  const excelBuffer: any = Xlsx.write(wb, { bookType: 'xlsx', type: 'array' });
  const data: Blob = new Blob([excelBuffer], { type: fileType });

  FileSaver.saveAs(data, `${fileName}${fileExtension}`);
};

export const exportFileFromBase64 = (base64EncodedFile: string, fileName: string): void => {
  const binaryData = atob(base64EncodedFile);
  const arrayBuffer = new ArrayBuffer(binaryData.length);
  const view = new Uint8Array(arrayBuffer);
  for (let i = 0; i < binaryData.length; i++) {
    view[i] = binaryData.charCodeAt(i);
  }

  const blob = new Blob([arrayBuffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });
  FileSaver.saveAs(blob, fileName);
};

export const validateUploadedFile = (file: File): boolean => {
  if (!file) {
    return false;
  }
  const allowedFormats = ['.xlsx'];
  const fileExtension = file.name.split('.').pop()?.toLowerCase();
  if (!allowedFormats.includes(`.${fileExtension}`)) {
    return false;
  }
  return true;
};

export const getColorTypeError = (status: string): string => {
  switch (status) {
    case DetailInfoType.SUCCESS:
      return 'blue';
    case DetailInfoType.WARNING:
      return 'orange';
    case DetailInfoType.ERROR:
      return 'red';
    default:
      throw new Error(`Unsupported status: ${status}`);
  }
};

export const sortedDetailErrors = (detailErrors: DetailInfoRowImportDTO[]) => {
  return detailErrors.sort((a, b) => {
    if (a.type === DetailInfoType.ERROR && b.type !== DetailInfoType.ERROR) {
      return -1;
    } else if (a.type !== DetailInfoType.ERROR && b.type === DetailInfoType.ERROR) {
      return 1;
    }
    return 0;
  });
};
