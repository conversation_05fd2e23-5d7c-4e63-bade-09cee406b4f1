import { CiRelationshipApi } from '@api/CiRelationshipApi';
import { ConfigItemApi } from '@api/ConfigItemApi';
import { NotificationSuccess, NotificationWarning } from '@common/utils/NotificationUtils';
import { buildCiTypeUrl, buildCiUrl, buildImpactedRuleDetailUrl } from '@common/utils/RouterUtils';
import { KanbanButton, KanbanTabsType, KanbanText, KanbanTooltip } from 'kanban-design-system';
import { KanbanSelect } from 'kanban-design-system';
import { ColumnType, KanbanTable, KanbanTableProps } from 'kanban-design-system';
import { KanbanTabs } from 'kanban-design-system';
import { Box, ComboboxItem, Container, Divider, Flex, Space } from '@mantine/core';
import type { CiRelationshipInfoModel, CiRelationshipInfoWithImpact } from '@models/CiRelationship';
import { ciRelationshipTypesSlice, getCiRelationshipTypes } from '@slices/CiRelationshipTypesSlice';
import { IconAffiliate } from '@tabler/icons-react';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useSearchParams } from 'react-router-dom';
import AddRelationshipPopup, { AddRelationshipPopupMethods } from './AddRelationshipPopup';
import { type CiRelationshipGraphMethods } from '../graph/CiRelationshipGraph';
import type { CIBusinessViews } from '@models/CIBusinessViews';
import { CiDetailScreenType, CiDetailSubTabType, ScreenTypeEnum } from '@common/constants/CiDetail';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { getSystemParamByKey } from '@slices/SystemParameterSlice';
import { SystemConfigParamKey } from '@common/constants/SystemConfigParam';
import { RuleAction } from '@models/ImpactedRule';
import styledCss from './CiRelationship.module.scss';
import { FlaggedImpactedCiAttachedType } from '@common/constants/CiManagement';
import { getRelationshipName } from '@common/utils/GoJsHelper';
import { toolBarConfigImpactMap } from '@pages/admins/changeAssessment/components/impactedCi/ImpactedCiFlagTableComponent';
import CiRelationshipGraphWrapper from '../graph/CiRelationshipGraphWrapper';
export interface ToolbarProps {
  locate: boolean;
  filter: boolean;
  save: boolean;
  downloadDiagram: boolean;
  viewUpOrViewStream: boolean;
  diagramStyle: boolean;
}

export interface HistoryChangeDiagram {
  data: CiRelationshipInfoModel[];
  upStream: boolean;
  downStream: boolean;
}
export type ShowActionDiagramConfigProps = {
  toolBar?: {
    isShowLocate?: boolean;
    isShowFilter?: boolean;
    isShowSave?: boolean;
    isShowDownload?: boolean;
    isShowUpStreamAndDownStream?: boolean;
    isShowChangeStyle?: boolean;
    isShowChangeLinkStyle?: boolean;
    isShowImpactToAndImpactBy?: boolean;
  };
  menu?: {
    isShowStreamImpact?: boolean;
    isShowHideCi?: boolean;
    isShowCiDetails?: boolean;
    isShowAddRelationship?: boolean;
  };
};

export type CiRelationshipProps = {
  ciId: number;
  ciTypeId: number;
  toolBarConfig?: ShowActionDiagramConfigProps;
  cIBusinessViews?: CIBusinessViews;
  isFromBusinessView?: boolean;
  isView?: boolean;
  onToggleFullScreenModel?: (newValue: boolean) => boolean | void;
  screenType?: ScreenTypeEnum;
  ciInChangePlan?: number[] | undefined;
  impactedCi?: number | undefined;
  attachedType?: FlaggedImpactedCiAttachedType | undefined;
  defaultLevel?: number;
};
const getArrLevel = (maxLevel: number): ComboboxItem[] => {
  if (maxLevel <= 0) {
    return [{ label: '1', value: '1' }];
  }
  return [
    { label: 'Max Level', value: maxLevel.toString() },
    ...Array.from({ length: maxLevel - 1 }, (_, i) => ({
      label: (i + 1).toString(),
      value: (i + 1).toString(),
    })),
  ];
};

export const CiRelationship = ({
  attachedType,
  cIBusinessViews,
  ciId,
  ciInChangePlan,
  ciTypeId,
  defaultLevel = 1,
  impactedCi,
  isFromBusinessView,
  isView,
  onToggleFullScreenModel,
  screenType,
  toolBarConfig,
}: CiRelationshipProps) => {
  const ciTypes = useGetCiTypes();
  const maxLevel = useSelector(getSystemParamByKey(SystemConfigParamKey.CI_MAX_LEVEL));
  const [currentTab, setCurrentTab] = useState<string>(isFromBusinessView ? CiDetailSubTabType.GRAPHVIEW : CiDetailSubTabType.LISTVIEW);
  const [searchParams, setSearchParams] = useSearchParams();
  const [currentLevel, setCurrentLevel] = useState(String(defaultLevel));
  const navigate = useNavigate();
  const [ciRelationships, setCiRelationships] = useState<CiRelationshipInfoModel[]>([]);
  const dispatch = useDispatch();
  const ciRelationshipTypes = useSelector(getCiRelationshipTypes).data;
  const [businessViews, setBusinessViews] = useState(cIBusinessViews);
  const [currentCiId] = useState(ciId);

  useEffect(() => {
    dispatch(ciRelationshipTypesSlice.actions.fetchForEmpty());
  }, [dispatch]);
  useEffect(() => {
    const subTab = searchParams.get('subTab');
    if (subTab) {
      setCurrentTab(subTab);
    }
  }, [searchParams]);
  useEffect(() => {
    if (businessViews?.dataActionParse?.level) {
      setCurrentLevel(businessViews.dataActionParse.level);
    }
  }, [businessViews]);

  // CI RELATION
  const fetchRelationship = useCallback(() => {
    if (screenType === ScreenTypeEnum.FLAGGED_CIS) {
      if (!ciInChangePlan?.length) {
        NotificationWarning({ message: 'Please enter CI in change plan to view Impact Map' });
        return;
      }
      CiRelationshipApi.flagCisForGraphView(ciInChangePlan, impactedCi, attachedType, Number(currentLevel))
        .then((res) => {
          setCiRelationships(res.data.data || []);
          if (res.data.message) {
            NotificationWarning({ message: res.data.message });
          }
        })
        .catch(() => {});
    } else if (CiDetailSubTabType.IMPACTMAP !== currentTab) {
      ConfigItemApi.getAllDirectRelationship(ciId, Number(currentLevel))
        .then((res) => {
          setCiRelationships(res.data || []);
        })
        .catch(() => {});
    }
  }, [screenType, currentTab, ciInChangePlan, impactedCi, attachedType, currentLevel, ciId]);

  useEffect(() => {
    fetchRelationship();
  }, [fetchRelationship]);

  const onDeleteData = (id: number) => {
    CiRelationshipApi.deleteById(id)
      .then(() => {
        setCiRelationships((prev) => {
          return prev.filter((x) => x.id !== id);
        });
        NotificationSuccess({
          message: 'Deleted successfully',
        });
      })
      .catch(() => {});
  };

  const deleteRelationships = useCallback(
    (ids: number[]) => {
      if (ids.length > 0) {
        CiRelationshipApi.deleteByIds(ids)
          .then(() => {
            NotificationSuccess({
              message: 'Deleted successfully',
            });
            const newData = ciRelationships.filter((item) => !ids.includes(item.id));
            setCiRelationships(newData);
          })
          .catch(() => {});
      }
    },
    [ciRelationships],
  );

  const handleOpenModalCreateNew = () => {
    // openModalCreateNew();
    createRelationshipPopupRef.current?.openModalCreateNew();
  };

  const ciRelationshipGraphRef = useRef<CiRelationshipGraphMethods | null>(null);

  const onSearched = useCallback(
    (datas: CiRelationshipInfoModel[], search: string): CiRelationshipInfoModel[] => {
      const lowerCaseSearch = search.toLowerCase();
      return datas.filter((item) => {
        const fromCIType = ciTypes.data.find((x) => x.id === item.fromCiTypeId)?.name || '';
        const toCIType = ciTypes.data.find((x) => x.id === item.toCiTypeId)?.name || '';
        const fromCI = item.fromCi === currentCiId ? 'Current CI' : item.fromCiName;
        const toCI = item.toCi === currentCiId ? 'Current CI' : item.toCiName;
        const currentRelationship = ciRelationshipTypes.find((x) => x.id === item.relationshipId);
        const relationship = `${currentRelationship?.type} - ${currentRelationship?.inverseType}`;
        if (
          fromCIType.toLowerCase().includes(lowerCaseSearch) ||
          toCIType.toLowerCase().includes(lowerCaseSearch) ||
          fromCI?.toLowerCase().includes(lowerCaseSearch) ||
          relationship.toLowerCase().includes(lowerCaseSearch) ||
          toCI?.toLowerCase().includes(lowerCaseSearch) ||
          item.id.toString().toLowerCase().includes(lowerCaseSearch)
        ) {
          return true;
        } else {
          return false;
        }
      });
    },
    [ciRelationshipTypes, ciTypes.data, currentCiId],
  );

  const getImpactStatus = (data: CiRelationshipInfoModel) => {
    return data.ciRuleRelationships ? 'Yes' : 'No';
  };

  const getRelationshipDisplayName = useCallback(
    (data: CiRelationshipInfoWithImpact) => {
      const currentRelationship = ciRelationshipTypes.find((x) => x.id === data.relationshipId);

      return getRelationshipName(!!data.impactedCi, data, currentRelationship);
    },
    [ciRelationshipTypes],
  );

  const getImpactDirection = useCallback(
    (data: CiRelationshipInfoWithImpact) => {
      let impactDirection = '';
      const currentName = data.fromCi === currentCiId ? data.fromCiName : data.toCi === currentCiId ? data.toCiName : '';

      data.ciRuleRelationships?.forEach((it) => {
        const fromCI = it.fromCi === currentCiId ? 'Current CI' : it.fromCi === data.fromCi ? data.fromCiName : data.toCiName;
        const toCI = it.toCi === currentCiId ? 'Current CI' : it.toCi === data.toCi ? data.toCiName : data.fromCiName;

        let directionName = '';
        if (fromCI === 'Current CI') {
          directionName = `${fromCI} impacted To ${toCI} | ${currentName} impacted To ${toCI}`;
        } else if (toCI === 'Current CI') {
          directionName = `${fromCI} impacted To ${toCI} | ${fromCI} impacted To ${currentName}`;
        } else {
          directionName = `${fromCI} impacted To ${toCI}`;
        }

        impactDirection = impactDirection ? `${impactDirection} | ${directionName}` : directionName;
      });
      return impactDirection;
    },
    [currentCiId],
  );

  const createRelationshipPopupRef = useRef<AddRelationshipPopupMethods | null>(null);

  const renderImpactedDirection = useCallback(
    (rowData: CiRelationshipInfoModel) => {
      return (
        <>
          {rowData.ciRuleRelationships?.map((it, idx) => {
            const ciRender = (ciTypeId: number, ciId: number, ciName: string, isCurrent: boolean) => (
              <KanbanButton
                size={'compact-xs'}
                radius={'lg'}
                variant={isCurrent ? 'outline' : ''}
                onClick={() => {
                  navigate(buildCiUrl(ciTypeId || 0, ciId));
                }}>
                {ciName}
              </KanbanButton>
            );

            // 4505: Update the logic to get [fromCi/toCi] from rowData -> [fromCi/toCi] from ciRuleRelationships
            const ci1 =
              it.fromCi === ciId
                ? ciRender(ciTypeId, ciId, 'Current CI', true)
                : ciRender(
                    (it.fromCi === rowData.fromCi ? rowData.fromCiTypeId : rowData.toCiTypeId) || 0,
                    it.fromCi || 0,
                    (it.fromCi === rowData.fromCi ? rowData.fromCiName : rowData.toCiName) || '',
                    false,
                  );
            const ci2 =
              it.toCi === ciId
                ? ciRender(ciTypeId, ciId, 'Current CI', true)
                : ciRender(
                    (it.toCi === rowData.toCi ? rowData.toCiTypeId : rowData.fromCiTypeId) || 0,
                    it.toCi || 0,
                    (it.toCi === rowData.toCi ? rowData.toCiName : rowData.fromCiName) || '',
                    false,
                  );

            //3849:ci relationship impacted to
            return (
              <>
                <Box key={it.impactedRuleId}>
                  {
                    <>
                      {ci1} impacted To {ci2}
                    </>
                  }
                </Box>
                {idx < (rowData.ciRuleRelationships?.length || 0) - 1 && <Divider w={'90%'} my={'xs'} />}
              </>
            );
          })}
        </>
      );
    },
    [ciId, ciTypeId, navigate],
  );
  const columns: ColumnType<CiRelationshipInfoWithImpact>[] = useMemo(() => {
    return [
      {
        title: 'ID',
        name: 'id',
        hidden: true,
      },
      {
        title: 'From CI Type',
        name: 'fromCiTypeName',

        customRender: (data, rowData) => {
          return rowData.fromCiTypeId ? (
            <KanbanButton
              size='compact-xs'
              radius={'lg'}
              onClick={() => {
                navigate(buildCiTypeUrl(rowData.fromCiTypeId || 0));
              }}>
              {rowData.fromCiTypeName}
            </KanbanButton>
          ) : undefined;
        },
      },
      {
        title: 'To CI Type',
        name: 'toCiTypeName',
        customRender: (data, rowData) => {
          return rowData.toCiTypeId ? (
            <KanbanButton
              size='compact-xs'
              radius={'lg'}
              onClick={() => {
                navigate(buildCiTypeUrl(rowData.toCiTypeId || 0));
              }}>
              {rowData.toCiTypeName}
            </KanbanButton>
          ) : undefined;
        },
      },
      {
        title: 'From CI',
        name: 'fromCiName',
        customRender: (data, rowData) => {
          return rowData.fromCi ? (
            <KanbanButton
              size='compact-xs'
              radius={'lg'}
              variant={rowData.fromCi === ciId ? 'outline' : ''}
              onClick={() => {
                navigate(buildCiUrl(rowData.fromCiTypeId || 0, rowData.fromCi));
              }}>
              {rowData.fromCi === ciId ? 'Current CI' : data}
            </KanbanButton>
          ) : undefined;
        },
        advancedFilter: {
          enabled: true,
          accessorFn: (rowData) => {
            return rowData.fromCi === ciId ? `Current CI | ${rowData.fromCiName}` : rowData.fromCiName;
          },
        },
      },
      {
        title: 'To CI',
        name: 'toCiName',
        customRender: (data, rowData) => {
          return rowData.toCi ? (
            <KanbanButton
              size='compact-xs'
              radius={'lg'}
              variant={rowData.toCi === ciId ? 'outline' : ''}
              onClick={() => {
                navigate(buildCiUrl(rowData.toCiTypeId || 0, rowData.toCi));
              }}>
              {rowData.toCi === ciId ? 'Current CI' : data}
            </KanbanButton>
          ) : undefined;
        },
        advancedFilter: {
          enabled: true,
          accessorFn: (rowData) => {
            return rowData.toCi === ciId ? `Current CI | ${rowData.toCiName}` : rowData.toCiName;
          },
        },
      },
      //3849: remove relationship inverse type name
      {
        title: 'Relationship',
        name: 'relationshipName',
        customRender: (_, rowData) => {
          return getRelationshipDisplayName(rowData);
        },
        advancedFilter: {
          enabled: true,
          accessorFn: (rowData) => {
            return getRelationshipDisplayName(rowData);
          },
        },
      },
      {
        title: 'Has impact',
        name: 'hasImpact',
        customRender: (_, rowData) => {
          return getImpactStatus(rowData);
        },
        advancedFilter: {
          enabled: true,
          accessorFn: (rowData) => {
            return getImpactStatus(rowData);
          },
        },
      },
      {
        title: 'Impact direction',
        name: 'impactDirection',
        width: '20%',
        customRender: (_, rowData) => renderImpactedDirection(rowData),
        advancedFilter: {
          enabled: true,
          accessorFn: (rowData) => {
            return getImpactDirection(rowData);
          },
        },
      },
      {
        title: 'Impact Rule',
        name: 'ruleName',
        customRender: (_, rowData) => {
          return (
            <>
              {rowData.ciRuleRelationships?.map((it, idx) => {
                return (
                  <>
                    <KanbanTooltip key={it.impactedRuleId} label={it.impactedRuleName}>
                      <KanbanButton
                        size='compact-xs'
                        radius={'lg'}
                        onClick={() => {
                          if (it.impactedRuleId) {
                            navigate(buildImpactedRuleDetailUrl(it.impactedRuleId, RuleAction.VIEW));
                          }
                        }}>
                        <KanbanText className={styledCss.clipText}>{it.impactedRuleName}</KanbanText>
                      </KanbanButton>
                    </KanbanTooltip>
                    <br />
                    {idx < (rowData.ciRuleRelationships?.length || 0) - 1 && <Divider w={'90%'} my={'xs'} />}
                  </>
                );
              })}
            </>
          );
        },
        advancedFilter: {
          enabled: true,
          accessorFn: (rowData) => {
            const impactedRuleNames = rowData.ciRuleRelationships?.map((it) => it.impactedRuleName) || [];
            return impactedRuleNames.join(' | ');
          },
        },
      },
    ];
  }, [ciId, getImpactDirection, getRelationshipDisplayName, navigate, renderImpactedDirection]);

  const tableProps: KanbanTableProps<CiRelationshipInfoModel> = useMemo(() => {
    return {
      title: 'Relationships',
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 300,
        onSearched: onSearched,
      },
      sortable: {
        enable: false,
      },
      advancedFilterable: {
        enable: !!ciRelationships.length,
        debounceTime: 1000,
        resetOnClose: true,
        compactMode: false,
      },
      columns: columns,
      data: ciRelationships,
      pagination: {
        enable: true,
      },
      actions: {
        deletable: isCurrentUserMatchPermissions([AclPermission.deleteCiRelationship])
          ? {
              onDeleted(data) {
                onDeleteData(data.id);
              },
            }
          : undefined,
      },
      selectableRows: {
        enable: !!isCurrentUserMatchPermissions([AclPermission.deleteCiRelationship]),
        onDeleted(rows) {
          deleteRelationships(rows.map((x) => x.id));
        },
      },
    };
  }, [ciRelationships, columns, deleteRelationships, onSearched]);

  const customTableProps = useMemo(() => {
    const tablePropsUpdate = { ...tableProps };
    if (isView) {
      const { actions, selectableRows, ...rest } = tablePropsUpdate;
      if (selectableRows && selectableRows.onDeleted) {
        selectableRows.enable = false;
      }
      if (actions && actions.deletable) {
        delete actions.deletable;
      }
      return { ...rest, selectableRows, actions };
    }

    return tablePropsUpdate;
  }, [isView, tableProps]);

  const updatedToolBarConfig = (toolBarConfigImpactMap: ShowActionDiagramConfigProps) => {
    return {
      ...toolBarConfigImpactMap,
      toolBar: {
        ...toolBarConfigImpactMap.toolBar,
        isShowImpactToAndImpactBy: true,
      },
    };
  };

  const [useDiagramVirtualized, setUseDiagramVirtualized] = useState<boolean>(false);
  const onSetUseDiagramVirtualized = (useDiagramVirtualized: boolean) => {
    setUseDiagramVirtualized(useDiagramVirtualized);
  };

  //ScreenTypeEnum.FLAGGED_CIS !== screenType : allTab
  const allTab: KanbanTabsType = {
    LISTVIEW: {
      title: 'List view',
      content: (
        <>
          <KanbanTable {...customTableProps} />
        </>
      ),
    },
    GRAPHVIEW: {
      title: 'Graph view',
      content: (
        <>
          <CiRelationshipGraphWrapper
            toolBarConfig={toolBarConfig}
            ciId={ciId}
            ciTypeId={ciTypeId}
            ciRelationships={ciRelationships}
            ref={ciRelationshipGraphRef}
            dataViewGraph={businessViews}
            currentLevel={currentLevel}
            showLayout={true}
            isView={isView}
            onToggleFullScreenModel={onToggleFullScreenModel}
            useDiagramVirtualized={useDiagramVirtualized}
            onSetDiagramVirtualized={onSetUseDiagramVirtualized}
          />
        </>
      ),
    },
    IMPACTMAP: {
      title: 'Impact map',
      content: (
        <>
          <CiRelationshipGraphWrapper
            toolBarConfig={updatedToolBarConfig(toolBarConfigImpactMap)}
            ciId={ciId}
            ciTypeId={ciTypeId}
            ciRelationships={ciRelationships}
            ref={ciRelationshipGraphRef}
            //dataViewGraph={businessViews}
            currentLevel={currentLevel}
            showLayout={true}
            isView={isView}
            activeSubTab={currentTab}
            useDiagramVirtualized={useDiagramVirtualized}
            onToggleFullScreenModel={onToggleFullScreenModel}
            onSetDiagramVirtualized={onSetUseDiagramVirtualized}
          />
        </>
      ),
    },
  };

  //ScreenTypeEnum.FLAGGED_CIS === screenType : tabListViewAndGraphView
  const tabListViewAndGraphView: KanbanTabsType = {
    LISTVIEW: {
      title: 'List view',
      content: (
        <>
          <KanbanTable {...customTableProps} />
        </>
      ),
    },
    GRAPHVIEW: {
      title: 'Graph view',
      content: (
        <>
          <CiRelationshipGraphWrapper
            toolBarConfig={toolBarConfig}
            ciId={ciId}
            ciTypeId={ciTypeId}
            ciRelationships={ciRelationships}
            ref={ciRelationshipGraphRef}
            //dataViewGraph={businessViews}
            currentLevel={currentLevel}
            showLayout={true}
            isView={isView}
            onToggleFullScreenModel={onToggleFullScreenModel}
            useDiagramVirtualized={useDiagramVirtualized}
            onSetDiagramVirtualized={onSetUseDiagramVirtualized}
          />
        </>
      ),
    },
  };
  return (
    <>
      <AddRelationshipPopup ref={createRelationshipPopupRef} ciId={ciId} ciTypeId={ciTypeId} fetchRelationship={fetchRelationship} />
      <Flex justify={'flex-end'}>
        <Container w={100} mr={0} p={0}>
          <KanbanSelect
            data={getArrLevel(Number(maxLevel?.value || 0))}
            value={currentLevel}
            onChange={(e) => {
              if (e) {
                setBusinessViews((prev) => {
                  if (prev) {
                    return {
                      ...prev,
                      graphView: undefined,
                      dataAction: undefined,
                      dataActionParse: undefined,
                    };
                  }
                  return undefined;
                });
                setCurrentLevel(e);
              }
            }}
            size={'xs'}
          />
        </Container>
        <Space w={'sm'} />
        {!isView && (
          <GuardComponent requirePermissions={[AclPermission.createCiRelationship]} hiddenOnUnSatisfy>
            <KanbanButton
              size='xs'
              onClick={() => {
                if (currentTab === CiDetailSubTabType.GRAPHVIEW) {
                  ciRelationshipGraphRef.current?.handleOpenModalCreateNewRelationship(true);
                } else {
                  handleOpenModalCreateNew();
                }
              }}
              leftSection={<IconAffiliate />}>
              Add relationship
            </KanbanButton>
          </GuardComponent>
        )}
      </Flex>
      <KanbanTabs
        configs={{
          defaultValue: isFromBusinessView ? CiDetailSubTabType.GRAPHVIEW : CiDetailSubTabType.LISTVIEW,
          value: currentTab,
          onChange: (value) => {
            // #4828: When adding the impact map tab, it will affect the comment API call to prevent the API from being called twice.
            // if (value === CiDetailSubTabType.LISTVIEW) {
            //   fetchRelationship();
            // }
            setCurrentTab(value || CiDetailSubTabType.LISTVIEW);
            if (!isFromBusinessView) {
              //preview-discovery :make ci detail screen -> pop up: when change tab =>  change param instead of redirect url
              // navigate(buildCiUrl(ciTypeId, ciId, CiDetailScreenType.RELATIONSHIPS, value || CiDetailSubTabType.LISTVIEW));
              const params = new URLSearchParams();
              params.set('tab', CiDetailScreenType.RELATIONSHIPS);
              params.set('subtab', value || CiDetailSubTabType.LISTVIEW);
              setSearchParams(params);
            }
          },
        }}
        tabs={ScreenTypeEnum.FLAGGED_CIS !== screenType ? allTab : tabListViewAndGraphView}
      />
    </>
  );
};
CiRelationship.whyDidYouRender = true;

export default CiRelationship;
