export enum FileImportMessage {
  MISSING_REQUIRED_FIELD = 'Field is mandatory.',
  IMPORT_FILE_SUCCESS = 'Import file success.',
  PREVIEW_FILE_SUCCESS = 'Preview file success.',
  IMPORT_FILE_FAIL = 'Import file fail.',
  PREVIEW_FILE_FAIL = 'Preview file fail.',
  NO_SELECTED_ROW = 'Please select the row before Import.',
  INVALID_FIELD_FORMAT_DATE = 'Field format date is valid.',
  INVALID_EXCEL_FORMAT = 'File not format excel',
  CI_TYPE_REQUIRED = 'Does not choose CI Type',
  LOAD_FILE_FAIL = 'Load file fail.',
  FILE_NOT_DATA = 'File not data.',
  KEY_EXIST_MAPPING = 'This column was mapped.',
}
