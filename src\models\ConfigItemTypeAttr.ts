import { CiTypeColumn, CiTypeAttributeDataType } from './CiType';

export type ConfigItemTypeAttrModel = {
  id: number;
  name: string;
  description?: string;
  ciTypeId?: number;
  ciTypeName?: string;
  type?: CiTypeAttributeDataType;
  mandatory?: boolean;
  order?: number;
  column: CiTypeColumn;
  tempId?: string;
  options?: string;
  hashId?: string;
  ciTypeReferenceId?: number;
  ciTypeReferenceData?: CiAttributeInfoModel[];
  deleted?: boolean;
};

export type CiAttributeInfoModel = {
  ciId: number;
  ciTypeId: number;
  ciName: string;
  ciAttributeId: number;
  ciAttributeValue?: string;
  ciTypeReferenceId: number;
  hashId?: string;
};

export type CiTypeAttributeReferInfoModel = {
  id: number;
  name: string;
  ciTypeId: number;
  ciTypeName: string;
  referAttributeName: string;
};

export type ConfigItemAttributeObject = {
  attributesDefault: ConfigItemTypeAttrModel[];
  attributes: ConfigItemTypeAttrModel[];
};

export type CiTypeReferenceModel = {
  id: number;
  ciTypeId: number;
  ciTypeAttributeId: number;
  ciTypeAttributeTempId?: string;
};

export type CiTypeReferFieldsModel = {
  id: number;
  ciTypeId: number;
  ciTypeName: string;
  ciTypeAttributeId: number;
  ciTypeAttributeName: string;
};

export enum CiTypeAttributeType {
  DEFAULT = 'DEFAULT',
  CUSTOM = 'CUSTOM',
}
export default 1;
