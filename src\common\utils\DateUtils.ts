import { TimeUnitEnum } from '@common/constants/TimeUnitEnum';
import { DateFilterOptionEnum, RangeDateModel } from '@models/ChangeSdp';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';

export const ISO_DATE_FORMAT = 'YYYY-MM-DDTHH:mm:ss';
export const DATE_FORMAT_2 = 'YYYYMMDD HH:mm:ss';
export const DD_MM_YYYY_HH_MM_FORMAT = 'DD/MM/YYYY HH:mm';
export const DD_MM_YYYY_FORMAT = 'DD/MM/YYYY';
export const ISO_DATE_TIME_FORMAT = 'YYYY-MM-DDTHH:mm:ss.sssZ';
export const HH_MM_SS_DD_MM_YYYY_FORMAT = 'HH:mm:ss DD/MM/YYYY';
export const DD_MM_YYYY_HH_MM_SS_FORMAT = 'DD/MM/YYYY HH:mm:ss';

export function dateToString(value: any, format: string = DD_MM_YYYY_HH_MM_FORMAT) {
  return dayjs(value).format(format);
}

export function stringToDate(value: string) {
  try {
    const result = new Date(value);
    if (result instanceof Date && !isNaN(result.valueOf())) {
      return result;
    }
    return undefined;
  } catch (e) {
    return undefined;
  }
}

export function stringToDateWithFormat(value: string, format: string) {
  dayjs.extend(customParseFormat);
  try {
    const result = dayjs(value, format).toDate();
    if (result instanceof Date && !isNaN(result.valueOf())) {
      return result;
    }
    return undefined;
  } catch (e) {
    return undefined;
  }
}

export function getAllFormatDateAtExcel() {
  const dateFormats: string[] = [
    'yyyy/MM/dd',
    'yyyy.MM.dd',
    'yyyy-MM-dd',
    'dd-MM-yyyy',
    'dd/MM/yyyy',
    'dd.MM.yyyy',
    'MM-dd-yyyy',
    'MM/dd/yyyy',
    'MM.dd.yyyy',
  ];

  return dateFormats;
}

export const maxDate = new Date(8640000000000000);
export const minDate = new Date(-8640000000000000);
const nowDate = new Date();
export const startDate = new Date(0);
export const endOfToday = new Date(nowDate.getFullYear(), nowDate.getMonth(), nowDate.getDate(), 23, 59, 59, 999);
const getStartOfToday = (): Date => new Date(nowDate.getFullYear(), nowDate.getMonth(), nowDate.getDate());
const getStartOfWeek = (): Date => {
  const startOfToday = getStartOfToday();
  const startOfWeek = new Date(startOfToday);
  startOfWeek.setDate(startOfToday.getDate() - startOfToday.getDay() + 1);
  return startOfWeek;
};
const getStartOfMonth = (): Date => new Date(nowDate.getFullYear(), nowDate.getMonth(), 1);
const getStartOfYear = (): Date => new Date(nowDate.getFullYear(), 0, 1);

export function getDateRange(option: DateFilterOptionEnum): RangeDateModel {
  const ranges: Record<DateFilterOptionEnum, RangeDateModel> = {
    [DateFilterOptionEnum.TODAY]: { fromDate: getStartOfToday(), toDate: endOfToday },
    [DateFilterOptionEnum.THIS_WEEK]: { fromDate: getStartOfWeek(), toDate: endOfToday },
    [DateFilterOptionEnum.THIS_MONTH]: { fromDate: getStartOfMonth(), toDate: endOfToday },
    [DateFilterOptionEnum.THIS_YEAR]: { fromDate: getStartOfYear(), toDate: endOfToday },
    [DateFilterOptionEnum.ALL]: { fromDate: startDate, toDate: endOfToday },
    [DateFilterOptionEnum.CUSTOM]: { fromDate: getStartOfToday(), toDate: endOfToday },
  };
  return ranges[option];
}

export const calculateNextRuns = (
  startDate: Date,
  endDate: Date | undefined,
  frequency: TimeUnitEnum | undefined,
  intervalTime: number,
  numRuns: number,
): string[] => {
  // Check if the required parameters are provided and valid
  if (!startDate || !frequency || !intervalTime || TimeUnitEnum.ONCE_TIME === frequency) {
    return [];
  }

  const nextRuns: string[] = [];
  const currentDate = new Date();
  let nextRun = new Date(startDate);

  while (nextRun < currentDate) {
    nextRun = calculateNextExecutionDate(nextRun, frequency, intervalTime);
  }

  for (let i = 0; i < numRuns; i++) {
    if (endDate && nextRun.getTime() > new Date(endDate).getTime()) {
      break;
    }

    nextRuns.push(dateToString(nextRun, DD_MM_YYYY_HH_MM_SS_FORMAT));
    nextRun = calculateNextExecutionDate(nextRun, frequency, intervalTime);
  }

  return nextRuns;
};

export const calculateNextExecutionDate = (date: Date, frequency: TimeUnitEnum | undefined, intervalTime: number): Date => {
  const newDate = new Date(date);

  switch (frequency) {
    case TimeUnitEnum.MINUTE:
      newDate.setMinutes(newDate.getMinutes() + intervalTime);
      break;
    case TimeUnitEnum.HOUR:
      newDate.setHours(newDate.getHours() + intervalTime);
      break;
    case TimeUnitEnum.DAY:
      newDate.setDate(newDate.getDate() + intervalTime);
      break;
    case TimeUnitEnum.WEEK:
      newDate.setDate(newDate.getDate() + 7 * intervalTime);
      break;
    default:
      break;
  }

  return newDate;
};
