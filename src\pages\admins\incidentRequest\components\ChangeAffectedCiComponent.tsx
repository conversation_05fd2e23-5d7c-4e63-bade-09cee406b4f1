import { Box, Flex } from '@mantine/core';
import { ImpactedDataTableComponent } from './ImpactedDataTableComponent';
import React from 'react';
import { ImpactedChangeProps, ImpactedTypeTable } from '@models/ChangeAssessment';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { IconPlus } from '@tabler/icons-react';

export type AffectedCiComponentProps = ImpactedChangeProps & {
  onProcessAdd: boolean;
  onAddCi: (val: boolean) => void;
  onDeleteCi: (val: number[]) => void;
  allowEdit: boolean;
};
export const AffectedCiComponent: React.FC<AffectedCiComponentProps> = ({ allowEdit, cis, onAddCi, onDeleteCi, onProcessAdd }) => {
  return (
    <Box>
      <Box mt='sm'>
        <HeaderTitleComponent
          title={'List Affected Cis'}
          rightSection={
            <Flex gap='xs'>
              {allowEdit && (
                <KanbanButton
                  leftSection={<IconPlus />}
                  disabled={onProcessAdd}
                  onClick={() => {
                    onAddCi(true);
                  }}>
                  Add Affected CIs
                </KanbanButton>
              )}
            </Flex>
          }
        />
      </Box>
      <ImpactedDataTableComponent typeTable={ImpactedTypeTable.CI} cis={cis} onDelete={onDeleteCi} allowEdit={allowEdit} />
    </Box>
  );
};
