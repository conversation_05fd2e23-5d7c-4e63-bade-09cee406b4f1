export type OutgoingMailConfigDto = {
  id?: number;
  connectionProtocol?: string;
  authenticationType?: string;
  serverName?: string;
  serverPort?: number;
  alternateServerName?: string;
  senderName?: string;
  replyTo?: string;
  authenticationEnabled?: boolean;
  mailUsername?: string;
  mailPassword?: string;
  protocol?: string;
  oauthClientId?: string;
  oauthClientSecret?: string;
  oauthAuthorizationUrl?: string;
  oauthTokenUrl?: string;
  oauthScope?: string;
  ewsConnectUrl?: string;
  proxyEnabled?: boolean;
  proxyHost?: string;
  proxyPort?: number;
  proxyUsername?: string;
  proxyPassword?: string;
  tlsEnabled?: boolean;
};
export type AuthenticationDetailDto = {
  oauthClientId?: string;
  oauthClientSecret?: string;
  oauthAuthorizationUrl?: string;
  oauthTokenUrl?: string;
  oauthScope?: string;
};

export type ProxyDto = {
  proxyEnabled: boolean;
  proxyHost: string;
  proxyPort: number;
  proxyUsername: string;
  proxyPassword: string;
};

export type OutGoingMailProps = {
  info: OutgoingMailConfigDto;
  setInfo: (val: OutgoingMailConfigDto) => void;
};
