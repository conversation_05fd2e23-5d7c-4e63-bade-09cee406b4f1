import { NotificationTemplateApi } from '@api/NotificationTemplateApi';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { useDisclosure } from '@mantine/hooks';
import type { NotificationTemplateDto } from '@models/NotificationTemplate';
import { ColumnType, KanbanButton, KanbanCheckbox, KanbanTable, KanbanTableProps } from 'kanban-design-system';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { NotificationTemplateDetailPage } from './NotificationTemplateDetailPage';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { BreadcrumbComponent } from '../breadcrumb/BreadcrumbComponent';

export const NotificaticationTemplateListPage = () => {
  const [listTemplate, setListTemplate] = useState<NotificationTemplateDto[]>([]);
  const [openedModalEditTemplate, { close: closeModalEditTemplate, open: openModalEditTemplate }] = useDisclosure(false);
  const [currentTemplateId, setCurrentTemplateId] = useState<number>();
  const handleOpenModalEditTemplate = useCallback(
    (data: NotificationTemplateDto) => {
      setCurrentTemplateId(data.id);
      openModalEditTemplate();
    },
    [openModalEditTemplate],
  );

  const fetchListTemplate = useCallback(() => {
    NotificationTemplateApi.getAll()
      .then((res) => {
        setListTemplate(res.data);
      })
      .catch(() => {})
      .finally();
  }, []);

  const handleActivateTemplates = useCallback((listTemplate: NotificationTemplateDto[]) => {
    NotificationTemplateApi.activateTemplates(listTemplate)
      .then(() => {
        NotificationSuccess({
          message: 'Config template activation successfully',
        });
      })
      .catch(() => {});
  }, []);

  useEffect(() => {
    fetchListTemplate();
  }, [fetchListTemplate]);

  const handleChangeActiveTemplate = useCallback((rowData: NotificationTemplateDto, data: boolean, listTemplate: NotificationTemplateDto[]) => {
    const index = listTemplate.findIndex((value) => {
      return value.id === rowData.id;
    });
    setListTemplate((prevList) => {
      const updateList = prevList.map((item, idx) => {
        return idx === index ? { ...item, active: data } : item;
      });
      return updateList;
    });
  }, []);
  const columns: ColumnType<NotificationTemplateDto>[] = useMemo(
    () => [
      {
        name: 'active',
        title: 'Active',
        sortable: false,
        width: '3%',
        customRender: (data: boolean, rowData) => {
          return (
            <KanbanCheckbox
              variant='transparent'
              size={'sm'}
              checked={data}
              onChange={(e) => handleChangeActiveTemplate(rowData, e.target.checked, listTemplate)}
            />
          );
        },
      },
      {
        name: 'description',
        title: 'Notification name',
        width: '90%',
      },
    ],
    [handleChangeActiveTemplate, listTemplate],
  );
  const tableProps: KanbanTableProps<NotificationTemplateDto> = useMemo(() => {
    return {
      columns: columns,
      data: listTemplate,
      showNumericalOrderColumn: true,
      actions: {
        customAction: (data: NotificationTemplateDto) => {
          return (
            <GuardComponent requirePermissions={[AclPermission.viewDetailNotificationTemplate]} hiddenOnUnSatisfy>
              <KanbanButton
                variant='transparent'
                size={'sm'}
                onClick={() => {
                  handleOpenModalEditTemplate(data);
                }}>
                Customize template
              </KanbanButton>
            </GuardComponent>
          );
        },
      },
    };
  }, [listTemplate, handleOpenModalEditTemplate, columns]);
  return (
    <>
      {/* 4736 email template  */}
      <BreadcrumbComponent />

      <HeaderTitleComponent
        rightSection={
          <GuardComponent requirePermissions={[AclPermission.changeActiveNotificationTemplate]} hiddenOnUnSatisfy>
            <KanbanButton onClick={() => handleActivateTemplates(listTemplate)}>Save</KanbanButton>
          </GuardComponent>
        }
        title='Notification Templates'
      />
      <KanbanTable {...tableProps} />
      <NotificationTemplateDetailPage
        id={currentTemplateId}
        closeModalEditTemplate={closeModalEditTemplate}
        openedModalEditTemplate={openedModalEditTemplate}
      />
    </>
  );
};
