import type { PayloadAction } from '@reduxjs/toolkit';

export type PayloadActionWithCallbackDataType<T, S = unknown, E = unknown> = {
  data: T;
  onSuccess?: PayloadAction<S>;
  onError?: PayloadAction<E>;
  onSuccessCallback?: (response: S) => void;
  onErrorCallback?: (error: E) => void;
};

export interface PayloadActionWithCallback<T, S = unknown, E = unknown> extends PayloadAction<PayloadActionWithCallbackDataType<T, S, E>> {}

export default 1;
