import { BaseApi } from '@core/api/BaseApi';
import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
import { DiscoveryPreviewDataCiDetailModel, DiscoveryPreviewDataCiGroupModel, DiscoveryPreviewDataCiModel } from '@models/DiscoveryPreviewDataCi';
import { CiIdentifierLogDto } from '@models/CiIdentifierRule';
import { BaseUrl } from '@core/api/BaseUrl';

export type DiscoveryPreviewDataCiResponseModel = PaginationResponseModel<DiscoveryPreviewDataCiModel>;
export type DiscoveryPreviewDataCiRequestModel = PaginationRequestModel<DiscoveryPreviewDataCiModel>;
export type CiIdentifierLogResponseDto = PaginationResponseModel<CiIdentifierLogDto>;
export type DiscoveryPreviewDataCiGroupResponseModel = PaginationResponseModel<DiscoveryPreviewDataCiGroupModel>;
export type DiscoveryPreviewDataCiGroupRequestModel = PaginationRequestModel<DiscoveryPreviewDataCiGroupModel>;

export class DiscoveryPreviewDataCiApi extends BaseApi {
  static baseUrl = BaseUrl.discoveryPreviewDataCis;

  static getAllDiscoveryPreviewDataCi(
    pagination: DiscoveryPreviewDataCiRequestModel,
    queryParams: Record<string, any>,
    controller?: AbortController,
  ) {
    return BaseApi.postData<DiscoveryPreviewDataCiResponseModel>(`${this.baseUrl}/detail`, pagination, queryParams, {}, {}, controller);
  }

  static getAllDiscoveryPreviewDataCiGroup(pagination: DiscoveryPreviewDataCiGroupRequestModel, controller?: AbortController) {
    return BaseApi.postData<DiscoveryPreviewDataCiGroupResponseModel>(`${this.baseUrl}`, pagination, {}, {}, {}, controller);
  }

  static getDiscoveryPreviewDataCiGroup(queryParams: Record<string, any>) {
    return BaseApi.getData<DiscoveryPreviewDataCiGroupModel>(`${this.baseUrl}`, queryParams);
  }

  static getById(id: number) {
    return BaseApi.getData<DiscoveryPreviewDataCiModel>(`${this.baseUrl}/${id}`);
  }

  static getLatestCiAttributeChange(ciId: number) {
    return BaseApi.getData<DiscoveryPreviewDataCiDetailModel[]>(`${this.baseUrl}/ci-attributes/latest?ciId=${ciId}`);
  }

  static findAllDiscoveryDataCiLogByRuleId(pagination: PaginationRequestModel<CiIdentifierLogDto>, ruleId?: number) {
    return BaseApi.getData<CiIdentifierLogResponseDto>(`${this.baseUrl}/logs?ruleId=${ruleId}`, pagination);
  }

  static transformDataDiscoveryToCi(ruleId: number) {
    return BaseApi.postData<boolean>(`${this.baseUrl}/${ruleId}/transform-cis`);
  }
}
