.invalidField {
  color: var(--mantine-color-red-filled);
}
.inheritField {
  color: inherit;
}

.tableCell {
  position: relative;
  padding-right: var(--mantine-spacing-md);
  height: 100%;
  width: 100%;
}

.content {
  position: relative;
  padding-bottom: 0;
}

.topRightButton {
  position: absolute;
  top: 0;
  right: 0;
}

.clipText {
  white-space: nowrap;
  width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 051224 cai tien comment text area
.textArea textarea {
  height: 500px;
}
