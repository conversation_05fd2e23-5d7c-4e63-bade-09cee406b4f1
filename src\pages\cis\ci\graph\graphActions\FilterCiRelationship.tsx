import type { GoJs } from '@common/libs';
import { LinkDataTemplate, appendNodeWithDataNode, getNodesOnPath, hiddenNode, removeLink } from '@common/utils/GoJsHelper';
import { KanbanCheckbox } from 'kanban-design-system';
import { KanbanInput } from 'kanban-design-system';
import { KanbanSwitch } from 'kanban-design-system';
import { KanbanTooltip } from 'kanban-design-system';
import { ComboboxItem, Divider, Group, Popover, rem, ThemeIcon } from '@mantine/core';
import { IconFilter, IconSearch } from '@tabler/icons-react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { KanbanTitle } from 'kanban-design-system';
import stylesCss from './FilterCiRelationship.module.scss';
import go, { ObjectData } from '@common/libs/gojs/go';
import type { ToolbarActionProps } from '../CiRelationshipGraph';
interface FilterLevelProps {
  nodeId: number;
  nodeName: string;
  minLevel: number;
}

interface FilterDataCheckBoxInfo {
  ciNameCheckeds: number[];
  ciRelationshipCheckeds: number[];
  ciLevelCheckeds: number[];
  ciTypeCheckeds: number[];
  ciNameCheckboxs: ComboboxItem[];
  ciLevelCheckboxs: ComboboxItem[];
  ciTypeCheckboxs: ComboboxItem[];
  ciRelationshipCheckboxs: ComboboxItem[];
}
/**
 * get all with min level info
 * @param goDiagram
 */
export function getNodeLevels(goDiagram: go.Diagram): FilterLevelProps[] {
  if (!goDiagram) {
    return [];
  }
  const nodeRoot = goDiagram.findNodesByExample({ isRoot: true }).first();
  if (!nodeRoot) {
    return [];
  }

  const queue: { nodeId: number; level: number }[] = [{ nodeId: nodeRoot.data.key, level: 0 }];
  const visited: Set<number> = new Set();
  const nodeLevels: { nodeId: number; nodeName: string; minLevel: number }[] = [];
  const nodeNames: Record<number, string> = {};

  // save name node
  goDiagram.nodes.each((node) => {
    const key = node.data.key;
    const label = node.data.label;
    if (key !== undefined && label !== undefined) {
      nodeNames[key] = label;
    }
  });

  while (queue.length > 0) {
    const queueNode = queue.shift();
    if (!queueNode) {
      continue;
    }
    const { level, nodeId } = queueNode;
    if (visited.has(nodeId)) {
      continue; // it's visited
    }
    visited.add(nodeId);

    nodeLevels.push({ nodeId, nodeName: nodeNames[nodeId], minLevel: level });

    const findNodeForKey = goDiagram.findNodeForKey(nodeId);
    if (!findNodeForKey) {
      continue;
    }
    const connectedNodes = findNodeForKey.findNodesConnected();
    connectedNodes.each((connectedNode) => {
      const nextNodeId = connectedNode.data.key;
      if (!visited.has(nextNodeId)) {
        queue.push({ nodeId: nextNodeId, level: level + 1 });
      }
    });
  }

  return nodeLevels;
}
/**
 * get checkbox filter
 * @param goDiagram
 * @returns
 */
function getCheckBoxInfo(goDiagram: go.Diagram | undefined): FilterDataCheckBoxInfo {
  const info: FilterDataCheckBoxInfo = {
    ciNameCheckeds: [],
    ciRelationshipCheckeds: [],
    ciLevelCheckeds: [],
    ciTypeCheckeds: [],
    ciNameCheckboxs: [],
    ciLevelCheckboxs: [],
    ciTypeCheckboxs: [],
    ciRelationshipCheckboxs: [],
  };
  if (!goDiagram) {
    return info;
  }
  const nodeLevels = getNodeLevels(goDiagram);

  const ciLevelDiagram: ComboboxItem[] = [];
  const ciNameDiagram: ComboboxItem[] = [];
  const ciNameChecked: number[] = [];
  const keyNodes: number[] = [];

  const ciTypeDiagram: ComboboxItem[] = [];
  const ciTypeChecked: number[] = [];

  goDiagram.nodes.each((node) => {
    if (!node.data.isRoot) {
      const newItem = { value: `${node.data.key}`, label: node.data.label };
      ciNameDiagram.push(newItem);
      ciNameChecked.push(node.data.key);
      keyNodes.push(node.data.key);

      // set ciType checkbox
      const newItemType = { value: `${node.data.data.ciTypeId}`, label: node.data.data.ciTypeName };
      if (!ciTypeDiagram.some((ciType) => ciType.value === newItemType.value)) {
        ciTypeDiagram.push(newItemType);
        ciTypeChecked.push(node.data.data.ciTypeId);
      }
    }
  });
  // set relationship checked
  const ciRelationshipDiagram: ComboboxItem[] = [];
  const ciRelationshipChecked: number[] = [];
  goDiagram.links.each((link) => {
    const newItemRelationship = { value: `${link.data.relationshipId}`, label: link.data.text };
    if (!ciRelationshipDiagram.some((relationship) => relationship.value === newItemRelationship.value)) {
      ciRelationshipDiagram.push(newItemRelationship);
      ciRelationshipChecked.push(link.data.relationshipId);
    }
  });
  const ciLevelChecked: number[] = [];
  nodeLevels.forEach((node) => {
    if (!ciLevelDiagram.some((item) => Number(item.value) === node.minLevel) && node.minLevel !== 0) {
      const newItem = { value: `${node.minLevel}`, label: `${node.minLevel}` };
      ciLevelDiagram.push(newItem);
      ciLevelChecked.push(node.minLevel);
    }
  });
  info.ciNameCheckeds = ciNameChecked;
  info.ciTypeCheckeds = ciTypeChecked;
  info.ciRelationshipCheckeds = ciRelationshipChecked;
  info.ciLevelCheckeds = ciLevelChecked;
  info.ciNameCheckboxs = ciNameDiagram;
  info.ciNameCheckboxs = ciNameDiagram;
  info.ciLevelCheckboxs = ciLevelDiagram;
  info.ciTypeCheckboxs = ciTypeDiagram;
  info.ciRelationshipCheckboxs = ciRelationshipDiagram;
  return info;
}

interface FilterCiRelationshipProps {
  goDiagram: GoJs.Diagram | undefined;
  toolbarAction: ToolbarActionProps;
  setToolbarAction: (toolbarAction: ToolbarActionProps) => void;
}

const FilterCiRelationship = (props: FilterCiRelationshipProps) => {
  const { goDiagram, toolbarAction } = props;
  const [ciNameCheckboxs, setCiNameCheckboxs] = useState<ComboboxItem[]>([]);
  const [ciLevelCheckboxs, setCiLevelCheckboxs] = useState<ComboboxItem[]>([]);
  const [ciNameSearchTxt, setCiNameSearchTxt] = useState<string>('');
  const [ciLevelSearchTxt, setCiLevelSearchTxt] = useState<string>('');
  const [cilevelCheckeds, setCiLevelCheckeds] = useState<number[]>([]);
  const [ciNameCheckeds, setCiNameCheckeds] = useState<number[]>([]);
  const [ciTypeCheckeds, setCiTypeCheckeds] = useState<number[]>([]);
  const [ciTypeCheckbox, setCiTypeCheckbox] = useState<ComboboxItem[]>([]);
  const [ciTypeSearchTxt, setCiTypeSearchTxt] = useState<string>('');

  const [ciRelationshipCheckeds, setCiRelationshipCheckeds] = useState<number[]>([]);
  const [ciRelationshipCheckboxs, setCiRelationshipCheckboxs] = useState<ComboboxItem[]>([]);
  const [ciRelationshipSearchTxt, setciRelationshipSearchTxt] = useState<string>('');
  const oldDatasCiNameRef = useRef<ObjectData[]>(toolbarAction.filter.oldDatasCiNameRef);
  const datasCiNameRef = useRef<ObjectData[]>([]);
  const oldLinksCiNameRef = useRef<LinkDataTemplate[]>(toolbarAction.filter.oldLinksCiNameRef);

  /**
   * create data checkbox from diagram
   */
  const createDataCheckboxIntoFilterView = useCallback(() => {
    if (!goDiagram) {
      return;
    }
    const oldDatasCiName = oldDatasCiNameRef.current || [];
    const oldLinksCiName = oldLinksCiNameRef.current || [];

    const currentDiagramInfo = getCheckBoxInfo(goDiagram);
    setCiNameCheckeds(currentDiagramInfo.ciNameCheckeds);
    setCiTypeCheckeds(currentDiagramInfo.ciTypeCheckeds);
    setCiRelationshipCheckeds(currentDiagramInfo.ciRelationshipCheckeds);
    setCiLevelCheckeds(currentDiagramInfo.ciLevelCheckeds);
    if (oldDatasCiName.length >= goDiagram.nodes.count && oldLinksCiName.length > 0) {
      // when init component and oldDatasCiName exist data then it's from business view
      // setting checkbox save
      const oldDiagram = new go.Diagram();
      appendNodeWithDataNode(oldDiagram, oldDatasCiName, oldLinksCiName);
      const oldDiagramInfo = getCheckBoxInfo(oldDiagram);
      setCiNameCheckboxs(oldDiagramInfo.ciNameCheckboxs);
      setCiLevelCheckboxs(oldDiagramInfo.ciLevelCheckboxs);
      setCiTypeCheckbox(oldDiagramInfo.ciTypeCheckboxs);
      setCiRelationshipCheckboxs(oldDiagramInfo.ciRelationshipCheckboxs);

      // save old data diagram bussiness view
      const oldDatasCiNameRefCp = [...oldDiagram.model.nodeDataArray];
      const oldLinksCiNameRefCp = [];
      const links = oldDiagram.links.iterator;
      while (links.next()) {
        oldLinksCiNameRefCp.push(links.value.data);
      }
      oldDatasCiNameRef.current = oldDatasCiNameRefCp;
      oldLinksCiNameRef.current = oldLinksCiNameRefCp;
    } else {
      setCiNameCheckboxs(currentDiagramInfo.ciNameCheckboxs);
      setCiLevelCheckboxs(currentDiagramInfo.ciLevelCheckboxs);
      setCiTypeCheckbox(currentDiagramInfo.ciTypeCheckboxs);
      setCiRelationshipCheckboxs(currentDiagramInfo.ciRelationshipCheckboxs);

      // save old data current diagram
      const oldDatasCiNameRefCp = [...goDiagram.model.nodeDataArray];
      const oldLinksCiNameRefCp = [];
      const links = goDiagram.links.iterator;
      while (links.next()) {
        oldLinksCiNameRefCp.push(links.value.data);
      }
      oldDatasCiNameRef.current = oldDatasCiNameRefCp;
      oldLinksCiNameRef.current = oldLinksCiNameRefCp;
    }

    datasCiNameRef.current = [...goDiagram.model.nodeDataArray];
  }, [goDiagram]);

  useEffect(() => {
    oldDatasCiNameRef.current = toolbarAction.filter.oldDatasCiNameRef;
    oldLinksCiNameRef.current = toolbarAction.filter.oldLinksCiNameRef;
  }, [toolbarAction.filter.oldDatasCiNameRef, toolbarAction.filter.oldLinksCiNameRef]);

  /**
   * Event when check/uncheck filter relationship
   * @param event event
   */
  const onChangeRelationshipCheck = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!goDiagram) {
      return;
    }
    const isChecked = event.target.checked;
    const value = Number(event.target.value);
    let relationshipCheckeds = [...ciRelationshipCheckeds];
    if (isChecked) {
      relationshipCheckeds.push(value);
    } else {
      relationshipCheckeds = relationshipCheckeds.filter((item) => item !== value);
    }

    appendNodeWithDataNode(goDiagram, oldDatasCiNameRef.current, oldLinksCiNameRef.current);

    const lstUnCheck = ciRelationshipCheckboxs.filter((item) => !relationshipCheckeds.some((itemChecked) => itemChecked === Number(item.value)));
    const linksRemove: go.Link[] = [];
    lstUnCheck.forEach((key) => {
      goDiagram.links.each((link) => {
        if (Number(key.value) === link.data.relationshipId) {
          linksRemove.push(link);
        }
      });
    });
    removeLink(goDiagram, linksRemove);
    createDataCheckboxIntoFilterView();
  };

  /**
   * Event when check/uncheck filter Citype
   * @param event event
   */
  const onChangeCiTypeCheck = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!goDiagram) {
      return;
    }
    goDiagram.commit(() => {
      const isChecked = event.target.checked;
      const value = Number(event.target.value);
      let lstCiTypeChecked = [...ciTypeCheckeds];
      if (isChecked) {
        lstCiTypeChecked.push(value);
      } else {
        lstCiTypeChecked = lstCiTypeChecked.filter((item) => item !== value);
      }
      appendNodeWithDataNode(goDiagram, oldDatasCiNameRef.current, oldLinksCiNameRef.current, false);

      const lstCiTypeUnCheck = ciTypeCheckbox.filter((item) => !lstCiTypeChecked.some((itemChecked) => itemChecked === Number(item.value)));

      lstCiTypeUnCheck.forEach((key) => {
        const nodeSeachByCiType = goDiagram.findNodesByExample({ data: { ciTypeId: Number(key.value) } });
        if (nodeSeachByCiType) {
          nodeSeachByCiType.each((node) => {
            if (!node.data.isRoot) {
              hiddenNode(goDiagram, node, false);
            }
          });
        }
      });
      createDataCheckboxIntoFilterView();
    }, 'onChangeCiTypeCheck');
  };

  /**
   * Event when check/uncheck filter level
   * @param event event
   */
  const onChangeLevelCheck = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!goDiagram) {
      return;
    }
    goDiagram.commit(() => {
      const isChecked = event.target.checked;
      const value = Number(event.target.value);
      let updatedLevels: number[] = [];

      if (isChecked) {
        updatedLevels = Array.from({ length: value }, (_, i) => i + 1);
      } else {
        updatedLevels = cilevelCheckeds.filter((level) => level < value);
      }
      appendNodeWithDataNode(goDiagram, oldDatasCiNameRef.current, oldLinksCiNameRef.current, false);

      const lstLevelUnCheck = ciLevelCheckboxs.filter((item) => !updatedLevels.some((itemChecked) => itemChecked === Number(item.value)));
      const nodeLevels = getNodeLevels(goDiagram);
      const listKeyHide = nodeLevels
        .filter((item) => lstLevelUnCheck.some((itemUncheck) => Number(itemUncheck.value) === item.minLevel))
        .map((key) => key.nodeId);
      listKeyHide.forEach((key) => {
        const node = goDiagram.findNodeForKey(key);
        if (node) {
          hiddenNode(goDiagram, node, false);
        }
      });

      createDataCheckboxIntoFilterView();
    }, 'onChangeLevelCheck');
  };

  const onChangeCiNameCheck = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!goDiagram) {
      return;
    }
    goDiagram.commit(() => {
      const isChecked = event.target.checked;
      const value = Number(event.target.value);
      const nodeRoot = goDiagram.findNodesByExample({ isRoot: true }).first();
      if (!nodeRoot) {
        return;
      }
      if (isChecked) {
        appendNodeWithDataNode(goDiagram, oldDatasCiNameRef.current, oldLinksCiNameRef.current, false);
        const node = goDiagram.findNodeForKey(value);

        if (node) {
          const nodesOnPath = getNodesOnPath(goDiagram, node, nodeRoot, true).map((item) => item.data.key);
          const ciNameChecks = [...ciNameCheckeds, ...nodesOnPath, value];
          const lstNameUnCheck = ciNameCheckboxs.filter((item) => !ciNameChecks.some((itemChecked) => itemChecked === Number(item.value)));
          lstNameUnCheck.forEach((key) => {
            const node = goDiagram.findNodeForKey(key.value);
            if (node && node.key !== nodeRoot?.key) {
              hiddenNode(goDiagram, node, false);
            }
          });
        }
      } else {
        const node = goDiagram.findNodeForKey(value);
        if (node && node.key !== nodeRoot?.key) {
          hiddenNode(goDiagram, node, false);
        }
      }
      createDataCheckboxIntoFilterView();
    }, 'onChangeCiNameCheck');
  };

  const getCheckBox = (datas: ComboboxItem[], txtSeach: string | undefined): ComboboxItem[] => {
    if (!txtSeach) {
      return datas;
    }
    return datas.filter((data) => data.label.toLowerCase().includes(txtSeach?.toLocaleLowerCase()));
  };
  const handleShowAllCi = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.currentTarget.checked) {
      appendNodeWithDataNode(goDiagram, oldDatasCiNameRef.current, oldLinksCiNameRef.current);
    } else {
      goDiagram?.nodes.each((node) => {
        if (!node.data.isRoot) {
          goDiagram.remove(node);
        }
      });
    }
    createDataCheckboxIntoFilterView();
  };

  return (
    <Popover
      withinPortal={false}
      position='right'
      shadow='md'
      width={400}
      closeOnClickOutside={true}
      clickOutsideEvents={['mouseup', 'touchend']}
      onOpen={() => createDataCheckboxIntoFilterView()}>
      <Popover.Target>
        <KanbanTooltip withinPortal={false} label='Filter'>
          <ThemeIcon variant='outline' color={'primary'} size={30}>
            <IconFilter size='3rem' stroke={2} />
          </ThemeIcon>
        </KanbanTooltip>
      </Popover.Target>
      <Popover.Dropdown>
        <KanbanSwitch
          checked={ciNameCheckboxs.length === ciNameCheckeds.length}
          onChange={handleShowAllCi}
          className={stylesCss['item-padding']}
          label='Show All (Ci Nodes & Relationship)'
        />
        <Divider />
        <Group className={stylesCss['item-padding']} align={'right'} grow>
          <KanbanTitle order={6}>CiType</KanbanTitle>
          <KanbanInput
            maxLength={250}
            placeholder='Seach ciType'
            leftSection={<IconSearch style={{ width: rem(16), height: rem(16) }} stroke={1.5} />}
            mb={0}
            size='xs'
            onChange={(e) => {
              const value = e.target.value;
              setCiTypeSearchTxt(value);
            }}
            value={ciTypeSearchTxt}
          />
        </Group>
        <div className={stylesCss['div-filter']}>
          {getCheckBox(ciTypeCheckbox, ciTypeSearchTxt).map((item, index) => (
            <KanbanCheckbox
              label={item.label}
              key={index}
              value={item.value}
              onChange={onChangeCiTypeCheck}
              checked={ciTypeCheckeds.some((ciType) => ciType === Number(item.value))}
            />
          ))}
        </div>
        <Divider />
        <Group className={stylesCss['item-padding']} align={'right'} grow>
          <KanbanTitle order={6}>CI name</KanbanTitle>
          <KanbanInput
            maxLength={250}
            placeholder='Search name'
            leftSection={<IconSearch style={{ width: rem(16), height: rem(16) }} stroke={1.5} />}
            mb={0}
            size='xs'
            onChange={(e) => {
              const value = e.target.value;
              setCiNameSearchTxt(value);
            }}
            value={ciNameSearchTxt}
          />
        </Group>
        <div className={stylesCss['div-filter']}>
          {getCheckBox(ciNameCheckboxs, ciNameSearchTxt).map((item, index) => (
            <KanbanCheckbox
              key={index}
              value={item.value}
              onChange={onChangeCiNameCheck}
              checked={ciNameCheckeds.some((ciName) => ciName === Number(item.value))}
              label={item.label}
            />
          ))}
        </div>
        <Divider />
        <Group className={stylesCss['item-padding']} align={'right'} grow>
          <KanbanTitle order={6}>Relationship</KanbanTitle>
          <KanbanInput
            maxLength={250}
            placeholder='Seach Relationship'
            leftSection={<IconSearch style={{ width: rem(16), height: rem(16) }} stroke={1.5} />}
            mb={0}
            size='xs'
            onChange={(e) => {
              const value = e.target.value;
              setciRelationshipSearchTxt(value);
            }}
            value={ciRelationshipSearchTxt}
          />
        </Group>
        <div className={stylesCss['div-filter']}>
          {getCheckBox(ciRelationshipCheckboxs, ciRelationshipSearchTxt).map((item, index) => (
            <KanbanCheckbox
              label={item.label}
              key={index}
              value={item.value}
              onChange={onChangeRelationshipCheck}
              checked={ciRelationshipCheckeds.some((ciType) => ciType === Number(item.value))}
            />
          ))}
        </div>
        <Divider />
        <Group className={stylesCss['item-padding']} align={'right'} grow>
          <KanbanTitle order={6}>Level</KanbanTitle>
          <KanbanInput
            maxLength={250}
            placeholder='Search level'
            leftSection={<IconSearch style={{ width: rem(16), height: rem(16) }} stroke={1.5} />}
            mb={0}
            size='xs'
            onChange={(e) => {
              const value = e.target.value;
              setCiLevelSearchTxt(value);
            }}
            value={ciLevelSearchTxt}
          />
        </Group>
        <div className={stylesCss['div-filter']}>
          {getCheckBox(ciLevelCheckboxs, ciLevelSearchTxt).map((item, index) => (
            <KanbanCheckbox
              label={item.label}
              key={index}
              value={item.value}
              onChange={onChangeLevelCheck}
              checked={cilevelCheckeds.some((level) => level === Number(item.value))}
            />
          ))}
        </div>
      </Popover.Dropdown>
    </Popover>
  );
};
export default FilterCiRelationship;
