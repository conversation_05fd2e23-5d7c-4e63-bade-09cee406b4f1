export enum DiscoverySourceTypeEnum {
  DISCOVER_SOURCE_RANCHER_K8S_PROJECT,
  DISCOVER_SOURCE_RANCHER_TANZU,
  DISCOVER_SOURCE_NETBOX,
  DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_MACHINE,
  DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_DISK,
  DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_CLUSTER,
  DISCOVER_SOURCE_AIX_HMC,
  DISCOVER_SOURCE_NETBOX_DICM_DEVICE,
  DISCOVERY_SOURCE_AIX_HMC_MANAGED_SYSTEM,
  DISCOVERY_SOURCE_AIX_HMC_LOGICAL_PARTITION,
  DISCOVER_SOURCE_VROPS_CLUSTER,
  DISCOVER_SOURCE_VROPS_RESOURCES,
  DISCOVER_SOURCE_SOLARWIND_NETWORK_USE_API,
  DISCOVER_SOURCE_RANCHER_TANZU_NODE,
  DISCOVER_SOURCE_RANCHER_TANZU_CLUSTERS,
  DISCOVERY_SOURCE_SNMP_NETWORK_CHECKPOINT,
  DISCOVERY_SOURCE_SNMP_NETWORK_PALOALTO,
  DISCOVERY_SOURCE_ACTIVE_IQ_CLUSTER_NODE,
  DISCOVERY_SOURCE_ACTIVE_IQ_STORAGE_VOLUMES,
  DISCOVERY_SOURCE_F5_SNMP,
  DISCOVERY_SOURCE_F5_API,
  DISCOVER_SOURCE_SOLARWIND_NETWORK_USE_DB,
  DISCOVER_SOURCE_NETBOX_DICM_DEVICE_ROLE_SERVER,
}
