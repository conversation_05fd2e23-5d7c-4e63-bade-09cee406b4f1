.clipText {
  white-space: nowrap;
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
}


.accordion {
  width: 95%;
}

.accordion div{
  border: none;
}

.accordion_content {
  padding:  var(--mantine-spacing-xs) 0px;
  max-height: 0; /* Start with collapsed content */
  overflow: hidden; /* Hide overflow when collapsed */
  transition: max-height 0.3s ease-out;
}

.accordion_content span{
  margin: 0
} 


.accordion_content_text{
  margin: 0;
  white-space: pre-wrap
}