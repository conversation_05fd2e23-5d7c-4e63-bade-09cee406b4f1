import { BaseApi } from '@core/api/BaseApi';
import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
import { BaseUrl } from '@core/api/BaseUrl';
import { CiAbsentRule } from '@models/CiAbsentRule';

export type CiAbsentRuleResponse = PaginationResponseModel<CiAbsentRule>;

export class CiAbsentRuleApi extends BaseApi {
  static baseUrl = BaseUrl.ciAbsentRules;

  static findAllWithPaging(pagination: PaginationRequestModel<CiAbsentRule>) {
    return BaseApi.postData<CiAbsentRuleResponse>(`${this.baseUrl}/filter`, pagination, {}, {}, { useLoading: false, useErrorNotification: true });
  }

  static findCiAbsentRuleById(id: number) {
    return BaseApi.getData<CiAbsentRule>(`${this.baseUrl}/${id}`);
  }

  static createNew(data: CiAbsentRule) {
    return BaseApi.postData<boolean>(`${this.baseUrl}`, data);
  }

  static updateRule(data: CiAbsentRule, id: number) {
    return BaseApi.putData<boolean>(`${this.baseUrl}/${id}`, data);
  }

  static deleteByIdIn(ids: number[]) {
    return BaseApi.deleteData<number[]>(`${this.baseUrl}`, {
      ids,
    });
  }
}
