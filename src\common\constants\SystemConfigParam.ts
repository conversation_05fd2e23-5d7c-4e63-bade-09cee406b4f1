import type { SystemConfigParamModel } from '@models/SystemConfigParam';

export enum SystemConfigParamType {
  NUMBER,
  TEXT,
}

export enum SystemConfigParamKey {
  CI_MAX_LEVEL = 'CI_MAX_LEVEL',
  CI_TYPE_BUSINESS_DOMAIN_LV1 = 'CI_TYPE_BUSINESS_DOMAIN_LV1',
  RELATIONSHIP_BELONG_TO = 'RELATIONSHIP_BELONG_TO',
  RELATIONSHIP_CONNECT = 'RELATIONSHIP_CONNECT',
  CI_TYPE_APPLICATION = 'CI_TYPE_APPLICATION',
  RELATIONSHIP_IMPLEMENT = 'RELATIONSHIP_IMPLEMENT',
  BUSINESS_MODEL_ATTRIBUTE = 'BUSINESS_MODEL_ATTRIBUTE',
  CI_TYPE_BUSINESS_FUNCTION = 'CI_TYPE_BUSINESS_FUNCTION',
  GRAPH_NODE_MAX = 'GRAPH_NODE_MAX',
  GRAPH_RELATIONSHIP_MAX = 'GRAPH_RELATIONSHIP_MAX',
}

export const systemConfigParams = (): SystemConfigParamModel[] => {
  return [
    { key: SystemConfigParamKey.CI_MAX_LEVEL, type: SystemConfigParamType.NUMBER, min: 1, max: 300 },
    { key: SystemConfigParamKey.CI_TYPE_BUSINESS_DOMAIN_LV1, type: SystemConfigParamType.NUMBER },
    { key: SystemConfigParamKey.RELATIONSHIP_BELONG_TO, type: SystemConfigParamType.NUMBER },
    { key: SystemConfigParamKey.RELATIONSHIP_CONNECT, type: SystemConfigParamType.NUMBER },
    { key: SystemConfigParamKey.CI_TYPE_APPLICATION, type: SystemConfigParamType.NUMBER },
    { key: SystemConfigParamKey.RELATIONSHIP_IMPLEMENT, type: SystemConfigParamType.NUMBER },
    { key: SystemConfigParamKey.BUSINESS_MODEL_ATTRIBUTE, type: SystemConfigParamType.NUMBER },
    { key: SystemConfigParamKey.CI_TYPE_BUSINESS_FUNCTION, type: SystemConfigParamType.NUMBER },
    { key: SystemConfigParamKey.GRAPH_NODE_MAX, type: SystemConfigParamType.NUMBER, min: 1, max: 3000 },
    { key: SystemConfigParamKey.GRAPH_RELATIONSHIP_MAX, type: SystemConfigParamType.NUMBER, min: 1, max: 3000 },
  ];
};
