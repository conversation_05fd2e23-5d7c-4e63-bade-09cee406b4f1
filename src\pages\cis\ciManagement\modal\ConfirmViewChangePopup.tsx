import { KanbanConfirmModal } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import React, { useCallback, useState } from 'react';
import { forwardRef, useImperativeHandle } from 'react';
import { ActionType, ScreenTypeManagement } from '@common/constants/CiManagement';
import { buildCiManageUrl } from '@common/utils/RouterUtils';
import { messageApprovalByAction } from '@common/utils/CiUtils';

export type ConfirmViewChangePopupMethods = {
  openModalConfirm: (val: ActionType) => void;
  closeModalConfirm: () => void;
};

const actionToNextStatusMap = {
  [ActionType.SEND]: ScreenTypeManagement.SENT,
  [ActionType.APPROVE]: ScreenTypeManagement.APPROVED,
  [ActionType.REJECT]: ScreenTypeManagement.REJECTED,
};

const titles = {
  [ActionType.SEND]: 'Send request successfully',
  [ActionType.APPROVE]: 'Successfully approved',
  [ActionType.REJECT]: 'Successfully rejected',
};

const textConfirms = {
  [ActionType.SEND]: 'View list of send CIs',
  [ActionType.APPROVE]: 'View list of approved CIs',
  [ActionType.REJECT]: 'View list of rejected CIs',
};

export const ConfirmViewChangePopup = forwardRef<ConfirmViewChangePopupMethods>((props, ref) => {
  const [openedConfirmViewChange, { close: closeConfirmViewChange, open: openConfirmViewChange }] = useDisclosure(false);
  const [actionType, setActionType] = useState<ActionType>(ActionType.SEND);

  const onOpenConfirmViewChange = useCallback(
    (action: ActionType) => {
      setActionType(action);
      openConfirmViewChange();
    },
    [openConfirmViewChange],
  );

  useImperativeHandle<any, ConfirmViewChangePopupMethods>(
    ref,
    () => ({
      openModalConfirm: onOpenConfirmViewChange,
      closeModalConfirm: closeConfirmViewChange,
    }),
    [onOpenConfirmViewChange, closeConfirmViewChange],
  );

  return (
    <>
      <KanbanConfirmModal
        title={titles[actionType]}
        onConfirm={() => {
          closeConfirmViewChange();
          window.open(buildCiManageUrl(actionToNextStatusMap[actionType]), '_blank');
        }}
        textConfirm={textConfirms[actionType]}
        onClose={closeConfirmViewChange}
        opened={openedConfirmViewChange}
        modalProps={{
          size: 'lg',
        }}>
        {`${messageApprovalByAction(actionType)}`}
      </KanbanConfirmModal>
    </>
  );
});
ConfirmViewChangePopup.displayName = 'ConfirmViewChangePopup';
export default ConfirmViewChangePopup;
