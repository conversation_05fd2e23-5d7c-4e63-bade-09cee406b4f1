import type { ComboboxItem, RadioProps } from '@mantine/core';

export enum AUTHENTICATION_TYPE_ENUM {
  BASIC = 'BASIC',
}

export enum CONNECTION_PROTOCOL_ENUM {
  MAIL_SERVICE = 'MAIL_SERVICE',
}

export const PASSWORD_PLACEHOLDER: string = '********';
export const AUTHENTYPE_LIST: ComboboxItem[] = [{ value: AUTHENTICATION_TYPE_ENUM.BASIC, label: 'Basic' }];
export const CONNECTION_PROTOCOL_LIST: RadioProps[] = [{ value: CONNECTION_PROTOCOL_ENUM.MAIL_SERVICE, label: 'Mail service (http)', checked: true }];
