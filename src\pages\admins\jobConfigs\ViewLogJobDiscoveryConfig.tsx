import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { ColumnType, KanbanButton, KanbanTooltip, type TableAffactedSafeType } from 'kanban-design-system';
import { KanbanTable, type KanbanTableProps } from 'kanban-design-system';
import { IconEyeShare, IconRefresh } from '@tabler/icons-react';
import { KanbanIconButton } from 'kanban-design-system';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import equal from 'fast-deep-equal';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { JobDiscoveryConfigApi } from '@api/discovery/JobDiscoveryConfigApi';
import { dateToString, DD_MM_YYYY_HH_MM_SS_FORMAT } from '@common/utils/DateUtils';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';
import { JobHistoryModel } from '@models/JobHistory';
import { JobTypeEnum } from '@common/constants/JobTypeEnum';
import { buildDiscoveryPreviewDataCiGroupDetailPath } from '@common/utils/RouterUtils';
import { MAX_NUMBER_LENGTH, MAX_TEXT_LENGTH } from '@common/constants/FieldLengthConstants';

export const ViewLogJobDiscoveryConfig = ({ jobId, jobName }: { jobId: number; jobName: string }) => {
  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType>();
  const [listJobHistory, setListJobHistory] = useState<JobHistoryModel[]>([]);

  const fetchListDcis = useCallback(() => {
    if (!tableAffected) {
      return;
    }

    const dataSend = tableAffectedToMultiColumnFilterPaginationRequestModel<JobHistoryModel>(
      tableAffected.sortedBy ? tableAffected : { ...tableAffected, sortedBy: 'createdDate', isReverse: true },
    );
    JobDiscoveryConfigApi.getAllJobHistoryByJobId(jobId, dataSend)
      .then((res) => {
        if (res.data) {
          const data = res.data;
          if (data) {
            setListJobHistory(data.content);
            setTotalRecords(data.totalElements);
          }
        }
      })
      .catch(() => {});
  }, [jobId, tableAffected]);

  const columns: ColumnType<JobHistoryModel>[] = useMemo(() => {
    const cols: ColumnType<JobHistoryModel>[] = [
      {
        title: 'ID',
        name: 'id',
        width: '10%',
        advancedFilter: {
          variant: 'number',
          filterModes: ['equals', 'notEquals', 'greaterThan', 'greaterThanOrEqualTo', 'lessThan', 'lessThanOrEqualTo'],
          customProps: { maxLength: MAX_NUMBER_LENGTH },
        },
      },
      {
        title: 'Start Time',
        name: 'startDate',
        advancedFilter: {
          variant: 'date',
          customProps: {
            popoverProps: {
              withinPortal: false,
            },
          },
        },
        customRender: (_, rowData: JobHistoryModel) => {
          return rowData.startDate ? dateToString(rowData.startDate, DD_MM_YYYY_HH_MM_SS_FORMAT) : '';
        },
      },
      {
        title: 'End Time',
        name: 'endDate',
        advancedFilter: {
          variant: 'date',
          customProps: {
            popoverProps: {
              withinPortal: false,
            },
          },
        },
        customRender: (_, rowData: JobHistoryModel) => {
          return rowData.endDate ? dateToString(rowData.endDate, DD_MM_YYYY_HH_MM_SS_FORMAT) : '';
        },
      },
      {
        title: 'Data source',
        name: 'jobValue',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Message',
        name: 'description',
        advancedFilter: {
          variant: 'text',
        },
        customRender: (_, rowData: JobHistoryModel) => {
          return rowData.description ? rowData.description.split('\n').map((line, index) => <div key={index}>{line}</div>) : '';
        },
      },
    ];

    return cols;
  }, []);

  const tableProps: KanbanTableProps<JobHistoryModel> = useMemo(() => {
    const cols = columns;

    return {
      maxHeight: '100%',
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 300,
      },
      sortable: {
        enable: true,
      },
      advancedFilterable: {
        enable: true,
        debounceTime: 1000,
        resetOnClose: true,
        compactMode: true,
      },
      columns: cols,
      data: listJobHistory,
      key: cols,
      serverside: {
        totalRows: totalRecords,
        onTableAffected(dataSet) {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
      pagination: {
        enable: true,
      },
      actions: {
        customAction: isCurrentUserMatchPermissions(AclPermission.actionTableJobConfigPermissions)
          ? (data: JobHistoryModel) => {
              return (
                <>
                  {isCurrentUserMatchPermissions([AclPermission.viewLogJobConfig]) && JobTypeEnum.GET_DISCOVERY_DATA === data.jobType && (
                    <KanbanTooltip label='Preview Discovery'>
                      <KanbanIconButton
                        variant='transparent'
                        size={'sm'}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          window.open(buildDiscoveryPreviewDataCiGroupDetailPath(data.id), '_blansk');
                        }}>
                        <IconEyeShare />
                      </KanbanIconButton>
                    </KanbanTooltip>
                  )}
                </>
              );
            }
          : undefined,
      },
    };
  }, [columns, listJobHistory, tableAffected, totalRecords]);

  useEffect(() => {
    fetchListDcis();
  }, [fetchListDcis]);

  return (
    <>
      <HeaderTitleComponent
        title={`View job history ${jobName}`}
        rightSection={
          <KanbanButton
            leftSection={<IconRefresh />}
            onClick={() => {
              fetchListDcis();
            }}>
            Refresh
          </KanbanButton>
        }
      />
      <KanbanTable {...tableProps} />
    </>
  );
};
