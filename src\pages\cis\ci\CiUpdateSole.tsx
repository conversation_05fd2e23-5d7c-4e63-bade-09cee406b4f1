import React, { useCallback, useEffect, useMemo, useState } from 'react';
import CiUpdate from './CiUpdate';
import { ConfigItemApi } from '@api/ConfigItemApi';
import { NotificationError, NotificationSuccess, NotificationWarning } from '@common/utils/NotificationUtils';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import type { ConfigItemAttrModel } from '@models/ConfigItemAttr';
import type { ConfigItemAttrCustomModel } from '@models/ConfigItemAttrCustom';
import type { ConfigItemTypeAttrResponse } from '@api/ConfigItemTypeAttrApi';
import type { HistoryDescription } from '@models/CiHistory';
import type { ConfigItemInfoModel, ConfigItemModel } from '@models/ConfigItem';
import { historyDescriptionToString } from '@common/utils/CiUtils';
import { ActionTypeEnum } from '@common/constants/CiDetail';

export type CiUpdateSoleMethods = {
  update: () => Promise<boolean>;
  isValid: boolean;
};
export type CiUpdateSoleProps = {
  ciTypeId: number;
  ciId: number;
  readOnly?: boolean;
  initData?: ConfigItemInfoModel;
  setIsValidUpdate?: (value: boolean) => void;
  forwardedRef: React.MutableRefObject<CiUpdateSoleMethods | null>;
  onAfterUpdate?: (val: number | undefined) => void;
  actionType?: ActionTypeEnum | null;
};

const defaultCiInfo: ConfigItemModel = {
  id: 0,
  ciTypeId: 0,
  name: '',
};

export const CiUpdateSole = (props: CiUpdateSoleProps) => {
  const { actionType, ciId, ciTypeId, initData, readOnly, setIsValidUpdate } = props;

  const [ciInfo, setCiInfo] = useState<ConfigItemModel>(defaultCiInfo);
  const [ciAttributes, setCiAttributes] = useState<ConfigItemAttrModel[]>([]);
  const [ciAttributesCustom, setCiAttributesCustom] = useState<ConfigItemAttrCustomModel[]>([]);
  const [listCiTypeAttribute, setListCiTypeAttribute] = useState<ConfigItemTypeAttrResponse[]>([]);
  const [historyDescription, setHistoryDescription] = useState<HistoryDescription | undefined>();

  const [ciAttributeMappingAttribute, setCiAttributeMappingAttribute] = useState<Record<number, ConfigItemAttrModel>>({});

  useEffect(() => {
    const result: Record<number, ConfigItemAttrModel> = {};

    for (const ciTypeAttribute of listCiTypeAttribute) {
      result[ciTypeAttribute.id] = ciAttributes.find((x) => x.ciTypeAttributeId === ciTypeAttribute.id) || {
        ciTypeAttributeId: ciTypeAttribute.id,
        id: 0,
        ciId: 0,
        value: '',
      };
    }
    setCiAttributeMappingAttribute(result);
  }, [ciAttributes, listCiTypeAttribute]);

  const update = useCallback(() => {
    const data: ConfigItemInfoModel = {
      ci: ciInfo,
      attributes: Object.values(ciAttributeMappingAttribute),
      attributeCustoms: ciAttributesCustom,
    };
    if (initData && initData.tempId) {
      data.tempId = initData.tempId;
    }
    if (historyDescription) {
      data.historyDescription = historyDescriptionToString(historyDescription);
    }
    return ConfigItemApi.saveInfo(data)
      .then((res) => {
        // devsec-833024: check res && res.data to avoid Property access or function call before check for null
        // or undefined   props?.onAfterUpdate(res?.data?.tempId);
        if (res && res.data) {
          setCiInfo(
            res.data.ci || {
              id: 0,
              ciTypeId: 0,
              name: '',
            },
          );
          setCiAttributes(res.data.attributes);
          setCiAttributesCustom(res.data.attributeCustoms);
          if (props?.onAfterUpdate) {
            props?.onAfterUpdate(res.data.tempId);
          }
          if (!initData) {
            NotificationSuccess({
              message: 'The CI has been updated but not approved yet. Please submit for approval at Tab CIs Management -> CIs Draft',
            });
          } else {
            NotificationSuccess({
              message: 'Update successfully',
            });
          }
          if (res.status === 200 && res.errorDescription) {
            NotificationWarning({
              message: res.errorDescription,
            });
          }
          return true;
        }
        return false;
      })
      .catch(() => {
        return false;
      })
      .finally(() => {
        setHistoryDescription(undefined);
      });
  }, [ciInfo, ciAttributeMappingAttribute, ciAttributesCustom, initData, historyDescription, props]);

  //useImperativeHandle<any, CiUpdateSoleMethods>(props.forwardedRef, () => methods, [methods]);
  const isValid = useMemo(() => {
    if (!ciInfo.name) {
      return false;
    }
    for (const attr of listCiTypeAttribute) {
      if (attr.mandatory) {
        const currentAttr = ciAttributeMappingAttribute[attr.id];
        if (!currentAttr || !currentAttr.value) {
          return false;
        }
      }
    }

    return true;
  }, [listCiTypeAttribute, ciAttributeMappingAttribute, ciInfo.name]);

  useEffect(() => {
    props.forwardedRef.current = {
      update,
      isValid,
    };
  }, [props.forwardedRef, update, isValid]);

  useEffect(() => {
    if (!setIsValidUpdate) {
      return;
    }
    setIsValidUpdate(isValid);
  }, [setIsValidUpdate, isValid]);

  useEffect(() => {
    if (initData) {
      setCiInfo(initData.ci || defaultCiInfo);
      setCiAttributes(initData.attributes || []);
      setCiAttributesCustom(initData.attributeCustoms || []);
    } else {
      ConfigItemApi.getInfoById(ciId)
        .then((res) => {
          if (res.data.ci) {
            if (actionType === ActionTypeEnum.CLONE) {
              setCiInfo({ ...res.data.ci, id: 0, name: `${res.data.ci.name} copy` });
            } else {
              setCiInfo(res.data.ci);
            }

            setCiAttributes(res.data.attributes);
            setCiAttributesCustom(res.data.attributeCustoms);
          } else {
            NotificationError({
              message: 'CI Not Found',
            });
          }
        })
        .catch(() => {});
    }
  }, [ciId, ciTypeId, initData, actionType]);

  const fetchCiTypesAttribute = useCallback(() => {
    ConfigItemTypeApi.getAllAttributes(ciTypeId)
      .then((res) => {
        const dataSorted = res.data.sort((a, b) => {
          const aOrder = a.order || 0;
          const bOrder = b.order || 0;
          if (a.id === 0 && b.id === 0) {
            return aOrder - bOrder;
          } else if (a.id === 0) {
            return -1;
          } else if (b.id === 0) {
            return 1;
          } else {
            return aOrder - bOrder;
          }
        });
        setListCiTypeAttribute(dataSorted);
      })
      .catch(() => {});
  }, [ciTypeId]);

  useEffect(() => {
    fetchCiTypesAttribute();
  }, [fetchCiTypesAttribute]);

  const onRemoveCiAttributeCustoms = (item: ConfigItemAttrCustomModel) => {
    setCiAttributesCustom((prev) => {
      return prev.filter((x) => x !== item);
    });
  };

  const onAddCiAttributeCustoms = (item: ConfigItemAttrCustomModel) => {
    setCiAttributesCustom((prev) => {
      return [...prev, item];
    });
  };

  return (
    <>
      <CiUpdate
        ci={ciInfo}
        ciAttributes={ciAttributeMappingAttribute}
        ciTypeAttributes={listCiTypeAttribute}
        onChangeCi={!readOnly ? setCiInfo : undefined}
        onChangeCiAttributes={!readOnly ? setCiAttributeMappingAttribute : undefined}
        ciAttributesCustom={ciAttributesCustom}
        onChangeCiAttributeCustoms={!readOnly ? setCiAttributesCustom : undefined}
        onRemoveCiAttributeCustoms={!readOnly ? onRemoveCiAttributeCustoms : undefined}
        onAddCiAttributeCustoms={!readOnly ? onAddCiAttributeCustoms : undefined}
        onChangeHistoryDescription={!readOnly ? setHistoryDescription : undefined}
      />
    </>
  );
};

export default CiUpdateSole;
