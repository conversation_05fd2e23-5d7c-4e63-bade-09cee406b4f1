import { Box, Flex } from '@mantine/core';
import { ImpactedChangeTableComponent } from './ImpactedChangeTableComponent';
import React from 'react';
import { ImpactedChangeProps, ImpactedTypeTable } from '@models/ChangeAssessment';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { IconPlus } from '@tabler/icons-react';
import type { ConfigItemResponse } from '@api/ConfigItemApi';

export type AffectedCiComponentProps = ImpactedChangeProps & {
  onProcessAdd: boolean;
  onAddCi: (val: boolean) => void;
  onDeleteCi: (val: ConfigItemResponse[]) => void;
  allowEdit: boolean;
};
export const AffectedCiComponent: React.FC<AffectedCiComponentProps> = ({ allowEdit, cis, onAddCi, onDeleteCi, onProcessAdd }) => {
  return (
    <Box>
      <Box mt='sm'>
        <HeaderTitleComponent
          title={'List Cis in change plan'}
          rightSection={
            <Flex gap='xs'>
              {allowEdit && (
                <KanbanButton
                  leftSection={<IconPlus />}
                  disabled={onProcessAdd}
                  onClick={() => {
                    onAddCi(true);
                  }}>
                  Add CIs in change plan
                </KanbanButton>
              )}
            </Flex>
          }
        />
      </Box>
      <ImpactedChangeTableComponent typeTable={ImpactedTypeTable.CI} cis={cis} onDelete={onDeleteCi} allowEdit={allowEdit} />
    </Box>
  );
};
