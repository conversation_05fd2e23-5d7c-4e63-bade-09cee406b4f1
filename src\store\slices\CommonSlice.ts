import type { Slice } from '@reduxjs/toolkit';
import type { RootStoreType } from '@store';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

type SliceWithFetchForEmptyType<T, Name extends string = string> = Slice<
  T,
  {
    fetchForEmpty(): void;
  },
  Name
>;

const useFetchForEmpty = <T>(get: (store: RootStoreType) => T, slice: SliceWithFetchForEmptyType<T>) => {
  const data = useSelector(get);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(slice.actions.fetchForEmpty());
  }, [dispatch, slice.actions]);

  return data;
};

// eslint-disable-next-line filenames-simple/named-export
export { useFetchForEmpty };
