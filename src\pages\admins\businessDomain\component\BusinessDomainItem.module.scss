.btn-icon-center {
    position: absolute;
    color: var(--mantine-color-dark-7)
}

.btn-border-color {
    border-color: var(--mantine-color-gray-4);
}
.group-translate-y{
    transform: translateY(-10px);
}

.scroll-wrapper {
    overflow-y: auto;
    overflow-x: hidden;
}


.link {
    border-left: 1px solid var(--mantine-color-gray-3);
    position: relative;

    &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        width: 5px;
        height: 1px;
        background-color: var(--mantine-color-gray-3);
    }
}
.disabled {
    cursor: not-allowed;
    opacity: 0.7;
}

.iconSection {
    transition: transform 150ms ease;
    transform: rotate(0deg);
}
.iconSectionActive {
    transition: transform 150ms ease;
    transform: rotate(90deg);
}