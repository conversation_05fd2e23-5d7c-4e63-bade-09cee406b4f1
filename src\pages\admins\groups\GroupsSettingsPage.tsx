import { GroupModel, GroupResponse, GroupsApi } from '@api/systems/GroupsApi';
import { KanbanCheckbox, KanbanTabs, useKanbanModals } from 'kanban-design-system';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { tableAffectedToMultiColumnFilterPaginationRequestModel, tableAffectedToPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import GuardComponent from '@components/GuardComponent';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { KanbanIconButton } from 'kanban-design-system';
import { KanbanInput } from 'kanban-design-system';
import { KanbanModal } from 'kanban-design-system';
import { KanbanSplitPagingContainer } from 'kanban-design-system';
import { KanbanTable, KanbanTableProps, KanbanTableSelectHandleMethods, TableAffactedSafeType } from 'kanban-design-system';
import { renderDateTime } from 'kanban-design-system';
import { KanbanTooltip } from 'kanban-design-system';
import { Flex, Space } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { AclPermission } from '@models/AclPermission';
import { IconCircleCheck, IconFriends, IconFriendsOff, IconPlus, IconSettings, IconUsersGroup, IconX } from '@tabler/icons-react';
import equal from 'fast-deep-equal';
import React, { useCallback, useEffect, useState, useRef, useMemo } from 'react';
import styled from 'styled-components';
import { type UserResponse, type UserResponseDto, UsersApi } from '@api/systems/UsersApi';
import { RolesApi, type RoleDto } from '@api/systems/RolesApi';
import { BreadcrumbComponent } from '../breadcrumb/BreadcrumbComponent';
import { MAX_TEXT_LENGTH } from '@common/constants/FieldLengthConstants';

const ListGroupWrapper = styled.div`
  display: grid;
  column-gap: var(--mantine-spacing-xs);
  row-gap: var(--mantine-spacing-xs);
  grid-template-columns: auto auto auto;
`;
const GroupItem = styled.div`
  padding: var(--mantine-spacing-xs);
  border: 1px solid gray;
  cursor: pointer;
  &:hover {
    border-color: var(--mantine-color-primary-6);
  }
  .title {
    font-size: var(--mantine-font-size-md);
  }
  .description {
    color: gray;
    font-size: var(--mantine-font-size-sm);
  }
`;

enum GroupsSettingTabs {
  UPDATE = 'UPDATE',
  SETTING_USERS = 'SETTING_USERS',
  SETTING_ROLES = 'SETTING_ROLES',
}

type GroupCreateInputProps = {
  newItem: GroupModel;
  setNewItem: React.Dispatch<React.SetStateAction<GroupModel>>;
};
export const GroupCreateInput: React.FC<GroupCreateInputProps> = ({ newItem, setNewItem }) => {
  return (
    <>
      <KanbanInput
        label='Name'
        required={true}
        maxLength={255}
        value={newItem.name || ''}
        onChange={(e) => {
          setNewItem((prev) => {
            return {
              ...prev,
              name: e.target.value,
            };
          });
        }}></KanbanInput>
      <KanbanInput
        label='Description'
        maxLength={255}
        value={newItem.description || ''}
        onChange={(e) => {
          setNewItem((prev) => {
            return {
              ...prev,
              description: e.target.value,
            };
          });
        }}></KanbanInput>
      <KanbanCheckbox
        label='Active'
        onChange={(e) =>
          setNewItem((prev) => {
            return {
              ...prev,
              active: e.target.checked,
            };
          })
        }
        checked={newItem.active}
      />
    </>
  );
};

export const GroupsSettingsPage = () => {
  const [totalRecords, setTotalRecords] = useState(0);
  const [totalUserRecords, setTotalUserRecords] = useState(0);
  const [totalRoleRecords, setTotalRoleRecords] = useState(0);
  const [openedModal, { close: closePopup, open: openModal }] = useDisclosure(false);
  const [openedModalCreate, { close: closeModalCreate, open: openModalCreate }] = useDisclosure(false);
  const [openedModalUserInGroup, { close: closeModalUserInGroup, open: openModalUserInGroup }] = useDisclosure(false);
  const [openedModalRoleInGroup, { close: closeModalRoleInGroup, open: openModalRoleInGroup }] = useDisclosure(false);
  const [newItem, setNewItem] = useState<GroupModel>({
    id: 0,
    name: '',
    active: false,
  });
  const [groupResponses, setGroupResponses] = useState<GroupResponse[]>([]);
  const [selectedItemUsers, setSelectedItemUsers] = useState<string[]>([]);
  const [selectedItemRoles, setSelectedItemRoles] = useState<number[]>([]);
  const [currentUserSelected, setCurrentUserSelected] = useState<UserResponse[]>([]);

  const [tableAffectedChange, setTableAffectedChange] = useState<TableAffactedSafeType | undefined>(undefined);
  const [userResponses, setUserResponses] = useState<UserResponseDto[]>([]);
  // const [userInGroups, setUserInGroups] = useState<UserResponse[]>([]);
  const [userTableAffected, setUserTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);

  const [currentRoleSelected, setCurrentRoleSelected] = useState<RoleDto[]>([]);
  const [roleResponses, setRoleResponses] = useState<RoleDto[]>([]);
  // const [roleDatas, setRoleDatas] = useState<RoleDto[]>([]);
  // const [roleInGroups, setRoleInGroups] = useState<number[]>([]);
  const [roleTableAffected, setRoleTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);
  const [isRoleFetched, setIsRoleFetched] = useState<boolean>(false);
  const [isUserFetched, setIsUserFetched] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>(GroupsSettingTabs.UPDATE);
  const modelProvider = useKanbanModals();

  const fetchRoles = useCallback(() => {
    if (!roleTableAffected) {
      return;
    }
    RolesApi.getAllToView(tableAffectedToPaginationRequestModel(roleTableAffected), newItem.id)
      .then((response) => {
        const data = response.data;
        setRoleResponses(data.content);
        setTotalRoleRecords(data.totalElements);
        setIsRoleFetched(false);
      })
      .catch(() => {
        setIsRoleFetched(false);
      });
  }, [roleTableAffected, newItem.id]);

  useEffect(() => {
    if (isRoleFetched) {
      fetchRoles();
    }
  }, [fetchRoles, isRoleFetched]);

  // useEffect(() => {
  //   const roles: RoleDto[] = roleResponses.map((role) => ({
  //     ...role,
  //     isSetting: roleInGroups.some((roleId) => roleId === role.id),
  //   }));
  //   setRoleDatas(roles);
  // }, [roleInGroups, roleResponses]);

  const tableViewListRoleProps: KanbanTableProps<RoleDto> = useMemo(() => {
    return {
      title: 'List roles setting',
      columns: [
        {
          name: 'roleOfGroup',
          title: 'Role in group',
          customRender: (data) => {
            if (data) {
              return (
                <KanbanIconButton variant={'transparent'}>
                  <IconCircleCheck></IconCircleCheck>
                </KanbanIconButton>
              );
            } else {
              return (
                <KanbanIconButton variant={'transparent'} c={'red'}>
                  <IconX></IconX>
                </KanbanIconButton>
              );
            }
          },
        },
        {
          name: 'name',
          title: 'Role name',
        },
      ],
      data: roleResponses,
      pagination: { enable: true },
      searchable: { enable: true, debounceTime: 300 },
      showNumericalOrderColumn: true,
      selectableRows: {
        enable: true,
        onSelectedRowsChanged(rows) {
          setSelectedItemRoles(rows.map((item) => item.id));
        },
        crossPageSelected: {
          rowKey: 'id',
          selectedRows: currentRoleSelected,
          setSelectedRows: setCurrentRoleSelected,
        },
      },
      serverside: {
        totalRows: totalRoleRecords,
        onTableAffected: (dataSet) => {
          if (!equal(roleTableAffected, dataSet)) {
            setRoleTableAffected(dataSet);
            setIsRoleFetched(true);
          }
        },
      },
    };
  }, [currentRoleSelected, roleResponses, roleTableAffected, totalRoleRecords]);

  const fetchUsers = useCallback(() => {
    if (!userTableAffected) {
      return;
    }
    UsersApi.getAllUserToView(tableAffectedToPaginationRequestModel(userTableAffected), newItem.id)
      .then((response) => {
        const data = response.data;
        setUserResponses(data.content);
        setTotalUserRecords(data.totalElements);
        setIsUserFetched(false);
      })
      .catch(() => {
        setIsUserFetched(false);
      });
  }, [userTableAffected, newItem.id]);

  useEffect(() => {
    if (isUserFetched) {
      fetchUsers();
    }
  }, [fetchUsers, isUserFetched]);

  // useEffect(() => {
  //   const userSettingGroups: UserResponseDto[] = userResponses.map((user) => ({
  //     ...user,
  //     isSetting: userInGroups.some((filteredUser) => filteredUser.id === user.id),
  //   }));
  //   setUserResponses(userSettingGroups);
  // }, [userInGroups, userResponses]);

  const tableViewListUserProps: KanbanTableProps<UserResponseDto> = useMemo(() => {
    return {
      title: 'List Users Setting',
      columns: [
        {
          name: 'userOfGroup',
          title: 'User In Group',
          customRender: (data) => {
            if (data) {
              return (
                <KanbanIconButton variant={'transparent'}>
                  <IconCircleCheck></IconCircleCheck>
                </KanbanIconButton>
              );
            } else {
              return (
                <KanbanIconButton variant={'transparent'} c={'red'}>
                  <IconX></IconX>
                </KanbanIconButton>
              );
            }
          },
        },
        {
          name: 'userName',
          title: 'Username',
        },
      ],
      data: userResponses,
      pagination: { enable: true },
      searchable: { enable: true, debounceTime: 300 },
      showNumericalOrderColumn: true,
      selectableRows: {
        enable: true,
        onSelectedRowsChanged(rows) {
          setSelectedItemUsers(rows.map((item) => item.userName));
        },
        crossPageSelected: {
          rowKey: 'id',
          selectedRows: currentUserSelected,
          setSelectedRows: setCurrentUserSelected,
        },
      },
      serverside: {
        totalRows: totalUserRecords,
        onTableAffected: (dataSet) => {
          if (!equal(userTableAffected, dataSet)) {
            setUserTableAffected(dataSet);
            setIsUserFetched(true);
          }
        },
      },
    };
  }, [currentUserSelected, totalUserRecords, userResponses, userTableAffected]);

  const fetchGroups = useCallback(() => {
    if (!tableAffectedChange) {
      return;
    }
    const dataSend = tableAffectedToMultiColumnFilterPaginationRequestModel<GroupResponse>(
      tableAffectedChange.sortedBy ? tableAffectedChange : { ...tableAffectedChange, sortedBy: 'active', isReverse: true },
    );

    GroupsApi.getAllGroupWithPaging(dataSend)
      .then((response) => {
        const data = response.data;
        setGroupResponses(data.content);
        setTotalRecords(data.totalElements);
      })
      .catch(() => {});
  }, [tableAffectedChange]);

  useEffect(() => {
    fetchGroups();
  }, [fetchGroups]);

  const activeGroups = useCallback(
    (ids: number[]) => {
      GroupsApi.activeGroups(ids)
        .then((response) => {
          if (response.status === 200) {
            fetchGroups();
            NotificationSuccess({
              message: 'Active group successfully',
            });
          }
        })
        .catch(() => {});
    },
    [fetchGroups],
  );

  // const getUserInGroup = useCallback((groupId: number) => {
  //   GroupsApi.findAllUserWithGroupId(groupId)
  //     .then((response) => {
  //       if (response.status === 200) {
  //         setUserInGroups(response.data);
  //       }
  //     })
  //     .catch(() => {});
  // }, []);

  // const getModalUserInGroup = useCallback(
  //   (groupId: number) => {
  //     getUserInGroup(groupId);
  //     openModalUserInGroup();
  //   },
  //   [getUserInGroup, openModalUserInGroup],
  // );

  // const fetchRoleInGroup = useCallback((groupId: number) => {
  //   GroupsApi.findAllRoleWithGroupId(groupId)
  //     .then((response) => {
  //       if (response.status === 200) {
  //         setRoleInGroups(response.data);
  //       }
  //     })
  //     .catch(() => {});
  // }, []);

  // const getModalRoleInGroup = useCallback(
  //   (groupId: number) => {
  //     fetchRoleInGroup(groupId);
  //     openModalRoleInGroup();
  //   },
  //   [fetchRoleInGroup, openModalRoleInGroup],
  // );

  const resetAndCloseModal = (closeModal: () => void) => {
    setNewItem({
      id: 0,
      name: '',
      active: false,
    });
    closeModal();
  };
  const closeModalSettingUserInGroup = () => {
    resetAndCloseModal(closeModalUserInGroup);
  };
  const closeModalSettingRoleInGroup = () => {
    resetAndCloseModal(closeModalRoleInGroup);
  };

  const inActiveGroups = useCallback(
    (ids: number[]) => {
      GroupsApi.inActiveGroups(ids)
        .then((response) => {
          if (response.status === 200) {
            fetchGroups();
            NotificationSuccess({
              message: 'Inactive group successfully',
            });
          }
        })
        .catch(() => {});
    },
    [fetchGroups],
  );

  const deleteGroups = useCallback(
    (ids: number[]) => {
      GroupsApi.deleteGroups(ids)
        .then((response) => {
          if (response.status === 200) {
            fetchGroups();
            NotificationSuccess({
              message: 'Delete group successfully',
            });
          }
        })
        .catch(() => {});
    },
    [fetchGroups],
  );

  const closeModal = useCallback(() => {
    setNewItem({
      id: 0,
      name: '',
      active: false,
    });

    closePopup();
  }, [closePopup]);

  const onCreateNew = useCallback(() => {
    GroupsApi.createOrUpdateGroup({ ...newItem, name: newItem.name.trim() })
      .then((_res) => {
        fetchGroups();
        NotificationSuccess({
          message: newItem.id ? 'Update group successfully' : 'Create group successfully',
        });
      })
      .catch(() => {});
    closeModalCreate();
    closeModal();
  }, [closeModal, closeModalCreate, fetchGroups, newItem]);

  const onDelete = useCallback(
    (id: number) => {
      GroupsApi.deleteGroup(id)
        .then((response) => {
          if (response.status === 200) {
            fetchGroups();
            NotificationSuccess({
              message: 'Delete group successfully',
            });
          }
        })
        .catch(() => {});
    },
    [fetchGroups],
  );

  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);

  const onAddUserIntoGroup = useCallback(() => {
    GroupsApi.addUsersIntoGroup(newItem.id, selectedItemUsers)
      .then((response) => {
        tableRef.current?.deselectAll();
        if (response.status === 200) {
          fetchUsers();
          setSelectedItemUsers([]);
          NotificationSuccess({
            message: 'Add users into group successfully',
          });
        }
      })
      .catch(() => {});
  }, [fetchUsers, newItem.id, selectedItemUsers]);

  const onAddRolesIntoGroup = useCallback(() => {
    GroupsApi.updateRolesIntoGroup(newItem.id, selectedItemRoles)
      .then((response) => {
        tableRef.current?.deselectAll();
        if (response.status === 200) {
          // fetchRoleInGroup(newItem.id);
          fetchRoles();
          setSelectedItemRoles([]);
          NotificationSuccess({
            message: 'Update roles into group successfully',
          });
        }
      })
      .catch(() => {});
  }, [fetchRoles, newItem.id, selectedItemRoles]);

  const onRemoveRolesFromGroup = useCallback(() => {
    GroupsApi.removeRolesFromGroup(newItem.id, selectedItemRoles)
      .then((response) => {
        tableRef.current?.deselectAll();
        if (response.status === 200) {
          // fetchRoleInGroup(newItem.id);
          fetchRoles();
          setSelectedItemRoles([]);
          NotificationSuccess({
            message: 'Delete roles from group successfully',
          });
        }
      })
      .catch(() => {});
  }, [fetchRoles, newItem.id, selectedItemRoles]);

  const onRemoveUserIntoGroup = useCallback(() => {
    GroupsApi.removeUsersIntoGroup(newItem.id, selectedItemUsers)
      .then((response) => {
        tableRef.current?.deselectAll();
        if (response.status === 200) {
          //get new update user in group
          // getUserInGroup(newItem.id);
          fetchUsers();
          setSelectedItemUsers([]);
          NotificationSuccess({
            message: 'Remove users into group successfully',
          });
        }
      })
      .catch(() => {});
  }, [fetchUsers, newItem.id, selectedItemUsers]);

  const tableViewListGroupProps: KanbanTableProps<GroupResponse> = useMemo(() => {
    return {
      title: 'Groups settings',
      columns: [
        {
          name: 'name',
          title: 'Name',
          advancedFilter: {
            variant: 'text',
            customProps: { maxLength: MAX_TEXT_LENGTH },
          },
        },
        {
          name: 'description',
          title: 'Description',
          advancedFilter: {
            variant: 'text',
            customProps: { maxLength: MAX_TEXT_LENGTH },
          },
        },
        {
          name: 'active',
          title: 'Active',
          advancedFilter: {
            enable: false,
          },
          customRender: (data) => {
            return data ? (
              <KanbanIconButton variant={'transparent'}>
                <IconCircleCheck />
              </KanbanIconButton>
            ) : (
              <KanbanIconButton variant={'transparent'} c={'red'}>
                <IconX />
              </KanbanIconButton>
            );
          },
        },
        {
          name: 'createdBy',
          title: 'Created by',
          advancedFilter: {
            variant: 'text',
            customProps: { maxLength: MAX_TEXT_LENGTH },
          },
        },
        {
          name: 'createdDate',
          title: 'Created date',
          advancedFilter: {
            variant: 'date',
            customProps: {
              popoverProps: {
                withinPortal: false,
              },
            },
          },
          customRender: renderDateTime,
        },
        {
          name: 'modifiedBy',
          title: 'Modified by',
          hidden: true,
          advancedFilter: {
            variant: 'text',
            customProps: { maxLength: MAX_TEXT_LENGTH },
          },
        },
        {
          name: 'modifiedDate',
          title: 'Modified date',
          hidden: true,
          advancedFilter: {
            variant: 'date',
            customProps: {
              popoverProps: {
                withinPortal: false,
              },
            },
          },
          customRender: renderDateTime,
        },
      ],
      data: groupResponses,
      onRowClicked: (data) => {
        if (isCurrentUserMatchPermissions([AclPermission.viewDetailGroupUser])) {
          setNewItem(data);
          setIsUserFetched(true);
          setIsRoleFetched(true);
          setActiveTab(GroupsSettingTabs.UPDATE);
          openModal();
        }
      },
      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffectedChange, dataSet)) {
            setTableAffectedChange(dataSet);
          }
        },
      },
      pagination: {
        enable: true,
      },
      advancedFilterable: {
        enable: true,
        debounceTime: 1000,
        resetOnClose: true,
        compactMode: true,
      },
      searchable: { enable: true, debounceTime: 300 },
      showNumericalOrderColumn: true,
      selectableRows: {
        enable: !!isCurrentUserMatchPermissions([AclPermission.deleteGroupUser, AclPermission.updateGroupUser]),
        onDeleted: isCurrentUserMatchPermissions([AclPermission.deleteGroupUser])
          ? (rows) => {
              deleteGroups(rows.map((x) => x.id));
            }
          : undefined,
        customAction: (rows, methods) => (
          <>
            <GuardComponent requirePermissions={[AclPermission.updateGroupUser]} hiddenOnUnSatisfy>
              <KanbanButton
                c={'white'}
                size='xs'
                onClick={() => {
                  const idModelActive = modelProvider.openConfirmModal({
                    title: 'Confirm active groups',
                    children: 'Are you sure to active these item(s)?',
                    onConfirm() {
                      activeGroups(rows.map((item) => item.id));
                      methods.deselectAll();
                      modelProvider.closeModal(idModelActive);
                    },
                  });
                }}>
                Active
              </KanbanButton>
              <KanbanButton
                c={'white'}
                size='xs'
                onClick={() => {
                  const idModelInActive = modelProvider.openConfirmModal({
                    title: 'Confirm inactive groups',
                    children: 'Are you sure to inactive these item(s)?',
                    onConfirm() {
                      inActiveGroups(rows.map((item) => item.id));
                      methods.deselectAll();
                      modelProvider.closeModal(idModelInActive);
                    },
                  });
                }}>
                Inactive
              </KanbanButton>
            </GuardComponent>
          </>
        ),
      },
      actions: {
        customAction: isCurrentUserMatchPermissions(AclPermission.actionTableGroupPermissions)
          ? (data) => (
              <>
                <GuardComponent requirePermissions={[AclPermission.updateGroupUser]} hiddenOnUnSatisfy>
                  <KanbanTooltip label={data.active ? 'Inactive group' : 'Active group'}>
                    <KanbanIconButton
                      key={1}
                      size='sm'
                      color='blue'
                      variant='transparent'
                      onClick={() => {
                        const idModelActive = modelProvider.openConfirmModal({
                          title: `Confirm  ${data.active ? 'inactive' : 'active'} groups`,
                          children: `Are you sure to ${data.active ? 'inactive' : 'active'} this group?`,
                          onConfirm() {
                            if (data.active) {
                              inActiveGroups([data.id]);
                            } else {
                              activeGroups([data.id]);
                            }
                            modelProvider.closeModal(idModelActive);
                          },
                        });
                      }}>
                      {data.active ? <IconFriendsOff /> : <IconFriends />}
                    </KanbanIconButton>
                  </KanbanTooltip>
                </GuardComponent>

                <GuardComponent requirePermissions={[AclPermission.addUserToGroupUser, AclPermission.deleteUserToGroupUser]} hiddenOnUnSatisfy>
                  <KanbanTooltip label='List users in group'>
                    <KanbanIconButton
                      key={3}
                      size='sm'
                      color='blue'
                      variant='transparent'
                      onClick={() => {
                        setNewItem(data);
                        openModalUserInGroup();
                        // getModalUserInGroup(data.id);
                        setIsUserFetched(true);
                      }}>
                      <IconUsersGroup />
                    </KanbanIconButton>
                  </KanbanTooltip>
                </GuardComponent>
                <GuardComponent requirePermissions={[AclPermission.addRoleToGroupUser, AclPermission.deleteRoleInGroupUser]} hiddenOnUnSatisfy>
                  <KanbanTooltip label='List roles in group'>
                    <KanbanIconButton
                      key={3}
                      size='sm'
                      color='blue'
                      variant='transparent'
                      onClick={() => {
                        setNewItem(data);
                        openModalRoleInGroup();
                        // getModalRoleInGroup(data.id);
                        setIsRoleFetched(true);
                      }}>
                      <IconSettings />
                    </KanbanIconButton>
                  </KanbanTooltip>
                </GuardComponent>
              </>
            )
          : undefined,
        deletable: isCurrentUserMatchPermissions([AclPermission.deleteGroupUser])
          ? {
              onDeleted: (data) => {
                onDelete(data.id);
              },
            }
          : undefined,
      },
    };
  }, [
    activeGroups,
    deleteGroups,
    groupResponses,
    inActiveGroups,
    modelProvider,
    onDelete,
    openModal,
    openModalRoleInGroup,
    openModalUserInGroup,
    tableAffectedChange,
    totalRecords,
  ]);

  const actionCreateOrUpdate = useMemo(() => {
    return (
      <GuardComponent requirePermissions={newItem.id > 0 ? [AclPermission.updateGroupUser] : [AclPermission.createGroupUser]} hiddenOnUnSatisfy>
        <KanbanButton disabled={!newItem.name || !newItem.name.trim()} onClick={onCreateNew}>
          {' '}
          {newItem.id > 0 ? 'Update' : 'Create'}
        </KanbanButton>
      </GuardComponent>
    );
  }, [newItem.id, newItem.name, onCreateNew]);

  const actionSettingUsers = useMemo(() => {
    return (
      <Flex>
        <GuardComponent requirePermissions={[AclPermission.addUserToGroupUser]} hiddenOnUnSatisfy>
          <KanbanButton disabled={!selectedItemUsers.length} leftSection={<IconFriends />} onClick={onAddUserIntoGroup}>
            Add
          </KanbanButton>
        </GuardComponent>
        <Space w={'xs'} />
        <GuardComponent requirePermissions={[AclPermission.deleteUserToGroupUser]} hiddenOnUnSatisfy>
          <KanbanButton disabled={!selectedItemUsers.length} leftSection={<IconFriendsOff />} onClick={onRemoveUserIntoGroup}>
            Remove
          </KanbanButton>
        </GuardComponent>
      </Flex>
    );
  }, [onAddUserIntoGroup, onRemoveUserIntoGroup, selectedItemUsers.length]);

  const actionSettingRoles = useMemo(() => {
    return (
      <Flex>
        <GuardComponent requirePermissions={[AclPermission.addRoleToGroupUser]} hiddenOnUnSatisfy>
          <KanbanButton disabled={!selectedItemRoles.length} leftSection={<IconFriends />} onClick={onAddRolesIntoGroup}>
            Add
          </KanbanButton>
        </GuardComponent>
        <Space w={'xs'} />
        <GuardComponent requirePermissions={[AclPermission.deleteRoleInGroupUser]} hiddenOnUnSatisfy>
          <KanbanButton disabled={!selectedItemRoles.length} leftSection={<IconFriendsOff />} onClick={onRemoveRolesFromGroup}>
            Remove
          </KanbanButton>
        </GuardComponent>
      </Flex>
    );
  }, [onAddRolesIntoGroup, onRemoveRolesFromGroup, selectedItemRoles.length]);

  const getActionsModel = useMemo(() => {
    if (GroupsSettingTabs.SETTING_USERS === activeTab) {
      return actionSettingUsers;
    } else if (GroupsSettingTabs.SETTING_ROLES === activeTab) {
      return actionSettingRoles;
    } else {
      return actionCreateOrUpdate;
    }
  }, [actionCreateOrUpdate, actionSettingRoles, actionSettingUsers, activeTab]);

  return (
    <div>
      {/* 4763 group list*/}
      <BreadcrumbComponent />
      <KanbanModal size={'lg'} opened={openedModalCreate} onClose={closeModalCreate} centered title={'Create Group'} actions={actionCreateOrUpdate}>
        <GroupCreateInput newItem={newItem} setNewItem={setNewItem} />
      </KanbanModal>

      <KanbanModal size={'70%'} opened={openedModal} onClose={closeModal} centered title={'Update Group'} actions={getActionsModel}>
        <KanbanTabs
          configs={{
            defaultValue: GroupsSettingTabs.UPDATE,
            value: activeTab,
            variant: 'outline',
            onChange: (value) => {
              if (value) {
                setActiveTab(value);
              }
            },
          }}
          tabs={{
            UPDATE: {
              title: 'Group Info',
              content: <GroupCreateInput newItem={newItem} setNewItem={setNewItem} />,
            },
            SETTING_USERS: {
              title: 'List users in group',
              content: <KanbanTable ref={tableRef} {...tableViewListUserProps} />,
            },
            SETTING_ROLES: {
              title: 'List roles in group',
              content: <KanbanTable ref={tableRef} {...tableViewListRoleProps} />,
            },
          }}
        />
      </KanbanModal>
      <KanbanModal
        size={'xl'}
        opened={openedModalUserInGroup}
        onClose={() => {
          closeModalSettingUserInGroup();
          tableRef.current?.deselectAll();
        }}
        centered
        title={`Setting group ${newItem.name}:`}
        actions={actionSettingUsers}>
        <KanbanTable ref={tableRef} {...tableViewListUserProps} />
      </KanbanModal>
      <KanbanModal
        size={'xl'}
        opened={openedModalRoleInGroup}
        onClose={() => {
          closeModalSettingRoleInGroup();
          tableRef.current?.deselectAll();
        }}
        centered
        title={`Setting role group ${newItem.name}:`}
        actions={actionSettingRoles}>
        <KanbanTable ref={tableRef} {...tableViewListRoleProps} />
      </KanbanModal>
      <HeaderTitleComponent
        title='Group Settings'
        rightSection={
          <GuardComponent requirePermissions={[AclPermission.createGroupUser]} hiddenOnUnSatisfy>
            <Flex>
              <KanbanButton leftSection={<IconPlus />} onClick={openModalCreate}>
                Create new
              </KanbanButton>
            </Flex>
          </GuardComponent>
        }
      />

      <KanbanTable {...tableViewListGroupProps} />

      {false && (
        <KanbanSplitPagingContainer
          title='List Groups'
          listData={groupResponses}
          pagination={{
            itemsPerPage: 10,
          }}
          renderItems={(listData) => {
            return (
              <ListGroupWrapper>
                {listData.map((x, key) => {
                  return (
                    <GroupItem
                      key={key}
                      onClick={() => {
                        setNewItem(x);
                        openModal();
                      }}>
                      <div className='title'>{x.name}</div>
                    </GroupItem>
                  );
                })}
              </ListGroupWrapper>
            );
          }}></KanbanSplitPagingContainer>
      )}
    </div>
  );
};

export default GroupsSettingsPage;
