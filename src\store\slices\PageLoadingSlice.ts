import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import type { RootStoreType } from '@store';

interface PageLoadingState {
  value: number;
}

const initialState: PageLoadingState = {
  value: 0,
};

export const pageLoadingSlice = createSlice({
  name: 'pageLoading',
  initialState,
  reducers: {
    increment(state) {
      state.value++;
    },
    decrement(state) {
      state.value--;
    },
    reset(state) {
      state.value = 0;
    },
    setValue(state, action: PayloadAction<number>) {
      state.value += action.payload;
    },
  },
});

export const getPageLoading = (store: RootStoreType) => store.pageLoading;
