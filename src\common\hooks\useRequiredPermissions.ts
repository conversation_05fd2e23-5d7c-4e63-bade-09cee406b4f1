import { useEffect, useState, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { AclPermission } from 'models/AclPermission';
import { ciIdParam, ciImportPath, ciPath, ciTypeIdParam, ciTypeParam, ciTypePath, isMatchPath, parsePathToObject } from '@common/utils/RouterUtils';
import { PermissionActionType } from '@common/constants/PermissionActionType';
import { PermissionAction } from '@common/constants/PermissionAction';

const useRequiredPermissions = (requirePermissions: AclPermission[], permissionType?: PermissionActionType): AclPermission[] => {
  const location = useLocation();
  const [allRequirePermissions, setAllRequirePermissions] = useState<AclPermission[]>([]);

  const getCiPermissions = useCallback(() => {
    const ciPermissions: AclPermission[] = [];

    if (permissionType === PermissionActionType.CI_TYPE) {
      const objectCiTypePath = parsePathToObject(ciTypePath, location.pathname, {
        end: false,
      });
      const isImportPath = isMatchPath(window.location.pathname, ciImportPath);
      const ciTypeId = objectCiTypePath[ciTypeParam] ? Number(objectCiTypePath[ciTypeParam]) : undefined;
      if (ciTypeId && ciTypeId !== 0) {
        const action = isImportPath ? PermissionAction.CI__IMPORT : PermissionAction.CI_TYPE__VIEW_LIST_CI;
        ciPermissions.push(AclPermission.createCiTypePermission(action, ciTypeId));
      } else {
        ciPermissions.push(AclPermission.showAllCiHasPermission);
      }
    }

    if (permissionType === PermissionActionType.CI) {
      const objectCiDetailPath = parsePathToObject(ciPath, location.pathname, {
        end: false,
      });
      const ciTypeId = objectCiDetailPath[ciTypeIdParam] ? Number(objectCiDetailPath[ciTypeIdParam]) : undefined;
      const ciId = objectCiDetailPath[ciIdParam] ? Number(objectCiDetailPath[ciIdParam]) : undefined;
      if (ciId && ciTypeId) {
        ciPermissions.push(...AclPermission.createViewCiPermissions(ciId, ciTypeId));
      }
    }

    return ciPermissions;
  }, [permissionType, location.pathname]);

  useEffect(() => {
    const ciPermissions = getCiPermissions();
    setAllRequirePermissions([...requirePermissions, ...ciPermissions]);
  }, [requirePermissions, getCiPermissions]);

  return allRequirePermissions;
};

export default useRequiredPermissions;
