import HeaderTitleComponent from '@components/HeaderTitleComponent';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Box, ComboboxItem, Flex, Group } from '@mantine/core';
import { KanbanButton, KanbanInput, KanbanModal, KanbanSelect, KanbanTextarea } from 'kanban-design-system';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { buildSourceDataUrl, createOrUpdateDataSourcePath, dataSourcePath, navigateTo } from '@common/utils/RouterUtils';
import { FormatTypeEnum, formatTypeList } from '@common/constants/FormatTypeEnum';
import { DiscoverySourceDataResponse } from '@models/discovery/DiscoverySourceData';
import { IconDatabaseImport, IconEdit } from '@tabler/icons-react';
import { IconPlus } from '@tabler/icons-react';
import { IconAlertTriangle } from '@tabler/icons-react';
import { DiscoverySourceDataApi } from '@api/discovery/DiscoverySourceDataApi';
import { SourceDataAction } from '@common/constants/SourceDataActionEnum';
import { DiscoverySourceResponse } from '@models/discovery/DiscoverySource';
import { formatStandardName } from '@common/utils/StringUtils';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { SetFieldValueFn } from '@common/utils/CommonUtils';
import { DiscoveryStagingDataApi } from '@api/discovery/DiscoveryStagingDataApi';
import { useDisclosure } from '@mantine/hooks';
import JsonTransformPage from '../jsonTransform';
import { convertCustomDataNodesToJsonNode, convertJsonToCustomDataNodeList, CustomDataNode } from '../jsonTransform/helper/JsonTransformHelper';
import GuardComponent from '@components/GuardComponent';
import { useDataTransform } from '@context/DataTransformContext';
import { useJsonTransformContext } from '@context/JsonTransformContext';
import { JsonTransformActionType } from '@common/constants/JsonTransformActionType';
import { SOURCE_DISCOVERIES_COMBOBOX, SOURCE_DISCOVERIES_LIVE_COMBOBOX } from '@common/constants/DiscoverySourceDataConstants';
import { BreadcrumbComponent, UrlBaseCrumbData } from '@pages/admins/breadcrumb/BreadcrumbComponent';
import { getConfigs } from '@core/configs/Configs';

const dataDefault: DiscoverySourceDataResponse = {
  id: 0,
  name: '',
  sourceId: 0,
  formatType: FormatTypeEnum.JSON,
  discoveryStagingId: 0,
  discoveryStagingName: '',
  selectedFields: [],
};

export const transformToComboboxDiscoverySourceItems = (sources: DiscoverySourceResponse[]): ComboboxItem[] => {
  return sources.map((source) => ({
    value: source.id.toString(),
    label: source.name,
  }));
};

const getTitlePage = (sourceDataId: number, action: SourceDataAction): string => {
  if (sourceDataId > 0) {
    return action === SourceDataAction.VIEW ? 'View data source' : 'Update data source';
  }
  return 'Create data source';
};

export const CreateOrUpdateDataSourcePage = () => {
  const { id } = useParams();
  const sourceDataId = Number(id);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const actionParam = searchParams.get('action');
  const action = Object.values(SourceDataAction).includes(actionParam as SourceDataAction)
    ? (actionParam as SourceDataAction)
    : SourceDataAction.VIEW;
  // const isUpdateAction = SourceDataAction.UPDATE === action;
  const isCreateAction = SourceDataAction.CREATE === action;
  const isViewAction = SourceDataAction.VIEW === action;

  const [sourceDataDetail, setSourceDataDetail] = useState<DiscoverySourceDataResponse>(dataDefault);
  // const [discoverySources, setDiscoverySources] = useState<DiscoverySourceResponse[]>([]);
  const titlePage = getTitlePage(sourceDataId, action);
  const [isClickSaveBtn, setIsClickSaveBtn] = useState<boolean>(false);
  // Add json Transform
  const [openedTransformJson, { close: closeTransformJsonModal, open: openTransformJsonModal }] = useDisclosure(false);

  const { state: dataTransform } = useDataTransform();
  const columnDatas = dataTransform?.columnDatasForInput; // all column before click Save
  const { dispatch: jsonTransformDispatch, state: jsonTransformState } = useJsonTransformContext();
  const stagingStructure = jsonTransformState?.stagingStructure; // all column after click Save
  const selectedFields: string = useMemo(() => {
    return stagingStructure.map((item: CustomDataNode) => String(item.key)).join(', ');
  }, [stagingStructure]);

  const isValidFormJson = jsonTransformState?.isValidFormJson;

  const showDatasource = getConfigs().features.discoverySourceData;

  const onOpenTransformJsonModal = () => {
    jsonTransformDispatch({ type: JsonTransformActionType.UPDATE_FORM_JSON, payload: false });
    openTransformJsonModal();
  };

  const getSourceDataById = useCallback(() => {
    DiscoverySourceDataApi.getById(sourceDataId)
      .then((response) => {
        if (response.status === 200) {
          setSourceDataDetail(response.data);
          jsonTransformDispatch({
            type: JsonTransformActionType.UPDATE_STAGING_STRUCTURE,
            payload: convertJsonToCustomDataNodeList(response.data.discoveryStagingStructureJson ?? ''),
          });
        }
      })
      .catch(() => {});
  }, [sourceDataId, jsonTransformDispatch]);

  const createOrUpdate = () => {
    const model = { ...sourceDataDetail };
    model.discoveryStagingStructureJson = JSON.stringify(convertCustomDataNodesToJsonNode(stagingStructure));
    DiscoverySourceDataApi.createOrUpdate(model)
      .then((response) => {
        if (response.status === 200) {
          setIsClickSaveBtn(true);
          // navigateTo(dataSourcePath);
          NotificationSuccess({ message: sourceDataId > 0 ? 'Update data source success.' : 'Create data source success.' });
          if (sourceDataId === 0) {
            setSourceDataDetail(response.data);
            navigate(buildSourceDataUrl(response.data.id, createOrUpdateDataSourcePath, SourceDataAction.VIEW));
          } else {
            setSourceDataDetail((prev) => ({
              ...prev,
              name: response.data.name,
            }));
          }
        }
      })
      .catch(() => {});
  };

  // const getAllDataSources = useCallback(() => {
  //   DiscoverySourceApi.getAll()
  //     .then((response) => {
  //       if (response.status === 200) {
  //         setDiscoverySources(response.data);
  //       }
  //     })
  //     .catch(() => {});
  // }, []);

  // useEffect(() => {
  //   getAllDataSources();
  // }, [getAllDataSources]);

  const handleChange: SetFieldValueFn<DiscoverySourceDataResponse> = (field, value) => {
    const updateData: DiscoverySourceDataResponse = {
      ...sourceDataDetail,
      [field]: value,
    };
    setSourceDataDetail(updateData);
  };

  const autoDiscovery = () => {
    DiscoveryStagingDataApi.discoverySourceByDiscoverySourceDataId(sourceDataDetail.id)
      .then((response) => {
        if (response.status === 200) {
          NotificationSuccess({ message: 'Auto discovery source success' });
        }
      })
      .catch(() => {});
  };

  useEffect(() => {
    if (sourceDataId > 0) {
      getSourceDataById();
    } else {
      jsonTransformDispatch({ type: JsonTransformActionType.RESET_STAGING_STRUCTURE });
    }
  }, [getSourceDataById, sourceDataId, jsonTransformDispatch]);
  const locationCustomPaths = useMemo((): UrlBaseCrumbData => {
    const originPath = buildSourceDataUrl(Number(id), createOrUpdateDataSourcePath, SourceDataAction.VIEW);
    return {
      [`/${id}`]: {
        title: SourceDataAction.CREATE === action ? `${formatStandardName(action)}` : `${formatStandardName(action)} ${sourceDataDetail.name}`,
        href: originPath,
      },
    };
  }, [action, id, sourceDataDetail.name]);
  return (
    <Box p={20}>
      {/* 4763 Datasource detail*/}
      <BreadcrumbComponent locationCustomPaths={locationCustomPaths} />
      <HeaderTitleComponent
        title={titlePage}
        rightSection={
          <Flex gap={10}>
            <KanbanButton
              variant='outline'
              onClick={() => {
                navigateTo(dataSourcePath);
              }}>
              Close
            </KanbanButton>
            <GuardComponent requirePermissions={[]} hiddenOnUnSatisfy>
              {isViewAction && (
                <KanbanButton
                  leftSection={<IconEdit />}
                  onClick={() => navigate(buildSourceDataUrl(Number(id), createOrUpdateDataSourcePath, SourceDataAction.UPDATE))}>
                  Edit
                </KanbanButton>
              )}
            </GuardComponent>
            {!isViewAction && (
              <KanbanButton
                disabled={
                  sourceDataDetail.name.trim().length === 0 ||
                  sourceDataDetail.discoveryStagingName?.trim().length === 0 ||
                  !sourceDataDetail.sourceId ||
                  stagingStructure.length === 0
                }
                leftSection={Number(id) > 0 ? <IconEdit /> : <IconPlus />}
                onClick={() => {
                  createOrUpdate();
                }}>
                Save
              </KanbanButton>
            )}
          </Flex>
        }
      />

      <Box mb={'md'}>
        <KanbanInput
          label='Name'
          width={'50%'}
          maxLength={100}
          value={sourceDataDetail.name ?? ''}
          onChange={(event) => handleChange('name', event.target.value)}
          onBlur={(e) => {
            const value = e.target.value;
            handleChange('name', formatStandardName(value));
          }}
          disabled={isViewAction}
          required></KanbanInput>
        {false && (
          <KanbanSelect
            label='Format Type'
            searchable
            defaultValue={sourceDataDetail.formatType}
            allowDeselect={false}
            value={sourceDataDetail.formatType}
            data={formatTypeList}
            disabled={sourceDataId > 0}
            onChange={(event) => handleChange('formatType', event as FormatTypeEnum)}></KanbanSelect>
        )}
        <KanbanSelect
          label='Source'
          required
          searchable
          // defaultValue={sourceDataDetail.sourceId.toString()}
          allowDeselect={false}
          value={sourceDataDetail.sourceId.toString()}
          autoChangeValueByOptions={false}
          // data={transformToComboboxDiscoverySourceItems(discoverySources)}
          data={showDatasource ? SOURCE_DISCOVERIES_COMBOBOX : SOURCE_DISCOVERIES_LIVE_COMBOBOX}
          disabled={sourceDataId > 0 || !isCreateAction}
          onChange={(event) => {
            handleChange('sourceId', Number(event));
            jsonTransformDispatch({ type: JsonTransformActionType.RESET_STAGING_STRUCTURE });
          }}></KanbanSelect>
        <KanbanInput
          label='Staging Table'
          maxLength={100}
          value={sourceDataDetail.discoveryStagingName ?? ''}
          disabled={sourceDataId > 0 || !isCreateAction}
          onBlur={(e) => {
            const value = e.target.value;
            handleChange('discoveryStagingName', formatStandardName(value));
          }}
          onChange={(event) => handleChange('discoveryStagingName', event.target.value)}
          required></KanbanInput>

        <KanbanTextarea label='Select fields' required autosize disabled value={selectedFields} />

        <Box>
          <Group justify='space-between'>
            <KanbanButton
              disabled={!sourceDataDetail.sourceId || isViewAction}
              leftSection={<IconAlertTriangle />}
              onClick={onOpenTransformJsonModal}>
              Configure
            </KanbanButton>

            <KanbanButton disabled={isCreateAction && !isClickSaveBtn} color={'cyan'} leftSection={<IconDatabaseImport />} onClick={autoDiscovery}>
              Run update
            </KanbanButton>
          </Group>
        </Box>
      </Box>

      <KanbanModal
        size={'100%'}
        opened={openedTransformJson}
        onClose={() => {
          closeTransformJsonModal();
        }}
        centered
        title={`Configure Selected Fields`}
        actions={
          <>
            <KanbanButton
              disabled={!isValidFormJson || dataTransform.isJsonStatic}
              onClick={() => {
                jsonTransformDispatch({ type: JsonTransformActionType.UPDATE_STAGING_STRUCTURE, payload: columnDatas });

                closeTransformJsonModal();
              }}>
              Save
            </KanbanButton>
          </>
        }>
        <JsonTransformPage sourceId={sourceDataDetail.sourceId} discoveryStagingId={sourceDataDetail.discoveryStagingId} />
      </KanbanModal>
    </Box>
  );
};
export default CreateOrUpdateDataSourcePage;
