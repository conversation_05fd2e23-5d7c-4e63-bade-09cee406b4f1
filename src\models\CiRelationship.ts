import { FlaggedImpactedCiAttachedType } from '@common/constants/CiManagement';
import type { EntityModelBase } from './EntityModelBase';
import { ImpactedCiRuleRelationModel } from './ImpactedRule';

export type CiRelationshipModel = EntityModelBase & {
  id: number;
  fromCi: number;
  toCi: number;
  relationshipId: number;
  listToCi?: number[];
  relationshipAttributes?: CiRelationshipAttributeModel[];
};

export type CiRelationshipAttributeModel = {
  id: number;
  attributeId?: number;
  value?: string;
};

export type CiRelationshipInfoModel = CiRelationshipModel & {
  fromCiName?: string;
  toCiName?: string;
  fromCiTypeId?: number;
  toCiTypeId?: number;
  fromCiTypeName?: string;
  toCiTypeName?: string;
  // ruleId?: number;
  // ruleName?: string;
  // ruleRelationDirection?: string;
  ciRuleRelationships?: ImpactedCiRuleRelationModel[];
};

export type CiInfoWithCiTypeDto = CiRelationshipCountModel & {
  ciName?: string;
  ciTypeId?: number;
  ciTypeName?: string;
};

export type CiRelationshipInfoWithImpact = CiRelationshipInfoModel & {
  impactedCi?: number;
  ciChangePlans?: number[];
  impactDirection?: string | undefined;
  relationshipName?: string;
  hasImpact?: string;
  ruleName?: string;
  inverse?: boolean;
  attachedType?: FlaggedImpactedCiAttachedType;
};

export type CiRelationshipInfoWithImpactMap = {
  message?: string;
  data?: CiRelationshipInfoWithImpact[];
};

export type CiRelationshipDetailsModel = {
  cisDetail: CiInfoWithCiTypeDto[];
  ciRelationships: CiRelationshipInfoModel[];
};

export type LinkDataModel = {
  from: number;
  to: number;
};

export type CiRelationshipCountModel = {
  ciId: number;
  totalFrom: number;
  totalTo: number;
};

export enum RelationshipTypeInput {
  SUGGEST = 'SUGGEST',
  SEARCH = 'SEARCH',
}

export enum ImpactTypeInput {
  IMPACTTO = 'IMPACTTO',
  IMPACTBY = 'IMPACTBY',
}
export default 1;
