import { ColumnType, KanbanTable, TableAffactedSafeType } from 'kanban-design-system';
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { renderDateTime } from 'kanban-design-system';
import equal from 'fast-deep-equal';
import { CiSdpRequestModel, DateFilterOptionEnum, RangeDateModel, selectDateFilterOptions } from '@models/ChangeSdp';
import { CustomLink } from '../CiRequest';
import { ConfigItemApi } from '@api/ConfigItemApi';
import { DD_MM_YYYY_FORMAT, endOfToday, getDateRange, startDate } from '@common/utils/DateUtils';
import { CiRequestTypeEnum } from '@common/constants/CiDetail';
import type { DatesRangeValue, DateValue } from '@mantine/dates';
import { KanbanSelect } from 'kanban-design-system';
import { KanbanTooltip } from 'kanban-design-system';
import { KanbanDatePicker } from 'kanban-design-system';
import { IncidentSdpApi } from '@api/IncidentSdpApi';
import { KanbanIconButton } from 'kanban-design-system';
import { buildIncidentDetailUrl } from '@common/utils/RouterUtils';
import { ChangeAssessmentAction } from '@models/ChangeAssessment';
import { IconEye } from '@tabler/icons-react';
import { tableAffectedAndRangeDateToPaginationRequestModel } from '@common/utils/KanbanTableUtils';

type CiIncidentProps = {
  ciId: number;
};
export const IncidentTableTab = ({ ciId }: CiIncidentProps) => {
  const [totalRecords, setTotalRecords] = useState(0);
  const [isLoadingTable, setIsLoadingTable] = useState(false);
  const [incidents, setIncidents] = useState<CiSdpRequestModel[]>([]);
  const [requestIdMappingStatus, setRequestIdMappingStatus] = useState<Record<string, string>>({});
  const [rangeDateToFilter, setRangeDateToFilter] = useState<RangeDateModel>({ fromDate: startDate, toDate: endOfToday });
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);
  const [showCustomRangeDatePicker, setShowCustomRangeDatePicker] = useState<boolean>(false);
  const fetchListChangeStatus = useCallback((incidentIds: number[]) => {
    IncidentSdpApi.getStatusByRequestIdIn(incidentIds, false)
      .then((res) => {
        if (res.data) {
          const statusMap = res.data.reduce((acc: Record<string, string>, e: CiSdpRequestModel) => {
            acc[e.requestId] = e.status;
            return acc;
          }, {});
          setRequestIdMappingStatus(statusMap);
        }
      })
      .catch(() => {});
  }, []);
  const fetchListIncident = useCallback(() => {
    if (!tableAffected) {
      return;
    }

    setIsLoadingTable(true);

    ConfigItemApi.getAllRequestByCiId(
      ciId,
      tableAffectedAndRangeDateToPaginationRequestModel(tableAffected, rangeDateToFilter),
      CiRequestTypeEnum.INCIDENT,
      false,
    )
      .then((res) => {
        setIncidents(res.data.content || []);
        setTotalRecords(res.data.totalElements);
        if (res.data.totalElements) {
          fetchListChangeStatus((res.data.content || []).map((e) => e.requestId));
        }
      })
      .catch(() => {})
      .finally(() => {
        setIsLoadingTable(false);
      });
  }, [tableAffected, ciId, rangeDateToFilter, fetchListChangeStatus]);

  const columns: ColumnType<CiSdpRequestModel>[] = useMemo(
    () => [
      {
        title: 'ID',
        name: 'requestId',
      },
      {
        title: 'Status',
        name: 'status',
        width: 150,
        sortable: false,
        customRender: (_, data) => {
          return <>{requestIdMappingStatus[data.requestId]}</>;
        },
      },
      {
        title: 'SDP url',
        name: 'requestUrl',
        sortable: false,
        customRender: (data) => {
          return (
            <CustomLink target={'_blank'} to={data}>
              {data}
            </CustomLink>
          );
        },
      },
      {
        title: 'Created date',
        name: 'createdDate',
        customRender: renderDateTime,
      },
    ],
    [requestIdMappingStatus],
  );
  const handleSelectChange = (e: string | null) => {
    setShowCustomRangeDatePicker(e === DateFilterOptionEnum.CUSTOM);
    const rangeDate = getDateRange(e as DateFilterOptionEnum);
    setRangeDateToFilter(rangeDate);
  };

  const handleFromDateChange = (e: DateValue | DatesRangeValue | Date[]) => {
    setRangeDateToFilter((prev) => ({ ...prev, fromDate: e as Date }));
  };

  const handleToDateChange = (e: DateValue | DatesRangeValue | Date[]) => {
    const endOfDay = new Date((e as Date).setHours(23, 59, 59, 999));
    setRangeDateToFilter((prev) => ({ ...prev, toDate: endOfDay }));
  };
  useEffect(() => {
    fetchListIncident();
  }, [fetchListIncident]);

  return (
    <>
      <div style={{ flex: 2 }}>
        <KanbanTable
          title='Incident'
          columns={columns}
          key={1}
          data={incidents}
          isLoading={isLoadingTable}
          showNumericalOrderColumn={true}
          searchable={{
            enable: true,
            debounceTime: 300,
          }}
          pagination={{
            enable: true,
          }}
          serverside={{
            totalRows: totalRecords,
            onTableAffected(dataSet) {
              if (!equal(tableAffected, dataSet)) {
                setTableAffected(dataSet);
              }
            },
          }}
          actions={{
            customAction: (data) => (
              <>
                <KanbanTooltip label='Go to detail'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      window.open(buildIncidentDetailUrl(data.requestId, ChangeAssessmentAction.VIEW), '_blank');
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                </KanbanTooltip>
              </>
            ),
          }}
          customAction={() => (
            <>
              <KanbanSelect
                size='xs'
                m={'xs'}
                miw={'120'}
                data={selectDateFilterOptions}
                defaultValue={DateFilterOptionEnum.ALL}
                onChange={handleSelectChange}
              />
              {showCustomRangeDatePicker && (
                <>
                  <KanbanTooltip label='From date'>
                    <KanbanDatePicker
                      size='xs'
                      my={'xs'}
                      mr={'xs'}
                      defaultValue={rangeDateToFilter.fromDate}
                      valueFormat={DD_MM_YYYY_FORMAT}
                      onChange={handleFromDateChange}
                    />
                  </KanbanTooltip>
                  <KanbanTooltip label='To date'>
                    <KanbanDatePicker
                      size='xs'
                      my={'xs'}
                      defaultValue={rangeDateToFilter.toDate}
                      valueFormat={DD_MM_YYYY_FORMAT}
                      onChange={handleToDateChange}
                    />
                  </KanbanTooltip>
                </>
              )}
            </>
          )}
        />
      </div>
    </>
  );
};
export default IncidentTableTab;
