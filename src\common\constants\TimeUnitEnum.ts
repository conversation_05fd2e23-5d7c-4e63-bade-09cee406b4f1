import { ComboboxItem } from '@mantine/core';

export enum TimeUnitEnum {
  ONCE_TIME = 'ONCE_TIME',
  MINUTE = 'MINUTE',
  HOUR = 'HOUR',
  DAY = 'DAY',
  WEEK = 'WEEK',
}

export const getTextInterval = (interval?: number, value?: TimeUnitEnum | undefined): string => {
  if (!value) {
    return '';
  }
  const intervalValue = interval || '';

  const map: Record<TimeUnitEnum, string> = {
    [TimeUnitEnum.ONCE_TIME]: 'Run only 1 time',
    [TimeUnitEnum.MINUTE]: `Run every ${intervalValue} minute(s)`,
    [TimeUnitEnum.HOUR]: `Run every ${intervalValue} hour(s)`,
    [TimeUnitEnum.DAY]: `Run every ${intervalValue} day(s)`,
    [TimeUnitEnum.WEEK]: `Run every ${intervalValue} week(s)`,
  };
  return map[value] || '';
};

export const getComboboxTimeUnitEnum = (values: TimeUnitEnum[]): ComboboxItem[] => {
  const comboboxItems: ComboboxItem[] = [];
  values.forEach((value) => {
    comboboxItems.push({
      value: value,
      label: value.toLowerCase(),
    });
  });
  return comboboxItems;
};

export const getTextTimeUnit = (value?: TimeUnitEnum | undefined): string => {
  if (!value) {
    return '';
  }

  const map: Record<TimeUnitEnum, string> = {
    [TimeUnitEnum.ONCE_TIME]: '1 time',
    [TimeUnitEnum.MINUTE]: `minute(s)`,
    [TimeUnitEnum.HOUR]: `hour(s)`,
    [TimeUnitEnum.DAY]: `day(s)`,
    [TimeUnitEnum.WEEK]: `week(s)`,
  };
  return map[value] || '';
};
