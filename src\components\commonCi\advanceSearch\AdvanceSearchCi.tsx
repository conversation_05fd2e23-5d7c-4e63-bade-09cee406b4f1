import React, { useCallback, useEffect, useRef, useState } from 'react';
import { forwardRef, useImperativeHandle } from 'react';
import AdvanceSearchComponent, { AdvanceSearchData, AdvanceSearchMethod, FieldChangeProps } from './AdvanceSearchComponent';
import { ConfigItemTypeAttrApi } from '@api/ConfigItemTypeAttrApi';
import type { Field, RuleGroupType } from 'react-querybuilder';
import type { QueryRequestModel } from '@models/EntityModelBase';
import { CiTypeAttributeDataType } from '@models/CiType';

type AdvanceSearchCiProps = {
  onSearch?: (value: AdvanceSearchData) => void;
  setShowBtnExport?: (value: boolean) => void;
  queryRequestModel?: QueryRequestModel<any>;
  ciTypeId?: number;
  initialQuery?: RuleGroupType;
} & FieldChangeProps;

export type AdvanceSearchCiMethod = {
  showBtnExport: (value: boolean) => void;
};

const fieldsSearchDataDefault = [
  {
    name: 'name',
    label: 'Name',
    placeholder: 'Enter name',
    inputType: 'ci',
    group: 'Attribute',
  },
  {
    name: 'ciTypeId',
    label: 'Type',
    placeholder: '',
    inputType: 'type',
    group: 'Attribute',
  },
  {
    name: 'description',
    label: 'Description',
    placeholder: '',
    inputType: 'text',
    group: 'Attribute',
  },
  {
    name: 'createdDate',
    label: 'Created Date',
    placeholder: '',
    inputType: 'date',
    group: 'Attribute',
  },
  {
    name: 'author', // update logic search CI by author (user create data CI), createdBy is history log in DB(user approval)
    label: 'Created By',
    placeholder: '',
    inputType: 'user',
    group: 'Attribute',
  },
  {
    name: 'id',
    label: 'CI Id',
    placeholder: '',
    inputType: 'number',
    group: 'Attribute',
  },
  {
    name: 'relationshipType',
    label: 'Relationship Type',
    placeholder: '',
    inputType: 'number',
    group: 'Relationship',
  },
  {
    name: 'fromCi',
    label: 'From CI',
    placeholder: '',
    inputType: 'ci',
    group: 'Relationship',
  },
  {
    name: 'toCi',
    label: 'To CI',
    placeholder: '',
    inputType: 'ci',
    group: 'Relationship',
  },
];

export const AdvanceSearchCi = forwardRef<AdvanceSearchCiMethod, AdvanceSearchCiProps>((props, ref) => {
  const [fieldsSearch, setFieldsSearch] = useState<Field[]>(fieldsSearchDataDefault);
  const { ciTypeId } = props;

  const fetchSuggestCiTypeAttribute = useCallback(() => {
    ConfigItemTypeAttrApi.getSuggestCiTypeAttribute(ciTypeId && ciTypeId !== 0 ? ciTypeId : undefined).then((res) => {
      if (res && res.data) {
        const customAttribute: Field[] = [];
        res.data.forEach((item) => {
          const { ciTypeId, ciTypeName, hashId, name, options, type } = item || {};
          let parsedOptions;
          try {
            parsedOptions = JSON.parse(options || '[]');
          } catch (e) {
            parsedOptions = [];
          }
          if (CiTypeAttributeDataType.REFERENCE !== type) {
            customAttribute.push({
              name: `${hashId || ''}`,
              label: name,
              inputType: CiTypeAttributeDataType.DATE === type ? 'date' : type,
              group: 'Custom Attribute',
              groupView: ciTypeId === 0 ? 'Attribute' : 'Custom Attribute',
              values: parsedOptions,
              type: type,
              tag: ciTypeName,
            });
          }
        });
        setFieldsSearch((prev) => [...prev, ...customAttribute]);
      }
    });
  }, [ciTypeId]);

  useEffect(() => {
    fetchSuggestCiTypeAttribute();
  }, [fetchSuggestCiTypeAttribute]);

  useImperativeHandle<any, AdvanceSearchCiMethod>(
    ref,
    () => ({
      showBtnExport: (val) => {
        exportComponentRef.current?.showBtnExport(val);
      },
    }),
    [],
  );

  const exportComponentRef = useRef<AdvanceSearchMethod | null>(null);

  return (
    <>
      <AdvanceSearchComponent
        fields={fieldsSearch}
        onSearch={props.onSearch}
        ref={exportComponentRef}
        queryRequestModel={props.queryRequestModel}
        ciTypeId={props.ciTypeId}
        initialQuery={props.initialQuery}
      />
    </>
  );
});
AdvanceSearchCi.displayName = 'AdvanceSearchCi';
export default AdvanceSearchCi;
