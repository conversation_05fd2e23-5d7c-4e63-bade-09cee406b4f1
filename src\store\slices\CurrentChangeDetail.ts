import type { SysGroupResponse } from '@api/systems/UsersApi';
import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import type { RootStoreType } from '@store';
import type { AclPermission } from 'models/AclPermission';

interface UserState {
  isFetching: boolean;
  data?: {
    username: string;
    email: string;
    isActive: boolean;
    isSuperAdmin: boolean;
    groups: SysGroupResponse[];
    aclPermissions: AclPermission[];
  };
}

const initialState: UserState = {
  isFetching: false,
};

export const currentUserSlice = createSlice({
  name: 'currentUser',
  initialState,
  reducers: {
    fetchData() {},
    setValue(_state, action: PayloadAction<UserState>) {
      return action.payload;
    },
  },
});

export const getCurrentUser = (store: RootStoreType) => store.currentUser;
