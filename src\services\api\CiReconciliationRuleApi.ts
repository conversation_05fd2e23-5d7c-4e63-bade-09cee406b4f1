import { BaseApi } from '@core/api/BaseApi';
import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
import type { CiReconciliationRule, CiReconciliationRuleDto } from '@models/CiReconciliationRule';
import { BaseUrl } from '@core/api/BaseUrl';

export type CiReconciliationRuleResponse = PaginationResponseModel<CiReconciliationRule>;

export class CiReconciliationRuleApi extends BaseApi {
  static baseUrl = BaseUrl.ciReconciliationRules;

  static findAllWithPaging(pagination: PaginationRequestModel<CiReconciliationRule>) {
    return BaseApi.postData<CiReconciliationRuleResponse>(
      `${this.baseUrl}/filter`,
      pagination,
      {},
      {},
      { useLoading: false, useErrorNotification: true },
    );
  }

  static findCiReconciliationRuleById(id: number) {
    return BaseApi.getData<CiReconciliationRuleDto>(`${this.baseUrl}/${id}`);
  }

  static createNew(data: CiReconciliationRuleDto) {
    return BaseApi.postData<boolean>(`${this.baseUrl}`, data);
  }

  static updateRule(data: CiReconciliationRuleDto, id: number) {
    return BaseApi.putData<boolean>(`${this.baseUrl}/${id}`, data);
  }

  static deleteByIdIn(ids: number[]) {
    return BaseApi.deleteData<number[]>(`${this.baseUrl}`, {
      ids,
    });
  }
}
