import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import type { BusinessDomainAggregateItem, BusinessDomainModel, BusinessDomainDetailModel, BusinessFunctionModel } from '@models/BusinessDomain';
import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
export class BusinessDomainApi extends BaseApi {
  static baseUrl = BaseUrl.businessDomains;

  static getAll() {
    return BaseApi.getData<BusinessDomainModel[]>(`${this.baseUrl}`);
  }

  static getByCiId(ciId: number) {
    return BaseApi.getData<BusinessDomainDetailModel>(`${this.baseUrl}?ciId=${ciId}`);
  }

  static getBusinessFunctionInfoByCiIdBusinessDomainAndCiIdBusinessFunction(ciIdBusinessDomain: number, ciIdBusinessFunction: number) {
    return BaseApi.getData<BusinessDomainDetailModel>(
      `${this.baseUrl}/business-functions?ciIdBusinessDomain=${ciIdBusinessDomain}&ciIdBusinessFunction=${ciIdBusinessFunction}`,
    );
  }

  static getBusinessModelByCiIdBusinessDomainAndCiIdBusinessFunction(ciIdBusinessDomain: number, ciIdBusinessFunction: number) {
    return BaseApi.getData<BusinessFunctionModel>(
      `${this.baseUrl}/business-functions/business-models?ciIdBusinessDomain=${ciIdBusinessDomain}&ciIdBusinessFunction=${ciIdBusinessFunction}`,
    );
  }

  static getAllWithPaging(pagination: PaginationRequestModel<BusinessDomainModel>, useLoading: boolean = true, controller?: AbortController) {
    return BaseApi.postData<PaginationResponseModel<BusinessDomainModel>>(
      `${this.baseUrl}`,
      { ...pagination },
      {},
      {},
      { useLoading: useLoading, useErrorNotification: true },
      controller,
    );
  }

  static getBusinessFunctionByCiIdWithPaging(
    ciId: number,
    pagination: PaginationRequestModel<BusinessDomainModel>,
    useLoading: boolean = true,
    controller?: AbortController,
  ) {
    return BaseApi.postData<PaginationResponseModel<BusinessDomainModel>>(
      `${this.baseUrl}/business-functions?ciId=${ciId}`,
      { ...pagination },
      {},
      {},
      { useLoading: useLoading, useErrorNotification: true },
      controller,
    );
  }

  static getBusinessDomainAggregate(
    pagination: PaginationRequestModel<BusinessDomainAggregateItem>,
    useLoading: boolean = true,
    controller?: AbortController,
  ) {
    return BaseApi.postData<PaginationResponseModel<BusinessDomainAggregateItem>>(
      `${this.baseUrl}/aggregate`,
      { ...pagination },
      {},
      {},
      { useLoading: useLoading, useErrorNotification: true },
      controller,
    );
  }
}
