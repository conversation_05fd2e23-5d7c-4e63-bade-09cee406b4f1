// import type { AdvanceSearchData } from "@components/commonCi/advanceSearch/AdvanceSearchComponent";

export type AttributeInfoDTO = {
  position: number;
  attributeId: number;
  nameAttribute: string;
  value: string;
  defaultAttribute: boolean;
};

export type AttributeInfoEleDTO = AttributeInfoDTO & {
  selected?: boolean;
};

export type ExportFileRequest = {
  attributeInfoList: AttributeInfoDTO[];
  searchData: any;
  typeFile?: string;
};
