import React from 'react';
import { Flex, SimpleGrid } from '@mantine/core';
import { KanbanText } from 'kanban-design-system';

import type { BusinessDomainDetailModel } from '@models/BusinessDomain';
import { dateToString } from '@common/utils/DateUtils';

type BusinessDomainDetailTabInformationProps = {
  isShowLevelLeaf?: boolean;
  data: BusinessDomainDetailModel;
};

export const BusinessDomainDetailTabInformation = (props: BusinessDomainDetailTabInformationProps) => {
  const styleLabel = { width: '200px', flex: '0 0 auto' };

  const renderDomainParent = (bs: BusinessDomainDetailModel) => {
    return bs.content.parents?.map((item, index) => (
      <Flex key={index}>
        <KanbanText style={styleLabel} fw={700}>
          Domain Level {item.levelLeaf}
        </KanbanText>
        <KanbanText>{item.name}</KanbanText>
      </Flex>
    ));
  };

  const businessDomainDetailModel = props.data;

  return (
    <>
      <SimpleGrid cols={1} p={'sm'} verticalSpacing={'xs'} bg={'var(--mantine-color-body)'}>
        <KanbanText style={styleLabel} fw={700} c={'var(--mantine-color-primary-outline)'} size='lg'>
          Infomation
        </KanbanText>
        <Flex>
          <KanbanText style={styleLabel} fw={700}>
            ID Number
          </KanbanText>
          <KanbanText>{businessDomainDetailModel.content.id}</KanbanText>
        </Flex>
        <Flex>
          <KanbanText style={styleLabel} fw={700}>
            Name
          </KanbanText>
          <KanbanText>{businessDomainDetailModel.content.name}</KanbanText>
        </Flex>

        {props.isShowLevelLeaf && (
          <>
            <Flex>
              <KanbanText style={styleLabel} fw={700}>
                Domain Level
              </KanbanText>
              <KanbanText>{businessDomainDetailModel.content.levelLeaf}</KanbanText>
            </Flex>
          </>
        )}

        {renderDomainParent(businessDomainDetailModel)}

        <Flex>
          <KanbanText style={styleLabel} fw={700}>
            Description
          </KanbanText>
          <KanbanText>{businessDomainDetailModel.content.description}</KanbanText>
        </Flex>
        <KanbanText style={styleLabel} fw={700} c={'var(--mantine-color-primary-outline)'} size='lg'>
          Info Request
        </KanbanText>
        <Flex>
          <KanbanText style={styleLabel} fw={700}>
            Creator
          </KanbanText>
          <KanbanText>{businessDomainDetailModel.content.author}</KanbanText>
        </Flex>
        <Flex>
          <KanbanText style={styleLabel} fw={700}>
            Created date
          </KanbanText>
          <KanbanText>
            {/* fix bug CMDB-4772 */}
            {businessDomainDetailModel.content.createdDate === undefined || businessDomainDetailModel.content.createdDate === null
              ? ''
              : dateToString(businessDomainDetailModel.content.createdDate)}
          </KanbanText>
        </Flex>
        <Flex>
          <KanbanText style={styleLabel} fw={700}>
            Approver
          </KanbanText>
          <KanbanText>{businessDomainDetailModel.content.approvedBy}</KanbanText>
        </Flex>
        <Flex>
          <KanbanText style={styleLabel} fw={700}>
            Approved date
          </KanbanText>
          <KanbanText>
            {/* fix bug CMDB-4772 */}
            {businessDomainDetailModel.content.approvedDate === undefined || businessDomainDetailModel.content.approvedDate === null
              ? ''
              : dateToString(businessDomainDetailModel.content.approvedDate)}
          </KanbanText>
        </Flex>
      </SimpleGrid>
    </>
  );
};

export default BusinessDomainDetailTabInformation;
