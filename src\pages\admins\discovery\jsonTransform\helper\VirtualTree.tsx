import React, { useRef } from 'react';
import Tree from 'rc-tree';
import { Key } from 'rc-tree/lib/interface';
import 'rc-tree/assets/index.css';
import { CustomDataNode } from './JsonTransformHelper';
import styled from 'styled-components';
import { debounce } from 'lodash';

interface VirtualTreeProps {
  treeData: CustomDataNode[];
  height?: number;
  width?: number;
  onDragStart: (info: any) => void;
  expandedKeys: Key[];
  onExpand: (expandedKeys: Key[]) => void;
}
const TreeWrapper = styled.div<{ height: number }>`
  height: ${(props) => props.height}px;
`;

export const VirtualTree: React.FC<VirtualTreeProps> = ({ expandedKeys, height = 500, onDragStart, onExpand, treeData }) => {
  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
  };
  const ref = useRef<HTMLButtonElement>(null);
  return (
    <TreeWrapper height={height} onDragOver={handleDragOver}>
      <button ref={ref} style={{ display: 'none' }}>
        Just for fix bug auto scroll after dragging item from virtual tree
      </button>
      <Tree
        treeData={treeData}
        draggable
        onDragStart={onDragStart}
        expandedKeys={expandedKeys}
        onExpand={debounce(onExpand, 200)}
        onDragEnd={() => {
          if (ref.current) {
            const target = ref.current;
            target.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
            target.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
          }
        }}
        motion={null}
        virtual={true}
        height={height}
        itemHeight={30}
        defaultExpandParent={false}
      />
    </TreeWrapper>
  );
};
