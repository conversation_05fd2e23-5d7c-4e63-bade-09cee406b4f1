/* Dropdown menu */
.dropdown-menu {
  background: var(--mantine-color-white);
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: var(--mantine-radius-md);
  box-shadow: var(--mantine-shadow-xs);
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
  overflow: auto;
  padding: 0.4rem;
  position: relative;

  button {
    align-items: center;
    background-color: transparent;
    display: flex;
    gap: 0.25rem;
    text-align: left;
    width: 100%;
    border: none;
    border-radius: var(--mantine-radius-md);

    &:hover,
    &:hover.is-selected {
      background-color: var(--mantine-color-gray-5);
    }

    &.is-selected {
      background-color: var(--mantine-color-gray-2);
    }
  }
}

.tiptap-mention {
  background-color: var(--mantine-color-primary-6);
  border-radius: var(--mantine-radius-md);
  box-decoration-break: clone;
  color: var(--mantine-color-white);
  padding: 0.1rem 0.3rem;
}
.editor-subject {
  padding: 1rem;
  margin-block-start: 0px;
  margin-block-end: 0px;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
}