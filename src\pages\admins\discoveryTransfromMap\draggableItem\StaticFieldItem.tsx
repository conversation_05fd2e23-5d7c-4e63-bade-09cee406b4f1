import { Text, Flex, ActionIcon, PaperProps } from '@mantine/core';
import { IconX } from '@tabler/icons-react';
import { useEffect, useState } from 'react';
import React from 'react';
import { DragTranfromMapType } from '@models/DiscoveryTransformMap';
import { DraggableItemBase } from './DraggableItemBase';

interface StaticFieldItemProps {
  item: DragTranfromMapType;
  index: number;
  addValue: string;
  staticFields: DragTranfromMapType[];
  classes: Record<string, string>;
  onClick: (id: number | string) => void;
  onConfirm: (id: string | number) => void;
  removeStaticField: (staticValue: string | undefined) => void;
  setAddValue: (val: string) => void;
  isDragging: boolean;
}

export const StaticFieldItem = ({ addValue, classes, index, isDragging, item, onClick, removeStaticField }: StaticFieldItemProps) => {
  const [hovered, setHovered] = useState(false);

  useEffect(() => {
    if (isDragging && hovered) {
      setHovered(false);
    }
  }, [isDragging, hovered]);
  return (
    <DraggableItemBase
      item={item}
      index={index}
      classes={classes}
      onClick={() => onClick(item.id)}
      draggableIdPrefix='static_field'
      paperProps={
        {
          onMouseEnter: () => setHovered(true),
          onMouseLeave: () => setHovered(false),
        } as PaperProps
      }
      renderContent={(item) => (
        <Flex align='center' justify='space-between'>
          <Text w={200} truncate='end'>
            {item.staticValue}
          </Text>
          {hovered && addValue.trim() === '' && (
            <ActionIcon
              color='red'
              variant='light'
              onClick={(e) => {
                e.stopPropagation();
                removeStaticField(item.staticValue);
              }}>
              <IconX size={16} />
            </ActionIcon>
          )}
        </Flex>
      )}
    />
  );
};
