import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { buildCiManageDetailUrl, buildCiTypeUrl } from '@common/utils/RouterUtils';
import { KanbanButton, KanbanTableSelectHandleMethods, KanbanTooltip } from 'kanban-design-system';
import { KanbanConfirmModal } from 'kanban-design-system';
import { ColumnType, KanbanTable, KanbanTableProps, TableAffactedSafeType } from 'kanban-design-system';
import { renderDateTime } from 'kanban-design-system';
import { KanbanText } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import { IconEdit, IconEye, IconTrash } from '@tabler/icons-react';
import equal from 'fast-deep-equal';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import CiUpdateSole, { type CiUpdateSoleMethods } from '../ci/CiUpdateSole';
import { KanbanIconButton } from 'kanban-design-system';
import { CiManagementApi, type CiManagementResponse } from '@api/CiManagementApi';
import type { ConfigItemInfoModel } from '@models/ConfigItem';
import StatusComponent from './detail/StatusComponent';
import { Group, Tooltip } from '@mantine/core';
import { ActionType, ScreenTypeManagement, StatusCiManagement } from '@common/constants/CiManagement';
import { CiRequestDetailPopup, type CiRequestDetailPopupMethods } from './modal/CiRequestDetailPopup';
import { CiManagementDetailViewPopup, type CiManagementDetailViewPopupMethods } from './modal/CiManagementDetailViewPopup';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import ConfirmViewChangePopup, { ConfirmViewChangePopupMethods } from './modal/ConfirmViewChangePopup';
import { AclPermission } from '@models/AclPermission';
import { PermissionAction } from '@common/constants/PermissionAction';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import { ConfigItemTypeAttrModel } from '@models/ConfigItemTypeAttr';
import AddColumnListCiComponent from '@components/commonCi/AddColumnListCiComponent';
import { LocalStorageKeyEnum } from '@common/constants/LocalStorageKeyEnum';
import { MAX_NUMBER_LENGTH, MAX_TEXT_LENGTH } from '@common/constants/FieldLengthConstants';

type CiManagementComponentProps = {
  screenType: ScreenTypeManagement;
};

const titles = {
  [ScreenTypeManagement.DRAFT]: 'List of CIs that have not been sent for approval',
  [ScreenTypeManagement.SENT]: 'List of CIs has been sent for approval',
  [ScreenTypeManagement.WAITING]: 'List of CIs waiting for approval',
  [ScreenTypeManagement.APPROVED]: 'List of approved CIs',
  [ScreenTypeManagement.REJECTED]: 'List of CIs that have been denied approval',
};

const screenTypeForFinalStatus = [ScreenTypeManagement.APPROVED, ScreenTypeManagement.REJECTED];
export const CiManagementComponent = (props: CiManagementComponentProps) => {
  const { screenType } = props;
  const navigate = useNavigate();
  const [ciIdDel, setCiIdDel] = useState<number>(0);
  const [listCI, setListCI] = useState<CiManagementResponse[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [isOpenDeleteItem, { close: closeDeleteItem, open: openDeleteItem }] = useDisclosure(false);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType<CiManagementResponse>>();
  const [openedModalEdit, { close: closeModalEdit, open: openModalEdit }] = useDisclosure(false);
  const [openedModalAfterUpdate, { close: closeModalAfterUpdate, open: openModalAfterUpdate }] = useDisclosure(false);
  const [isValidUpdate, setIsValidUpdate] = useState(false);
  const [currentCiSelected, setCurrentCiSelected] = useState<CiManagementResponse[]>([]);
  const [currentCiSelectedSubmit, setCurrentCiSelectedSubmit] = useState<CiManagementResponse[]>([]);
  const [ciSelected, setCiSelected] = useState<CiManagementResponse>();
  const [actionType, setActionType] = useState<ActionType>(ActionType.SEND);
  const [idUpdate, setIdUpdate] = useState<number | undefined>();
  // const ciHasPermissions = useSelector(getCiHasPermissions)?.ciHasPermissions;
  // const dispatch = useDispatch();
  const currentUser = useSelector(getCurrentUser);
  const isSuperAdmin = currentUser.data?.isSuperAdmin === true;
  const ciRequestDetailRef = useRef<CiRequestDetailPopupMethods | null>(null);

  const [isLoadingTable, setIsLoadingTable] = useState(false);
  const [dataAttributeSelected, setDataAttributeSelected] = useState<ConfigItemTypeAttrModel[]>([]);
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);

  const fetchCis = useCallback(
    (controller?: AbortController) => {
      if (!tableAffected) {
        return;
      }
      setIsLoadingTable(true);
      const tableAffectedClone = { ...tableAffected };
      if (!tableAffectedClone.sortedBy) {
        tableAffectedClone.sortedBy = 'modifiedDate';
        tableAffectedClone.isReverse = true;
      }
      const dataSend = tableAffectedToMultiColumnFilterPaginationRequestModel<CiManagementResponse>(tableAffectedClone);
      const ciTypeAttributeIds: number[] = Array.from(
        new Set(
          dataAttributeSelected
            .filter((item) => item.tempId)
            .flatMap((item) => item.tempId?.split(','))
            .map((str) => Number(str?.trim()))
            .filter((num) => !isNaN(num)),
        ),
      );
      CiManagementApi.getAllCisManagementByScreen(screenType || ScreenTypeManagement.DRAFT, dataSend, controller, ciTypeAttributeIds)
        .then((res) => {
          setTotalRecords(res.data.totalElements);

          const updatedList = (res.data.content || []).map((item) => {
            return { ...item, dataParse: JSON.parse(item.data) };
          });

          // const deleteCiTypePermissions: AclPermission[][] = updatedList.map((item) => [
          //   AclPermission.createCiTypePermission(PermissionAction.CI_DRAFT__DELETE, item.ciTypeId),
          // ]);
          // const hasDeletePermissions = checkAllCiPermissions(deleteCiTypePermissions);

          // dispatch(ciHasPermissionsSlice.actions.updateCiHasPermissions(hasDeletePermissions));

          setListCI(updatedList);
        })
        .catch(() => {})
        .finally(() => {
          setIsLoadingTable(false);
        });
    },
    [dataAttributeSelected, screenType, tableAffected],
  );

  useEffect(() => {
    const controller = new AbortController();
    fetchCis(controller);
    return () => controller.abort();
  }, [fetchCis]);

  const onOpenModalReview = (action: ActionType) => {
    setActionType(action);
    const cloneSelectCis = [...currentCiSelected];
    setCurrentCiSelectedSubmit(cloneSelectCis);
    ciRequestDetailRef.current?.openPopupReview();
  };

  const onCloseModalReview = (action: ActionType) => {
    fetchCis();
    setCurrentCiSelected([]);
    setCurrentCiSelectedSubmit([]);
    if (ActionType.SEND === action) {
      openModalConfirmViewChange(action);
    }
  };

  const openModalConfirmViewChange = (action: ActionType) => {
    childRefConfirmViewChange.current?.openModalConfirm(action);
  };

  const onOpenModalReviewAfterUpdate = () => {
    setActionType(ActionType.SEND);
    if (screenType === ScreenTypeManagement.DRAFT) {
      const dataUpdate = listCI.find((x) => x.id === idUpdate);
      if (dataUpdate) {
        setCurrentCiSelectedSubmit([dataUpdate]);
        ciRequestDetailRef.current?.openPopupReview();
      }
    } else {
      CiManagementApi.getById(Number(idUpdate))
        .then((res) => {
          if (res.data) {
            setCurrentCiSelectedSubmit([res.data]);
            ciRequestDetailRef.current?.openPopupReview();
          }
        })
        .catch(() => {});
    }
  };

  const deleteCi = useCallback(
    (id: number) => {
      CiManagementApi.deleteById(id)
        .then(() => {
          NotificationSuccess({
            message: 'Deleted successfully',
          });
          fetchCis();
          closeDeleteItem();
        })
        .catch(() => {})
        .finally(() => {});
    },
    [closeDeleteItem, fetchCis],
  );

  const deleteCis = useCallback(
    (ids: number[]) => {
      CiManagementApi.deleteByIds(ids)
        .then(() => {
          NotificationSuccess({
            message: 'Deleted successfully',
          });
          fetchCis();
        })
        .catch(() => {})
        .finally(() => {});
    },
    [fetchCis],
  );

  const allCiTypes = useGetCiTypes();

  const [updateCiInfo, setUpdateCiInfo] = useState<
    | {
        ciTypeId: number;
        ciId: number;
        readOnly: boolean;
        initData: ConfigItemInfoModel | undefined;
      }
    | undefined
  >();
  const editCiClick = useCallback(
    (dataEdit: CiManagementResponse, readOnly: boolean) => {
      if (dataEdit.dataParse) {
        openModalEdit();
        const selectedData = dataEdit.dataParse;
        selectedData.tempId = dataEdit.id;
        setUpdateCiInfo({
          ciId: 0,
          ciTypeId: dataEdit.ciTypeId,
          readOnly,
          initData: selectedData,
        });
      }
    },
    [openModalEdit],
  );

  const childRefViewDetail = useRef<CiManagementDetailViewPopupMethods | null>(null);
  const childRefConfirmViewChange = useRef<ConfirmViewChangePopupMethods | null>(null);

  const viewDetailData = (data: CiManagementResponse) => {
    setCiSelected(data);
    childRefViewDetail.current?.openPopupView();
  };

  const childRef = useRef<CiUpdateSoleMethods | null>(null);

  const onUpdateCI = () => {
    childRef.current?.update().then((res) => {
      if (res) {
        fetchCis();
        closeModalEdit();
        openModalAfterUpdate();
      }
    });
  };

  const columns: ColumnType<CiManagementResponse>[] = useMemo(() => {
    return [
      {
        title: 'Action',
        name: 'action',
        width: '5%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'SysId',
        name: 'sysId',
        hidden: true,
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Id',
        name: 'id',
        hidden: true,
        advancedFilter: {
          variant: 'number',
          filterModes: ['equals', 'notEquals', 'greaterThan', 'greaterThanOrEqualTo', 'lessThan', 'lessThanOrEqualTo'],
          customProps: { maxLength: MAX_NUMBER_LENGTH },
        },
      },
      {
        title: 'CI Name',
        name: 'ciTempName',
        width: '10%',
        sortable: false,
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (_, row) => {
          return <KanbanText>{row.ciName || row.dataParse?.ci?.name}</KanbanText>;
        },
      },
      {
        title: 'CI ID',
        name: 'ciId',
        advancedFilter: {
          variant: 'number',
          filterModes: ['equals', 'notEquals', 'greaterThan', 'greaterThanOrEqualTo', 'lessThan', 'lessThanOrEqualTo'],
          customProps: { maxLength: MAX_NUMBER_LENGTH },
        },
        customRender: (_data, row) => {
          const ciId = row.dataParse?.ci?.id || row.ciId;
          return <KanbanText lineClamp={2}>{ciId || ''}</KanbanText>;
        },
        width: '5%',
      },
      {
        title: 'Ci Type',
        name: 'ciTypeName',
        sortable: false,
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (_data: any, row) => {
          const ciType = allCiTypes.data.find((x) => x.id === row.dataParse?.ci?.ciTypeId);
          if (!ciType) {
            return <></>;
          }

          const ciName = ciType.name || '';
          return (
            <Tooltip label={ciName} multiline w={350} style={{ wordBreak: 'break-word' }}>
              <KanbanButton
                size='compact-xs'
                radius={'lg'}
                maw={'200px'}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  navigate(buildCiTypeUrl(ciType.id));
                }}>
                {ciName}
              </KanbanButton>
            </Tooltip>
          );
        },
      },
      {
        title: 'Approver User',
        name: 'verifyUser',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data) => {
          return <KanbanText lineClamp={2}>{data || '---'}</KanbanText>;
        },
        width: '8%',
      },
      {
        title: 'Status',
        name: 'status',
        sortable: false,
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data: StatusCiManagement) => {
          return <StatusComponent value={data} />;
        },
        width: '6%',
      },
      {
        title: 'Description',
        name: 'description',
        sortable: false,
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data) => {
          return (
            <Tooltip label={data || ''} multiline w={350} style={{ wordBreak: 'break-word' }}>
              <KanbanText lineClamp={2} style={{ wordBreak: 'break-word' }}>
                {data || ''}
              </KanbanText>
            </Tooltip>
          );
        },
      },
      {
        title: 'Approval comments',
        name: 'approvalComment',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data) => {
          return (
            <Tooltip label={data || ''} multiline w={350} style={{ wordBreak: 'break-word' }}>
              <KanbanText lineClamp={2} style={{ wordBreak: 'break-word' }}>
                {data || ''}
              </KanbanText>
            </Tooltip>
          );
        },
      },
      {
        title: 'Created by',
        name: 'createdBy',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        width: '5%',
      },
      {
        title: 'Created date',
        name: 'createdDate',
        hidden: true,
        customRender: renderDateTime,
        advancedFilter: {
          variant: 'date',
          customProps: {
            popoverProps: {
              withinPortal: false,
            },
          },
        },
        width: '8%',
      },
      {
        title: 'Modified date',
        name: 'modifiedDate',
        customRender: renderDateTime,
        advancedFilter: {
          variant: 'date',
          customProps: {
            popoverProps: {
              withinPortal: false,
            },
          },
        },
        width: '8%',
      },
      {
        title: 'Modified by',
        name: 'modifiedBy',
        hidden: true,
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        width: '5%',
      },
    ];
  }, [allCiTypes.data, navigate]);

  const shouldDisplayEditDraftButton = useCallback(
    (data: CiManagementResponse) => {
      if (isSuperAdmin) {
        return true;
      }
      const hasEditPermission = isCurrentUserMatchPermissions([
        AclPermission.createCiTypePermission(PermissionAction.CI_DRAFT__UPDATE, data.ciTypeId),
      ]);

      return ScreenTypeManagement.WAITING !== screenType && currentUser.data?.username === data.owner && hasEditPermission;
    },
    [currentUser.data?.username, isSuperAdmin, screenType],
  );

  const shouldDisplayDeleteDraftButton = useCallback(
    (data: CiManagementResponse) => {
      if (isSuperAdmin) {
        return true;
      }
      if (ScreenTypeManagement.APPROVED !== screenType) {
        const hasDeletePermission = isCurrentUserMatchPermissions([
          AclPermission.createCiTypePermission(PermissionAction.CI_DRAFT__DELETE, data.ciTypeId),
        ]);

        return hasDeletePermission && currentUser.data?.username === data.owner;
      }
      return false;
    },
    [currentUser.data?.username, isSuperAdmin, screenType],
  );

  const isHasViewCiDraftPermission = (data: CiManagementResponse) => {
    const ciId = data.ciId;
    const ciTypeId = data.ciTypeId;

    const viewBasicCiPermissions = AclPermission.createViewCiPermissions(ciId, ciTypeId);
    const viewAdvancedCiPermissions = AclPermission.createCiPermissions(PermissionAction.CI__VIEW_ADVANCED, ciId, ciTypeId);

    // const createCiPermission = new AclPermission(PermissionActionType.CI_TYPE, PermissionAction.CI__CREATE, ciTypeId);
    // const updatePermissions = AclPermission.createCiPermissions(PermissionAction.CI__UPDATE, ciId, ciTypeId);
    // const basePermissions = [createCiPermission, ...updatePermissions];
    // const viewInfoCiPermissions = AclPermission.createViewCiPermissions(ciId, ciTypeId);
    // viewInfoCiPermissions.push(...basePermissions);
    return isCurrentUserMatchPermissions([...viewBasicCiPermissions, ...viewAdvancedCiPermissions]);
  };

  const updateDataAttributeSelected = (datas: ConfigItemTypeAttrModel[]) => {
    setDataAttributeSelected(datas);
  };

  useEffect(() => {
    const data = localStorage.getItem(LocalStorageKeyEnum.ATTRIBUTE_LIST_CI_MANAGEMENT);
    if (data) {
      try {
        const parsedData = JSON.parse(data);
        setDataAttributeSelected(parsedData);
      } catch (error) {
        setDataAttributeSelected([]);
      }
    } else {
      setDataAttributeSelected([]);
    }
  }, []);

  const updatedColumns = useMemo(() => {
    const newColumns = columns.filter((col) => {
      if (ScreenTypeManagement.WAITING === screenType && 'verifyUser' === col.name) {
        return false;
      }

      if (!screenTypeForFinalStatus.includes(screenType) && 'approvalComment' === col.name) {
        return false;
      }

      return true;
    });

    const insertIndex = newColumns.length - 1;
    const newArrayColumns = dataAttributeSelected.map((obj) => ({
      title: obj.name,
      name: `${obj.hashId}`,
      sortable: false,
      advancedFilter: {
        enable: false,
      },
      customRenderHeader: () => (
        <>
          <KanbanTooltip label={obj.ciTypeId === 0 ? '(Default Attribute)' : obj.ciTypeName || ''}>
            <KanbanText truncate='end' fw={700} maw={'200px'}>
              {obj.name}
            </KanbanText>
          </KanbanTooltip>
        </>
      ),
      customRender: (_data: any, rowData: CiManagementResponse) => {
        const ciTypeAttributeIdList = obj.tempId ? obj.tempId.split(',').map((str) => Number(str.trim())) : [];
        const matchedAttribute = rowData?.ciAttributes?.find((item) => ciTypeAttributeIdList.includes(item.ciAttributeId));
        const valueView = matchedAttribute?.ciAttributeValue || '';
        return (
          <KanbanTooltip label={valueView} multiline w={350}>
            <KanbanText lineClamp={2}>{valueView}</KanbanText>
          </KanbanTooltip>
        );
      },
    }));

    newColumns.splice(insertIndex, 0, ...newArrayColumns);

    return newColumns;
  }, [columns, dataAttributeSelected, screenType]);

  const tableProps: KanbanTableProps<CiManagementResponse> = useMemo(() => {
    return {
      title: titles[screenType] || 'List CIs',
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 900,
      },
      sortable: {
        enable: true,
      },
      advancedFilterable: {
        enable: true,
        debounceTime: 1000,
        resetOnClose: true,
        compactMode: true,
      },
      columns: updatedColumns,
      data: listCI,

      pagination: {
        enable: true,
      },
      onRowClicked: (data) => {
        const isShowInfoCi = isHasViewCiDraftPermission(data);
        if (isShowInfoCi) {
          navigate(buildCiManageDetailUrl(data.id));
        }
      },
      serverside: {
        totalRows: totalRecords,
        onTableAffected(dataSet) {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
      actions: {
        // deletable: isCurrentUserMatchPermissions([AclPermission.deleteCiDraft])
        //   ? {
        //       onDeleted(data) {
        //         deleteCi(data.id);
        //       },
        //     }
        //   : undefined,
        customAction: (data: CiManagementResponse) => {
          return (
            <>
              {isHasViewCiDraftPermission(data) && (
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    viewDetailData(data);
                  }}>
                  <IconEye />
                </KanbanIconButton>
              )}
              {shouldDisplayEditDraftButton(data) && (
                <KanbanIconButton
                  variant='transparent'
                  size='sm'
                  onClick={() => {
                    editCiClick(data, false);
                  }}>
                  <IconEdit />
                </KanbanIconButton>
              )}
              {shouldDisplayDeleteDraftButton(data) && (
                <KanbanIconButton
                  color='red'
                  size={'sm'}
                  onClick={() => {
                    setCiIdDel(data.id);
                    openDeleteItem();
                  }}>
                  <IconTrash />
                </KanbanIconButton>
              )}
            </>
          );
        },
      },
      customAction: () => (
        <>
          <AddColumnListCiComponent
            dataAttributeSelected={dataAttributeSelected}
            updateDataAttributeSelected={updateDataAttributeSelected}
            localStorageKey={LocalStorageKeyEnum.ATTRIBUTE_LIST_CI_MANAGEMENT}
          />
        </>
      ),
      selectableRows: {
        enable: true,
        onDeleted: (rows) => {
          deleteCis(rows.map((x) => x.id));
        },

        crossPageSelected: {
          rowKey: 'id',
          selectedRows: currentCiSelected,
          setSelectedRows: setCurrentCiSelected,
        },
      },
    };
  }, [
    screenType,
    updatedColumns,
    listCI,
    totalRecords,
    currentCiSelected,
    navigate,
    tableAffected,
    shouldDisplayEditDraftButton,
    shouldDisplayDeleteDraftButton,
    editCiClick,
    openDeleteItem,
    dataAttributeSelected,
    deleteCis,
  ]);

  const customTableProps = useMemo(() => {
    const tablePropsUpdate = { ...tableProps };
    if (![ScreenTypeManagement.DRAFT, ScreenTypeManagement.SENT].includes(screenType)) {
      const { actions, selectableRows, ...rest } = tablePropsUpdate;
      // if (selectableRows && selectableRows.onDeleted) {
      //   delete selectableRows.onDeleted;
      // }
      // if (actions && actions.deletable) {
      //   delete actions.deletable;
      // }
      if (screenTypeForFinalStatus.includes(screenType) && selectableRows) {
        selectableRows.enable = false;
      }
      return { ...rest, selectableRows, actions };
    }

    return tablePropsUpdate;
  }, [tableProps, screenType]);

  useEffect(() => {
    tableRef.current?.columnPropsChangesNotify({ resetHiddenColumnByTheNew: true, resetAdvancedFilterMapping: true });
  }, [updatedColumns]);

  return (
    <>
      <KanbanConfirmModal
        opened={isOpenDeleteItem}
        onClose={closeDeleteItem}
        title='Confirm delete'
        onConfirm={() => {
          deleteCi(ciIdDel);
        }}>
        Are you sure to delete this item?
      </KanbanConfirmModal>
      <KanbanConfirmModal
        title={!updateCiInfo?.readOnly ? 'Update CI' : 'CI Detail'}
        onConfirm={!updateCiInfo?.readOnly ? onUpdateCI : undefined}
        textConfirm='Update'
        onClose={closeModalEdit}
        opened={openedModalEdit}
        disabledConfirmButton={!isValidUpdate}
        modalProps={{
          size: '60%',
        }}>
        {updateCiInfo && (
          <CiUpdateSole
            setIsValidUpdate={setIsValidUpdate}
            readOnly={updateCiInfo.readOnly}
            ciId={updateCiInfo.ciId}
            ciTypeId={updateCiInfo.ciTypeId}
            initData={updateCiInfo.initData}
            onAfterUpdate={setIdUpdate}
            forwardedRef={childRef}
          />
        )}
      </KanbanConfirmModal>

      {/* modal view detail list request for action */}
      <CiRequestDetailPopup
        ref={ciRequestDetailRef}
        listData={currentCiSelectedSubmit}
        screenAction={actionType}
        onCloseModal={onCloseModalReview}
        openModalConfirmViewChange={openModalConfirmViewChange}
      />

      {/* modal view detail info of CI management draft */}
      <CiManagementDetailViewPopup ref={childRefViewDetail} initData={ciSelected} />

      {/* modal confirm view data change after update action */}
      <ConfirmViewChangePopup ref={childRefConfirmViewChange} />

      <KanbanConfirmModal
        title={'Updated successfully'}
        onConfirm={() => {
          closeModalAfterUpdate();
          onOpenModalReviewAfterUpdate();
        }}
        textConfirm=''
        onClose={closeModalAfterUpdate}
        opened={openedModalAfterUpdate}
        modalProps={{
          size: 'lg',
        }}>
        {`The CI record has been modified but hasn't been approved yet. Do you want to submit this record for review?`}
      </KanbanConfirmModal>

      <Group justify='flex-end' mb={20}>
        {screenType === ScreenTypeManagement.DRAFT && (
          <KanbanButton
            onClick={() => {
              onOpenModalReview(ActionType.SEND);
            }}
            disabled={!currentCiSelected.length}>
            Send request for approval
          </KanbanButton>
        )}
        {screenType === ScreenTypeManagement.WAITING && (
          <KanbanButton
            onClick={() => {
              onOpenModalReview(ActionType.APPROVE);
            }}
            disabled={!currentCiSelected.length}>
            Approvals
          </KanbanButton>
        )}
      </Group>
      <KanbanTable ref={tableRef} isLoading={isLoadingTable} {...customTableProps} />
    </>
  );
};
CiManagementComponent.whyDidYouRender = true;
CiManagementComponent.displayName = 'CiManagementComponent';
export default CiManagementComponent;
