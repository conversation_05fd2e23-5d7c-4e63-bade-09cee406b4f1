import { ciIdentifierRulePath, dataSourcePath } from '@common/utils/RouterUtils';
import { AclPermission } from '@models/AclPermission';
import { IconAlignBoxLeftStretch, IconFilter, IconMail } from '@tabler/icons-react';
import { IconAffiliate, IconAutomaticGearbox, IconSettings, IconUser, type TablerIconsProps } from '@tabler/icons-react';

export type SelectionItem = {
  title: string;
  path: string;
  requirePermissions: AclPermission[];
};

export type SelectionConfigType = {
  icon: {
    icon: React.ComponentType<TablerIconsProps>;
    props?: TablerIconsProps;
  };
  title: string;
  requirePermissions?: AclPermission[];
  selections: SelectionItem[];
};

export const SelectionConfigs: SelectionConfigType[] = [
  {
    icon: {
      icon: IconSettings,
    },
    title: 'General Settings',
    requirePermissions: [AclPermission.manageSystem],
    selections: [
      {
        title: 'Security settings',
        path: '',
        requirePermissions: [],
      },

      {
        title: 'Theme',
        path: '',
        requirePermissions: [],
      },
      {
        title: 'API',
        path: '',
        requirePermissions: [],
      },
      {
        title: 'WebHook',
        path: '',
        requirePermissions: [],
      },
    ],
  },
  {
    icon: {
      icon: IconUser,
    },
    title: 'Users',
    requirePermissions: [AclPermission.viewListUser, AclPermission.viewListGroupUser, AclPermission.viewListRole],
    selections: [
      {
        title: 'Users',
        path: 'users',
        requirePermissions: [AclPermission.viewListUser],
      },
      {
        title: 'Groups',
        path: 'groups',
        requirePermissions: [AclPermission.viewListGroupUser],
      },
      {
        title: 'Roles',
        path: 'roles',
        requirePermissions: [AclPermission.viewListRole],
      },
    ],
  },
  {
    icon: {
      icon: IconAffiliate,
    },
    title: 'Asset Management',
    requirePermissions: [AclPermission.viewListCiType, ...AclPermission.actionOfRelationshipTypePermissions],
    selections: [
      {
        title: 'Configuration Item Types',
        path: 'ci-types',
        requirePermissions: [AclPermission.viewListCiType],
      },

      {
        title: 'Relationship Types',
        path: 'relationship-types',
        requirePermissions: AclPermission.actionOfRelationshipTypePermissions,
      },
      {
        title: 'Product Type',
        path: '',
        requirePermissions: [],
      },
      {
        title: 'CI Rules',
        path: ciIdentifierRulePath,
        requirePermissions: [],
      },
    ],
  },
  {
    icon: {
      icon: IconAutomaticGearbox,
    },
    title: 'Auto Discovery',
    selections: [
      {
        title: 'Discovery Source',
        path: dataSourcePath,
        requirePermissions: [],
      },

      // {
      //   title: 'Auto Discovery Settings',
      //   path: '',
      //   requirePermissions: [],
      // },
    ],
  },
  {
    icon: {
      icon: IconAutomaticGearbox,
    },
    title: 'Superiors',
    selections: [
      {
        title: 'Sql Execution',
        path: '',
        requirePermissions: [AclPermission.manageSystem],
      },
    ],
  },
  {
    icon: {
      icon: IconAlignBoxLeftStretch,
    },
    title: 'Systems',
    selections: [
      {
        title: 'Systems Parameter',
        path: 'parameter',
        requirePermissions: [AclPermission.manageSystem],
      },
    ],
  },
  {
    icon: {
      icon: IconMail,
    },
    title: 'Mail Server Settings',
    requirePermissions: [],
    selections: [
      {
        title: 'Outgoing mail config',
        path: 'outgoing-mail-configs',
        requirePermissions: [AclPermission.viewOutGoingMailConfig],
      },
      {
        title: 'Notification rules management',
        path: 'notification-templates',
        requirePermissions: [AclPermission.viewListNotificationTemplate],
      },
    ],
  },
  {
    icon: {
      icon: IconFilter,
    },
    title: 'Impact Managements',
    requirePermissions: [],
    selections: [
      {
        title: 'Impacted rules',
        path: 'impacted-rules',
        requirePermissions: [AclPermission.viewListImpactedRule],
      },
    ],
  },
];
