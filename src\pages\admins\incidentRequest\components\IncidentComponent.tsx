import { Box, Flex } from '@mantine/core';
import React, { useCallback, useEffect, useState } from 'react';
import { KanbanText } from 'kanban-design-system';
import { ChangeDetailComponent } from './ChangeDetailComponent';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { KanbanNumberInput } from 'kanban-design-system';
import { ChangeAssessmentAction } from '@models/ChangeAssessment';
import { IncidentSdpApi } from '@api/IncidentSdpApi';
import type { IncidentRequestResponseDto } from '@models/IncidentSdp';

const CustomLink = styled(Link)`
  text-decoration: none;
  color: blue;

  &:hover {
    text-decoration: underline;
  }
`;

const FieldRequired = styled.span`
  color: red;
`;

export type IncidentComponentProps = {
  changeId: number | undefined;
  setChangeId: (val: number | undefined) => void;
  setAllowViewImpactData: (val: boolean) => void;
  action: ChangeAssessmentAction;
};

export const defaultIncidentResponse: IncidentRequestResponseDto = {
  requestId: 0,
  status: '',
  requester: '',
  subject: '',
  incidentSdpLink: '',
  responseStatusCode: '',
};

export const IncidentComponent: React.FC<IncidentComponentProps> = ({ action, changeId, setAllowViewImpactData, setChangeId }) => {
  const [changeData, setChangeData] = useState<IncidentRequestResponseDto>(defaultIncidentResponse);
  const [showData, setShowData] = useState(false);
  const [valueInput, setValueInput] = useState<string | number>(changeId || '');

  const resetDataPage = useCallback(() => {
    setShowData(false);
    setChangeData(defaultIncidentResponse);
  }, []);

  useEffect(() => {
    setChangeId(valueInput ? Number(valueInput) : undefined);
  }, [setChangeId, valueInput]);

  useEffect(() => {
    if (ChangeAssessmentAction.CREATE === action) {
      setAllowViewImpactData(false);
    }
  }, [action, setAllowViewImpactData]);

  const getChangeData = useCallback(() => {
    IncidentSdpApi.getIncidentRequestById(Number(valueInput), action)
      .then((res) => {
        const resData = res.data;
        setChangeData(resData);
        setShowData(true);
        setAllowViewImpactData(true);
      })
      .catch(() => {
        resetDataPage();
      });
  }, [valueInput, action, setAllowViewImpactData, resetDataPage]);

  useEffect(() => {
    if (valueInput) {
      getChangeData();
    }
  }, [getChangeData, valueInput]);

  useEffect(() => {
    if (changeId) {
      setValueInput(changeId);
    }
  }, [changeId]);

  return (
    <Box mt='lg'>
      <Flex direction='column' gap='sm'>
        <Flex gap='xl'>
          <KanbanText fw={500} w={'10%'}>
            SDP Incident ID <FieldRequired>*</FieldRequired>
          </KanbanText>
          <Box w={'100%'}>
            <Flex gap='sm'>
              <KanbanNumberInput
                placeholder='Incident ID'
                w={'40%'}
                value={valueInput}
                allowNegative={false}
                allowDecimal={false}
                disabled
                onChange={(e) => {
                  setValueInput(e);
                }}
              />
            </Flex>
          </Box>
        </Flex>
        {showData ? (
          <>
            <Flex gap='xl'>
              <KanbanText w={'10%'} fw={500}>
                SDP Incident URL:
              </KanbanText>

              <CustomLink target={'_blank'} to={changeData.incidentSdpLink}>
                {changeData.incidentSdpLink}
              </CustomLink>
            </Flex>
            <ChangeDetailComponent data={changeData} />
          </>
        ) : (
          <KanbanText c={'red'}>Incident Id not found on SDP server. Please verify and try again.</KanbanText>
        )}
      </Flex>
    </Box>
  );
};
