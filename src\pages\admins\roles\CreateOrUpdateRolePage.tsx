import { PermissionActionType } from '@common/constants/PermissionActionType';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import ViewListPermission from './components/ViewListPermission';
import styled from 'styled-components';
import type { RoleModel } from '@models/Role';
import { Box, Flex, Space } from '@mantine/core';
import GroupSettingsPermissionModal from '../groups/permissions/GroupSettingsPermissionModal';
import { adminPermissionDescriptions, applicationPermissionDescriptions } from '@common/constants/PermissionAction';
import type { ConfigItemPermissionOtherDto, ConfigItemTypePermissionModel } from '@models/ConfigItemTypePermission';
import { KanbanButton, KanbanInput, KanbanTitle, KanbanTabs, KanbanText, KanbanModal } from 'kanban-design-system';
import { useParams, useSearchParams } from 'react-router-dom';
import { buildCreateOrUpdateRoleUrl, createOrUpdateRolePath, isMatchPath, navigateTo, roleViewListPath } from '@common/utils/RouterUtils';
import PermissionServiceExecute from '@service/PermissionServiceExecute';
import { SysPermissionCiApi, type SysPermissionCiResponse } from '@api/SysPermissionCisApi';
import { RolesApi } from '@api/systems/RolesApi';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { SysPermissionOthersApi } from '@api/SysPermissionOthersApi';
import { formatStandardName } from '@common/utils/StringUtils';
import { GroupsApi, type SysPermissionCiModel, type UserSettingGroupModel } from '@api/systems/GroupsApi';
import SettingGroupIntoRoleContent, { SettingGroupIntoRoleContentMethod } from './components/SettingGroupIntoRoleContent';
import { useDisclosure } from '@mantine/hooks';
import { IconFriends } from '@tabler/icons-react';
import { useSelector } from 'react-redux';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { RoleAction } from '@common/constants/RoleActionEnum';
import { BreadcrumbComponent, UrlBaseCrumbData } from '../breadcrumb/BreadcrumbComponent';

export const StyledBox = styled(Box)`
  border: 1px solid var(--mantine-color-gray-3);
  padding: var(--mantine-spacing-md);
`;
export const basicInfoRoleDefault: RoleModel = {
  id: 0,
  name: '',
  description: '',
};
const convertToConfigItemTypePermission = (sysPermissionCis: SysPermissionCiModel[]): ConfigItemTypePermissionModel[] => {
  const ciPermissionModels: ConfigItemTypePermissionModel[] = [];

  sysPermissionCis.forEach((sysPermissionCi) => {
    const id = PermissionActionType.CI === sysPermissionCi.type ? sysPermissionCi.ciId : sysPermissionCi.ciTypeId;
    const parentId = PermissionActionType.CI === sysPermissionCi.type ? sysPermissionCi.ciTypeId : undefined;
    if (id === undefined || id === null) {
      return;
    }

    let ciPermissionModel = ciPermissionModels.find((pc) => pc.id === id);

    if (ciPermissionModel) {
      ciPermissionModel.action = ciPermissionModel.action
        ? [...ciPermissionModel.action, sysPermissionCi.action ?? '']
        : [sysPermissionCi.action ?? ''];
    } else {
      ciPermissionModel = {
        id,
        parentId,
        name: sysPermissionCi.name,
        description: sysPermissionCi.description,
        action: [sysPermissionCi.action ?? ''],
        type: sysPermissionCi.type,
      };
      ciPermissionModels.push(ciPermissionModel);
    }
  });

  return ciPermissionModels;
};

const roleClonePrefix: string = '_copy';
export const CreateOrUpdateRolePage = () => {
  // CMDB-4143: clone role
  const [searchParams] = useSearchParams();
  const actionParam = searchParams.get('action');
  const action = Object.values(RoleAction).includes(actionParam as RoleAction) ? (actionParam as RoleAction) : RoleAction.VIEW;
  const isCopyAction = RoleAction.COPY === action;
  // ---------------------------------
  const { id } = useParams();
  const roleId = Number(id);
  const [activeTab, setActiveTab] = useState<PermissionActionType>(PermissionActionType.ADMIN);
  const [basicInfoRole, setBasicInfoRole] = useState<RoleModel>(basicInfoRoleDefault);
  const [adminPermissions, setAdminPermissions] = useState<ConfigItemPermissionOtherDto[]>(adminPermissionDescriptions);
  const [applicationPermissions, setApplicationPermissions] = useState<ConfigItemPermissionOtherDto[]>(applicationPermissionDescriptions);
  const titlePage = isCopyAction ? 'Copy role' : roleId > 0 ? 'Update role' : 'Create new role';
  const [ciPermissions, setCiPermissions] = useState<ConfigItemTypePermissionModel[]>([]);
  const [sysPermissionCiList, setSysPermissionCiList] = useState<SysPermissionCiResponse[]>([]);
  const [renderOnlyOne, setRenderOnlyOne] = useState<boolean>(true);
  const isUpdatePath = isMatchPath(window.location.pathname, createOrUpdateRolePath);
  // Update choose group

  const [openedModalGroupInRole, { close: closeModalGroupInRole, open: openModalGroupInRole }] = useDisclosure(false);
  const [isFetched, setIsFetched] = useState<boolean>(false);
  const mountData = (value: boolean) => {
    setIsFetched(value);
  };
  const childRef = useRef<SettingGroupIntoRoleContentMethod>(null);
  const [selectedItems, setSelectedItems] = useState<UserSettingGroupModel[]>([]); // group to choose from list
  // const [chooseGroups, setChooseGroups] = useState<UserSettingGroupModel[]>([]); // Group after confirm
  // Update comment view Group of Role : 1/11/2024
  const [nameGroupOfRoles, setNameGroupOfRoles] = useState<string[]>([]);
  const currentUser = useSelector(getCurrentUser);
  const isSuperAdmin = currentUser.data?.isSuperAdmin === true;
  // const [totalUserSelectDataEmpty, setTotalUserSelectDataEmpty] = useState<number>(0);

  const [selectedItemCheckeds, setSelectedItemCheckeds] = useState<UserSettingGroupModel[]>([]);

  const findAllGroupActivesInRoleToView = useCallback((roleId: number) => {
    GroupsApi.findAllGroupActivesInRoleToView(roleId)
      .then((response) => {
        const data = response.data;

        setSelectedItemCheckeds(data);
      })
      .catch(() => {});
  }, []);

  const updateCheckedListByUserAction = (
    initialList: UserSettingGroupModel[],
    addedItems: UserSettingGroupModel[],
    removedItems: UserSettingGroupModel[],
  ) => {
    let updatedList = [...initialList];

    addedItems.forEach((item) => {
      if (!updatedList.some((existingItem) => existingItem.id === item.id)) {
        updatedList.push(item);
      }
    });

    updatedList = updatedList.filter((item) => !removedItems.some((removedItem) => removedItem.id === item.id));
    setSelectedItemCheckeds(updatedList);
  };

  const onUpdateSelected = (datas: UserSettingGroupModel[]) => {
    setSelectedItems(datas);
  };
  //
  const updateAdminPermissions = (adminPermissions: ConfigItemPermissionOtherDto[]) => {
    setAdminPermissions(adminPermissions);
  };

  const updateApplicationPermissions = (applicationPermissions: ConfigItemPermissionOtherDto[]) => {
    setApplicationPermissions(applicationPermissions);
  };

  const onSetBasicInfoRole = (e: React.ChangeEvent<HTMLInputElement>, name: string) => {
    const { target } = e;
    setBasicInfoRole((prev) => {
      return {
        ...prev,
        [name]: target.value,
      };
    });
  };

  const handleUpdateCiPermissions = useCallback((ciPermissions: ConfigItemTypePermissionModel[]) => {
    setCiPermissions(ciPermissions);
  }, []);

  const setCallApiOnlyOne = useCallback((value: boolean) => {
    setRenderOnlyOne(value);
  }, []);
  const fetchAllPermissionOthersByRoleId = useCallback(() => {
    SysPermissionOthersApi.getAllPermissionOtherByRoleId(roleId)
      .then((response) => {
        if (response.status === 200) {
          const updatedAdminPermissionOthers = adminPermissionDescriptions.map((permission) => ({ ...permission }));
          const updatedApplicationPermissionOthers = applicationPermissionDescriptions.map((permission) => ({ ...permission }));
          const adminPermissionMap = new Map(updatedAdminPermissionOthers.map((permission) => [permission.action, permission]));
          const applicationPermissionMap = new Map(updatedApplicationPermissionOthers.map((permission) => [permission.action, permission]));
          for (const permissionOther of response.data) {
            if (PermissionActionType.ADMIN === permissionOther.type) {
              const adminPermission = adminPermissionMap.get(permissionOther.action);
              if (adminPermission) {
                adminPermission.isChecked = true;
              }
            } else if (PermissionActionType.APPLICATIONS === permissionOther.type) {
              const applicationPermission = applicationPermissionMap.get(permissionOther.action);
              if (applicationPermission) {
                applicationPermission.isChecked = true;
              }
            }
          }

          // Cập nhật state
          setAdminPermissions([...adminPermissionMap.values()]);
          setApplicationPermissions([...applicationPermissionMap.values()]);
        }
      })
      .catch((error) => {
        console.error('Error fetching permissions:', error);
      });
  }, [roleId]);

  const fetchAllPermissionCisByRoleId = useCallback(() => {
    SysPermissionCiApi.findAllByRoleId(roleId).then((response) => {
      setSysPermissionCiList(response.data);
    });
  }, [roleId]);
  const fetchRoleInfo = useCallback(() => {
    if (roleId > 0) {
      RolesApi.getById(roleId)
        .then((response) => {
          if (response.status === 200) {
            const groupOfRoles = response.data.groupOfRoles ?? [];
            if (isCopyAction) {
              const roleClone = { ...response.data };
              roleClone.name = `${response.data.name}${roleClonePrefix}`;
              // roleClone.description = '';
              setBasicInfoRole(roleClone);
            } else {
              setBasicInfoRole(response.data);
            }
            setNameGroupOfRoles(groupOfRoles.map((item) => item.name));
            setSelectedItems(groupOfRoles);
            fetchAllPermissionOthersByRoleId();
            fetchAllPermissionCisByRoleId();
          }
        })
        .catch(() => {});
    }
  }, [fetchAllPermissionCisByRoleId, fetchAllPermissionOthersByRoleId, isCopyAction, roleId]);

  useEffect(() => {
    if (isUpdatePath && roleId > 0) {
      fetchRoleInfo();
    } else {
      setAdminPermissions((prevPermissions) =>
        prevPermissions.map((permission) => ({
          ...permission,
          isChecked: false,
        })),
      );
      setApplicationPermissions((prevPermissions) =>
        prevPermissions.map((permission) => ({
          ...permission,
          isChecked: false,
        })),
      );
    }
    if (isSuperAdmin) {
      findAllGroupActivesInRoleToView(roleId);
    }
  }, [fetchRoleInfo, findAllGroupActivesInRoleToView, isSuperAdmin, isUpdatePath, roleId]);

  const executeLogicWhenCreateOrUpdateRole = () => {
    const otherPermissions = [...adminPermissions, ...applicationPermissions];
    const checkedPermissions = otherPermissions.filter((permission) => permission.isChecked);
    const otherPermissionModels = PermissionServiceExecute.convertPermissionOtherDtoToModel(checkedPermissions);
    let ciPermissionModels = ciPermissions.filter((permission) => permission.isSetting);
    if (roleId > 0 && ciPermissions.length === 0) {
      ciPermissionModels = convertToConfigItemTypePermission(sysPermissionCiList);
    }
    const groupIds: number[] = selectedItems.map((item) => item.id);
    if (isCopyAction) {
      basicInfoRole.id = 0;
    }
    RolesApi.saveOrUpdate(groupIds, basicInfoRole, otherPermissionModels, ciPermissionModels)
      .then((response) => {
        if (response.status === 200) {
          navigateTo(roleViewListPath);
          NotificationSuccess({ message: 'Setting role successfully' });
        }
      })
      .catch(() => {});
  };

  const executeLogicWhenChooseDataSelected = () => {
    setNameGroupOfRoles(selectedItems.map((item) => item.name));
    // if (selectedItems.length === 0) {
    //   setTotalUserSelectDataEmpty((oldData) => oldData + 1);
    // }
    closeModalGroupInRole();
  };

  const getTitlePageInBreadcrumb = useMemo((): string => {
    const roleName = basicInfoRole?.name ?? '';
    if (roleId === 0) {
      return `${formatStandardName(RoleAction.CREATE)}`;
    }
    return RoleAction.COPY === action ? `${formatStandardName(RoleAction.COPY)} ${roleName}` : `${formatStandardName(RoleAction.UPDATE)} ${roleName}`;
  }, [action, basicInfoRole.name, roleId]);

  const orderFullCustomPaths = useMemo((): UrlBaseCrumbData => {
    const originPath = buildCreateOrUpdateRoleUrl(roleId);

    const result: UrlBaseCrumbData = {
      ['admins']: {
        title: 'Admins',
        href: '/admins',
      },
      ['roles']: {
        title: 'Roles',
        href: roleViewListPath,
      },
      // 4736 CREATE role screen has no action -> base on id
      [`${createOrUpdateRolePath}`]: {
        title: getTitlePageInBreadcrumb,
        href: originPath,
      },
    };
    return result;
  }, [getTitlePageInBreadcrumb, roleId]);
  return (
    <>
      {/* 4746  role create/update/copy page*/}
      <BreadcrumbComponent orderFullCustomPaths={orderFullCustomPaths} />
      <Box p={20}>
        <HeaderTitleComponent
          title={titlePage}
          rightSection={
            <Flex gap={10}>
              <KanbanButton
                variant='outline'
                onClick={() => {
                  navigateTo(roleViewListPath);
                }}>
                Cancel
              </KanbanButton>
              <KanbanButton
                disabled={basicInfoRole.name.trim().length === 0}
                onClick={() => {
                  executeLogicWhenCreateOrUpdateRole();
                }}>
                Save
              </KanbanButton>
            </Flex>
          }
        />

        <Box mb={'md'}>
          <KanbanTitle order={4} c={'var(--mantine-color-primary-5)'}>
            Basic Information Role
          </KanbanTitle>
          <KanbanInput
            label='Name'
            required={true}
            maxLength={255}
            value={basicInfoRole.name || ''}
            onChange={(e) => {
              onSetBasicInfoRole(e, 'name');
            }}
            onBlur={(e) => {
              const value = e.target.value;
              setBasicInfoRole((prev) => {
                return { ...prev, name: formatStandardName(value) };
              });
              onSetBasicInfoRole(e, formatStandardName(value));
            }}></KanbanInput>

          <KanbanInput
            label='Description'
            value={basicInfoRole.description || ''}
            maxLength={2000}
            onChange={(e) => {
              onSetBasicInfoRole(e, 'description');
            }}></KanbanInput>

          {isSuperAdmin && (
            <>
              <Flex gap='md' justify='flex-start' align='center' direction='row' wrap='wrap' mb={5}>
                <KanbanText fw={500}>Groups of role:</KanbanText>
                <KanbanText>{nameGroupOfRoles.join(', ')}</KanbanText>
              </Flex>
              <KanbanButton
                onClick={() => {
                  openModalGroupInRole();
                  // setSelectedItems([]);
                }}>
                Choose group
              </KanbanButton>
            </>
          )}
        </Box>
        <Space h='sm' />
        <StyledBox>
          <KanbanTitle order={4} c={'var(--mantine-color-primary-5)'} mb={'md'}>
            Add Permission
          </KanbanTitle>
          <KanbanTabs
            configs={{
              orientation: 'vertical',
              defaultValue: activeTab,
              onChange: (val) => {
                setActiveTab(val as PermissionActionType);
              },
            }}
            tabs={{
              ADMIN: {
                content: <ViewListPermission permissions={adminPermissions} updateOtherPermissions={updateAdminPermissions} />,
                title: 'Admin',
              },
              APPLICATIONS: {
                content: <ViewListPermission permissions={applicationPermissions} updateOtherPermissions={updateApplicationPermissions} />,
                title: 'Application',
              },
              CI: {
                content: (
                  <Box ml={'md'}>
                    <GroupSettingsPermissionModal
                      updateCiPermission={handleUpdateCiPermissions}
                      roleId={roleId}
                      renderOnlyOne={renderOnlyOne}
                      setCallApiOnlyOne={setCallApiOnlyOne}
                      sysPermissionCi={sysPermissionCiList}
                    />
                  </Box>
                ),
                title: 'CI',
              },
            }}
          />
        </StyledBox>
      </Box>
      <KanbanModal
        keepMounted={false}
        size={'xl'}
        opened={openedModalGroupInRole}
        onClose={() => {
          // childRef.current?.clearSelected();
          executeLogicWhenChooseDataSelected();
          // closeModalGroupInRole();
        }}
        centered
        title={`Setting role group ${basicInfoRole.name}:`}
        actions={
          isSuperAdmin && (
            // <GuardComponent requirePermissions={[AclPermission.addGroupToRole]} hiddenOnUnSatisfy>
            <KanbanButton
              // disabled={selectedItems.length === 0}
              leftSection={<IconFriends />}
              onClick={() => {
                // if (selectedItems.length > 0) {
                // setChooseGroups([...selectedItems]);
                executeLogicWhenChooseDataSelected();
                // closeModalGroupInRole();
                // }
              }}>
              Choose
            </KanbanButton>
            // </GuardComponent>
          )
        }>
        <SettingGroupIntoRoleContent
          isFetched={isFetched}
          mountData={mountData}
          role={basicInfoRole}
          onUpdateSelected={onUpdateSelected}
          isShowChecked={true}
          // groupSelecteds={selectedItems}
          isShowActiveColumn={false}
          // totalUserSelectDataEmpty={totalUserSelectDataEmpty}
          selectedItemCheckeds={selectedItemCheckeds}
          updateCheckedListByUserAction={updateCheckedListByUserAction}
          ref={childRef}
        />
      </KanbanModal>
    </>
  );
};
export default CreateOrUpdateRolePage;
