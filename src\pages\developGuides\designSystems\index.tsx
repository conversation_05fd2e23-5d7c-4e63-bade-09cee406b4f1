import React, { useMemo, useRef, useState } from 'react';
import {
  KanbanAccordion,
  KanbanAutocomplete,
  KanbanButton,
  KanbanCheckbox,
  KanbanColorInput,
  KanbanContentLoading,
  KanbanDateInput,
  KanbanDatePicker,
  KanbanDateTimePicker,
  KanbanDropZone,
  KanbanFileInput,
  KanbanIconButton,
  KanbanIconSelect,
  KanbanInput,
  KanbanLoading,
  KanbanModal,
  KanbanMonthPicker,
  KanbanMultiSelect,
  KanbanNumberInput,
  KanbanPagination,
  KanbanRadio,
  KanbanSelect,
  KanbanSlider,
  KanbanSwitch,
  KanbanTabs,
  KanbanTagsInput,
  KanbanText,
  KanbanTextarea,
  KanbanTimePicker,
  KanbanTooltip,
  KanbanYearPicker,
  renderDate,
  useDebounceCallback,
  KanbanTable,
  KanbanModalMethods,
  useKanbanDesignSystemContext,
  ColumnType,
} from 'kanban-design-system';

import { IconCameraSelfie, IconHeart, IconPhoto } from '@tabler/icons-react';

import { useDisclosure, useListState } from '@mantine/hooks';

import { random } from 'lodash';

import { Container } from '@mantine/core';
import KanbanTable2Demo from './kanbanTable2Demo';

const dataTable = (from = 0, length = 50) =>
  Array.from({ length: length }).map((_value, index) => ({
    id: index + from,
    col1: `Name ${index + from}`,
    col4: random(0, 1000),
    col5: random(0, 1000),
    col2: new Date(new Date().getMilliseconds() - Math.random() * 1e12),
    col3: !(random() < 0.5),
  }));

export const DesignSystemPage = () => {
  const [openedModal, { close: closeModal, open: _openModal }] = useDisclosure(false);

  const [tableData, { ...handlerTableData }] = useListState(dataTable(0));

  const [tableDataInfinite, setTableDataInfinite] = useState(() => dataTable(0));

  const [tableDataVirtualize, { ...handlerTableDataVirtualize }] = useListState(dataTable(0, 100000));

  ///const [tableDataVirtualize] = useState(() => dataTable(0, 1000));
  const tableDataVirtualizeColumns: ColumnType<{
    id: number;
    col1: string;
    col4: number;
    col5: number;
    col2: Date;
    col3: boolean;
  }>[] = useMemo(() => {
    return [
      {
        name: 'id',
        title: 'id',
      },
      {
        name: 'col1',
        title: 'Name',
      },
      {
        name: 'col4',
        title: 'Number',
        advancedFilter: {
          variant: 'number',
        },
      },
      {
        name: 'col5',
        title: 'Number 11',
        advancedFilter: {
          variant: 'range',
          customProps: {
            max: 10000,
          },
        },
      },
      {
        name: 'col2',
        title: 'Birdday',
        customRender: renderDate,
        advancedFilter: {
          variant: 'date',
        },
      },
      {
        name: 'col3',
        title: 'Is valid',
        customRender: (value) => {
          return <KanbanCheckbox defaultChecked={Boolean(value)} />;
        },
        advancedFilter: {
          variant: 'checkbox',
        },
      },
    ];
  }, []);
  const kanbanTableRef = useRef<KanbanModalMethods>(null);
  const openModal = () => {
    _openModal();
    kanbanTableRef.current?.setMinimize(false);
  };
  const context = useKanbanDesignSystemContext();
  // eslint-disable-next-line no-console

  const [icon, onChangeIcon] = useState('Icon123');
  const [value, setValue] = useState('');
  const [debounce, setDebounce] = useState('');
  const debounced = useDebounceCallback((e) => {
    setDebounce(e);
  }, 200);
  return (
    <>
      {JSON.stringify(context)}
      <br />
      API Defination Rules 1:{' '}
      <a href='https://restfulapi.net/resource-naming/' target='_blank' rel='noreferrer'>
        https://restfulapi.net/resource-naming/
      </a>
      CSS Variables are require:{' '}
      <a href='https://mantine.dev/styles/css-variables' target='_blank' rel='noreferrer'>
        https://mantine.dev/styles/css-variables/
      </a>
      Icons:{' '}
      <a href='https://tabler-icons.io/' target='_blank' rel='noreferrer'>
        https://tabler-icons.io/
      </a>
      <KanbanAccordion
        data={[
          {
            content: (
              <>
                <KanbanInput
                  label='Enter value to see debounce'
                  value={value}
                  style={{ flex: 1 }}
                  onChange={(event) => {
                    const value = event.target.value;
                    setValue(value);
                    debounced(value);
                  }}
                />
                <KanbanText>Value: {value}</KanbanText>
                <KanbanText>Debounced value: {debounce}</KanbanText>
                <br />
                <KanbanInput
                  label='Input'
                  description='Input description'
                  placeholder='Input placeholder'
                  readOnly
                  value={'http://google.com'}
                  urlDetect
                />
                <br />
                <KanbanSelect label='Select' placeholder='Pick value' data={['React', 'Angular', 'Vue', 'Svelte']} />
                <br />
                <KanbanMultiSelect label='Multi select' placeholder='Pick value' data={['React', 'Angular', 'Vue', 'Svelte']} />
                <br />
                <KanbanIconSelect
                  value={icon}
                  label='Icon select'
                  placeholder='Pick an icon'
                  onChange={(e) => {
                    onChangeIcon(e || '');
                  }}
                />
                <br />
                <KanbanCheckbox defaultChecked label='I agree to sell my privacy' />
                <br />
                <KanbanColorInput readOnly label='Color input' description='Input description' placeholder='Input placeholder' />
                <br />
                <KanbanFileInput label='File input' description='Input description' placeholder='Input placeholder' />
                <br />
                <KanbanTextarea label='Textarea' description='Input description' placeholder='Input placeholder' />
                <br />
                <KanbanNumberInput label='Number' description='Input description' placeholder='Input placeholder' />
                <br />
                <KanbanSwitch defaultChecked label='I agree to sell my privacy' />
                <br />
                <KanbanDropZone />
                <br />
                <KanbanDateInput label='Date input' />
                <br />
                <KanbanDatePicker label='Date picker' type={'multiple'} />
                <br />
                <KanbanDateTimePicker label='Date time picker' />
                <br />
                <KanbanMonthPicker label='Month picker' />
                <br />
                <KanbanYearPicker label='Year picker' />
                <br />
                <KanbanTimePicker label='Time picker' />
                <br />
                <KanbanRadio
                  group={{
                    name: 'favoriteFramework',
                    label: 'Radio',
                    description: 'This is anonymous',
                    withAsterisk: true,
                  }}
                  radios={[
                    {
                      value: 'react',
                      label: 'react',
                    },
                    {
                      value: 'svelte',
                      label: 'svelte',
                    },
                    {
                      value: 'vue',
                      label: 'vue',
                    },
                  ]}
                />
                <br />
                <KanbanSlider
                  withAsterisk
                  label='Slider'
                  marks={[
                    { value: 20, label: '20%' },
                    { value: 50, label: '50%' },
                    { value: 80, label: '80%' },
                  ]}
                />
                <br />
                <KanbanAutocomplete label='Auto complete' placeholder='Pick value or enter anything' data={['React', 'Angular', 'Vue', 'Svelte']} />
                <br />
                <KanbanTagsInput label='Press Enter to submit a tag' placeholder='Tags input' description='Description' />
                <br />
                <KanbanPagination total={10} color='primary' />
                <br />
              </>
            ),
            title: 'Input',
          },
          {
            title: 'KanbanTable',
            content: (
              <>
                <KanbanTable
                  rowOrderable={{
                    enable: true,
                    onDragEnd: ({ destination, source }) => {
                      handlerTableData.reorder({
                        from: source.index,
                        to: destination?.index || 0,
                      });
                    },
                  }}
                  showNumericalOrderColumn={true}
                  columns={[
                    {
                      name: 'id',
                      title: 'id',
                    },
                    {
                      name: 'col1',
                      title: 'Name',
                      customRender: (value, row) => {
                        const index = tableData.indexOf(row);
                        return {
                          value: value,
                          customProps: {
                            /**
                             * Đây là test thôi, suggest best practice:
                             * Object column dạng nên dạng thế này:
                             * {
                             * name: 'abc',
                             * age: 123,
                             * ...
                             * __rowSpan: 3
                             *
                             * }
                             * Sau đó thì ở chỗ custom render này sẽ dùng là rowSpan: row._rowSpan
                             */
                            rowSpan: index === 1 ? 3 : 1, //Row span test
                            colSpan: index === 6 ? 3 : 1, //Col span test
                          },
                        };
                      },
                    },
                    {
                      name: 'col4',
                      title: 'Number',
                      advancedFilter: {
                        variant: 'number',
                      },
                    },
                    {
                      name: 'col5',
                      title: 'Number 11',
                      advancedFilter: {
                        variant: 'range',
                        customProps: {
                          max: 10000,
                        },
                      },
                    },
                    {
                      name: 'col2',
                      title: 'Birdday',
                      customRender: renderDate,
                      advancedFilter: {
                        variant: 'date',
                      },
                    },
                    {
                      name: 'col3',
                      title: 'Is valid',
                      customRender: (value) => {
                        return <KanbanCheckbox defaultChecked={Boolean(value)} />;
                      },
                      advancedFilter: {
                        variant: 'checkbox',
                      },
                    },
                  ]}
                  advancedFilterable={{
                    enable: true,
                    debounceTime: 200,
                  }}
                  data={tableData}
                  selectableRows={{
                    enable: true,
                  }}
                  pagination={{
                    enable: true,
                  }}
                  sortable={{
                    enable: true,
                  }}
                  searchable={{
                    enable: true,
                  }}
                  actions={{
                    deletable: {
                      onDeleted() {},
                    },
                    customAction: () => (
                      <>
                        <KanbanIconButton key={1} size='sm' color='blue' variant='transparent'>
                          <IconHeart />
                        </KanbanIconButton>
                      </>
                    ),
                  }}
                />
                <br />
                Infinity scroll
                <KanbanTable
                  columns={[
                    {
                      name: 'id',
                      title: 'id',
                    },
                    {
                      name: 'col1',
                      title: 'Name',
                    },
                    {
                      name: 'col4',
                      title: 'Number',
                      advancedFilter: {
                        variant: 'number',
                      },
                    },
                    {
                      name: 'col5',
                      title: 'Number 11',
                      advancedFilter: {
                        variant: 'range',
                        customProps: {
                          max: 10000,
                        },
                      },
                    },
                    {
                      name: 'col2',
                      title: 'Birdday',
                      customRender: renderDate,
                      advancedFilter: {
                        variant: 'date',
                      },
                    },
                    {
                      name: 'col3',
                      title: 'Is valid',
                      customRender: (value) => {
                        return <KanbanCheckbox defaultChecked={Boolean(value)} />;
                      },
                      advancedFilter: {
                        variant: 'checkbox',
                      },
                    },
                  ]}
                  data={tableDataInfinite}
                  isLoading={false}
                  serverside={{
                    totalRows: 1000,
                    infiniteScroll: {
                      threshold: 1000,
                    },
                    onTableAffected() {
                      return new Promise((res) => {
                        setTimeout(() => {
                          res();
                          setTableDataInfinite((prev) => {
                            const data = [...prev, ...dataTable(prev[prev.length - 1].id + 1)];
                            return data;
                          });
                        }, 3000);
                      });
                    },
                  }}
                  pagination={{
                    enable: true,
                  }}
                />
                <br />
                Virtualize
                <KanbanTable
                  columns={tableDataVirtualizeColumns}
                  data={tableDataVirtualize}
                  rowOrderable={{
                    enable: true,
                    onDragEnd: ({ destination, source }) => {
                      handlerTableDataVirtualize.reorder({
                        from: source.index,
                        to: destination?.index || 0,
                      });
                    },
                    getNodeDraggingVirtual: (data) => {
                      return <>{data.col1}</>;
                    },
                  }}
                  isLoading={false}
                  virtualizer={{
                    enable: true,
                    estimateSize() {
                      return 50;
                    },
                  }}
                  pagination={{
                    enable: false,
                  }}
                />
                <br />
              </>
            ),
          },
          {
            title: 'KanbanTableV2',
            content: <KanbanTable2Demo />,
          },
          {
            title: 'Button',
            content: (
              <>
                <div>
                  <KanbanButton variant='filled' size='xs'>
                    Button
                  </KanbanButton>
                  <KanbanButton variant='filled' size='sm'>
                    Button
                  </KanbanButton>
                  <KanbanButton variant='filled' size='md'>
                    Button
                  </KanbanButton>
                  <KanbanButton variant='filled' size='lg'>
                    Button
                  </KanbanButton>
                  <KanbanButton variant='filled' size='xl'>
                    Button
                  </KanbanButton>
                  <br />
                  <KanbanButton variant='filled' size='compact-xs'>
                    Button
                  </KanbanButton>
                  <KanbanButton variant='filled' size='compact-sm'>
                    Button
                  </KanbanButton>
                  <KanbanButton variant='filled' size='compact-md'>
                    Button
                  </KanbanButton>
                  <KanbanButton variant='filled' size='compact-lg'>
                    Button
                  </KanbanButton>
                  <KanbanButton variant='filled' size='compact-xl'>
                    Button
                  </KanbanButton>
                  <br />
                  <KanbanButton variant='default'>Button</KanbanButton>
                  <KanbanButton variant='light'>Button</KanbanButton>
                  <KanbanButton variant='outline'>Button</KanbanButton>
                  <KanbanButton variant='subtle'>Button</KanbanButton>
                  <KanbanButton variant='transparent'>Button</KanbanButton>
                  <KanbanButton variant='white'>Button</KanbanButton>
                  <KanbanIconButton size='md'>
                    <IconHeart />
                  </KanbanIconButton>
                </div>
              </>
            ),
          },

          {
            title: 'Other',
            content: (
              <>
                <br />
                <br />
                <div style={{ display: 'flex' }}>
                  <KanbanLoading color='primary' />
                  <KanbanLoading color='primary' type='bars' />
                  <KanbanLoading color='primary' type='dots' />
                </div>
                <br />
                <KanbanContentLoading loading={true}>
                  <KanbanInput label='Input' description='Input description' placeholder='Input placeholder' />
                  <br />
                  <KanbanSelect label='Select' placeholder='Pick value' data={['React', 'Angular', 'Vue', 'Svelte']} />
                  <br />
                </KanbanContentLoading>
                <br />
                <KanbanModal
                  imperativeRef={kanbanTableRef}
                  opened={openedModal}
                  onClose={closeModal}
                  minimizable={{
                    enable: true,
                    onMinimize() {
                      return true;
                    },
                  }}>
                  <KanbanInput label='ok' />
                  <KanbanButton variant='default'>Button</KanbanButton>
                  <KanbanButton variant='filled'>Button</KanbanButton>
                  <KanbanButton variant='light'>Button</KanbanButton>
                </KanbanModal>
                <KanbanButton variant='default' onClick={openModal}>
                  Open modal
                </KanbanButton>
                <br />
                <KanbanTooltip label='This is Tooltip'>
                  <div style={{ display: 'inline-block', width: 'min-content' }}>
                    <KanbanButton variant='default'>Tooltip</KanbanButton>
                  </div>
                </KanbanTooltip>
                <br />
                <KanbanAccordion
                  data={[
                    {
                      content: 'Bender Bending Rodríguez',
                      title: 'Fascinated with cooking, though has no sense of taste',
                      icon: {
                        component: IconPhoto,
                      },
                    },
                    {
                      content: <p>One of the richest people on Earth</p>,
                      title: 'Carol Miller',
                      icon: {
                        component: IconCameraSelfie,
                        color: 'secondary',
                      },
                    },
                    {
                      content: <p>Overweight, lazy, and often ignorant</p>,
                      title: 'Homer Simpson',
                    },
                  ]}
                />
                <br />
                <KanbanTabs
                  configs={{
                    defaultValue: '1',
                  }}
                  tabs={{
                    '1': {
                      content: 'Gallery',
                      title: 'Gallery',
                    },
                    '2': {
                      content: 'Gallery 1',
                      title: 'Gallery 1',
                      icon: IconHeart,
                    },
                  }}
                />
                <br />
                <KanbanTabs
                  configs={{
                    defaultValue: '1',
                    orientation: 'vertical',
                    variant: 'default',
                  }}
                  tabs={{
                    '1': {
                      content: <Container ml={'xs'}>Gallery</Container>,
                      title: 'Gallery',
                    },
                    '2': {
                      content: <Container ml={'xs'}>Gallery1</Container>,
                      title: 'Gallery 1',
                      icon: IconHeart,
                    },
                  }}
                />
                <br />
                <br />
                <br />
              </>
            ),
          },
        ]}></KanbanAccordion>
    </>
  );
};

export default DesignSystemPage;
