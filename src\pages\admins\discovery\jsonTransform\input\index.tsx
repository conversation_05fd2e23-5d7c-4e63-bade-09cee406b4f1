import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Key } from 'rc-tree/lib/interface';
import 'rc-tree/assets/index.css';
import { Box, Divider, Flex, JsonInput } from '@mantine/core';
import { CustomDataNode, filterKeysByMaxDepth, getAllKeysFromObject } from '../helper/JsonTransformHelper';
import { convertJsonToDataNodeWithSearch } from '../helper/RcTreeHelper';
import { KanbanButton, KanbanInput, KanbanModal, KanbanText, KanbanTitle, useDebounceCallback } from 'kanban-design-system';
import { IconSearch } from '@tabler/icons-react';
import JsonConfigView, { JsonConfigViewerMethods } from './JsonConfigView';
import { JsonTransformPageProps } from '..';
import { NotificationWarning } from '@common/utils/NotificationUtils';
import { useDroppedItemsRefs } from '@context/DragScrollContext';
import { JSONPath } from 'jsonpath-plus';
import { Panel, PanelResizeHandle } from 'react-resizable-panels';
import styleIndexs from './Index.module.scss';
import { useDataTransform } from '@context/DataTransformContext';
import { DataTransformActionType } from '@common/constants/DataTransformActionType';
import { useJsonTransformContext } from '@context/JsonTransformContext';
import { JsonTransformActionType } from '@common/constants/JsonTransformActionType';
import { v4 as uuidv4 } from 'uuid';
import { DiscoveryApi } from '@api/discovery/sources/DiscoveryApi';
import { SOURCES } from '@common/constants/DiscoverySourceDataConstants';
import { useDisclosure } from '@mantine/hooks';
import { VirtualTree } from '../helper/VirtualTree';

const JsonTransformInputPage: React.FC<JsonTransformPageProps> = ({ sourceId }) => {
  const { dispatch: jsonTransformDispatch, state: jsonTransformState } = useJsonTransformContext();
  const stagingStructure = jsonTransformState?.stagingStructure; // get Staging Structure

  const [draggedNode, setDraggedNode] = useState<CustomDataNode | null>(null);
  // const [droppedItems, setDroppedItems] = useState<CustomDataNode[]>(stagingStructure);
  const [expandedKeys, setExpandedKeys] = useState<Key[]>([]);
  const [treeData, setTreeData] = useState<CustomDataNode[]>([]);
  const [searchValue, setSearchValue] = useState<string>('');
  // const dispatch = useDispatch();
  const { dispatch: dataTransformDispatch, state: dataTransform } = useDataTransform();
  const jsonData = dataTransform?.jsonData;
  const columnDatasForInput = dataTransform?.columnDatasForInput;
  const jsonConfigRef = useRef<JsonConfigViewerMethods>(null);
  // const [discoverySourceType, setDiscoverySourceType] = useState<DiscoverySourceTypeEnum>(DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX);
  // const { dispatch: keysDispatch } = useKeysOfJsonData();

  const { highlightItem, onUpdateItemIdWhenDropCurrent, scrollToItem } = useDroppedItemsRefs();

  // const onUpdateDroppedItems = (nodes: CustomDataNode[]) => {
  //   setDroppedItems(nodes);
  // };

  useEffect(() => {
    jsonConfigRef.current?.handleAddField(stagingStructure);
  }, [stagingStructure]);

  const debounced = useDebounceCallback((e: string) => {
    setSearchValue(e);
  }, 400);

  const onDragStart = useCallback((info: { node: CustomDataNode }) => {
    const updatedNode = { ...info.node, id: uuidv4() };
    setDraggedNode(updatedNode);
  }, []);

  const onDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  }, []);

  const onDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      if (draggedNode) {
        const duplicateItem = columnDatasForInput.find((item) => String(item.key).toLowerCase() === String(draggedNode.key).toLowerCase());
        if (duplicateItem) {
          NotificationWarning({ message: `Element with name "${draggedNode.key}" is existed` });
          // scrollToAndHighlight(String(duplicateItem.key));
          const elementExistKey = String(duplicateItem.key).toLowerCase();
          scrollToItem(elementExistKey);
          highlightItem([elementExistKey]);
          return;
        }
        const valueJson = JSONPath({ path: draggedNode.jsonPath, json: jsonData });
        const value = valueJson.length ? (valueJson.length === 1 ? valueJson[0] : valueJson) : 'undefined';
        if (Array.isArray(value) || (typeof value === 'object' && value !== null)) {
          NotificationWarning({ message: `Value can't be Object.` });
          return;
        }

        const elementKey = String(draggedNode.key).toLowerCase();
        onUpdateItemIdWhenDropCurrent(elementKey);
        jsonConfigRef.current?.handleAddField([draggedNode]);
        setDraggedNode(null);
      }
    },
    [columnDatasForInput, draggedNode, highlightItem, jsonData, onUpdateItemIdWhenDropCurrent, scrollToItem],
  );

  const onExpand = useCallback((keys: Key[]) => {
    setExpandedKeys(keys);
  }, []);

  const handleSuccessResponse = useCallback(
    (jsonData: any) => {
      dataTransformDispatch({ type: DataTransformActionType.UPDATE_JSON_DATA, payload: jsonData });
      jsonTransformDispatch({ type: JsonTransformActionType.UPDATE_KEYS, payload: getAllKeysFromObject(jsonData) });
      setStaticJsonValue('');
    },
    [dataTransformDispatch, jsonTransformDispatch],
  );

  const handleImportJsonActive = useCallback((jsonData: any) => {
    if (jsonData) {
      onHandleJsonChange(JSON.stringify(jsonData));
    }
  }, []);

  const fetchAndProcessData = (sourceId: string, onSuccess: (jsonData: any) => void) => {
    const sourceId_ = sourceId;
    const sourceInfo = SOURCES.get(sourceId_);
    const sourceType = sourceInfo?.type;
    const sourceData = sourceInfo?.data;

    if (sourceType !== undefined && sourceData !== undefined) {
      // setDiscoverySourceType(sourceType);
      DiscoveryApi.discoveryData(sourceType, sourceData)
        .then((response) => {
          if (response.status === 200) {
            const jsonData = response.data;
            onSuccess(jsonData);
          }
        })
        .catch(() => {});
    }
  };

  const onGetData = useCallback(() => {
    dataTransformDispatch({ type: DataTransformActionType.REFRESH_JSON_DATA_AND_OUTPUT });
    jsonTransformDispatch({ type: JsonTransformActionType.RESET_KEYS });
    fetchAndProcessData(String(sourceId), handleSuccessResponse);
  }, [dataTransformDispatch, handleSuccessResponse, jsonTransformDispatch, sourceId]);

  useEffect(() => {
    onGetData();
  }, [onGetData]);

  useEffect(() => {
    if (jsonData) {
      const dataNodeAndKeys = searchValue ? convertJsonToDataNodeWithSearch(jsonData, '', searchValue) : convertJsonToDataNodeWithSearch(jsonData);
      const filteredKeys = searchValue ? dataNodeAndKeys.keys : filterKeysByMaxDepth(dataNodeAndKeys.keys, 10);
      setExpandedKeys(filteredKeys);
      setTreeData(dataNodeAndKeys.dataNodes);
    }
  }, [jsonData, searchValue]);

  const [openedImportStaticData, { close: closeImportStaticData, open: openImportStaticData }] = useDisclosure(false);
  const [staticJsonValue, setStaticJsonValue] = useState<string>('');
  const onImportStaticDataModal = () => {
    openImportStaticData();
  };
  const [jsonError, setJsonError] = useState<boolean>(false);
  const onHandleJsonChange = (value: string) => {
    setStaticJsonValue(value);
    try {
      JSON.parse(value);
      setJsonError(false);
    } catch (e) {
      setJsonError(true);
    }
  };

  const formattedJson = useMemo(() => {
    try {
      const jsonObj = JSON.parse(staticJsonValue);
      return JSON.stringify(jsonObj, null, 2);
    } catch (error) {
      return staticJsonValue;
    }
  }, [staticJsonValue]);

  return (
    <>
      <Panel id='left' order={1} defaultSize={30} className={styleIndexs['panel-height']}>
        <Box p={'xs'}>
          <KanbanTitle order={4}>INPUT</KanbanTitle>
          <KanbanText>
            Import data from source
            <br />
            or set mock data
          </KanbanText>
          <Flex gap={10} pt={'50px'} justify={'space-between'}>
            <KanbanInput
              placeholder='Search here'
              leftSection={<IconSearch size='1rem' />}
              onChange={(e) => debounced(e.target.value)}
              mb={'sm'}></KanbanInput>
            <Box>
              <KanbanButton onClick={onGetData} mr={'10px'} mb={'sm'}>
                Get data
              </KanbanButton>
              <KanbanButton onClick={onImportStaticDataModal} color={'cyan'} mb={'sm'}>
                Import static data
              </KanbanButton>
            </Box>
          </Flex>
          {/* {DiscoverySourceTypeEnum.DISCOVER_SOURCE_SOLARWIND_NETWORK === discoverySourceType && (
            <KanbanText c='dimmed' size='xs' fs='italic'>
              ( Data from Solarwind source shows up to 1000 results )
            </KanbanText>
          )} */}
          <Divider my='sm' />

          <VirtualTree treeData={treeData} onDragStart={onDragStart} expandedKeys={expandedKeys} onExpand={onExpand} />
        </Box>
      </Panel>
      <PanelResizeHandle />
      <Panel defaultSize={30} id='center' order={2} className={styleIndexs['panel-center']}>
        <JsonConfigView
          // droppedItems={droppedItems}
          ref={jsonConfigRef}
          onDragOver={onDragOver}
          onDrop={onDrop}
          sourceId={sourceId}
          // onUpdateDroppedItems={onUpdateDroppedItems}
        />
      </Panel>

      <KanbanModal
        size={'100%'}
        opened={openedImportStaticData}
        onClose={() => {
          closeImportStaticData();
        }}
        centered
        title={
          <Flex gap={10} justify={'space-between'}>
            <KanbanButton
              onClick={() => {
                fetchAndProcessData(String(sourceId), handleImportJsonActive);
              }}
              color='cyan'>
              Import from current live data
            </KanbanButton>
          </Flex>
        }
        actions={
          <>
            <KanbanButton
              disabled={!staticJsonValue}
              onClick={() => {
                if (jsonError) {
                  NotificationWarning({ message: 'Invalid JSON' });
                  return;
                }
                const json = JSON.parse(staticJsonValue);
                dataTransformDispatch({ type: DataTransformActionType.UPDATE_JSON_DATA, payload: json });
                dataTransformDispatch({ type: DataTransformActionType.UPDATE_IS_JSON_STATIC, payload: true });
                jsonTransformDispatch({ type: JsonTransformActionType.UPDATE_KEYS, payload: getAllKeysFromObject(json) });
                closeImportStaticData();
              }}
              mr={'sm'}>
              OK
            </KanbanButton>
            <KanbanButton
              disabled={!staticJsonValue}
              onClick={() => {
                setStaticJsonValue('');
              }}
              color='yellow'>
              Clear
            </KanbanButton>
          </>
        }>
        <div>
          <JsonInput value={formattedJson} validationError='Invalid JSON' formatOnBlur autosize minRows={30} onChange={onHandleJsonChange} />
        </div>
      </KanbanModal>
    </>
  );
};

export default JsonTransformInputPage;
