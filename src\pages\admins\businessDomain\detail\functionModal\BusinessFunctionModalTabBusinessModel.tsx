import React, { useState, useEffect, useCallback, useMemo } from 'react';
import type { BusinessFunctionModel } from '@models/BusinessDomain';
import { BusinessModelAttributeEnum, BusinessModelAttributeKeys } from '@common/constants/BusinessDomainConstants';
import { groupByToMap, transformEnum } from '@common/utils/CommonUtils';
import { Box, SimpleGrid, Card } from '@mantine/core';
import { KanbanText, KanbanTooltip, KanbanButton } from 'kanban-design-system';
import styles from './BusinessFunctionModel.module.scss';
import { BusinessDomainApi } from '@api/BusinessDomainApi';

export type BusinessFunctionModalTabBusinessModelProps = {
  ciIdBusinessDomain: number;
  ciIdBusinessFunction: number;
};
const BusinessModelAttributeEnumTransformed = transformEnum(BusinessModelAttributeEnum);

const defaultValue: BusinessFunctionModel = {
  contents: [],
};

const OFFICE_KEYS = [
  BusinessModelAttributeEnumTransformed.FRONT_OFFICE.key.toString(),
  BusinessModelAttributeEnumTransformed.MIDDLE_OFFICE.key.toString(),
  BusinessModelAttributeEnumTransformed.BACK_OFFICE.key.toString(),
];
const renderColor = (key: string) => (OFFICE_KEYS.includes(key) ? 'var(--mantine-color-yellow-1)' : 'var(--mantine-color-violet-1)');

export const BusinessFunctionModalTabBusinessModel = (props: BusinessFunctionModalTabBusinessModelProps) => {
  const [businessModels, setBusinessModels] = useState<BusinessFunctionModel>(defaultValue);

  const mapBusinessModels = useMemo(() => {
    return groupByToMap(businessModels.contents, (v) => v.businessAttributeValue);
  }, [businessModels]);

  const renderBusinessModels = (key: string) => {
    return (
      <Box className={styles['scroll-wrapper']}>
        {mapBusinessModels.get(key)?.map((item, index) => (
          <KanbanTooltip label={item.name} key={index}>
            <KanbanButton
              mr={'xs'}
              mt={5}
              mb={5}
              variant='outline'
              size='xs'
              maw={'100%'}
              bg='var(--mantine-color-white)'
              color='var(--mantine-color-black)'>
              <KanbanText truncate='end'>{item.name}</KanbanText>
            </KanbanButton>
          </KanbanTooltip>
        ))}
      </Box>
    );
  };

  const fetchData = useCallback(() => {
    BusinessDomainApi.getBusinessModelByCiIdBusinessDomainAndCiIdBusinessFunction(props.ciIdBusinessDomain, props.ciIdBusinessFunction)
      .then((res) => {
        setBusinessModels(res.data);
      })
      .catch(() => {
        setBusinessModels(defaultValue);
      });
  }, [props.ciIdBusinessDomain, props.ciIdBusinessFunction]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return (
    <>
      <Box className={styles['overflow-tab-panel']}>
        <SimpleGrid style={{ gridTemplateColumns: '3fr 4fr 3fr' }} spacing='xs'>
          {/* fix-bug CMDB-4989 */}
          <Card withBorder h={940} bg={'var(--mantine-color-blue-0)'}>
            <Box>
              <KanbanText fw={700}>{BusinessModelAttributeEnum.STRATEGY_AND_PROPOSITION}</KanbanText>
            </Box>
            {renderBusinessModels(BusinessModelAttributeEnumTransformed.STRATEGY_AND_PROPOSITION.key)}
          </Card>
          <Box>
            <SimpleGrid cols={1} verticalSpacing='xs'>
              {(Object.keys(BusinessModelAttributeEnum) as BusinessModelAttributeKeys[]).map((enumKey, indexEnum) => {
                if (
                  enumKey !== BusinessModelAttributeEnumTransformed.STRATEGY_AND_PROPOSITION.key &&
                  enumKey !== BusinessModelAttributeEnumTransformed.RISK_FINANCE_AND_COMPLIANCE.key
                ) {
                  return (
                    <Card key={indexEnum} withBorder h={180} bg={renderColor(enumKey)}>
                      <Box>
                        <KanbanText fw={700}>{BusinessModelAttributeEnum[enumKey]}</KanbanText>
                      </Box>
                      {renderBusinessModels(enumKey)}
                    </Card>
                  );
                }
                return <></>;
              })}
            </SimpleGrid>
          </Box>
          {/* fix-bug CMDB-4989 */}
          <Card withBorder h={940} bg={'var(--mantine-color-blue-0)'}>
            <Box>
              <KanbanText fw={700}>{BusinessModelAttributeEnum.RISK_FINANCE_AND_COMPLIANCE}</KanbanText>
            </Box>
            {renderBusinessModels(BusinessModelAttributeEnumTransformed.RISK_FINANCE_AND_COMPLIANCE.key)}
          </Card>
        </SimpleGrid>
      </Box>
    </>
  );
};
export default BusinessFunctionModalTabBusinessModel;
