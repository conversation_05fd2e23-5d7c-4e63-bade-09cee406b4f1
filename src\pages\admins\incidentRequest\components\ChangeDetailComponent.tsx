import { KanbanText } from 'kanban-design-system';
import { KanbanTitle } from 'kanban-design-system';
import { Title, Box, Flex } from '@mantine/core';
import type { IncidentRequestResponseDto } from '@models/IncidentSdp';
import React from 'react';

export interface ChangeDetailProps {
  title: string;
  coordinator: string;
  stage: string;
  status: string;
}

export interface ChangeDataProps {
  data: IncidentRequestResponseDto;
}

export interface ChangeDetailProps {
  title: string;
  coordinator: string;
  stage: string;
  status: string;
}

export interface InformationChangeProps {
  label: string;
  value: string;
}

export const InformationChange: React.FC<InformationChangeProps> = ({ label, value }) => {
  return (
    <Flex gap='xl'>
      <KanbanText w={'10%'} fw={500}>
        {label}
      </KanbanText>
      <KanbanText w={'60%'}>{value}</KanbanText>
    </Flex>
  );
};

export const ChangeDetailComponent: React.FC<ChangeDataProps> = ({ data }) => {
  return (
    <Box>
      <KanbanTitle order={3} mb='md' c='#1864AB'>
        SDP Link Info
      </KanbanTitle>
      <Title order={3} mb='md'>
        Current information in SDP (for review purpose and will not be saved to CMDB)
      </Title>
      <Flex direction='column' gap='sm'>
        <InformationChange label='Incident subject:' value={data.subject} />
        <InformationChange label='Technician:' value={data.technician || ''} />
        <InformationChange label='Incident status:' value={data.status} />
      </Flex>
    </Box>
  );
};
