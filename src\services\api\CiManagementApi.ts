import type { ApiResponseDataBase } from '@core/api/ApiResponse';
import { BaseApi } from '@core/api/BaseApi';

import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
import type { CiManagementModel } from '@models/CiManagement';
import type { EntityUserInfoResponse, EntityUserInfoResponsePagingResponse } from './systems/UsersApi';
import { CiChangeModel } from '@models/ConfigItem';
import { BaseUrl } from '@core/api/BaseUrl';

export type CiManagementResponse = ApiResponseDataBase & CiManagementModel;
export type CiManagementResponsePagingResponse = PaginationResponseModel<CiManagementResponse>;
export type CiManagementRequest = CiManagementModel & {
  importData?: boolean;
};
export class CiManagementApi extends BaseApi {
  static baseUrl = BaseUrl.ciManagements;

  static getAllCisManagementByScreen(
    screenType: string,
    pagination: PaginationRequestModel<CiManagementResponse>,
    controller?: AbortController,
    ciTypeAttributeIds?: number[],
  ) {
    return BaseApi.postData<CiManagementResponsePagingResponse>(
      `${this.baseUrl}/filter`,
      { ...pagination },
      { manageType: screenType, ciTypeAttributeIds },
      {},
      { useLoading: false, useErrorNotification: true },
      controller,
    );
  }

  static async getById(id: number) {
    const response = await BaseApi.getData<CiManagementResponse>(`${this.baseUrl}/${id}`);
    if (response.data && response.data.data) {
      response.data.dataParse = JSON.parse(response.data.data);
    }
    return response;
  }

  static deleteById(id: number) {
    return BaseApi.deleteData<CiManagementResponse[]>(`${this.baseUrl}/${id}`);
  }

  static deleteByIds(ids: number[]) {
    return BaseApi.deleteData<CiManagementResponse[]>(`${this.baseUrl}/batch`, {
      ids,
    });
  }

  static updateAction(actionType: string, datas: CiManagementModel[]) {
    const payload: CiManagementRequest[] = datas.map((obj) => ({
      ...obj,
      importData: obj.isImport,
    }));
    return BaseApi.postData<CiManagementResponse[]>(`${this.baseUrl}/actions`, payload, { actionType: actionType });
  }

  static async getCiTempByCiIdIn(ciIds: number[]) {
    //051224 bug error impacted overload
    const response = await BaseApi.postData<CiManagementResponse[]>(`${this.baseUrl}`, ciIds);

    if (response.data) {
      const updatedList = (response.data || []).map((item) => {
        return { ...item, dataParse: JSON.parse(item.data) };
      });
      response.data = updatedList;
    }
    return response;
  }

  static getAllUserApprovalByCiType(ciTypeId: number, pagination: PaginationRequestModel<EntityUserInfoResponse>) {
    return BaseApi.getData<EntityUserInfoResponsePagingResponse>(
      `${this.baseUrl}/${ciTypeId}/approval-users`,
      pagination,
      {},
      { useLoading: false, useErrorNotification: true },
    );
  }
  static findChangeInfoByCiTempIds(ciTempIds: number[]) {
    return BaseApi.getData<CiChangeModel[]>(`${this.baseUrl}/changes`, { ciTempIds });
  }

  static async findByNameCiTemp(id: number, ciTypeId: number, ciName: string) {
    const response = await BaseApi.getData<CiManagementResponse>(`${this.baseUrl}/${id}`, { ciTypeId, ciName });
    if (response.data && response.data.data) {
      response.data.dataParse = JSON.parse(response.data.data);
    }
    return response;
  }
}
