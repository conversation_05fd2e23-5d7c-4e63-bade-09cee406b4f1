import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton, KanbanSelect } from 'kanban-design-system';
import type { CiImpactedByRelationshipInformationResponseDto, ImpactedCiComponentProps } from '@models/ChangeAssessment';
import { IconPlus } from '@tabler/icons-react';
import { ImpactedCiFlagTableComponent } from './ImpactedCiFlagTableComponent';
import { CiManagementApi, CiManagementResponse } from '@api/CiManagementApi';
import { arraysEqual } from '@common/utils/StringUtils';
import { ImpactedCiTableComponent } from './ImpactedCiTableComponent';
import { ComboboxData, Flex } from '@mantine/core';
import { SystemConfigParamKey } from '@common/constants/SystemConfigParam';
import { getSystemParam<PERSON>yKey } from '@slices/SystemParameterSlice';
import { useSelector } from 'react-redux';
import { ImpactedCiTableViewEnum } from '@common/constants/ChangeAssessmentConstants';
import { FlaggedImpactedCiAttachedType } from '@common/constants/CiManagement';

export const ImpactedCiComponent: React.FC<ImpactedCiComponentProps> = ({
  allowEdit,
  isChangeCorForImpactedCi,
  listCiChange,
  listFlagCi,
  onAddCi,
  onProcessAdd,
  setListFlagCi,
}) => {
  const [listImpactedCiNew, setListImpactedCiNew] = useState<CiImpactedByRelationshipInformationResponseDto[]>([]);
  const [listImpactedCi, setListImpactedCi] = useState<CiImpactedByRelationshipInformationResponseDto[]>([]);
  const maxLevel = Number(useSelector(getSystemParamByKey(SystemConfigParamKey.CI_MAX_LEVEL))?.value) || 10;
  const [level, setLevel] = useState<number>(maxLevel);

  const listAllDraftId = useMemo(() => {
    const listCiInFlagAndImpactedTable = [...listFlagCi, ...listImpactedCi].map((item) => {
      return item.ciImpactedId;
    });
    return [...new Set(listCiInFlagAndImpactedTable.sort())];
  }, [listFlagCi, listImpactedCi]);
  const prevIdsRef = useRef<number[]>(listAllDraftId);

  const [listCiDraft, setListCiDraft] = useState<CiManagementResponse[]>([]);

  const IMPACTED_CI_VIEW_LEVELS: ComboboxData = [
    {
      value: `${maxLevel}`,
      label: 'Max level',
    },
    ...Array.from({ length: maxLevel - 1 }, (_, index) => {
      return {
        value: `${index + 1}`,
        label: `${index + 1}`,
      };
    }),
  ];
  const fetchDataDraft = useCallback(() => {
    const currentIds = [...listAllDraftId];
    if (!arraysEqual(currentIds, prevIdsRef.current)) {
      prevIdsRef.current = currentIds;
    } else {
      return;
    }
    if (listAllDraftId && listAllDraftId.length > 0) {
      //261224: impactedCiByRules table: client paging -> get ci draft by all page -> large input listAllDraftId (> 1000) => jpa fail
      CiManagementApi.getCiTempByCiIdIn(listAllDraftId)
        .then((res) => {
          if (res.data && res.data.length > 0) {
            setListCiDraft(res.data);
          }
        })
        .catch(() => {});
    }
  }, [listAllDraftId]);

  useEffect(() => {
    fetchDataDraft();
  }, [fetchDataDraft]);

  const handleAddFlagCis = useCallback(() => {
    //3291 merge list checked in table 'By rule' into a map(key = ci , data = merged list ci in change plan)
    const mapImpactedCiWithListChangePlanInFlaggedTable = new Map<number, CiImpactedByRelationshipInformationResponseDto>();
    listImpactedCiNew.forEach((it) => {
      const impactedCi = mapImpactedCiWithListChangePlanInFlaggedTable.get(it.ciImpactedId);
      if (impactedCi) {
        const mergedCiInChange = (impactedCi.ciChangePlans || []).concat([...(it.ciChangePlans || [])]);
        mapImpactedCiWithListChangePlanInFlaggedTable.set(it.ciImpactedId, { ...it, ciChangePlans: mergedCiInChange });
      } else {
        mapImpactedCiWithListChangePlanInFlaggedTable.set(it.ciImpactedId, it);
      }
    });
    //merge ciChangePlans with exist CALCULATED impacted ci in list flag cis
    const finalListFlagCi = listFlagCi.map((it) => {
      if (FlaggedImpactedCiAttachedType.CALCULATED === it.attachedType) {
        const calculatedImpactedCi = mapImpactedCiWithListChangePlanInFlaggedTable.get(it.ciImpactedId);
        if (calculatedImpactedCi) {
          const mergedCiInChanges = (calculatedImpactedCi.ciChangePlans || []).concat([...(it.ciChangePlans || [])]);
          //delete found impcated ci  in list new, remain new impacted ci , keep add them
          mapImpactedCiWithListChangePlanInFlaggedTable.delete(it.ciImpactedId);
          return { ...it, ciChangePlans: mergedCiInChanges };
        }
      }
      return it;
    });

    // append not exist CALCULATED impacted ci
    mapImpactedCiWithListChangePlanInFlaggedTable.values().forEach((it) => {
      finalListFlagCi.unshift(it);
    });

    //add not existed
    setListFlagCi(finalListFlagCi);
    setListImpactedCiNew([]);
    // disable added
  }, [listFlagCi, setListFlagCi, listImpactedCiNew]);
  return (
    <>
      {/* TABLE flagged cis */}
      {/* 2494: use the same table component as list manual impacted cis(in ImpactedCiTableComponent) bellow */}
      <HeaderTitleComponent
        title={'Flagged CIs'}
        rightSection={
          allowEdit && (
            <KanbanButton
              leftSection={<IconPlus />}
              mb={'var(--mantine-spacing-xs)'}
              disabled={onProcessAdd}
              onClick={() => {
                onAddCi(true);
              }}>
              Add Impacted CIs By User
            </KanbanButton>
          )
        }
      />
      <ImpactedCiFlagTableComponent
        allowEdit={allowEdit}
        listFlagCi={listFlagCi}
        setListFlagCi={setListFlagCi}
        listCiDraft={listCiDraft}
        listCiChange={listCiChange}
        isChangeCorForImpactedCi={isChangeCorForImpactedCi}
      />

      {/* TABLE to mark flag ci */}
      <HeaderTitleComponent
        //051224
        title={'List Impacted CIs By Rules'}
        rightSection={
          <Flex gap={10} align={'flex-end'}>
            <KanbanSelect
              label='Level to calculate'
              searchable
              defaultValue={`${maxLevel}`}
              allowDeselect={false}
              value={`${level}`}
              data={IMPACTED_CI_VIEW_LEVELS}
              onChange={(e) => {
                setLevel(e ? Number(e) : 1);
              }}></KanbanSelect>
            {/* 2494: Add flag cis , disapear in VIEW*/}
            {allowEdit && (
              <KanbanButton
                leftSection={<IconPlus />}
                mb={'var(--mantine-spacing-xs)'}
                disabled={listImpactedCiNew.length === 0}
                onClick={() => {
                  handleAddFlagCis();
                }}>
                {/* 051224 */}
                Add Impacted CIs By Rules
              </KanbanButton>
            )}
          </Flex>
        }
      />
      <ImpactedCiTableComponent
        showAllCiImpacted={ImpactedCiTableViewEnum.BY_PRIORITY_LEVEL}
        level={level}
        listCiChange={listCiChange}
        allowEdit={allowEdit}
        listFlagCi={listFlagCi}
        listImpactedCi={listImpactedCi}
        setListImpactedCi={setListImpactedCi}
        listCiDraft={listCiDraft}
        listImpactedCiNew={listImpactedCiNew}
        setListImpactedCiNew={setListImpactedCiNew}
      />

      {/* 3130:  remove table non related service / cis */}
    </>
  );
};
