import { PermissionAction } from '@common/constants/PermissionAction';
import { PermissionActionType } from '@common/constants/PermissionActionType';

export class AclPermission {
  static manageSystem = new AclPermission(PermissionActionType.ADMIN, PermissionAction.SYSTEM__MANAGE);
  static viewDevelopGuide = new AclPermission(PermissionActionType.ADMIN, PermissionAction.SYSTEM__VIEW_GUIDE);
  //List permission of config out going mail
  static saveOutGoingMailConfig = new AclPermission(PermissionActionType.ADMIN, PermissionAction.OUT_GOING_MAIL_CONFIG__CREATE);
  static viewOutGoingMailConfig = new AclPermission(PermissionActionType.ADMIN, PermissionAction.OUT_GOING_MAIL_CONFIG__VIEW);
  static testOutGoingMailConfig = new AclPermission(PermissionActionType.ADMIN, PermissionAction.OUT_GOING_MAIL_CONFIG__TEST_MAIL);
  // List permission of User
  static viewListUser = new AclPermission(PermissionActionType.ADMIN, PermissionAction.USER__VIEW_LIST);
  static viewDetailUser = new AclPermission(PermissionActionType.ADMIN, PermissionAction.USER__VIEW_DETAIL);
  static chooseGroupToAddUser = new AclPermission(PermissionActionType.ADMIN, PermissionAction.USER__ADD_TO_GROUP);
  static deleteGroupInUser = new AclPermission(PermissionActionType.ADMIN, PermissionAction.USER__DELETE_GROUP_IN_USER);
  static updateUser = new AclPermission(PermissionActionType.ADMIN, PermissionAction.USER__UPDATE);
  static deleteUser = new AclPermission(PermissionActionType.ADMIN, PermissionAction.USER__DELETE);
  static addUser = new AclPermission(PermissionActionType.ADMIN, PermissionAction.USER__CREATE);
  // List permission of Group-User
  static viewListGroupUser = new AclPermission(PermissionActionType.ADMIN, PermissionAction.GROUP__VIEW_LIST);
  static createGroupUser = new AclPermission(PermissionActionType.ADMIN, PermissionAction.GROUP__CREATE);
  static viewDetailGroupUser = new AclPermission(PermissionActionType.ADMIN, PermissionAction.GROUP__VIEW_DETAIL);
  static updateGroupUser = new AclPermission(PermissionActionType.ADMIN, PermissionAction.GROUP__UPDATE);
  // static updatePermissionOtherGroupUser = new AclPermission(PermissionActionType.ADMIN, PermissionAction.GROUP__UPDATE_PERMISSION_OTHER);
  static addUserToGroupUser = new AclPermission(PermissionActionType.ADMIN, PermissionAction.GROUP__ADD_USER);
  static deleteUserToGroupUser = new AclPermission(PermissionActionType.ADMIN, PermissionAction.GROUP__DELETE_USER);
  static addRoleToGroupUser = new AclPermission(PermissionActionType.ADMIN, PermissionAction.GROUP__ADD_ROLE_TO_GROUP);
  static deleteRoleInGroupUser = new AclPermission(PermissionActionType.ADMIN, PermissionAction.GROUP__DELETE_ROLE_IN_GROUP);
  // static updatePermissionCiGroupUser = new AclPermission(PermissionActionType.ADMIN, PermissionAction.GROUP__UPDATE_PERMISSION_CI_TYPE_OR_CI);
  static deleteGroupUser = new AclPermission(PermissionActionType.ADMIN, PermissionAction.GROUP__DELETE);
  // static viewListUserInGroup = new AclPermission(PermissionActionType.ADMIN, PermissionAction.GROUP__VIEW_LIST_USER);
  // static viewListRoleInGroup = new AclPermission(PermissionActionType.ADMIN, PermissionAction.GROUP__VIEW_LIST_ROLE_IN_GROUP);
  // List permission of CiType
  static viewListCiType = new AclPermission(PermissionActionType.ADMIN, PermissionAction.CI_TYPE__VIEW_LIST);
  static viewDetailCiType = new AclPermission(PermissionActionType.ADMIN, PermissionAction.CI_TYPE__VIEW_DETAIL);
  static viewListCiInCiType = new AclPermission(PermissionActionType.CI_TYPE, PermissionAction.CI_TYPE__VIEW_LIST_CI);
  static createCiType = new AclPermission(PermissionActionType.ADMIN, PermissionAction.CI_TYPE__CREATE);
  static configAttributeOfCiType = new AclPermission(PermissionActionType.ADMIN, PermissionAction.CI_TYPE__CONFIG_ATTRIBUTE);
  // static addSuggestRelationShipInCiType = new AclPermission(PermissionActionType.CI_TYPE, PermissionAction.CI_TYPE__ADD_SUGGEST_RELATIONSHIP);
  // static createCiTypeChild = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.CI_TYPE__CREATE_CHILD);
  static updateCiType = new AclPermission(PermissionActionType.ADMIN, PermissionAction.CI_TYPE__UPDATE);
  static deleteCiType = new AclPermission(PermissionActionType.ADMIN, PermissionAction.CI_TYPE__DELETE);
  // List permission of RelationshipType
  static createRelationshipType = new AclPermission(PermissionActionType.ADMIN, PermissionAction.RELATIONSHIP_TYPE__CREATE);
  static updateRelationshipType = new AclPermission(PermissionActionType.ADMIN, PermissionAction.RELATIONSHIP_TYPE__UPDATE);
  static deleteRelationshipType = new AclPermission(PermissionActionType.ADMIN, PermissionAction.RELATIONSHIP_TYPE__DELETE);
  // List permission of BusinessView
  static viewListBusinessView = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.BUSINESS_VIEW__VIEW_LIST);
  static viewDetailBusinessView = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.BUSINESS_VIEW__VIEW_DETAIL);
  static createBusinessView = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.BUSINESS_VIEW__CREATE);
  static updateBusinessView = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.BUSINESS_VIEW__UPDATE);
  static deleteBusinessView = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.BUSINESS_VIEW__DELETE);
  // List permission of Ci
  static createCi = new AclPermission(PermissionActionType.CI, PermissionAction.CI__CREATE);
  static viewBasicInformationCi = new AclPermission(PermissionActionType.CI, PermissionAction.CI__VIEW_BASIC);
  static viewAdvancedInformationCi = new AclPermission(PermissionActionType.CI, PermissionAction.CI__VIEW_ADVANCED);
  static approveCi = new AclPermission(PermissionActionType.CI, PermissionAction.CI__APPROVE);
  static updateCi = new AclPermission(PermissionActionType.CI, PermissionAction.CI__UPDATE);
  static deleteCi = new AclPermission(PermissionActionType.CI, PermissionAction.CI__DELETE);
  // List permission of Service Mapping
  static viewListServiceMap = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.SERVICE_MAP__VIEW_LIST);
  static createServiceMap = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.SERVICE_MAP__CREATE);
  static viewDetailServiceMap = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.SERVICE_MAP__VIEW_DETAIL);
  static updateServiceMap = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.SERVICE_MAP__UPDATE);
  // static deleteServiceMap = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.SERVICE_MAP__DELETE);
  // List permission of CiRelationship
  static createCiRelationship = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.CI_RELATIONSHIP__CREATE);
  static importCiRelationship = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.CI_RELATIONSHIP__IMPORT);
  static deleteCiRelationship = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.CI_RELATIONSHIP__DELETE);
  // List permission of Ci Draft
  // static updateCiDraft = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.CI_DRAFT__UPDATE);
  // static deleteCiDraft = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.CI_DRAFT__DELETE);
  // List permission of Change Assessment
  static viewListChangeAssessment = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.CHANGE__VIEW_LIST);
  static viewDetailChangeAssessment = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.CHANGE__VIEW_DETAIL);
  static createChangeAssessment = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.CHANGE__CREATE);
  static updateChangeAssessment = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.CHANGE__UPDATE);
  static deleteChangeAssessment = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.CHANGE__DELETE);

  // List permission of Role
  static viewListRole = new AclPermission(PermissionActionType.ADMIN, PermissionAction.ROLE__VIEW_LIST);
  static createRole = new AclPermission(PermissionActionType.ADMIN, PermissionAction.ROLE__CREATE);
  static viewDetailRole = new AclPermission(PermissionActionType.ADMIN, PermissionAction.ROLE__VIEW_DETAIL);
  static updateRole = new AclPermission(PermissionActionType.ADMIN, PermissionAction.ROLE__UPDATE);
  static deleteRole = new AclPermission(PermissionActionType.ADMIN, PermissionAction.ROLE__DELETE);
  static addGroupToRole = new AclPermission(PermissionActionType.ADMIN, PermissionAction.ROLE__ADD_GROUP_TO_ROLE);
  static deleteGroupInRole = new AclPermission(PermissionActionType.ADMIN, PermissionAction.ROLE__DELETE_GROUP_INTO_ROLE);
  // static viewListGroupInRole = new AclPermission(PermissionActionType.ADMIN, PermissionAction.ROLE__VIEW_LIST_GROUP_IN_ROLE);

  // List permission of Incident Request
  static viewListIncidentRequest = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.INCIDENT__VIEW_LIST);
  static viewDetailIncidentRequest = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.INCIDENT__VIEW_DETAIL);
  static createIncidentRequest = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.INCIDENT__CREATE);
  static updateIncidentRequest = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.INCIDENT__UPDATE);
  static deleteIncidentRequest = new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.INCIDENT__DELETE);

  static showAllCiHasPermission = new AclPermission(PermissionActionType.CI_TYPE, PermissionAction.CI_TYPE__VIEW_LIST_CI, 0);

  //add permission for show/disable component
  static changeActiveNotificationTemplate = new AclPermission(PermissionActionType.ADMIN, PermissionAction.NOTIFICATION_TEMPLATE__CHANGE_ACTIVE);
  static updateNotificationTemplate = new AclPermission(PermissionActionType.ADMIN, PermissionAction.NOTIFICATION_TEMPLATE__UPDATE);
  static viewListNotificationTemplate = new AclPermission(PermissionActionType.ADMIN, PermissionAction.NOTIFICATION_TEMPLATE__VIEW_LIST);
  static viewDetailNotificationTemplate = new AclPermission(PermissionActionType.ADMIN, PermissionAction.NOTIFICATION_TEMPLATE__VIEW_DETAIL);

  //impacted rules
  static viewListImpactedRule = new AclPermission(PermissionActionType.ADMIN, PermissionAction.IMPACTED_RULE__VIEW_LIST);
  static viewDetailImpactedRule = new AclPermission(PermissionActionType.ADMIN, PermissionAction.IMPACTED_RULE__VIEW_DETAIL);
  static createImpactedRule = new AclPermission(PermissionActionType.ADMIN, PermissionAction.IMPACTED_RULE__CREATE);
  static updateImpactedRule = new AclPermission(PermissionActionType.ADMIN, PermissionAction.IMPACTED_RULE__UPDATE);
  static deleteImpactedRule = new AclPermission(PermissionActionType.ADMIN, PermissionAction.IMPACTED_RULE__DELETE);

  // job config
  static viewListJobConfig = new AclPermission(PermissionActionType.ADMIN, PermissionAction.JOB_CONFIG__VIEW_LIST);
  static createJobConfig = new AclPermission(PermissionActionType.ADMIN, PermissionAction.JOB_CONFIG__CREATE);
  static viewDetailJobConfig = new AclPermission(PermissionActionType.ADMIN, PermissionAction.JOB_CONFIG__VIEW_DETAIL);
  static updateJobConfig = new AclPermission(PermissionActionType.ADMIN, PermissionAction.JOB_CONFIG__UPDATE);
  static deleteJobConfig = new AclPermission(PermissionActionType.ADMIN, PermissionAction.JOB_CONFIG__DELETE);
  static viewLogJobConfig = new AclPermission(PermissionActionType.ADMIN, PermissionAction.JOB_CONFIG__VIEW_LOG);

  static actionOfRelationshipTypePermissions = [
    AclPermission.createRelationshipType,
    AclPermission.updateRelationshipType,
    AclPermission.deleteRelationshipType,
  ];

  static actionTableCiPermissions = [
    new AclPermission(PermissionActionType.CI_TYPE, PermissionAction.CI_RELATIONSHIP__CREATE),
    new AclPermission(PermissionActionType.CI, PermissionAction.CI_RELATIONSHIP__CREATE),
    new AclPermission(PermissionActionType.CI_TYPE, PermissionAction.CI__UPDATE),
    new AclPermission(PermissionActionType.CI, PermissionAction.CI__UPDATE),
    new AclPermission(PermissionActionType.CI_TYPE, PermissionAction.CI__DELETE),
    new AclPermission(PermissionActionType.CI, PermissionAction.CI__DELETE),
  ];

  static actionTableBusinessViewPermissions = [
    new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.BUSINESS_VIEW__DELETE),
    new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.BUSINESS_VIEW__VIEW_DETAIL),
    new AclPermission(PermissionActionType.APPLICATIONS, PermissionAction.BUSINESS_VIEW__UPDATE),
  ];

  // List group action table each Object
  static actionTableGroupPermissions = [
    AclPermission.deleteGroupUser,
    AclPermission.updateGroupUser,
    AclPermission.addUserToGroupUser,
    AclPermission.addRoleToGroupUser,
    AclPermission.deleteUserToGroupUser,
    AclPermission.deleteRoleInGroupUser,
  ];

  static actionTableUserPermissions = [AclPermission.deleteUser, AclPermission.updateUser];

  // List role action table each Object
  static actionTableRolePermissions = [
    AclPermission.deleteRole,
    AclPermission.updateRole,
    AclPermission.addGroupToRole,
    AclPermission.viewDetailRole,
  ];

  // list  action table each Object in table Change assessment
  static actionTableChangeAssessmentPermissions = [
    AclPermission.viewDetailChangeAssessment,
    AclPermission.deleteChangeAssessment,
    AclPermission.updateChangeAssessment,
    AclPermission.createChangeAssessment,
  ];
  // list  action table each Object in table Incident Request
  static actionTableIncidentRequestPermissions = [
    AclPermission.viewDetailIncidentRequest,
    AclPermission.deleteIncidentRequest,
    AclPermission.updateIncidentRequest,
    AclPermission.createIncidentRequest,
  ];

  // list action table each Object in table Job config
  static actionTableJobConfigPermissions = [
    AclPermission.viewDetailJobConfig,
    AclPermission.deleteJobConfig,
    AclPermission.updateJobConfig,
    AclPermission.createJobConfig,
  ];

  // list action table each Object in table Service Mapping
  static actionTableServiceMappingPermissions = [AclPermission.viewDetailServiceMap, AclPermission.updateServiceMap];
  // list action table each Object in table Relationship Type
  static actionTableRelationshipTypePermissions = [AclPermission.deleteRelationshipType, AclPermission.updateRelationshipType];

  static defaultDeleteCiPermissions = [
    // new AclPermission(PermissionActionType.CI, PermissionAction.CI__DELETE),
    new AclPermission(PermissionActionType.CI_TYPE, PermissionAction.CI__DELETE),
  ];

  type: PermissionActionType;
  action: PermissionAction;
  typeId?: number;
  constructor(type: PermissionActionType, action: PermissionAction, actionTypeId?: number) {
    this.type = type;
    this.action = action;
    this.typeId = actionTypeId;
  }

  static createCiPermission(action: PermissionAction, ciId: number) {
    return new AclPermission(PermissionActionType.CI, action, ciId);
  }

  static createCiTypePermission(action: PermissionAction, ciTypeId: number) {
    return new AclPermission(PermissionActionType.CI_TYPE, action, ciTypeId);
  }

  static createViewCiPermissions(ciId: number, ciTypeId: number) {
    return [
      ...this.createCiPermissions(PermissionAction.CI__VIEW_BASIC, ciId, ciTypeId),
      ...this.createCiPermissions(PermissionAction.CI__VIEW_ADVANCED, ciId, ciTypeId),
    ];
  }

  static createCiPermissions(actionType: PermissionAction, ciId: number, ciTypeId: number): AclPermission[] {
    return [new AclPermission(PermissionActionType.CI, actionType, ciId), new AclPermission(PermissionActionType.CI_TYPE, actionType, ciTypeId)];
  }

  static cloneCiPermissionsForCiType(ciTypeId: number) {
    return [
      new AclPermission(PermissionActionType.CI_TYPE, PermissionAction.CI__CREATE, ciTypeId),
      new AclPermission(PermissionActionType.CI_TYPE, PermissionAction.CI__VIEW_ADVANCED, ciTypeId),
    ];
  }

  static cloneCiPermissionsForCi(ciId: number, ciTypeId: number) {
    return [
      new AclPermission(PermissionActionType.CI_TYPE, PermissionAction.CI__CREATE, ciTypeId),
      new AclPermission(PermissionActionType.CI, PermissionAction.CI__VIEW_ADVANCED, ciId),
    ];
  }
}
