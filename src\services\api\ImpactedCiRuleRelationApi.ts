import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { CiImpactedByRelationshipInformationResponseDto } from '@models/ChangeAssessment';

export class ImpactedCiRuleRelationApi extends BaseApi {
  static baseUrl = BaseUrl.impactedCiRuleRelationships;

  static fetchImpactedCisByLevelAndCiInChangePlans(level: number, keys: number[]) {
    return BaseApi.postData<CiImpactedByRelationshipInformationResponseDto[]>(`${this.baseUrl}/calculation?level=${level}`, keys);
  }

  static synchronizeImpactedCiRuleRelationShip() {
    return BaseApi.postData<CiImpactedByRelationshipInformationResponseDto[]>(`${this.baseUrl}/sync`);
  }
}
