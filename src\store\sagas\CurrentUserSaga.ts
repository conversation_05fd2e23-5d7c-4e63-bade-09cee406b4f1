import { UserInfoResponse, UsersApi } from '@api/systems/UsersApi';
import { PermissionAction } from '@common/constants/PermissionAction';
import { PermissionActionType } from '@common/constants/PermissionActionType';
import type { ApiResponse } from '@core/api/ApiResponse';
import { currentUserSlice } from '@slices/CurrentUserSlice';
import { call, put, takeEvery } from 'redux-saga/effects';
const showAllCiHasPermission = {
  type: PermissionActionType.CI_TYPE,
  action: PermissionAction.CI_TYPE__VIEW_LIST_CI,
  typeId: 0,
};
function* fetchData() {
  yield put(
    currentUserSlice.actions.setValue({
      isFetching: true,
    }),
  );
  try {
    const response: ApiResponse<UserInfoResponse> = yield call(UsersApi.getMe.bind(UsersApi));
    yield put(
      currentUserSlice.actions.setValue({
        isFetching: false,
        data: {
          email: response.data.email,
          username: response.data.username,
          isActive: response.data.isActive,
          isSuperAdmin: response.data.isSuperAdmin,
          aclPermissions: [...response.data.aclPermissions, ...response.data.aclOtherPermissions, showAllCiHasPermission],
          groups: response.data.groups,
        },
      }),
    );
  } catch (ex) {
    yield put(
      currentUserSlice.actions.setValue({
        isFetching: false,
      }),
    );
  }
}

export function* currentUserSaga() {
  yield takeEvery(currentUserSlice.actions.fetchData, fetchData);
}
