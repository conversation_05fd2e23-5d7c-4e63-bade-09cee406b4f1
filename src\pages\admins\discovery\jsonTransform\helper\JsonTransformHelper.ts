import { ColumnType } from 'kanban-design-system';
import { DataNode, Key } from 'rc-tree/lib/interface';
import { JSONPath } from 'jsonpath-plus';
import { CSSProperties } from 'react';

export type TextHighlighterProps = {
  text: string;
  searchValue: string;
};

export type CustomDataNode = DataNode & {
  value?: string;
  id: string;
  jsonPath: string;
  originalJsonPath?: string;
  identifyAll?: boolean;
  identifyValue?: string;
  columnId?: number;
};

export type JsonTransformProps = {
  jsonTransform: JSONNode[];
  tableMaxHeight?: CSSProperties['maxHeight'];
};

export type JSONNode = {
  [key: string]: any;
};

export type JsonToDataNodeResult = {
  dataNodes: CustomDataNode[];
  keys: string[];
};

export const normalizeJsonValue = (jsonValue: unknown): string => {
  if (typeof jsonValue === 'object') {
    return JSON.stringify(jsonValue);
  }
  return String(jsonValue);
};

export const jsonPathsWithArrayWildcard = (allFields: CustomDataNode[]): CustomDataNode[] => {
  const fieldWithArrayWidcard = allFields.filter((path) => path.jsonPath.includes('[*]'));
  return fieldWithArrayWidcard;
};

export const isMatch = (key: string, jsonValue: unknown, searchValue: string): boolean => {
  const normalizedSearchValue = searchValue.toLowerCase();
  const valueString = normalizeJsonValue(jsonValue).toLowerCase();

  return key.toLowerCase().includes(normalizedSearchValue) || valueString.includes(normalizedSearchValue);
};

export const getAllKeysFromObject = (obj: JSONNode): string[] => {
  const keysSet: Set<string> = new Set();

  function extractKeys(obj: JSONNode): void {
    if (Array.isArray(obj)) {
      obj.forEach((item) => {
        if (typeof item === 'object' && item !== null) {
          extractKeys(item);
        }
      });
    } else if (typeof obj === 'object' && obj !== null) {
      for (const key in obj) {
        keysSet.add(key);
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          extractKeys(obj[key]);
        }
      }
    }
  }

  extractKeys(obj);
  return Array.from(keysSet);
};

export const getSegmentSpecialWithSlash = (input: string): string[] => {
  return input.split('.').filter((part) => part.includes('/'));
};

export const convertToJsonPath = (jsonPath: string, keys: string[]): string => {
  if (!jsonPath.trim()) {
    return jsonPath;
  }

  const segmentsWithSlash = getSegmentSpecialWithSlash(jsonPath);

  for (const segment of segmentsWithSlash) {
    const matchingKey = keys.find((key) => key.includes(segment));

    if (matchingKey) {
      const normalizedKey = `["${matchingKey.replace(/"/g, '\\"')}"]`;
      jsonPath = jsonPath.replace(matchingKey, normalizedKey);
    }
  }

  if (!jsonPath.trim().startsWith('$.')) {
    jsonPath = `$.${jsonPath}`;
  }

  return jsonPath;
};

export const convertJsonToCustomDataNodeList = (jsonString: string): CustomDataNode[] => {
  try {
    const parsedObject = JSON.parse(jsonString);

    return Object.entries(parsedObject).map(([key, value]) => ({
      id: key,
      jsonPath: value as string,
      originalJsonPath: value as string,
      key: key,
    }));
  } catch (error) {
    return [];
  }
};

export const convertCustomDataNodesToJsonNode = (customDataNodes: CustomDataNode[]): JSONNode => {
  return customDataNodes.reduce((jsonNode, customDataNode) => {
    if (customDataNode.key) {
      jsonNode[String(customDataNode.key)] = customDataNode.jsonPath;
    }
    return jsonNode;
  }, {} as JSONNode);
};

export const filterJsonByKeyValue = (jsonTransform: JSONNode[], search: string): JSONNode[] => {
  if (!search.trim()) {
    return jsonTransform;
  }

  const lowerSearch = search.toLowerCase();

  return jsonTransform.filter((node) => {
    return Object.entries(node).some(([key, value]) => {
      return isMatch(key, value, lowerSearch);
    });
  });
};

export const getColumnTypeFromNodes = (nodes: CustomDataNode[]): ColumnType<unknown>[] => {
  return nodes.map((item) => ({
    name: String(item.key),
    title: String(item.key),
  }));
};

export const getColumnTypeFromJson = (jsonArray: JSONNode[]): ColumnType<unknown>[] => {
  try {
    if (!Array.isArray(jsonArray) || jsonArray.length === 0) {
      return [];
    }

    const json = jsonArray[0];

    const jsonObject = typeof json === 'string' ? JSON.parse(json) : json;

    if (typeof jsonObject !== 'object' || jsonObject === null || Array.isArray(jsonObject)) {
      throw new Error('Input JSON must be a valid object');
    }
    return Object.keys(jsonObject).map((key) => ({
      name: key,
      title: key,
    }));
  } catch (error) {
    return [];
  }
};

export const getKeysFromJsonArray = (jsonArray: JSONNode[]): string[] => {
  if (!Array.isArray(jsonArray) || jsonArray.length === 0) {
    return [];
  }

  const firstElement = jsonArray[0];
  return Object.keys(firstElement);
};

const transformJsonNode = (node: JSONNode): JSONNode => {
  const transformedNode: JSONNode = {};
  Object.keys(node).forEach((key) => {
    const value = node[key];
    transformedNode[key] = typeof value === 'object' && value !== null ? JSON.stringify(value) : String(value);
  });
  return transformedNode;
};

export const transformJsonArray = (jsonArray: JSONNode[]): JSONNode[] => {
  return jsonArray.map(transformJsonNode);
};

export const filterKeysByMaxDepth = (keys: Key[], maxDepth: number = 2): Key[] => {
  return keys.filter((key) => {
    const levels = key.toString().split('.').length;
    return levels <= maxDepth;
  });
};

export const inValidCustomDataNodes = (nodes: CustomDataNode[], jsonData: JSONNode): boolean => {
  const keysSet = new Set<string>();
  for (const node of nodes) {
    const key = String(node.key);
    if (!key || keysSet.has(key)) {
      return true; // Invalid if key is empty or duplicate
    }
    keysSet.add(key);

    // check jsonPath is required
    if (!node.jsonPath) {
      return true;
    }
    //  Check for value is not object
    if (checkJsonByJsonPathIsObject(node.jsonPath, jsonData)) {
      return true; // Invalid if jsonPath is not valid
    }
  }

  // All validations passed
  return false;
};

export const checkJsonByJsonPathIsObject = (jsonPath: string, jsonData: JSONNode): boolean => {
  if (jsonPath.startsWith('$.')) {
    jsonPath = jsonPath.slice(2);
  }

  const valueJson = JSONPath({ path: jsonPath, json: jsonData });
  const value = valueJson.length ? (valueJson.length === 1 ? valueJson[0] : valueJson) : 'undefined';

  const isObjectRecursive = (val: any): boolean => {
    if (typeof val !== 'object' || val === null) {
      return false;
    }
    if (!Array.isArray(val)) {
      return true;
    }
    if (Array.isArray(val)) {
      if (val.length === 0) {
        return false;
      }
      return val.every((item) => isObjectRecursive(item));
    }
    return false;
  };

  return isObjectRecursive(value);
};
// export const validateJsonPath = (path: string): boolean => {
//   try {
//     JSONPath({ path, json: {}, wrap: false });
//     console.info(path, JSONPath({ path, json: {}, wrap: false }));
//     return true;
//   } catch (error) {
//     return false;
//   }
// };

// export const validateJsonPath = (jsonPath: string): boolean => {
//   if (typeof jsonPath !== 'string' || jsonPath.trim() === '') {
//     return false;
//   }

//   const jsonPathRegex = /^([\w\-$@\\/]+(\[(\d+|\*)\])?(\.[\w\-$@\\/]+(\[(\d+|\*)\])?)*)$/;

//   return jsonPathRegex.test(jsonPath.trim());
// };
export const mapToJsonPaths = (paths: string[]): string[] => {
  return paths.map((path) => {
    if (!path.startsWith('$')) {
      path = `$.${path}`;
    }
    return path;
  });
};

export const extractValuesWithJsonPath = (obj: JSONNode, jsonPaths: string[]): JSONNode => {
  const result: JSONNode = {};

  jsonPaths.forEach((path) => {
    try {
      const value = JSONPath({ path, json: obj });
      result[path] = value.length === 1 ? value[0] : value.length > 1 ? value : 'undefined';
    } catch (error) {
      result[path] = undefined;
    }
  });
  return result;
};

export const extractValuesWithFields = (obj: JSONNode, fields: CustomDataNode[]): JSONNode => {
  const result: JSONNode = {};

  fields.forEach((field) => {
    try {
      let jsonPath = field.jsonPath;
      if (jsonPath.startsWith('$.')) {
        jsonPath = jsonPath.slice(2);
      }
      const value = JSONPath({ path: jsonPath, json: obj });
      //jp.query(obj, field.jsonPath);
      result[String(field.key)] = value.length === 1 ? value[0] : value.length > 1 ? value : 'undefined';
    } catch (error) {
      result[String(field.key)] = undefined;
    }
  });
  return result;
};

export const findJSONPathFromNode = (nodeKey: string): string => {
  return `{{ $json.${nodeKey} }}`;
};
