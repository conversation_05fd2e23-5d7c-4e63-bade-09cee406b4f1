import { Breadcrumbs, Anchor } from '@mantine/core';
import { IconHome } from '@tabler/icons-react';
import { match } from 'path-to-regexp';
import React, { useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

export type CrumbData = { title: string; href: string; isLast?: boolean };
export type UrlBaseCrumbData = { [routerUtilUrlId: string]: CrumbData };
export enum BreadcrumbReplacedType {
  PARTIAL = 'PARTIAL',
  ALL = 'ALL',
}
export type BreadcrumData = {
  data: UrlBaseCrumbData;
  type: BreadcrumbReplacedType;
  disabled: boolean;
};

export type BreadCrumbProps = { orderFullCustomPaths?: UrlBaseCrumbData; locationCustomPaths?: UrlBaseCrumbData; disabled?: boolean };
const AnchorBtn: React.FC<CrumbData> = ({ href, isLast, title }) => {
  const navigate = useNavigate();
  return (
    <Anchor
      onClick={() => {
        if (navigate) {
          navigate(href);
        }
      }}
      fw={isLast ? 'bold' : 'lighter'}>
      {title}
    </Anchor>
  );
};
const mapLocations = {
  CI_TYPES: { title: 'CI types', path: 'ci-types' },
  CIS: { title: 'CIs', path: 'cis' },
  IMPORT: { title: 'Import', path: 'import' },
  BUSINESS_VIEWS: { title: 'Business Views', path: 'business-views' },
  CI_MANAGEMENTS: { title: 'CIs Management', path: 'ci-managements' },
  CI_RELATIONSHIP: { title: 'CI Relationships', path: 'ci-relationship' },
  SERVICE_MAPPING: { title: 'Service Mappings', path: 'service-mapping' },
  CHANGE_ASSESSMENTS: { title: 'Change Assessments', path: 'change-assessments' },
  INCIDENT_REQUESTS: { title: 'Incident Requests', path: 'incident-requests' },
  ADMINS: { title: 'Admins', path: 'admins' },
  ROLES: { title: 'Roles', path: 'roles' },
  RELATIONSHIP_TYPES: { title: 'Relationship Types', path: 'relationship-types' },
  NOTIFICATION_TEMPLATES: { title: 'Notification Templates', path: 'notification-templates' },
  IMPACTED_RULES: { title: 'Impacted Rules', path: 'impacted-rules' },
  OUTGOING_MAIL_CONFIGS: { title: 'Outgoing Mail Configurations', path: 'outgoing-mail-configs' },
  BUSINESS_DOMAINS: { title: 'Business Architecture Management', path: 'business-domains' },
  BUSINESS_DOMAINS_AGGREGATE: { title: 'Business Architecture Management Aggregate', path: 'aggregate' },
  INTEGRATIONS: { title: 'Integrations', path: 'integrations' },
  SOURCE_DATAS: { title: 'Data Sources', path: 'source-datas' },
  STAGGING_TABLES: { title: 'Stagging Tables', path: 'stagging-tables' },
  TRANSFORM_MAPS: { title: 'Transform Maps', path: 'transform-maps' },
  CI_IDENTIFIER_RULES: { title: 'CI Identifiers', path: 'ci-identifier-rules' },
  CI_RECONCILIATION_RULES: { title: 'CI Reconcile', path: 'ci-reconciliation-rules' },
  CI_ABSENT_RULES: { title: 'CI Absent', path: 'ci-absent-rules' },
  DISCOVERY_PREVIEW_DATA_CIS: { title: 'Preview Discovery', path: 'discovery-preview-data-cis' },
  JOB_CONFIGS: { title: 'Schedule Jobs', path: 'job-discovery-configs' },
  USERS: { title: 'User Settings', path: 'users' },
  GROUPS: { title: 'Group Settings', path: 'groups' },
  SYSTEMS: { title: 'Systems', path: 'systems' },
  PARAMETER: { title: 'Parameters', path: 'parameter' },
  SUPERIORS: { title: 'Superiors', path: 'superiors' },
  SQL_EXECUTION: { title: 'SQL Execution', path: 'sql-execution' },
  UNKNOWN: { title: 'Unknown', path: 'unknown' },
} as const;
type MapLocationsType = typeof mapLocations;
type PathType = MapLocationsType[keyof MapLocationsType]['path'];

type LocationMap = Record<PathType, { type: string; title: string; path: string }>;

const transformedMap: LocationMap = Object.entries(mapLocations).reduce((acc, [key, value]) => {
  acc[value.path] = { type: key, title: value.title, path: value.path };
  return acc;
}, {} as LocationMap);

/**
 * disabled: turn on off brc
   orderFullCustomPaths: thay doi toan bo bread crumbs =  input orderFullCustomPaths
   locationCustomPaths:
      - thay doi 1 vai breadcrumb dua theo format, danh sach format trong input  locationCustomPaths
 */

export const BreadcrumbComponent: React.FC<BreadCrumbProps> = ({ disabled, locationCustomPaths, orderFullCustomPaths }) => {
  const location = useLocation();
  const navigate = useNavigate();
  //location base / custom base
  const locationPath: string[] = location.pathname.split('/').filter((x) => x);

  const resolveLocationPaths = useMemo((): CrumbData[] => {
    const result: CrumbData[] = [];
    let pathIndex = 0;

    while (pathIndex < locationPath.length) {
      const remainingPathParts = locationPath.slice(pathIndex);
      const remainingPath = `/${remainingPathParts.join('/')}`;

      let matchedCustomKey: string | undefined = undefined;
      let matchedSegmentsCount = 0;

      if (locationCustomPaths) {
        for (const customKey of Object.keys(locationCustomPaths)) {
          const matcher = match(customKey, { decode: decodeURIComponent });
          const matched = matcher(remainingPath);

          if (matched) {
            matchedCustomKey = customKey;
            matchedSegmentsCount = customKey.split('/').filter(Boolean).length;

            const customData = locationCustomPaths[customKey];

            result.push({
              title: customData.title,
              href: customData.href || customKey,
            });

            if (matchedSegmentsCount === remainingPathParts.length) {
              return result;
            }

            pathIndex += matchedSegmentsCount;
            break;
          }
        }

        if (matchedCustomKey) {
          continue;
        }
      }

      const currentSegment = locationPath[pathIndex] as PathType;
      const currentFullPath = `/${locationPath.slice(0, pathIndex + 1).join('/')}`;
      const definedPathObj = transformedMap[currentSegment];

      if (definedPathObj) {
        result.push({
          title: definedPathObj.title,
          href: currentFullPath,
        });
      }
      // else {
      //   result.push({
      //     title: mapLocations.UNKNOWN.title,
      //     href: currentFullPath,
      //   });
      // }

      pathIndex++;
    }

    return result;
  }, [locationPath, locationCustomPaths]);

  const renderBreadCrumbs = useMemo(() => {
    const lstPath = orderFullCustomPaths ? Object.values(orderFullCustomPaths) : resolveLocationPaths;

    const breadCrumbs = lstPath.map((onePath, pathIndex) => {
      return <AnchorBtn href={onePath.href} key={pathIndex} title={onePath.title} isLast={pathIndex === lstPath.length - 1} />;
    });

    return (
      breadCrumbs.length > 0 && [
        <Anchor
          onClick={() => {
            navigate('/');
          }}
          key={-1}>
          <IconHome />
        </Anchor>,
        ...breadCrumbs,
      ]
    );
  }, [navigate, orderFullCustomPaths, resolveLocationPaths]);

  return (
    !disabled && (
      <Breadcrumbs mb={'xl'} separator='›'>
        {renderBreadCrumbs}
      </Breadcrumbs>
    )
  );
};
