import React, { forwardRef, useEffect, useImperative<PERSON><PERSON>le, useState } from 'react';
import { useForm, useFieldArray, Controller } from 'react-hook-form';
import { Paper, Box, Flex, ComboboxItem } from '@mantine/core';
import { IconTrash, IconRefresh } from '@tabler/icons-react';
import { KanbanCheckbox, KanbanInput, KanbanSelect, KanbanText } from 'kanban-design-system';
import {
  checkJsonByJsonPathIsObject,
  convertCustomDataNodesToJsonNode,
  convertToJsonPath,
  CustomDataNode,
  JSONNode,
  jsonPathsWithArrayWildcard,
} from '../helper/JsonTransformHelper';
import styles from './JsonConfigView.module.scss';
import { NotificationError } from '@common/utils/NotificationUtils';
import droppedItemsFormstyles from './DroppedItemsForm.module.scss';
import { useDroppedItemsRefs } from '@context/DragScrollContext';
import { useDataTransform } from '@context/DataTransformContext';
import { DataTransformActionType } from '@common/constants/DataTransformActionType';
import { useJsonTransformContext } from '@context/JsonTransformContext';
import { JsonTransformActionType } from '@common/constants/JsonTransformActionType';
import { DiscoverySourceDataApi } from '@api/discovery/DiscoverySourceDataApi';
import { DiscoverySourceDataModel } from '@models/discovery/DiscoverySourceData';
import { FormatTypeEnum } from '@common/constants/FormatTypeEnum';
import { DragDropContext, Draggable, Droppable, DropResult } from '@hello-pangea/dnd';
import { useDraggableInPortal } from '@common/hooks/useDraggableInPortal';
export type DroppedItemsFormMethods = {
  handleAddField: (newFields: CustomDataNode[]) => void;
  submitForm: () => void;
};

export type DroppedItemsFormProps = {
  sourceId: number;
};

function containsArrayElement(path: string): boolean {
  const regex = /\[\d+\]|\[\*\]/;
  return regex.test(path);
}

const replaceSubstring = (original: string, replacement: string): string => {
  const regex = new RegExp(replacement.replace(/\[\*\]/g, '\\[(\\d+|\\*)\\]'));
  return original.replace(regex, replacement);
};

const simplifyPathsToComboboxDatas = (path: string): ComboboxItem[] => {
  if (!containsArrayElement(path)) {
    return [];
  }
  const parts = path.split('.');
  const results = [];

  let currentPath = '';

  for (const part of parts) {
    if (part !== '$') {
      const updatedPart = part.replace(/\[\d+\]/g, '[*]');
      currentPath = currentPath ? `${currentPath}.${updatedPart}` : updatedPart;
      if (/\[\*\]/.test(updatedPart)) {
        results.push({ value: currentPath, label: currentPath });
      }
    }
  }
  return results;
};

const jsonPathWithWildCardMaxSize = 20;

export const defaultDiscoverySourceDataModel: DiscoverySourceDataModel = {
  id: 0,
  name: '',
  formatType: FormatTypeEnum.JSON,
  sourceId: 0,
  discoveryStagingId: 0,
  selectedFields: [],
};

const DroppedItemsForm = forwardRef<DroppedItemsFormMethods, DroppedItemsFormProps>(({ sourceId }, ref) => {
  const renderDraggable = useDraggableInPortal();
  const [allFields, setAllFields] = useState<CustomDataNode[]>([]);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const { dispatch, state: dataTransform } = useDataTransform();
  const jsonData = dataTransform.jsonData;
  const { highlightedItemIds, highlightItem, registerRef } = useDroppedItemsRefs();
  // const { state: keysState } = useKeysOfJsonData();
  // const { dispatch: isValidFormJsonsDispatch } = useIsValidFormJson();
  const { dispatch: jsonTransformDispatch, state: jsonTransformState } = useJsonTransformContext();
  const keysOfJsonData = jsonTransformState?.keys;

  const { control, handleSubmit, watch } = useForm<{ items: CustomDataNode[] }>({
    defaultValues: { items: [] },
    mode: 'onSubmit',
  });

  const { fields, remove, replace, update } = useFieldArray({
    control,
    name: 'items',
  });

  const watchedItems = watch('items');

  useEffect(() => {
    const updatedFields = allFields.map((item) => ({
      ...item,
      jsonPath: convertToJsonPath(item.jsonPath || '', keysOfJsonData),
    }));
    replace(updatedFields);
  }, [allFields, keysOfJsonData, replace]);

  useEffect(() => {
    dispatch({
      type: DataTransformActionType.UPDATE_COLUMN_DATAS_FOR_INPUT,
      payload: watchedItems.map(({ jsonPath, key }) => ({
        id: String(key),
        key,
        jsonPath,
        originalJsonPath: jsonPath,
      })),
    });
    jsonTransformDispatch({ type: JsonTransformActionType.UPDATE_FORM_JSON, payload: false });
  }, [dispatch, jsonTransformDispatch, watchedItems]);

  const onTestTransformJson = (columnDatas: CustomDataNode[], isJsonStatic: boolean) => {
    dispatch({ type: DataTransformActionType.UPDATE_IS_EXECUTING, payload: true });
    const model: DiscoverySourceDataModel = { ...defaultDiscoverySourceDataModel };
    model.discoveryStagingStructureJson = JSON.stringify(convertCustomDataNodesToJsonNode(columnDatas));
    model.sourceId = sourceId;
    if (isJsonStatic) {
      model.jsonStatic = jsonData;
    }
    DiscoverySourceDataApi.executionTestTransformJson(model)
      .then((response) => {
        if (response.status === 200) {
          const jsonTransform: JSONNode[] = response.data.map((item) => JSON.parse(item));
          dispatch({ type: DataTransformActionType.UPDATE_JSON_TRANSFORM, payload: jsonTransform });
          dispatch({ type: DataTransformActionType.UPDATE_IS_EXECUTING, payload: false });
        }
      })
      .catch(() => {});
  };

  const onSubmit = (data: { items: CustomDataNode[] }) => {
    const fields = data.items;

    if (fields.length === 0) {
      NotificationError({ message: 'Fields is not empty' });
      jsonTransformDispatch({ type: JsonTransformActionType.UPDATE_FORM_JSON, payload: false });
      return;
    }
    const fieldWithArrayWidcard = jsonPathsWithArrayWildcard(fields);
    const keys = fieldWithArrayWidcard.map((node) => String(node.key).toLowerCase());
    if (fieldWithArrayWidcard.length > jsonPathWithWildCardMaxSize) {
      NotificationError({
        message: `To ensure optimal performance, the system only supports a maximum of ${jsonPathWithWildCardMaxSize} array parsing fields. Please make the necessary adjustments.`,
      });
      highlightItem(keys);
      return;
    }

    if (!dataTransform.isJsonStatic) {
      jsonTransformDispatch({ type: JsonTransformActionType.UPDATE_FORM_JSON, payload: true });
      onTestTransformJson(fields, false);
    } else {
      onTestTransformJson(fields, true);
    }

    dispatch({
      type: DataTransformActionType.UPDATE_COLUMN_DATAS_FOR_OUTPUT,
      payload: fields.map(({ jsonPath, key }) => ({
        id: String(key),
        key,
        jsonPath,
      })),
    });

    // setTimeout(() => {
    //   dispatch({ type: DataTransformActionType.UPDATE_IS_EXECUTING, payload: false });
    // }, 1000);
  };

  const handleAddField = (newFields: CustomDataNode[]) => {
    setAllFields((prevFields) => [...prevFields, ...newFields]);
  };

  useImperativeHandle(ref, () => ({
    handleAddField,
    submitForm: () => handleSubmit(onSubmit)(),
  }));

  const validateKey = (value: string, fields: CustomDataNode[], index: number) => {
    const valueFormat = value ? value.trim().toLowerCase() : '';
    const isUnique = fields.filter((_, i) => i !== index).every((item) => String(item.key).trim().toLowerCase() !== valueFormat);
    return isUnique || 'Name must be unique';
  };

  const handleInputChange = (index: number, field: keyof CustomDataNode, value: string) => {
    const updatedItems = [...allFields];
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: value,
    };
    setAllFields(updatedItems);
  };

  const handleDragEnd = (result: DropResult) => {
    const { destination, source } = result;
    if (!destination) {
      return;
    }
    const newDatas = [...watchedItems];
    const [removed] = newDatas.splice(source.index, 1);
    newDatas.splice(destination.index, 0, removed);
    replace(newDatas);
    setAllFields(newDatas);
  };

  const columnOfStagingTables = watchedItems.map((item, index) => (
    <Draggable key={item.id} index={index} draggableId={String(item.id)}>
      {renderDraggable((provided) => (
        <Paper
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          key={item.id}
          ref={(el) => {
            provided.innerRef(el);
            registerRef(item.key ? String(item.key).toLowerCase() : String(item.id).toLowerCase(), el);
          }}
          className={highlightedItemIds.includes(String(item.key).toLowerCase()) ? droppedItemsFormstyles['highlight'] : ''}
          p='xs'
          mb='sm'
          radius='sm'
          shadow='sm'
          withBorder
          bg={'var(--mantine-color-gray-0)'}
          onMouseEnter={() => {
            setHoveredItem(item.id);
          }}
          onMouseLeave={() => {
            setHoveredItem(null);
          }}>
          <Box>
            {/* Name Field */}
            <Controller
              name={`items.${index}.key`}
              control={control}
              rules={{
                required: 'Name is required',
                validate: (value) => validateKey(String(value), fields, index),
              }}
              render={({ field, fieldState }) => (
                <KanbanInput
                  label='Name'
                  placeholder='Enter name'
                  maxLength={2000}
                  {...field}
                  rightSection={
                    hoveredItem === item.id ? (
                      <span
                        className={styles['json-field-icon']}
                        onClick={(e) => {
                          e.stopPropagation();
                          update(index, { ...watchedItems[index], key: '' });
                          const updatedFields = allFields.map((field) => (field.key === watchedItems[index].key ? { ...field, key: '' } : field));
                          setAllFields(updatedFields);
                        }}>
                        <IconRefresh size='1rem' />
                      </span>
                    ) : null
                  }
                  value={String(item.key)}
                  error={fieldState.error?.message}
                  onChange={(e) => {
                    field.onChange(e);
                    handleInputChange(index, 'key', e.target.value);
                  }}
                  onBlur={(e) => {
                    e.stopPropagation();
                    field.onBlur();
                    handleInputChange(index, 'key', e.target.value.trim());
                  }}
                />
              )}
            />
            <Controller
              name={`items.${index}.jsonPath`}
              control={control}
              rules={{
                required: 'JsonPath is required',
                validate: (value) => {
                  if (!value.startsWith('$.')) {
                    return 'JsonPath start with $.';
                  }
                  if (checkJsonByJsonPathIsObject(value, jsonData)) {
                    return `Value can't be Object`;
                  }
                  return true;
                },
              }}
              render={({ field, fieldState }) => (
                <KanbanInput
                  label='JsonPath'
                  placeholder='Enter jsonPath'
                  maxLength={2000}
                  {...field}
                  error={fieldState.error?.message}
                  value={item.jsonPath}
                  disabled={!!item.identifyValue && item.identifyAll}
                  onChange={(e) => {
                    field.onChange(e);
                    const value = e.target.value;
                    const updatedFields = allFields.map((field) =>
                      field.id === watchedItems[index].id
                        ? {
                            ...field,
                            originalJsonPath: value,
                            jsonPath: value,
                          }
                        : field,
                    );

                    setAllFields(updatedFields);
                    // handleInputChange(index, 'jsonPath', e.target.value);
                  }}
                  onBlur={(e) => {
                    e.stopPropagation();
                    field.onBlur();
                    handleInputChange(index, 'jsonPath', e.target.value.trim());
                  }}
                />
              )}
            />
          </Box>
          <Flex justify='space-between' align='center' gap={3}>
            {containsArrayElement(item.jsonPath) ? (
              <>
                <Controller
                  name={`items.${index}.identifyAll`}
                  control={control}
                  render={() => (
                    <KanbanCheckbox
                      size='xs'
                      m={0}
                      label={
                        <KanbanText size='xs' c={'dimmed'} m={0} lineClamp={1}>
                          Identify all similar fields in this array
                        </KanbanText>
                      }
                      checked={item.identifyAll}
                      onChange={(event) => {
                        event.stopPropagation();
                        // update(index, {
                        //   ...watchedItems[index],
                        //   identifyAll: event.currentTarget.checked,
                        // });
                        const isChecked = event.currentTarget.checked;
                        const updatedFields = allFields.map((field) =>
                          field.id === watchedItems[index].id
                            ? {
                                ...field,
                                identifyAll: isChecked,
                                ...(isChecked ? {} : { jsonPath: item.originalJsonPath ?? '', identifyValue: '' }),
                              }
                            : field,
                        );

                        setAllFields(updatedFields);
                      }}
                    />
                  )}
                />
                {item.identifyAll && (
                  <Box>
                    <KanbanSelect
                      searchable
                      allowDeselect={false}
                      value={item.identifyValue}
                      autoChangeValueByOptions={false}
                      data={simplifyPathsToComboboxDatas(item.jsonPath)}
                      onChange={(e) => {
                        // update(index, {
                        //   ...watchedItems[index],
                        //   identifyValue: e ?? '',
                        // });

                        const updatedFields = allFields.map((field) =>
                          field.id === watchedItems[index].id
                            ? { ...field, identifyValue: e ?? '', jsonPath: replaceSubstring(item.originalJsonPath ?? '', e ?? '') }
                            : field,
                        );
                        const fieldWithArrayWidcard = jsonPathsWithArrayWildcard(updatedFields);
                        const keys = fieldWithArrayWidcard
                          .filter((node) => String(node.key).toLowerCase() !== String(item.key).toLowerCase())
                          .map((node) => String(node.key).toLowerCase());
                        if (fieldWithArrayWidcard.length > jsonPathWithWildCardMaxSize) {
                          NotificationError({
                            message: `To ensure optimal performance, the system only supports a maximum of ${jsonPathWithWildCardMaxSize} array parsing fields. Please make the necessary adjustments.`,
                          });
                          highlightItem(keys);
                          const updatedFields = allFields.map((field) =>
                            field.id === watchedItems[index].id
                              ? {
                                  ...field,
                                  identifyAll: false,
                                  jsonPath: item.originalJsonPath ?? '',
                                  identifyValue: '',
                                }
                              : field,
                          );

                          setAllFields(updatedFields);
                        } else {
                          setAllFields(updatedFields);
                        }
                      }}></KanbanSelect>
                  </Box>
                )}
              </>
            ) : (
              <div></div>
            )}

            <Box>
              <Flex>
                <span
                  className={styles[hoveredItem === item.id ? 'json-field-icon-delete-active' : 'json-field-icon-delete-inactive']}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (hoveredItem === item.id) {
                      setAllFields((prev) => prev.filter((field) => field.id !== item.id));
                      remove(index);
                      // onUpdateDroppedItems(droppedItems.filter((element) => element.key !== item.key));
                    }
                  }}>
                  <IconTrash size='1rem' color='red' />
                </span>
              </Flex>
            </Box>
            {/* ) : (
          <span></span>
        )} */}
          </Flex>
        </Paper>
      ))}
    </Draggable>
  ));

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <Droppable droppableId='structure-table'>
        {(provided) => (
          <form {...provided.droppableProps} ref={provided.innerRef}>
            {columnOfStagingTables}
            {provided.placeholder}
          </form>
        )}
      </Droppable>
    </DragDropContext>
  );
});
DroppedItemsForm.displayName = 'DroppedItemsForm';
export default DroppedItemsForm;
