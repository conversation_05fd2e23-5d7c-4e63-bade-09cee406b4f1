import type { ApiResponseDataBase } from '@core/api/ApiResponse';
import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
import type { ServiceMapingDetailsModel, ServiceMapingModel } from '@models/ServiceMapping';
import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';

export type ServiceMapingModelResponse = ApiResponseDataBase & ServiceMapingModel;
export type ServiceMapingModelPagingResponse = PaginationResponseModel<ServiceMapingModelResponse>;
export class ServiceMappingApi extends BaseApi {
  static baseUrl = BaseUrl.serviceMaps;

  static getAll(pagination: PaginationRequestModel<ServiceMapingModelResponse>) {
    return BaseApi.postData<ServiceMapingModelPagingResponse>(`${this.baseUrl}/filter`, pagination);
  }

  static getById(id: number) {
    return BaseApi.getData<ServiceMapingModelResponse>(`${this.baseUrl}/${id}`);
  }

  static getByCiId(ciId: number) {
    return BaseApi.getData<ServiceMapingModelResponse>(`${this.baseUrl}?ciId=${ciId}`);
  }

  static getAllByStartCiId(startCiId: number) {
    return BaseApi.getData<ServiceMapingModelResponse[]>(`${this.baseUrl}?startCiId=${startCiId}`);
  }

  static getDetailsById(id: number) {
    return BaseApi.getData<ServiceMapingDetailsModel>(`${this.baseUrl}/${id}/detail`);
  }

  static saveMap(data: ServiceMapingModel) {
    return BaseApi.postData<ServiceMapingModelResponse>(this.baseUrl, data);
  }
}
