import type { CiManagementResponse } from '@api/CiManagementApi';
import { StatusCiManagement } from '@common/constants/CiManagement';
import { Badge } from '@mantine/core';
import React from 'react';
import StatusComponent from './StatusComponent';
import { KanbanText } from 'kanban-design-system';

type LeftHeaderViewProps = {
  dataView: CiManagementResponse | undefined;
  isOwnerView: boolean;
  isUpdate: boolean;
  status: StatusCiManagement;
  verifyUser: string | undefined;
};

export const LeftHeaderView = (props: LeftHeaderViewProps) => {
  const { dataView, isOwnerView, isUpdate, status, verifyUser } = props;

  const renderBadge = () => {
    if (!dataView) {
      return null;
    }
    return (
      <Badge mt='5' color={isUpdate ? 'yellow' : 'green'}>
        {dataView.action}
      </Badge>
    );
  };

  const renderUserApproval = () => {
    if (!verifyUser || StatusCiManagement.DRAFT === status || !isOwnerView) {
      return null;
    }
    return <KanbanText mt={3}>User Approval: {verifyUser}</KanbanText>;
  };

  const renderOwnerInfo = () => {
    if (isOwnerView || !dataView?.owner) {
      return null;
    }
    return <KanbanText mt={3}>Owner: {dataView.owner}</KanbanText>;
  };

  return (
    <>
      <>
        {renderBadge()}
        <StatusComponent value={status} />
        {renderUserApproval()}
        {renderOwnerInfo()}
      </>
    </>
  );
};

export default LeftHeaderView;
