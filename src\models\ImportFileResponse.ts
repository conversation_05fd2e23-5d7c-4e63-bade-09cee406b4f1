import type { CiManagementResponse } from '@api/CiManagementApi';

export type ImportFileResponse = {
  totalRecordSuccess: number;
  totalRecordFail: number;
  detailErrors: DetailInfoRowImportDTO[];
  urlFile: string;
};

export type ImportFileCiResponse = ImportFileResponse & {
  ciTempEntities: CiManagementResponse[];
};
export type LoadFileResponse = {
  excelFields: string[];
  totalRowValue: number;
  templateFile: boolean;
};

export type DetailInfoRowImportDTO = {
  line: number;
  type: DetailInfoType;
  detail: string;
};

export type FileUploadRequest = {
  file: File;
  mappingFields: string;
  ciTypeId: number;
  formatDate: string;
  rowImportSelected?: number[];
  importAll?: boolean;
  finalize?: boolean;
  isImportOldVersion?: boolean;
};

export type ImportFileSuccessResponse = ImportFileResponse & LoadFileResponse;

// export type ImportFileError = {
//     numberRow: number;
//     lineError: string;
//     detailError: string;
// }

export enum DetailInfoType {
  ERROR = 'ERROR',
  SUCCESS = 'SUCCESS',
  WARNING = 'WARNING',
}
