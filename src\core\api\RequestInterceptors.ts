import type { InternalAxiosRequestConfig } from 'axios';
import KeycloakService from 'core/auth/Keycloak';
import { v4 as uuid } from 'uuid';
const keycloakService = KeycloakService;

export const requestIdInterceptor = (config: InternalAxiosRequestConfig<any>) => {
  config.headers = config.headers ?? {};
  config.headers.set('Request-id', uuid());
  return config;
};
export const keycloakAuthenticationInterceptor = (config: InternalAxiosRequestConfig<any>) => {
  config.headers = config.headers ?? {};
  const token = keycloakService.getToken();
  config.headers.Authorization = `Bearer ${token}`;
  return config;
};
