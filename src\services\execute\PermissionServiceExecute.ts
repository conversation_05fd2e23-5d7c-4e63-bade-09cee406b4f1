import type { PermissionAction } from '@common/constants/PermissionAction';
import type { PermissionActionType } from '@common/constants/PermissionActionType';
import type { ConfigItemPermissionOtherDto, ConfigItemPermissionOtherModel } from '@models/ConfigItemTypePermission';

export class PermissionServiceExecute {
  static convertPermissionOtherDtoToModel = (otherPermissions: ConfigItemPermissionOtherDto[]): ConfigItemPermissionOtherModel[] => {
    const grouped = otherPermissions.reduce(
      (acc, item) => {
        if (!acc[item.type]) {
          acc[item.type] = [];
        }
        acc[item.type].push(item.action);
        return acc;
      },
      {} as Record<PermissionActionType, PermissionAction[]>,
    );

    return Object.keys(grouped).map((type) => ({
      actions: grouped[type as PermissionActionType],
      type: type as PermissionActionType,
    }));
  };
}

export default PermissionServiceExecute;
