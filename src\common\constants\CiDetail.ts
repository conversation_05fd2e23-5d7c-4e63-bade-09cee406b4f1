export enum CiDetailScreenType {
  INFO = 'INFO',
  RELATIONSHIPS = 'RELATIONSHIPS',
  HISTORY = 'HISTORY',
  REQUEST = 'REQUEST',
  DISCOVERY = 'DISCOVERY',
}

export enum CiDetailSubTabType {
  // tab relationship
  LISTVIEW = 'LISTVIEW',
  GRAPHVIEW = 'GRAPHVIEW',
  IMPACTMAP = 'IMPACTMAP',
  //tab request
  SERVICEREQUEST = 'SERVICEREQUEST',
  INCIDENT = 'INCIDENT',
  CHANGEREQUEST = 'CHANGEREQUEST',
  PROBLEM = 'PROBLEM',
}
export enum CiRequestTypeEnum {
  CHANGE = 'CHANGE',
  INCIDENT = 'INCIDENT',
}

export enum ServiceAttachedTypeEnum {
  MANUAL = 'MANUAL',
  CALCULATED = 'CALCULATED',
}

export enum ImpactedServiceActEnum {
  ADD = 'ADD',
  CALCULATION = 'CALCULATION',
  REMOVE = 'REMOVE',
}

export enum ActionTypeEnum {
  EDIT = 'EDIT',
  CLONE = 'CLONE',
  VIEW = 'VIEW',
}

export enum ScreenTypeEnum {
  FLAGGED_CIS = 'FLAGGED_CIS',
}
