
.customInputStyle{
  //common ci infos
  //ci type attributes
  //ci custom attributes
  // border-left: solid;
  // border-radius: unset;
  border-top:  0.5px solid var(--mantine-color-gray-2); 
  border-left:  0.5px solid var(--mantine-color-gray-2); 
  border-radius: var(--mantine-radius-default);

  

  padding-left: var(--mantine-spacing-xs);
  display: grid;
  align-items: left;

  //3 dong 2 cot : cot 1 : lable+ description, cot 2: cho input value + error
  grid-template-columns: 0.5fr 0.5fr;
  grid-template-rows: auto auto; 
  grid-column-gap: var(--mantine-spacing-xs);



  label{
    div{
      justify-content: flex-start;
    }
  }

  //css cho div wrap input
  div{
    textarea:disabled{
      background-color: var(--mantine-color-white);
      border: none;
      padding: 0;
      color: var(--mantine-color-black);
      cursor: default;
      opacity: unset;
    }
    input:disabled{
      background-color: var(--mantine-color-white);
      border: none;
      padding: 0;
      color: var(--mantine-color-black);
      cursor: default;
      opacity: unset;
    }

  }
}
