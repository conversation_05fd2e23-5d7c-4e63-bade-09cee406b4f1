import { ComboboxItem } from '@mantine/core';

export enum JobTypeEnum {
  GET_DISCOVERY_DATA = 'GET_DISCOVERY_DATA',
  CLEAR_DATA_STAGING_TABLE = 'CLEAR_DATA_STAGING_TABLE',
}

export const getTextJobTypeEnum = (value: JobTypeEnum | undefined): string => {
  if (!value) {
    return '';
  }
  const map: Record<JobTypeEnum, string> = {
    [JobTypeEnum.GET_DISCOVERY_DATA]: 'Get Discovery Data',
    [JobTypeEnum.CLEAR_DATA_STAGING_TABLE]: 'Clear Data In Staging Table',
  };
  return map[value] || '';
};

export const getComboboxJobTypeEnum = (values: JobTypeEnum[]): ComboboxItem[] => {
  const comboboxItems: ComboboxItem[] = [];
  values.forEach((value) => {
    comboboxItems.push({
      value: value,
      label: getTextJobTypeEnum(value),
    });
  });
  return comboboxItems;
};
