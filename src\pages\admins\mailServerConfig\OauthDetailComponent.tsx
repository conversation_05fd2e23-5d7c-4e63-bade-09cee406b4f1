import { SimpleGrid } from '@mantine/core';
import type { AuthenticationDetailDto, OutgoingMailConfigDto } from '@models/OutgoingMail';
import { KanbanInput, KanbanTitle } from 'kanban-design-system';
import React from 'react';

export type AuthenticationDetailProps = {
  detail: AuthenticationDetailDto;
  setDetail: (val: AuthenticationDetailDto) => void;
};

export const OauthDetailComponent: React.FC<AuthenticationDetailProps> = ({ detail, setDetail }) => {
  const handleChange = <K extends keyof OutgoingMailConfigDto, V extends OutgoingMailConfigDto[K]>(field: K, value: V) => {
    const updateData: OutgoingMailConfigDto = {
      ...detail,
      [field]: value,
    };
    setDetail(updateData);
  };

  return (
    <>
      <KanbanTitle order={3}>Authorization Server Details</KanbanTitle>
      <SimpleGrid cols={2}>
        <SimpleGrid cols={2}>
          <KanbanInput
            label='Client ID'
            value={detail.oauthClientId ?? ''}
            onChange={(event) => handleChange('oauthClientId', event.target.value)}
            required
          />
          <KanbanInput
            label='Client Secret'
            value={detail.oauthClientSecret ?? ''}
            onChange={(event) => {
              handleChange('oauthClientSecret', event.target.value);
            }}
            required
          />
          <KanbanInput
            label='Authorization URL'
            value={detail.oauthAuthorizationUrl ?? ''}
            onChange={(event) => {
              handleChange('oauthAuthorizationUrl', event.target.value);
            }}
            required
          />
          <KanbanInput
            label='Token URL'
            value={detail.oauthTokenUrl ?? ''}
            onChange={(event) => {
              handleChange('oauthTokenUrl', event.target.value);
            }}
            required
          />
          <KanbanInput
            label='Scope'
            value={detail.oauthScope ?? ''}
            onChange={(event) => {
              handleChange('oauthScope', event.target.value);
            }}
            required
          />
        </SimpleGrid>
      </SimpleGrid>
    </>
  );
};
