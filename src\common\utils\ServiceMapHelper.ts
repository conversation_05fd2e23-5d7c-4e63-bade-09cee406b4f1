import type { ConfigItemTypeModel } from '@models/ConfigItemType';
import { GRAPH_STYLES, LinkDataTemplate, PalleteTemplateFull } from './GoJsHelper';
import type { ServiceMapRuleModel, ServiceMapingDetailsModel, ServiceMapingModel } from '@models/ServiceMapping';
import type { CiRelationshipTypeModel } from '@models/CiRelationshipType';
import go, { GraphLinksModel } from '@common/libs/gojs/go';
import type { TablerIconKeys } from './IconsUtils';
const $ = go.GraphObject.make;

export const appendNodeServiceMap = async (
  goDiagram: go.Diagram | undefined,
  serviceMapDetails: ServiceMapingDetailsModel,
  getRelationshipTypeById: (id: number) => CiRelationshipTypeModel | undefined,
  ciTypes: ConfigItemTypeModel[],
  relationships: CiRelationshipTypeModel[],
  isUseTransaction: boolean = true,
) => {
  if (!goDiagram || !serviceMapDetails || !serviceMapDetails.ciImpacts) {
    return Promise.resolve(false);
  }

  const template: PalleteTemplateFull[] = [];
  let links: LinkDataTemplate[] = [];
  const groupMap = new Map<number, { key: number; isGroup: boolean; label: string }>();
  // style in group (Grop is Ci type)
  goDiagram.groupTemplate = $(
    go.Group,
    'Auto',
    {
      layout: go.GraphObject.make(go.LayeredDigraphLayout, {
        layerSpacing: 0,
        columnSpacing: 0,
        direction: 90,
      }),
    },
    $(go.Shape, 'RoundedRectangle', {
      fill: 'rgba(255, 255, 255, 0.8)',
      stroke: 'gray',
      strokeWidth: 1,
      strokeDashArray: GRAPH_STYLES.DASH,
    }),
    $(
      go.Panel,
      'Vertical',
      $(go.Placeholder, { padding: 10 }),
      $(go.TextBlock, { font: 'Bold 12pt Sans-Serif', margin: 10, textAlign: 'center', stroke: '#333' }, new go.Binding('text', 'label')),
    ),
  );
  // draw graph group with model design
  const modelGroupMapOld = go.GraphLinksModel.fromJson(serviceMapDetails.graphData || '') as GraphLinksModel;
  modelGroupMapOld.nodeDataArray.forEach((node) => {
    // fetch name ciType
    const ciType = ciTypes.find((item) => item.id === node.ciTypeId);

    const groupId = node.key * -1; // setting node group id < 0 because maybe id same node CI id
    const groupNode = {
      key: groupId,
      isGroup: true,
      label: ciType?.name || node.label,
      loc: node.loc,
    };
    groupMap.set(groupId, groupNode);
    template.push(groupNode);
  });

  modelGroupMapOld.linkDataArray.forEach((link) => {
    //fetch name relationship
    const relationship = relationships.find((item) => item.id === link?.data?.relationshipId);

    links.push({
      relationshipId: link.relationshipId,
      from: link.from * -1,
      to: link.to * -1,
      text: relationship?.type || link.text,
      style: GRAPH_STYLES.DASH_DOT,
      color: 'red',
    });
  });

  if (serviceMapDetails.ciImpactRelationships.length > 0) {
    for (const item of serviceMapDetails.ciImpactRelationships) {
      const fromGroupId = item.fromNodeId * -1;
      const toGroupId = item.toNodeId * -1;
      const nodeStartId = (serviceMapDetails.startNodeId || 0) * -1;
      const fromCiItem: PalleteTemplateFull = {
        key: `${item.fromCi}-${fromGroupId}`,
        icon: (ciTypes.find((x) => x.id === item.fromCiTypeId)?.icon || 'IconHome') as TablerIconKeys,
        label: `${item.fromCiName}`,
        isRoot: item.fromCi === serviceMapDetails.startCiId && nodeStartId === fromGroupId,
        group: fromGroupId,
      };

      const toCiItem: PalleteTemplateFull = {
        key: `${item.toCi}-${toGroupId}`,
        icon: (ciTypes.find((x) => x.id === item.toCiTypeId)?.icon || 'IconHome') as TablerIconKeys,
        label: `${item.toCiName}`,
        isRoot: item.toCi === serviceMapDetails.startCiId && nodeStartId === toGroupId,
        group: toGroupId,
      };

      if (!template.some((x) => x.key === `${item.fromCi}-${fromGroupId}` && x.group === fromGroupId)) {
        template.push(fromCiItem);
      }

      if (!template.some((x) => x.key === `${item.toCi}-${toGroupId}` && x.group === toGroupId)) {
        template.push(toCiItem);
      }
      const relationship = getRelationshipTypeById(item.relationshipId);
      const isMappingModel = modelGroupMapOld.linkDataArray.some((link) => link.from === item.fromNodeId && link.to === item.toNodeId);
      if (isMappingModel) {
        links = links.filter((link) => !(link.from === fromGroupId && link.to === toGroupId));
        links.push({
          relationshipId: item.relationshipId,
          from: `${item.fromCi}-${fromGroupId}`,
          to: `${item.toCi}-${toGroupId}`,
          text: relationship?.type || '',
        });
      }
    }
  } else {
    const item = serviceMapDetails.ciImpacts[0];

    const ciImpact = serviceMapDetails?.ciImpacts?.find((ciImpact) => ciImpact?.ciId === item.ciId);
    const groupId = ciImpact ? ciImpact.nodeId * -1 : 0;
    const node: PalleteTemplateFull = {
      key: item.ciId || 0,
      icon: (ciTypes.find((x) => x.id === item.ciTypeId)?.icon || 'IconHome') as TablerIconKeys,
      label: `${item.ciName}`,
      isRoot: true,
      group: groupId,
    };
    template.push(node);
  }
  goDiagram.layout = new go.Layout();
  goDiagram.model = new go.GraphLinksModel(template, links);

  //update position when group scale with child note
  goDiagram.model.nodeDataArray.forEach((nodeData) => {
    if (nodeData.isGroup) {
      const node = goDiagram.findNodeForData(nodeData);
      if (node !== null && nodeData.loc) {
        const scaleFactor = 1.5;
        const currentLocation = go.Point.parse(nodeData.loc);
        const newLocation = new go.Point(currentLocation.x * scaleFactor, currentLocation.y * scaleFactor);

        node.location = newLocation;
      }
    }
  });

  // check if diagram  width > height then show CI into group style left-right
  if (goDiagram.documentBounds.width < goDiagram.documentBounds.height) {
    goDiagram.layoutDiagram(true);
    goDiagram.groupTemplate.layout = go.GraphObject.make(go.LayeredDigraphLayout, {
      layerSpacing: 0,
      columnSpacing: 0,
      direction: 90,
    });
  }

  const appendNodeTransaction = 'appendNodeTransaction';
  if (isUseTransaction) {
    goDiagram.startTransaction(appendNodeTransaction);
  }

  // Add animation add node
  goDiagram.animationManager.isEnabled = true;
  goDiagram.animationManager.duration = 400;

  goDiagram.addDiagramListener('LayoutCompleted', function () {
    const fromNode = goDiagram.findNodeForKey(serviceMapDetails.startCiId);
    if (fromNode) {
      goDiagram.alignDocument(go.Spot.Center, go.Spot.Center);
    }
  });

  if (isUseTransaction) {
    goDiagram.commitTransaction(appendNodeTransaction);
  }
  return true;
};

export const fetchDiagramServiceMapping = (
  goDiagram: go.Diagram | undefined,
  ciTypes: ConfigItemTypeModel[],
  relationships: CiRelationshipTypeModel[],
  serviceMapingModel?: ServiceMapingModel | ServiceMapingDetailsModel,
) => {
  if (!goDiagram) {
    return;
  }
  const rules = serviceMapingModel?.rules || [];
  const transactionFetchServiceMap = 'transactionFetchServiceMap';
  goDiagram.nodes.each((node) => {
    goDiagram.startTransaction(transactionFetchServiceMap);
    // fetch start CI
    if (node.data?.data?.startCiId && serviceMapingModel?.startCiId) {
      goDiagram.model.setDataProperty(node.data, 'data', { startCiId: serviceMapingModel?.startCiId, startCiName: serviceMapingModel?.startCiName });
    }

    const ciType = ciTypes.find((item) => item.id === node.data.ciTypeId);
    if (ciType) {
      //fetch ciType
      goDiagram.model.setDataProperty(node.data, 'label', ciType?.name || '');
      goDiagram.model.setDataProperty(node.data, 'icon', ciType?.icon || 'IconHome');
      // fetch rules
      const diagramRule = node.data?.rules as ServiceMapRuleModel;
      if (diagramRule) {
        const rule = rules.find((item) => item.ciTypeAttrId === diagramRule.ciTypeAttrId);
        diagramRule.ciTypeAttrName = rule ? rule.ciTypeAttrName : diagramRule.ciTypeAttrName;
        goDiagram.model.setDataProperty(node.data, 'rules', diagramRule);
      }
    }
    goDiagram.commitTransaction(transactionFetchServiceMap);
  });
  // fetch link
  goDiagram.links.each((link) => {
    goDiagram.startTransaction(transactionFetchServiceMap);
    const relationship = relationships.find((item) => item.id === link.data.relationshipId);
    if (relationship) {
      goDiagram.model.setDataProperty(link.data, 'text', relationship?.type);
    }
    goDiagram.commitTransaction(transactionFetchServiceMap);
  });
};
