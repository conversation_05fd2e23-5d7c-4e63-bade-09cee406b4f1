import { all } from 'redux-saga/effects';
import { countersSaga } from './CountersSaga';
import { ciTypesSaga } from './CiTypesSaga';
import { currentUserSaga } from './CurrentUserSaga';
import { ciRelationshipTypesSaga } from './CiRelationshipTypesSaga';
import { systemParameterSaga } from './SystemParameterSaga';

export default function* rootSaga() {
  yield all([currentUserSaga(), countersSaga(), ciTypesSaga(), ciRelationshipTypesSaga(), systemParameterSaga()]);
}
