import React, { useCallback, useEffect, useState } from 'react';
import type { ConfigItemResponse } from '@api/ConfigItemApi';
import { Box, Divider, Flex, Group, NavLink, ScrollAreaAutosize, Stack } from '@mantine/core';
import ImpactDataGraphViewDetail from './ImpactDataGraphViewDetail';
import { KanbanInput } from 'kanban-design-system';
import { IconSearch } from '@tabler/icons-react';
import { klona } from 'klona';
import { ImpactedTypeTable } from '@models/ChangeAssessment';
import { KanbanTabs } from 'kanban-design-system';
import HeaderTitleComponent from '@components/HeaderTitleComponent';

type ImpactDataGraphViewComponentProps = {
  listCiImpact: ConfigItemResponse[];
  listServiceImpact: ConfigItemResponse[];
};

type ViewListCiImpactComponentProps = {
  listItems: ConfigItemResponse[];
  searchVal: string;
  setSearchVal: (val: string) => void;
  activeNavLink: number;
  setActiveNavLink: (val: number) => void;
  setSelectedItem: (val: ConfigItemResponse) => void;
};

export const ViewListCiImpactComponent = (props: ViewListCiImpactComponentProps) => {
  const { activeNavLink, listItems, searchVal, setActiveNavLink, setSearchVal, setSelectedItem } = props;

  return (
    <>
      <Stack gap={0}>
        <KanbanInput
          placeholder='Search here'
          value={searchVal}
          onChange={(e) => {
            const value = e.target.value;
            setSearchVal(value);
          }}
          rightSection={<IconSearch />}
        />
        <ScrollAreaAutosize h={'750px'} scrollbarSize={6}>
          {listItems.map((item, _index) => (
            <NavLink
              key={item.id}
              active={item.id === activeNavLink}
              label={item.name}
              leftSection={''}
              onClick={() => {
                setActiveNavLink(item.id);
                setSelectedItem(item);
              }}
            />
          ))}
        </ScrollAreaAutosize>
      </Stack>
    </>
  );
};

export const ImpactDataGraphViewComponent = (props: ImpactDataGraphViewComponentProps) => {
  const { listCiImpact, listServiceImpact } = props;
  const [activeNavLink, setActiveNavLink] = useState(0);
  const [selectedItem, setSelectedItem] = useState<ConfigItemResponse>();
  const [searchVal, setSearchVal] = useState('');
  const [listItems, setListItems] = useState<ConfigItemResponse[]>([]);
  const [activeTab, setActiveTab] = useState<ImpactedTypeTable>(ImpactedTypeTable.CI);

  useEffect(() => {
    setListItems(listCiImpact);
    if (listCiImpact && listCiImpact.length > 0) {
      setSelectedItem(listCiImpact[0]);
    }
  }, [listCiImpact]);

  const onFilterValue = useCallback(() => {
    let listItemsClone: ConfigItemResponse[] = [];
    if (ImpactedTypeTable.CI === activeTab) {
      listItemsClone = klona(listCiImpact);
    } else {
      listItemsClone = klona(listServiceImpact);
    }

    const listItemsFilter = listItemsClone.filter((x) => x.name && x.name.toLocaleLowerCase().includes(searchVal.toLocaleLowerCase()));
    setListItems(listItemsFilter);
  }, [activeTab, listCiImpact, listServiceImpact, searchVal]);

  useEffect(() => {
    onFilterValue();
  }, [onFilterValue]);

  const onTabChange = (val: string) => {
    setActiveTab(ImpactedTypeTable.SERVICE === val ? ImpactedTypeTable.SERVICE : ImpactedTypeTable.CI);
    setSearchVal('');
    if (ImpactedTypeTable.CI === val) {
      setSelectedItem(listCiImpact.length > 0 ? listCiImpact[0] : undefined);
    } else {
      setSelectedItem(listServiceImpact.length > 0 ? listServiceImpact[0] : undefined);
    }
  };

  useEffect(() => {
    if (selectedItem) {
      setActiveNavLink(selectedItem.id);
    }
  }, [selectedItem]);

  return (
    <>
      <Box mt='sm'>
        <HeaderTitleComponent title={'Graph View'} />
      </Box>
      <Flex mt={10}>
        <Box w={'20%'}>
          <Group gap={5}>
            <KanbanTabs
              configs={{
                defaultValue: activeTab,
                onChange: (val) => {
                  onTabChange(val || '');
                },
              }}
              tabs={{
                CI: {
                  content: (
                    <ViewListCiImpactComponent
                      listItems={listItems}
                      searchVal={searchVal}
                      setSearchVal={setSearchVal}
                      activeNavLink={activeNavLink}
                      setActiveNavLink={setActiveNavLink}
                      setSelectedItem={setSelectedItem}
                    />
                  ),
                  title: 'AFFECTED CIS',
                },
                SERVICE: {
                  content: (
                    <ViewListCiImpactComponent
                      listItems={listItems}
                      searchVal={searchVal}
                      setSearchVal={setSearchVal}
                      activeNavLink={activeNavLink}
                      setActiveNavLink={setActiveNavLink}
                      setSelectedItem={setSelectedItem}
                    />
                  ),
                  title: 'IMPACTED SERVICE/CIS',
                },
              }}
            />
            <Divider orientation='vertical' />
          </Group>
        </Box>
        <Box w={'80%'}>
          <Box m='sm' mah={'750px'}>
            {selectedItem && <ImpactDataGraphViewDetail ciId={selectedItem.id} ciTypeId={selectedItem.ciTypeId} />}
          </Box>
        </Box>
      </Flex>
    </>
  );
};
export default ImpactDataGraphViewComponent;
