import { DragDrop<PERSON>ontext, Droppable, DropResult } from '@hello-pangea/dnd';
import { Center, ScrollArea, Table } from '@mantine/core';
import { IconSearch, IconSquareArrowLeft, IconSquareArrowRight } from '@tabler/icons-react';
import { KanbanIconButton, KanbanInput, KanbanTooltip, KanbanText, KanbanTabs } from 'kanban-design-system';
import { Paper, Flex, ActionIcon } from '@mantine/core';
import { IconCheck, IconX } from '@tabler/icons-react';
import React, { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import classes from './TransfromMappingJson.module.scss';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import { DiscoveryStagingApi } from '@api/DiscoveryStagingApi';
import { DiscoveryTransformMapDetailModel, DiscoveryTransformMapModel, DragTranfromMapType } from '@models/DiscoveryTransformMap';
import styled from 'styled-components';
import { DEFAULT_ID_FOR_CI_DESCRIPTION, DEFAULT_ID_FOR_CI_NAME } from '@common/constants/CommonConstants';
import { CiTypeAttributeDataType } from '@models/CiType';
import { SourceColumnMapItem } from './draggableItem/SourceColumnMapItem';
import { TargetAttributeSearchItem } from './draggableItem/TargetAttributeSearchItem';
import { TargetAttributeItem } from './draggableItem/TargetAttributeItem';
import { StaticFieldItem } from './draggableItem/StaticFieldItem';
import { SourceColumnSearchItem } from './draggableItem/SourceColumnSearchItem';

type TransfromMappingJsonProps = {
  discoveryTransformMap: DiscoveryTransformMapModel;
  setDetails: (value: DiscoveryTransformMapDetailModel[]) => void;
};
const TableWrapper = styled.div`
  max-height: 562px;
  min-height: 562px;
  overflow-y: auto;
  border: 1px solid #ddd;
`;

const SOURCE_FIELD = 'SOURCE_FIELD';
const SOURCE_FIELD_MAP = 'SOURCE_FIELD_MAP';
const TARGET_FIELD = 'TARGET_FIELD';
const TARGET_FIELD_MAP = 'TARGET_FIELD_MAP';
const STATIC_FIELD = 'STATIC_FIELD';
const DROPABLE_CHILD = 'DROPABLE_CHILD';
const DROPPABLE_TYPE_OF_ATTRIBUTE = 'ATTRIBUTE';
const DROPPABLE_TYPE_OF_FIELD = 'FIELD';
/**
 * Selected/Deselected list drag.
 * @param data datas
 * @param isSelelected isSelelected
 * @returns new list
 */
const getNewDragSelect = (datas: DragTranfromMapType[], isSelelected: boolean) => {
  return datas.map((obj) => {
    return { ...obj, selected: isSelelected };
  });
};
const getUniqueStaticFields = (list: DragTranfromMapType[], regenerateId: boolean = false): DragTranfromMapType[] => {
  const setStaticValues = new Set<string>();
  const result: DragTranfromMapType[] = [];

  const handleStaticItem = (item: DragTranfromMapType, setStaticValues: Set<string>, regenerateId: boolean, result: DragTranfromMapType[]): void => {
    if (item.isStatic && item.staticValue !== undefined && !setStaticValues.has(item.staticValue)) {
      setStaticValues.add(item.staticValue);
      const newItem = regenerateId ? { ...item, id: crypto.randomUUID() } : item;
      result.push(newItem);
    }
  };
  for (const item of list) {
    if (item.listChildren && item.listChildren.length > 0) {
      for (const child of item.listChildren) {
        handleStaticItem(child, setStaticValues, regenerateId, result);
      }
    }
    handleStaticItem(item, setStaticValues, regenerateId, result);
  }

  return result;
};
const convertToDragTransformMapType = (item: DiscoveryTransformMapDetailModel): DragTranfromMapType => {
  return {
    id: item.isStatic ? crypto.randomUUID() : item.discoveryStagingStructureId,
    label: (item.isStatic ? item.staticValue : item.columnName) ?? '',
    selected: false,
    hashId: String(crypto.randomUUID()),
    isStatic: item.isStatic,
    staticValue: item.staticValue,
  };
};
export const TransfromMappingJson = React.memo((props: TransfromMappingJsonProps) => {
  const { discoveryTransformMap, setDetails } = props;
  const [ciTypeAttrs, setCiTypeAttrs] = useState<DragTranfromMapType[]>([]);
  const [ciTypeAttrsInit, setCiTypeAttrsInit] = useState<DragTranfromMapType[]>([]);
  const [ciTypeAttrsSearch, setCiTypeAttrsSearch] = useState<DragTranfromMapType[]>([]);
  const [ciTypeAttrsMap, setCiTypeAttrsMap] = useState<DragTranfromMapType[]>([]);
  const [ciTypeAttrsMapInit, setCiTypeAttrsMapInit] = useState<DragTranfromMapType[]>([]);
  const [columnsSearch, setColumnsSearch] = useState<DragTranfromMapType[]>([]);
  const [columns, setColumns] = useState<DragTranfromMapType[]>([]);
  const [columnsMap, setColumnsMap] = useState<DragTranfromMapType[]>([]);
  const initRef = useRef(true);
  const [activeTab, setActiveTab] = useState<'staging' | 'static'>('staging');
  const [staticFields, setStaticFields] = useState<DragTranfromMapType[]>([]);
  const [addValue, setAddValue] = useState('');
  const draggableFieldRef = useRef<(HTMLDivElement | null)[]>([]);
  const draggableAttributedRef = useRef<(HTMLDivElement | null)[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [syncedHeights, setSyncedHeights] = useState<number[]>([]);
  /**
   * Init load data from server to map
   */
  useEffect(() => {
    if (!initRef.current || !ciTypeAttrsSearch?.length || !discoveryTransformMap.details?.length) {
      return;
    }
    const columnsMapInit: DragTranfromMapType[] = [];
    const ciTypeAttrsMapInit: DragTranfromMapType[] = [];
    const dataDetails = discoveryTransformMap.details || [];
    for (const item of dataDetails) {
      const attr = ciTypeAttrsInit.find((obj) => obj.id === item.attributeId);
      if (attr) {
        columnsMapInit.push({
          id: item.isStatic ? crypto.randomUUID() : item.discoveryStagingStructureId,
          label: (item.isStatic ? item.staticValue : item.columnName) ?? '',
          selected: false,
          hashId: String(crypto.randomUUID()),
          isStatic: item.isStatic,
          staticValue: item.staticValue,
          listChildren: item.listChildren?.map(convertToDragTransformMapType),
        });
        ciTypeAttrsMapInit.push({
          id: `${attr.id}`,
          label: attr.label,
          selected: false,
          deleted: attr.deleted,
          isStatic: item.isStatic,
          staticValue: item.staticValue,
        });
      }
    }

    setColumnsMap(columnsMapInit);
    setStaticFields(getUniqueStaticFields(columnsMapInit, true));
    setCiTypeAttrsSearch((prev) => {
      return prev.filter((obj) => !ciTypeAttrsMapInit.some((item) => item.id === `${obj.id}`));
    });
    setCiTypeAttrsMap(ciTypeAttrsMapInit);
    setCiTypeAttrsMapInit(ciTypeAttrsMapInit);
    initRef.current = false;
  }, [discoveryTransformMap.details, columnsSearch, ciTypeAttrsSearch, ciTypeAttrsInit]);

  /**
   * Calculate dataDetailsMap using useMemo for better performance
   */
  const dataDetailsMap = useMemo((): DiscoveryTransformMapDetailModel[] => {
    if (columnsMap.length === 0 || columnsMap.length !== ciTypeAttrsMap.length) {
      return [];
    }

    return columnsMap.map((item, index) => {
      const baseItem: DiscoveryTransformMapDetailModel = {
        transformMapId: discoveryTransformMap.id || 0,
        discoveryStagingStructureId: item.isStatic ? 0 : Number(item.id),
        columnName: item.isStatic ? '' : item.label,
        attributeId: Number(ciTypeAttrsMap[index].id),
        attributeName: ciTypeAttrsMap[index].label,
        position: index,
        attributeDeleted: ciTypeAttrsMap[index].deleted,
        staticValue: item.staticValue,
        isStatic: item.isStatic,
      };

      if (item.listChildren?.length) {
        baseItem.listChildren = item.listChildren.map((child) => ({
          transformMapId: discoveryTransformMap.id || 0,
          discoveryStagingStructureId: child.isStatic ? 0 : Number(child.id),
          columnName: child.isStatic ? '' : child.label,
          attributeId: Number(ciTypeAttrsMap[index].id),
          attributeName: ciTypeAttrsMap[index].label,
          position: index,
          attributeDeleted: ciTypeAttrsMap[index].deleted,
          staticValue: child.staticValue,
          isStatic: child.isStatic,
        }));
      }

      return baseItem;
    });
  }, [columnsMap, ciTypeAttrsMap, discoveryTransformMap]);

  /**
   * Set details when dataDetailsMap changes
   */
  useEffect(() => {
    setDetails(dataDetailsMap);
  }, [dataDetailsMap, setDetails]);

  /**
   * get all attribute by citype
   */
  const fetchCiTypesAttribute = useCallback(() => {
    ConfigItemTypeApi.getAllAttributes(
      discoveryTransformMap.ciTypeId,
      {
        useLoading: false,
      },
      true,
    )
      .then((res) => {
        const datas: DragTranfromMapType[] = res.data
          .filter((obj) => CiTypeAttributeDataType.REFERENCE !== obj.type)
          .map((obj) => {
            return {
              id: obj.id,
              label: obj.name,
              selected: false,
              deleted: obj.deleted,
              isStatic: false,
            };
          });
        //push description to attribute
        datas.unshift({
          id: DEFAULT_ID_FOR_CI_DESCRIPTION,
          label: 'Description',
          selected: false,
          isStatic: false,
        });
        //push name to attribute
        datas.unshift({
          id: DEFAULT_ID_FOR_CI_NAME,
          label: 'Name',
          selected: false,
          isStatic: false,
        });
        setCiTypeAttrsInit(datas);
        const attributeInView = datas.filter((obj) => !obj.deleted);
        setCiTypeAttrs(attributeInView);
        setCiTypeAttrsSearch(attributeInView);
      })
      .catch(() => {});
  }, [discoveryTransformMap.ciTypeId]);

  /**
   * Get all column mapping json from staging table.
   */

  const fetchColumns = useCallback(() => {
    DiscoveryStagingApi.getAllColumns(discoveryTransformMap.stagingTableId)
      .then((res) => {
        const data: DragTranfromMapType[] = res.data.map((obj) => {
          return {
            id: obj.id,
            label: obj.columnName,
            selected: false,
            isStatic: false,
            hashId: String(crypto.randomUUID()),
          };
        });
        setColumns(data);
        setColumnsSearch(data);
      })
      .catch(() => {});
  }, [discoveryTransformMap.stagingTableId]);

  useEffect(() => {
    fetchCiTypesAttribute();
    fetchColumns();
  }, [fetchCiTypesAttribute, fetchColumns]);

  useEffect(() => {
    setCiTypeAttrsSearch((prev) =>
      prev.map((obj) => ({
        ...obj,
        isStatic: activeTab === 'static',
        selected: false,
      })),
    );
    setCiTypeAttrsMap((prev) =>
      prev.map((obj) => ({
        ...obj,
        selected: false,
      })),
    );
    setColumnsMap((prev) =>
      prev.map((obj) => ({
        ...obj,
        selected: false,
      })),
    );
    setColumnsSearch((prev) =>
      prev.map((obj) => ({
        ...obj,
        selected: false,
      })),
    );
    setStaticFields((prev) =>
      prev.map((obj) => ({
        ...obj,
        selected: false,
      })),
    );
  }, [activeTab]);
  /**
   * Source item column.
   */
  const sourceColumns = useMemo(() => {
    return columnsSearch.map((item, index) => (
      <SourceColumnSearchItem
        key={item.id}
        item={item}
        index={index}
        classes={classes}
        onClick={() => {
          setColumnsMap((prev) => getNewDragSelect(prev, false));
          setCiTypeAttrsSearch((prev) => getNewDragSelect(prev, false));
          setCiTypeAttrsMap((prev) => getNewDragSelect(prev, false));
          setStaticFields((prev) => getNewDragSelect(prev, false));
          setColumnsSearch((prev) =>
            prev.map((attr) => (attr.id === item.id && attr.hashId === item.hashId ? { ...attr, selected: !attr.selected } : attr)),
          );
        }}
      />
    ));
  }, [columnsSearch]);

  const onConfirm = useCallback(
    (id: string | number) => {
      const trimmed = addValue.trim();
      if (trimmed && !staticFields.some((f) => f.label === trimmed && f.id !== id)) {
        setStaticFields((prev) => prev.map((f) => (f.id === id ? { ...f, label: trimmed } : f)));
      }
    },
    [addValue, staticFields],
  );
  const removeStaticField = useCallback((staticValue: string | undefined) => {
    setStaticFields((prev) => prev.filter((item) => item.staticValue !== staticValue));
  }, []);

  const addNewStaticField = () => {
    const trimmed = addValue.trim();

    if (trimmed === '' || staticFields.some((f) => f.label === trimmed)) {
      return;
    }
    const newId = crypto.randomUUID();
    const newItem = {
      id: newId,
      hashId: String(crypto.randomUUID()),
      label: addValue,
      selected: false,
      isStatic: true,
      staticValue: addValue,
    };
    setStaticFields([...staticFields, newItem]);
    setAddValue('');
  };

  const staticFieldList = useMemo(() => {
    return staticFields.map((item, index) => (
      <StaticFieldItem
        classes={classes}
        key={item.id}
        item={item}
        index={index}
        addValue={addValue}
        staticFields={staticFields}
        onConfirm={onConfirm}
        removeStaticField={removeStaticField}
        setAddValue={setAddValue}
        isDragging={isDragging}
        onClick={() => {
          setColumnsSearch((prev) => getNewDragSelect(prev, false));
          setCiTypeAttrsSearch((prev) => getNewDragSelect(prev, false));
          setCiTypeAttrsMap((prev) => getNewDragSelect(prev, false));
          setColumnsMap((prev) => getNewDragSelect(prev, false));
          setStaticFields((prev) =>
            prev.map((attr) => (attr.id === item.id && attr.hashId === item.hashId ? { ...attr, selected: !attr.selected } : attr)),
          );
        }}
      />
    ));
  }, [staticFields, addValue, onConfirm, removeStaticField, isDragging]);

  // Đồng bộ height cho map
  useLayoutEffect(() => {
    draggableFieldRef.current = draggableFieldRef.current.slice(0, columnsMap.length);
    draggableAttributedRef.current = draggableAttributedRef.current.slice(0, ciTypeAttrsMap.length);
    const minLength = Math.min(draggableFieldRef.current.length, draggableAttributedRef.current.length);

    const newSyncedHeights: number[] = [];

    for (let i = 0; i < minLength; i++) {
      const fieldEl = draggableFieldRef.current[i];
      const attrEl = draggableAttributedRef.current[i];

      if (fieldEl && attrEl) {
        const sourceHeight = fieldEl.offsetHeight;
        newSyncedHeights[i] = sourceHeight;
        attrEl.style.height = `${sourceHeight}px`;
      }
    }
    for (let i = minLength; i < draggableAttributedRef.current.length; i++) {
      const attrEl = draggableAttributedRef.current[i];
      if (attrEl) {
        newSyncedHeights[i] = 49;
        attrEl.style.height = '49px';
      }
    }

    setSyncedHeights(newSyncedHeights);
  }, [columnsMap, ciTypeAttrsMap]);
  /*
   * view item source column map
   */
  const sourceColumnMap = useMemo(() => {
    return columnsMap.map((item, index) => (
      <SourceColumnMapItem
        dragableRef={(el) => (draggableFieldRef.current[index] = el)}
        classes={classes}
        key={`${item.id}_${item.hashId || ''}`}
        item={item}
        index={index}
        onClick={() => {
          setColumnsSearch((prev) => getNewDragSelect(prev, false));
          setCiTypeAttrsSearch((prev) => getNewDragSelect(prev, false));
          setCiTypeAttrsMap((prev) => getNewDragSelect(prev, false));
          setStaticFields((prev) => getNewDragSelect(prev, false));
          setColumnsMap((prev) =>
            prev.map((attr) => (attr.id === item.id && attr.hashId === item.hashId ? { ...attr, selected: !attr.selected } : attr)),
          );
        }}
      />
    ));
  }, [columnsMap]);

  /**
   * item drag target attribute
   */
  const targetAttributes = useMemo(() => {
    return ciTypeAttrsSearch
      .filter((obj) => !obj.deleted || ciTypeAttrsMapInit.some((mapInit) => mapInit.id === obj.id))
      .map((item, index) => (
        <TargetAttributeSearchItem
          key={item.id}
          item={item}
          index={index}
          classes={classes}
          onClick={() => {
            setColumnsSearch((prev) => getNewDragSelect(prev, false));
            setColumnsMap((prev) => getNewDragSelect(prev, false));
            setStaticFields((prev) => getNewDragSelect(prev, false));
            setCiTypeAttrsMap((prev) => getNewDragSelect(prev, false));
            setCiTypeAttrsSearch((prev) => prev.map((attr) => (attr.id === item.id ? { ...attr, selected: !attr.selected } : attr)));
          }}
        />
      ));
  }, [ciTypeAttrsMapInit, ciTypeAttrsSearch]);

  const targetAttributesMap = useMemo(() => {
    return ciTypeAttrsMap.map((item, index) => (
      <TargetAttributeItem
        dragableRef={(el) => (draggableAttributedRef.current[index] = el)}
        key={item.id}
        item={item}
        index={index}
        classes={classes}
        paperStyle={{
          height: syncedHeights[index] ? `${syncedHeights[index]}px` : undefined,
        }}
        onClick={() => {
          setColumnsSearch((prev) => getNewDragSelect(prev, false));
          setColumnsMap((prev) => getNewDragSelect(prev, false));
          setCiTypeAttrsSearch((prev) => getNewDragSelect(prev, false));
          setStaticFields((prev) => getNewDragSelect(prev, false));
          setCiTypeAttrsMap((prev) => prev.map((attr) => (attr.id === item.id ? { ...attr, selected: !attr.selected } : attr)));
        }}
      />
    ));
  }, [ciTypeAttrsMap, syncedHeights]);

  /**
   * Hander drag drop.
   * @param result result
   * @returns
   */
  const handleDragOrDropDestinationEnd = (result: DropResult) => {
    setIsDragging(false);
    const { combine, destination, source } = result;
    const originalSourceDroppableId = result.source.droppableId;
    const [parentId, parentHashId] = originalSourceDroppableId.replace('DROPABLE_CHILD_', '').split('_');
    const destOriginalDroppableId = result.destination?.droppableId || '';
    const [destParentId, destParentHashId] = destOriginalDroppableId.replace('DROPABLE_CHILD_', '').split('_');

    // chuẩn hóa droppableId

    const cleanSourceDroppableId = source.droppableId.replace(/_\d+$/, '').replace(/^DROPABLE_CHILD_.*/, DROPABLE_CHILD);

    const cleanDestinationDroppableId = destination?.droppableId.replace(/_\d+$/, '').replace(/^DROPABLE_CHILD_.*/, DROPABLE_CHILD);

    source.droppableId = cleanSourceDroppableId;
    if (destination && cleanDestinationDroppableId) {
      destination.droppableId = cleanDestinationDroppableId;
    }
    const droppableMap = {
      [SOURCE_FIELD]: columnsSearch,
      [SOURCE_FIELD_MAP]: columnsMap,
      [STATIC_FIELD]: staticFields,
    };
    //
    if (combine && [SOURCE_FIELD, SOURCE_FIELD_MAP, STATIC_FIELD].includes(source.droppableId)) {
      const listSource = [...droppableMap[source.droppableId as keyof typeof droppableMap]];
      const [destinationParentId, destinationParentHashId] = combine.draggableId.replace('DRAGGABLE_PARENT_', '').split('_');

      const [sourceItem] = listSource.splice(source.index, 1);
      if (sourceItem.listChildren?.length ?? 0 >= 1) {
        return;
      }
      sourceItem.hashId = String(crypto.randomUUID());

      const movedItemCopy = {
        ...sourceItem,
        hashId: String(crypto.randomUUID()),
      };

      const updatedColumnsMap = columnsMap.map((item) => {
        if (String(item.id) === String(destinationParentId) && String(item.hashId) === destinationParentHashId) {
          const updatedChildItem = item.listChildren ? [...item.listChildren, movedItemCopy] : [movedItemCopy];
          return {
            ...item,
            listChildren: updatedChildItem,
          };
        }
        return item;
      });
      const filtered = updatedColumnsMap.filter(
        (obj) => !(String(obj.id) === String(sourceItem.id) && String(obj.hashId) === String(sourceItem.hashId)),
      );

      // // Gán vào state
      setColumnsMap(filtered);

      return;
    }
    // remove Item nếu source là dropable child
    if (DROPABLE_CHILD === source.droppableId && !destination) {
      setColumnsMap((prev) => {
        return prev.map((column) => {
          if (String(column.id) === String(parentId) && String(column.hashId) === String(parentHashId)) {
            return {
              ...column,
              listChildren: column.listChildren?.filter((_, idx) => idx !== source.index),
            };
          }
          return column;
        });
      });
      return;
    }
    if (!destination) {
      return;
    }
    if (source.droppableId === destination.droppableId && source.index === destination.index) {
      return;
    }

    if (
      ![SOURCE_FIELD, SOURCE_FIELD_MAP, STATIC_FIELD].includes(source.droppableId) &&
      [SOURCE_FIELD, SOURCE_FIELD_MAP, STATIC_FIELD].includes(destination.droppableId)
    ) {
      return;
    }

    if (![TARGET_FIELD, TARGET_FIELD_MAP].includes(source.droppableId) && [TARGET_FIELD, TARGET_FIELD_MAP].includes(destination.droppableId)) {
      return;
    }

    if (TARGET_FIELD === source.droppableId || TARGET_FIELD_MAP === source.droppableId) {
      const sourceList = TARGET_FIELD === source.droppableId ? [...ciTypeAttrsSearch] : [...ciTypeAttrsMap];
      const destinationList = TARGET_FIELD === destination.droppableId ? [...ciTypeAttrsSearch] : [...ciTypeAttrsMap];

      // mve item
      const [movedItem] = sourceList.splice(source.index, 1);
      let isSort = false;
      // move to other box
      if (source.droppableId !== destination.droppableId) {
        destinationList.splice(destination.index, 0, movedItem);
      } else {
        isSort = true;
        // sort in box
        sourceList.splice(destination.index, 0, movedItem);
      }

      //update list
      if (TARGET_FIELD === source.droppableId) {
        if (isSort) {
          setCiTypeAttrsSearch(sourceList);
        } else {
          setCiTypeAttrsSearch(sourceList);
          setCiTypeAttrsMap(destinationList);
        }
      } else {
        if (isSort) {
          setCiTypeAttrsMap(sourceList);
        } else {
          setCiTypeAttrsSearch(destinationList);
          setCiTypeAttrsMap(sourceList);
        }
      }
      return;
    }

    // column
    if (SOURCE_FIELD === source.droppableId || STATIC_FIELD === source.droppableId) {
      const sourceList = SOURCE_FIELD === source.droppableId ? [...columnsSearch] : [...staticFields];
      const destinationList = [...columnsMap];

      const [movedItem] = sourceList.splice(source.index, 1);
      // create hash copy
      const movedItemCopy = {
        ...movedItem,
        hashId: String(crypto.randomUUID()),
      };
      let isSort = false;
      if (source.droppableId !== destination.droppableId) {
        destinationList.splice(destination.index, 0, movedItemCopy);
      } else {
        isSort = true;
        sourceList.splice(destination.index, 0, movedItemCopy);
      }

      if (isSort) {
        if (SOURCE_FIELD === source.droppableId) {
          setColumnsSearch(sourceList);
        } else {
          setStaticFields(sourceList);
        }
      } else {
        setColumnsMap(destinationList);
      }
      return;
    }
    if (SOURCE_FIELD_MAP === source.droppableId) {
      const sourceList = [...columnsMap];
      const destinationList = STATIC_FIELD === destination.droppableId ? [...staticFields] : [...columnsSearch];

      const [movedItem] = sourceList.splice(source.index, 1);
      // create hash copy
      const movedItemCopy = {
        ...movedItem,
        hashId: String(crypto.randomUUID()),
      };
      let isSort = false;
      if (source.droppableId !== destination.droppableId) {
        destinationList.splice(destination.index, 0, movedItemCopy);
      } else {
        isSort = true;
        sourceList.splice(destination.index, 0, { ...movedItem });
      }

      setColumnsMap(sourceList);
      if (!isSort) {
        if (destination.droppableId === STATIC_FIELD) {
          setStaticFields(getUniqueStaticFields(destinationList));
        }
      }
      return;
    }
    // Handle drag from DROPABLE_CHILD
    if (DROPABLE_CHILD === source.droppableId && DROPABLE_CHILD === destination.droppableId) {
      const listColumnsMap = [...columnsMap];

      const parentItem = listColumnsMap.find((item) => String(item.id) === parentId && String(item.hashId) === parentHashId);

      if (!parentItem || !parentItem.listChildren) {
        return;
      }

      const sourceList = [...parentItem.listChildren];
      const [movedItem] = sourceList.splice(source.index, 1);

      if (parentId === destParentId && parentHashId === destParentHashId) {
        // Moving within the same parent's child items
        sourceList.splice(destination.index, 0, movedItem);
        const updatedParentItem = { ...parentItem, listChildren: sourceList };
        const updatedColumnsMap = listColumnsMap.map((item) =>
          item.id === parentItem.id && item.hashId === parentItem.hashId ? updatedParentItem : item,
        );
        setColumnsMap(updatedColumnsMap);
      }

      return;
    }
  };

  const sourceFieldToMap = useCallback(() => {
    if (activeTab === 'staging') {
      const lstColumnSelected = columnsSearch
        .filter((obj) => obj.selected)
        .map((obj) => ({ ...obj, hashId: String(crypto.randomUUID()), selected: false }));

      const columnsMapNew = [...columnsMap, ...lstColumnSelected];
      // unseleced
      const updatedColumns = columnsSearch.map((obj) => ({ ...obj, selected: false }));

      setColumnsMap(columnsMapNew);
      setColumnsSearch(updatedColumns);
    } else {
      const lstStaticFieldSelected = staticFields
        .filter((obj) => obj.selected)
        .map((obj) => ({ ...obj, hashId: String(crypto.randomUUID()), selected: false }));

      const columnsMapNew = [...columnsMap, ...lstStaticFieldSelected];
      // unseleced
      const updatedColumns = staticFields.map((obj) => ({ ...obj, selected: false }));

      setColumnsMap(columnsMapNew);
      setStaticFields(updatedColumns);
    }
  }, [activeTab, columnsSearch, columnsMap, staticFields]);

  const targetFieldMapPrev = useCallback(() => {
    const lstTargetSelected = ciTypeAttrsSearch.filter((obj) => obj.selected).map((obj) => ({ ...obj, selected: false }));
    const lstTargetMapNew = [...ciTypeAttrsMap, ...lstTargetSelected];
    // unseleced
    const updatedTarget = ciTypeAttrsSearch.filter((obj) => !obj.selected);
    setCiTypeAttrsSearch(updatedTarget);
    setCiTypeAttrsMap(lstTargetMapNew);
  }, [ciTypeAttrsSearch, ciTypeAttrsMap]);

  const targetFieldToMap = useCallback(() => {
    const lstTargetMapSelected = ciTypeAttrsMap.filter((obj) => obj.selected).map((obj) => ({ ...obj, selected: false }));
    const lstTargetNew = [...ciTypeAttrsSearch, ...lstTargetMapSelected];
    // unseleced
    const updatedTargetMap = ciTypeAttrsMap.filter((obj) => !obj.selected);
    setCiTypeAttrsSearch(lstTargetNew);
    setCiTypeAttrsMap(updatedTargetMap);
  }, [ciTypeAttrsSearch, ciTypeAttrsMap]);
  const error = useMemo(() => {
    return staticFields.length >= 20
      ? 'Maximum of 20 static fields allowed'
      : staticFields.some((f) => f.staticValue === addValue.trim())
        ? 'Static field must be unique'
        : undefined;
  }, [addValue, staticFields]);
  return (
    <>
      <Flex>
        <DragDropContext onDragEnd={handleDragOrDropDestinationEnd} onDragStart={() => setIsDragging(true)}>
          <Paper w={'25%'}>
            <KanbanTabs
              configs={{
                defaultValue: 'staging',
                value: activeTab,
                variant: 'outline',
                onChange: (val) => setActiveTab(val as 'staging' | 'static'),
              }}
              tabs={{
                staging: {
                  title: <KanbanText fw={500}>Staging</KanbanText>,
                  content: (
                    <Droppable droppableId={SOURCE_FIELD} type={DROPPABLE_TYPE_OF_FIELD}>
                      {(provided) => (
                        <Paper withBorder p='xs' {...provided.droppableProps} ref={provided.innerRef}>
                          <KanbanInput
                            placeholder='Search here'
                            leftSection={<IconSearch size='1rem' />}
                            size='xs'
                            onChange={(data) => {
                              if (data.target.value?.trim()?.length) {
                                const columnSearch = columns.filter((obj) => obj.label.toLowerCase().includes(data.target.value.toLowerCase()));
                                setColumnsSearch(columnSearch);
                              } else {
                                setColumnsSearch([...columns]);
                              }
                            }}></KanbanInput>
                          <ScrollArea h={500}>
                            {sourceColumns}
                            {provided.placeholder}
                          </ScrollArea>
                        </Paper>
                      )}
                    </Droppable>
                  ),
                },
                static: {
                  title: <KanbanText fw={500}>Static</KanbanText>,
                  content: (
                    <Droppable droppableId={STATIC_FIELD} type={DROPPABLE_TYPE_OF_FIELD}>
                      {(provided) => (
                        <Paper withBorder p='xs' {...provided.droppableProps} ref={provided.innerRef} radius='sm' shadow='md'>
                          <Flex align='center' gap='xs' justify='space-between' mb='sm'>
                            <KanbanInput
                              mb={0}
                              value={addValue}
                              autoFocus
                              w={200}
                              maxLength={1000}
                              size='md'
                              onChange={(e) => setAddValue(e.target.value)}
                              placeholder='Add new ...'
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  if (!error) {
                                    addNewStaticField();
                                  }
                                } else if (e.key === 'Escape') {
                                  setAddValue('');
                                }
                              }}
                              error={error}
                            />
                            {addValue.trim() !== '' && (
                              <>
                                <ActionIcon
                                  color='green'
                                  variant='light'
                                  onClick={() => {
                                    if (!error) {
                                      addNewStaticField();
                                    }
                                  }}
                                  disabled={addValue.trim() === '' || !!error}>
                                  <IconCheck size={16} />
                                </ActionIcon>
                                <ActionIcon
                                  color='red'
                                  variant='light'
                                  onClick={() => {
                                    setAddValue('');
                                  }}>
                                  <IconX size={16} />
                                </ActionIcon>
                              </>
                            )}
                          </Flex>
                          <ScrollArea h={500}>
                            {staticFieldList}
                            {provided.placeholder}
                          </ScrollArea>
                        </Paper>
                      )}
                    </Droppable>
                  ),
                },
              }}
            />
          </Paper>
          <Center w={'5%'}>
            <Flex direction='column'>
              <KanbanTooltip label='Add'>
                <KanbanIconButton variant='transparent' mb={'xs'} size='36' onClick={sourceFieldToMap}>
                  <IconSquareArrowRight size='36' />
                </KanbanIconButton>
              </KanbanTooltip>
              <KanbanTooltip label='Remove'>
                <KanbanIconButton
                  variant='transparent'
                  size='36'
                  onClick={() => {
                    setColumnsMap((prev) => prev.filter((obj) => !obj.selected));
                  }}>
                  <IconSquareArrowLeft size='36' />
                </KanbanIconButton>
              </KanbanTooltip>
            </Flex>
          </Center>
          {/* data mapping */}
          <Paper w={'50%'}>
            <Flex justify='center' align='center'>
              <KanbanText fw={500} mb='sm'>
                Mapping Fields
              </KanbanText>
            </Flex>

            <TableWrapper>
              <Table className={`${classes['table']}`}>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th className={`${classes['table-th']}`}>
                      <KanbanText fw={500} mb='sm'>
                        Column Fields
                      </KanbanText>
                    </Table.Th>
                    <Table.Th className={`${classes['table-th']}`}>
                      <KanbanText fw={500} mb='sm'>
                        Attribute Fields
                      </KanbanText>
                    </Table.Th>
                  </Table.Tr>
                </Table.Thead>

                <Table.Tbody>
                  <Table.Tr className={`${classes['table-tr']}`}>
                    <Table.Td className={`${classes['table-td']}`}>
                      <Droppable droppableId={SOURCE_FIELD_MAP} isCombineEnabled type={DROPPABLE_TYPE_OF_FIELD}>
                        {(provided) => (
                          <Paper
                            withBorder
                            p='xs'
                            pl={'2'}
                            {...provided.droppableProps}
                            ref={provided.innerRef}
                            className={`${classes['table-data']}`}>
                            {sourceColumnMap}
                            {provided.placeholder}
                          </Paper>
                        )}
                      </Droppable>
                    </Table.Td>
                    <Table.Td className={`${classes['table-td']}`}>
                      <Droppable droppableId={TARGET_FIELD_MAP} type={DROPPABLE_TYPE_OF_ATTRIBUTE}>
                        {(provided) => (
                          <Paper
                            withBorder
                            p='xs'
                            pl={'2'}
                            {...provided.droppableProps}
                            ref={provided.innerRef}
                            className={`${classes['table-data']}`}>
                            {targetAttributesMap}
                            {provided.placeholder}
                          </Paper>
                        )}
                      </Droppable>
                    </Table.Td>
                  </Table.Tr>
                </Table.Tbody>
              </Table>
            </TableWrapper>
          </Paper>
          <Center w={'5%'}>
            <Flex direction='column'>
              <KanbanTooltip label='Add'>
                <KanbanIconButton variant='transparent' mb={'xs'} size='36' onClick={targetFieldMapPrev}>
                  <IconSquareArrowLeft size='36' />
                </KanbanIconButton>
              </KanbanTooltip>
              <KanbanTooltip label='Remove'>
                <KanbanIconButton variant='transparent' size='36' onClick={targetFieldToMap}>
                  <IconSquareArrowRight size='36' />
                </KanbanIconButton>
              </KanbanTooltip>
            </Flex>
          </Center>

          <Paper w={'25%'}>
            <KanbanText fw={500} mb='sm'>
              {`Attribute of CiType: ${discoveryTransformMap.ciTypeName}`}
            </KanbanText>
            <Droppable droppableId={TARGET_FIELD} type={DROPPABLE_TYPE_OF_ATTRIBUTE}>
              {(provided) => (
                <Paper withBorder p='xs' {...provided.droppableProps} ref={provided.innerRef}>
                  <KanbanInput
                    placeholder='Search here'
                    leftSection={<IconSearch size='1rem' />}
                    size='md'
                    onChange={(data) => {
                      if (data.target.value?.trim()?.length) {
                        const search = ciTypeAttrs.filter(
                          (obj) =>
                            !ciTypeAttrsMap.some((map) => String(map.id) === String(obj.id)) &&
                            obj.label.toLowerCase().includes(data.target.value.toLowerCase()),
                        );
                        setCiTypeAttrsSearch(search);
                      } else {
                        setCiTypeAttrsSearch([...ciTypeAttrs.filter((obj) => !ciTypeAttrsMap.some((map) => String(map.id) === String(obj.id)))]);
                      }
                    }}></KanbanInput>
                  <ScrollArea h={500}>
                    {targetAttributes}
                    {provided.placeholder}
                  </ScrollArea>
                </Paper>
              )}
            </Droppable>
          </Paper>
        </DragDropContext>
      </Flex>
    </>
  );
});
TransfromMappingJson.displayName = 'TransfromMappingJson';
