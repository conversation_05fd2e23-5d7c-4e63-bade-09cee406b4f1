import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import type { CiRelationshipTypeModel } from '@models/CiRelationshipType';

export type CiRelationshipTypeResponse = CiRelationshipTypeModel;

export class CiRelationshipTypeApi extends BaseApi {
  static baseUrl = BaseUrl.ciRelationshipTypes;

  static getAll() {
    return BaseApi.getData<CiRelationshipTypeResponse[]>(`${this.baseUrl}`);
  }
  static saveNewRelationship(data: CiRelationshipTypeResponse) {
    return BaseApi.postData<CiRelationshipTypeResponse>(`${this.baseUrl}`, data);
  }
  static deleteById(id: number) {
    return BaseApi.deleteData<boolean>(`${this.baseUrl}/${id}`);
  }
  static deleteByIds(ids: number[]) {
    return BaseApi.deleteData<number[]>(`${this.baseUrl}/batch`, {
      ids,
    });
  }
}
