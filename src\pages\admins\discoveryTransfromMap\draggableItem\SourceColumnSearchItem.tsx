import React from 'react';
import { Text } from '@mantine/core';
import { DragTranfromMapType } from '@models/DiscoveryTransformMap';
import { DraggableItemBase } from './DraggableItemBase';

export interface SourceColumnSearchItemProps {
  item: DragTranfromMapType;
  index: number;
  onClick: (id: number | string) => void;
  classes: Record<string, string>;
}

export const SourceColumnSearchItem = React.memo(({ classes, index, item, onClick }: SourceColumnSearchItemProps) => {
  return (
    <DraggableItemBase
      item={item}
      index={index}
      classes={classes}
      onClick={() => onClick(item.id)}
      draggableIdPrefix='source_column'
      paperStyle={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}
      renderContent={(item) => (
        <Text w={200} truncate='end'>
          {item.label}
        </Text>
      )}
    />
  );
});

SourceColumnSearchItem.displayName = 'SourceColumnSearchItem';
