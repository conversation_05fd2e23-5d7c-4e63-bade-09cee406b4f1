import { KanbanInput } from 'kanban-design-system';
import { Box } from '@mantine/core';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react';
import type { ConfigItemTypeModel } from '@models/ConfigItemType';
import { useForm } from '@mantine/form';
import { KanbanTextarea } from 'kanban-design-system';
import { KanbanIconSelect } from 'kanban-design-system';
import SelectCiTypeComponent from '@components/commonCi/SelectCiTypeComponent';
import { KanbanSwitch } from 'kanban-design-system';
import { formatStandardName } from '@common/utils/StringUtils';
import { NotificationError } from '@common/utils/NotificationUtils';
import { useGetCiTypes } from '@slices/CiTypesSlice';

type FormCiType = {
  name: string;
  description: string;
};

const validateName = (value: string | undefined) => {
  if (!value || value.trim().length === 0) {
    return 'Name cannot be empty';
  }
  return undefined;
};

export interface CiTypeBasicInfoMethods {
  onCreateUpdate: () => ConfigItemTypeModel | undefined;
}

export interface CiTypeBasicInfoProps {
  ciTypeInfo: ConfigItemTypeModel;
  isAddByParent: boolean;
  setIsChange: (value: boolean) => void;
}

export const CiTypeBasicInfo = forwardRef<CiTypeBasicInfoMethods, CiTypeBasicInfoProps>((props, ref) => {
  const { ciTypeInfo, isAddByParent, setIsChange } = props;
  const ciTypes = useGetCiTypes();

  const [newItem, setNewItem] = useState<ConfigItemTypeModel>(ciTypeInfo);

  const { setValues, ...form } = useForm<FormCiType>({
    initialValues: {
      name: '',
      description: '',
    },

    validate: {
      name: validateName,
    },
  });

  useEffect(() => {
    if (setValues) {
      setValues({ name: newItem.name, description: newItem.description });
    }
  }, [newItem, setValues]);

  const updateNewItem = (key: keyof ConfigItemTypeModel, value: string | number | boolean | undefined) => {
    setNewItem((prev) => ({ ...prev, [key]: value }));
  };

  const onCreateUpdate = useCallback(() => {
    form.validate();
    if (!form.isValid()) {
      NotificationError({
        message: 'Data is not valid',
      });
      return undefined;
    }
    const indexSameData = (ciTypes.data || []).findIndex(
      (ob) =>
        // (ob.parentId === newItem.parentId || newItem.parentId === 0 && !ob.parentId) &&
        ob.name.toLowerCase() === newItem.name.toLocaleLowerCase() && ob.id !== newItem.id,
    );

    if (indexSameData !== -1) {
      NotificationError({
        message: 'Name is duplicated',
      });
      form.setFieldError('name', 'Name is duplicated');
      return undefined;
    }
    return newItem;
  }, [ciTypes.data, form, newItem]);

  useImperativeHandle<any, CiTypeBasicInfoMethods>(
    ref,
    () => ({
      onCreateUpdate: onCreateUpdate,
    }),
    [onCreateUpdate],
  );

  return (
    <>
      <Box mr={'sm'}>
        <KanbanInput
          label='Name'
          withAsterisk
          {...form.getInputProps('name')}
          value={newItem.name || ''}
          maxLength={255}
          onChange={(e) => {
            updateNewItem('name', e.target.value);
          }}
          onBlur={(e) => {
            const value = e.target.value;
            updateNewItem('name', formatStandardName(value));
            setIsChange(true);
          }}
        />

        <SelectCiTypeComponent
          label='Parent'
          disabled={isAddByParent}
          value={newItem.parentId}
          allowDeselect={!isAddByParent}
          onChange={(e) => {
            let targetValue = 0;
            if (typeof e === 'number') {
              targetValue = e;
            }
            updateNewItem('parentId', targetValue);
            setIsChange(true);
          }}
        />

        <KanbanTextarea
          label='Description'
          maxLength={2000}
          value={newItem.description || ''}
          onChange={(e) => {
            updateNewItem('description', e.target.value);
            setIsChange(true);
          }}
        />
        {/* TODO */}
        <KanbanSwitch
          defaultChecked
          mb={'xs'}
          checked={newItem.active}
          disabled
          display={'none'}
          onChange={(event) => {
            const value = event.currentTarget.checked;
            updateNewItem('active', value);
            setIsChange(true);
          }}
          labelPosition='left'
          label='Is Active?'
          radius='md'
        />

        <KanbanIconSelect
          label='Icon'
          value={newItem.icon}
          onChange={(e) => {
            updateNewItem('icon', e || '');
            setIsChange(true);
          }}
        />
      </Box>
    </>
  );
});
CiTypeBasicInfo.displayName = 'CiTypeBasicInfo';
export default CiTypeBasicInfo;
