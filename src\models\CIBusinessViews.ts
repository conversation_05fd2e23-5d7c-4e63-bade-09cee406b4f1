import type { GRAPH_VIEW_LINK_STYPES, GRAPH_VIEW_TYPE } from '@common/utils/GoJsHelper';
import type { EntityModelBase } from './EntityModelBase';
// import type { ConfigItemResponse } from '@api/ConfigItemApi';
import type { FilterModel } from '@pages/cis/ci/graph/CiRelationshipGraph';
import type { ConfigItemModel } from './ConfigItem';
export type CIBusinessViews = EntityModelBase & {
  viewName: string;
  visibility: string;
  description?: string;
  share: boolean;
  graphView?: string;
  ciId: number;
  ciName: string;
  dataAction?: string;
  dataActionParse?: DataActionDTO;
};

export type CIBusinessViewResponseDTO = {
  ciInfo: ConfigItemModel;
  entity: CIBusinessViews;
};

export type RelationShipOfBusinessView = {
  from: number;
  to: number;
};

export type DataActionDTO = {
  level?: string;
  filter?: FilterModel;
  graphViewStyle?: GRAPH_VIEW_TYPE;
  graphViewLinkStyle?: GRAPH_VIEW_LINK_STYPES;
};

export default 1;
