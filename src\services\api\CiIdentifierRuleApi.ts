import { BaseApi } from '@core/api/BaseApi';
import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
import type { CiIdentifierRule, CiIdentifierRuleDto } from '@models/CiIdentifierRule';
import { BaseUrl } from '@core/api/BaseUrl';

export type CiIdentifierRuleResponse = PaginationResponseModel<CiIdentifierRule>;

export class CiIdentifierRuleApi extends BaseApi {
  static baseUrl = BaseUrl.ciIdentifierRules;

  static findAllWithPaging(pagination: PaginationRequestModel<CiIdentifierRule>) {
    return BaseApi.postData<CiIdentifierRuleResponse>(
      `${this.baseUrl}/filter`,
      pagination,
      {},
      {},
      { useLoading: false, useErrorNotification: true },
    );
  }

  static findCiIdentifierRuleById(id: number) {
    return BaseApi.getData<CiIdentifierRuleDto>(`${this.baseUrl}/${id}`);
  }

  static createNew(data: CiIdentifierRuleDto) {
    return BaseApi.postData<boolean>(`${this.baseUrl}`, data);
  }

  static updateRule(data: CiIdentifierRuleDto, id: number) {
    return BaseApi.putData<boolean>(`${this.baseUrl}/${id}`, data);
  }

  static deleteByIdIn(ids: number[]) {
    return BaseApi.deleteData<number[]>(`${this.baseUrl}`, {
      ids,
    });
  }
}
