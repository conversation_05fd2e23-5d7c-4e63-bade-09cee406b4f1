import type { GoJs } from '@common/libs';
import { drawHightLightFromNode, resetHightLight, scrollAndZoomToItem, zoomIn, zoomOut } from '@common/utils/GoJsHelper';
import { Anchor, ThemeIcon } from '@mantine/core';
import {
  IconX,
  IconArrowBackUp,
  IconArrowForwardUp,
  IconArrowsDiagonalMinimize2,
  IconArrowsDiagonal,
  IconZoomIn,
  IconZoomOut,
  IconZoomReset,
  IconArrowsRightLeft,
  IconArrowNarrowLeft,
  IconSquareFilled,
} from '@tabler/icons-react';
import { KanbanIconButton, KanbanText, KanbanTooltip } from 'kanban-design-system';
import React, { useCallback, useEffect, useState } from 'react';
import type { HighlightNodeModel } from '../CiRelationshipGraph';
import stylesCss from '../CiRelationshipGraph.module.scss';
import type { FullScreenHandle } from 'react-full-screen';
import type { ChangedEvent } from '@common/libs/gojs/go';
import { FlaggedImpactedCiAttachedType } from '@common/constants/CiManagement';

type RightGraphButtonProps = {
  goDiagram: GoJs.Diagram | undefined;
  nodeHightLight: HighlightNodeModel;
  levelFilter: number;
  setNodeHightLight: (node: HighlightNodeModel) => void;
  isShowPreview: boolean;
  setIsShowPreview: (isShowPreview: boolean) => void;
  handleFullScreen: FullScreenHandle;
  attachedType?: FlaggedImpactedCiAttachedType;
};
const RightGraphButton = (props: RightGraphButtonProps) => {
  const { attachedType, goDiagram, handleFullScreen, isShowPreview, levelFilter, nodeHightLight, setIsShowPreview, setNodeHightLight } = props;

  const [isShowLabelHightLight, setShowLabelHightLight] = useState<boolean>(false);

  /**
   * trigger when change model of node
   */
  const onUpdateHightLight = useCallback(
    (e?: ChangedEvent) => {
      if (
        e &&
        !['Insert', 'Remove'].includes(e.change.name) &&
        !['FinishedUndo', 'FinishedRedo', 'StartedTransaction'].includes(e.propertyName.toString()) &&
        e.modelChange !== 'nodeDataArray'
      ) {
        return;
      }
      const isShowLabel = !!nodeHightLight?.nodeId && !!goDiagram?.findNodeForKey(nodeHightLight.nodeId);
      //when hiden not highlighted then reset highlight
      setShowLabelHightLight(isShowLabel);
      if (!isShowLabel) {
        resetHightLight(goDiagram);
        return;
      }

      resetHightLight(goDiagram);
      drawHightLightFromNode(goDiagram, nodeHightLight.nodeId, nodeHightLight.upStream);
    },
    [goDiagram, nodeHightLight.nodeId, nodeHightLight.upStream],
  );

  const getIconColorByType = (attachedType: FlaggedImpactedCiAttachedType): string => {
    return attachedType === FlaggedImpactedCiAttachedType.CALCULATED ? '#CC0099' : '#000000';
  };
  /**
   * init check show label highlight
   */
  useEffect(() => {
    onUpdateHightLight();
  }, [onUpdateHightLight]);
  /**
   * trigger when change model of node
   */
  useEffect(() => {
    if (!goDiagram) {
      return;
    }
    goDiagram.addModelChangedListener(onUpdateHightLight);
    return () => {
      goDiagram.removeModelChangedListener(onUpdateHightLight);
    };
  }, [goDiagram, onUpdateHightLight]);
  return (
    <>
      <div className={stylesCss['icon-graph-view-top-right']}>
        {attachedType && (
          <>
            <KanbanTooltip withinPortal={false} label='Impacted Ci'>
              <ThemeIcon variant='outline' color={'#FFCECE'} size={30} m={'xs'}>
                <IconSquareFilled />
              </ThemeIcon>
            </KanbanTooltip>
            <KanbanTooltip
              withinPortal={false}
              label={attachedType === FlaggedImpactedCiAttachedType.CALCULATED ? 'Impact Direction' : 'Relationship Direction'}>
              <ThemeIcon variant='outline' color={getIconColorByType(attachedType)} size={30} m={'xs'}>
                <IconArrowNarrowLeft color={getIconColorByType(attachedType)} />
              </ThemeIcon>
            </KanbanTooltip>
            <KanbanTooltip withinPortal={false} label='Ci In Change Plan'>
              <ThemeIcon variant='outline' color={'#e79242fa'} size={30} m={'xs'}>
                <IconSquareFilled />
              </ThemeIcon>
            </KanbanTooltip>
          </>
        )}
        {isShowLabelHightLight && (
          <>
            <KanbanIconButton
              onClick={() => {
                setNodeHightLight({});
                resetHightLight(goDiagram);
              }}
              variant='transparent'
              size={'lg'}
              pl={12}
              c={'red'}>
              <IconX />
            </KanbanIconButton>
            <KanbanText m={'xs'}>Highlight {nodeHightLight.upStream ? 'Up Stream' : 'Down Stream'} CI:</KanbanText>
            <KanbanText m={'xs'}>
              <Anchor
                onClick={() => {
                  if (nodeHightLight?.nodeId) {
                    scrollAndZoomToItem(goDiagram, nodeHightLight.nodeId);
                  }
                }}
                target='_blank'
                underline='always'>
                {nodeHightLight?.nodeName}
              </Anchor>
            </KanbanText>
          </>
        )}
        <KanbanText m={'xs'}>level filter: {levelFilter}</KanbanText>
        <KanbanTooltip withinPortal={false} label='undo'>
          <ThemeIcon variant='outline' color={'primary'} size={30} m={'xs'}>
            <IconArrowBackUp
              onClick={() => {
                goDiagram?.commandHandler.undo();
              }}
              size='3rem'
              stroke={2}
            />
          </ThemeIcon>
        </KanbanTooltip>
        <KanbanTooltip withinPortal={false} label='redo'>
          <ThemeIcon variant='outline' color={'primary'} size={30} m={'xs'}>
            <IconArrowForwardUp
              onClick={() => {
                goDiagram?.commandHandler.redo();
              }}
              size='3rem'
              stroke={2}
            />
          </ThemeIcon>
        </KanbanTooltip>
        <KanbanTooltip withinPortal={false} label={handleFullScreen.active ? 'Exit view full screen' : 'View full screen'}>
          <ThemeIcon variant='outline' color={'primary'} size={30} m={'xs'}>
            {handleFullScreen.active ? (
              <IconArrowsDiagonalMinimize2
                onClick={() => {
                  handleFullScreen.exit();
                  goDiagram?.requestUpdate();
                }}
                size='3rem'
                stroke={2}
              />
            ) : (
              <IconArrowsDiagonal
                onClick={() => {
                  handleFullScreen.enter();
                  goDiagram?.requestUpdate();
                }}
                size='3rem'
                stroke={2}
              />
            )}
          </ThemeIcon>
        </KanbanTooltip>
      </div>
      <div className={stylesCss['icon-zoom']}>
        <div className={stylesCss['icon']}>
          <KanbanTooltip withinPortal={false} label='Zoom In'>
            <ThemeIcon variant='outline' color={'primary'} size={30}>
              <IconZoomIn onClick={() => zoomIn(goDiagram)} stroke={2} />
            </ThemeIcon>
          </KanbanTooltip>
        </div>

        <div className={stylesCss['icon']}>
          <KanbanTooltip withinPortal={false} label='Zoom out'>
            <ThemeIcon variant='outline' color={'primary'} size={30}>
              <IconZoomOut onClick={() => zoomOut(goDiagram)} stroke={2} />
            </ThemeIcon>
          </KanbanTooltip>
        </div>
        <div className={stylesCss['icon']}>
          <KanbanTooltip withinPortal={false} label='Zoom to fit'>
            <ThemeIcon variant='outline' color={'primary'} size={30}>
              <IconZoomReset onClick={() => goDiagram?.zoomToFit()} stroke={2} />
            </ThemeIcon>
          </KanbanTooltip>
        </div>
        <div className={stylesCss['icon']}>
          <KanbanTooltip withinPortal={false} label='Show/hide preview'>
            <ThemeIcon variant='outline' color={'primary'} size={30}>
              <IconArrowsRightLeft onClick={() => setIsShowPreview(!isShowPreview)} stroke={2} />
            </ThemeIcon>
          </KanbanTooltip>
        </div>
      </div>
    </>
  );
};

export default RightGraphButton;
