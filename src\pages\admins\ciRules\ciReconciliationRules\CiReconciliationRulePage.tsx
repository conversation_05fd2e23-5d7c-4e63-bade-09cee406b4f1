import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { ColumnType, KanbanButton, KanbanTable, KanbanText, TableAffactedSafeType } from 'kanban-design-system';
import { IconEdit, IconEye } from '@tabler/icons-react';
import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { KanbanIconButton } from 'kanban-design-system';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { useNavigate } from 'react-router-dom';
import { buildCiReconciliationRuleDetailUrl, buildCiTypeUrl } from '@common/utils/RouterUtils';
import equal from 'fast-deep-equal';
import { CiReconciliationRuleAction, type CiReconciliationRule } from '@models/CiReconciliationRule';
import { Tooltip } from '@mantine/core';
import { BreadcrumbComponent } from '@pages/admins/breadcrumb/BreadcrumbComponent';
import { CiReconciliationRuleApi } from '@api/CiReconciliationRuleApi';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import { MAX_NUMBER_LENGTH, MAX_TEXT_LENGTH } from '@common/constants/FieldLengthConstants';

export const CiReconciliationRulePage = () => {
  const navigate = useNavigate();

  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);

  const [listData, setListData] = useState<CiReconciliationRule[]>([]);
  const [isLoadingTable, setIsLoadingTable] = useState(false);

  const columns: ColumnType<CiReconciliationRule>[] = useMemo(
    () => [
      {
        title: 'Id',
        name: 'id',
        hidden: true,
        advancedFilter: {
          variant: 'number',
          filterModes: ['equals', 'notEquals', 'greaterThan', 'greaterThanOrEqualTo', 'lessThan', 'lessThanOrEqualTo'],
          customProps: { maxLength: MAX_NUMBER_LENGTH },
        },
      },
      {
        title: 'Name Rules',
        name: 'name',
        width: '10%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Description',
        name: 'description',
        width: '50%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data) => {
          return (
            <>
              <Tooltip label={data} color='gray' multiline w={'20%'} withArrow>
                <KanbanText lineClamp={2}>{data}</KanbanText>
              </Tooltip>
            </>
          );
        },
      },
      {
        title: 'Apply to',
        name: 'ciTypeName',
        width: '15%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (_, rowData) => {
          const ciTypeName = rowData?.ciTypeName || 'Not Available';
          return (
            <Tooltip label={ciTypeName} multiline w={350} style={{ wordBreak: 'break-word' }}>
              <KanbanButton
                size='compact-xs'
                radius={'lg'}
                maw={'200px'}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  navigate(buildCiTypeUrl(rowData.id));
                }}>
                {ciTypeName}
              </KanbanButton>
            </Tooltip>
          );
        },
      },
    ],
    [navigate],
  );

  const findAllWithPaging = useCallback(() => {
    if (!tableAffected) {
      return;
    }
    const dataSend = tableAffectedToMultiColumnFilterPaginationRequestModel<CiReconciliationRule>(
      tableAffected.sortedBy ? tableAffected : { ...tableAffected, sortedBy: 'createdDate', isReverse: true },
    );

    setIsLoadingTable(true);
    CiReconciliationRuleApi.findAllWithPaging(dataSend)
      .then((res) => {
        if (res.data) {
          setListData(res.data?.content || []);
          setTotalRecords(res.data.totalElements);
        }
      })
      .catch(() => {})
      .finally(() => {
        setIsLoadingTable(false);
      });
  }, [tableAffected]);

  useEffect(() => {
    findAllWithPaging();
  }, [findAllWithPaging]);

  const deleteRows = (ids: number[]) => {
    CiReconciliationRuleApi.deleteByIdIn(ids)
      .then(() => {
        NotificationSuccess({
          message: 'Deleted successfully',
        });
        const newData = listData.filter((item) => !ids.includes(item.id));
        setListData(newData);
      })
      .catch(() => {});
  };

  return (
    <>
      {/* 4746 ci rules*/}
      <BreadcrumbComponent />
      <HeaderTitleComponent title='CI Reconcile' />
      <div style={{ flex: 2 }}>
        <KanbanTable
          columns={columns}
          key={1}
          data={listData}
          isLoading={isLoadingTable}
          showNumericalOrderColumn={true}
          searchable={{
            enable: true,
            debounceTime: 800,
          }}
          advancedFilterable={{
            enable: true,
            debounceTime: 1000,
            resetOnClose: true,
            compactMode: true,
          }}
          onRowClicked={(data) => {
            navigate(buildCiReconciliationRuleDetailUrl(data.id, CiReconciliationRuleAction.VIEW));
          }}
          actions={{
            customAction: (data) => {
              return (
                <>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      navigate(buildCiReconciliationRuleDetailUrl(data.id, CiReconciliationRuleAction.VIEW));
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      navigate(buildCiReconciliationRuleDetailUrl(data.id, CiReconciliationRuleAction.UPDATE));
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                </>
              );
            },
          }}
          selectableRows={{
            enable: false,
            onDeleted(rows) {
              deleteRows(rows.map((x) => x.id));
            },
          }}
          pagination={{
            enable: true,
          }}
          serverside={{
            totalRows: totalRecords,
            onTableAffected(dataSet) {
              if (!equal(tableAffected, dataSet)) {
                setTableAffected(dataSet);
              }
            },
          }}
        />
      </div>
    </>
  );
};
CiReconciliationRulePage.whyDidYouRender = true;
CiReconciliationRulePage.displayName = 'CiReconciliationRulePage';
export default CiReconciliationRulePage;
