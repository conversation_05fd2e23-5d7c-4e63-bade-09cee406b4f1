import { KanbanConfirmModal } from 'kanban-design-system';
import { KanbanTable, ColumnType } from 'kanban-design-system';
import { renderDateTime } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import { IconEdit } from '@tabler/icons-react';
import React, { useEffect, useState, useMemo } from 'react';
import { KanbanIconButton } from 'kanban-design-system';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { CiAdvancedSearchResponse, CiAdvancedSearchApi } from '@api/CiAdvanedSearchApi';
import { KanbanText } from 'kanban-design-system';
import type { CiAdvancedSearchDTO, CiAdvancedSearchModel } from '@models/CiAdvancedSearch';
import AdvancedSearchForm from './AdvancedSearchForm';

type ListAdvancedSearchProps = {
  updateShowLoadButton: (isShow: boolean) => void;
  updateDataSelected: (datas: CiAdvancedSearchModel) => void;
  updateTotalSelectedLoadSearch: (total: number) => void;
  ciTypeId?: number;
};

export const ListAdvancedSearchComponent = (props: ListAdvancedSearchProps) => {
  const { ciTypeId, updateDataSelected, updateShowLoadButton, updateTotalSelectedLoadSearch } = props;
  const columns: ColumnType<CiAdvancedSearchResponse>[] = useMemo(() => {
    return [
      {
        title: 'Name',
        name: 'name',
        customRender: (data) => {
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {data}
            </KanbanText>
          );
        },
        width: '20%',
      },
      {
        title: 'Description',
        name: 'description',
        customRender: (data) => {
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {data}
            </KanbanText>
          );
        },
        width: '30%',
      },
      {
        title: 'Created By',
        name: 'createdBy',
      },
      {
        title: 'Created Date',
        name: 'createdDate',
        customRender: renderDateTime,
      },
    ];
  }, []);

  const [listData, setListData] = useState<CiAdvancedSearchResponse[]>([]);
  const [isDetailView, setIsDetailView] = useState<boolean>(false);
  const defaultData = {
    id: 0,
    name: '',
    description: '',
    dataQuery: '',
  };
  const [advancedSearchFormData, setAdvancedSearchFormData] = useState<CiAdvancedSearchDTO>(defaultData);
  const [openedModalUpdate, { close: closeModalUpdate, open: openModalUpdate }] = useDisclosure(false);
  const [openedModalConfirmOverride, { close: closeModalConfirmOverride, open: openModalConfirmOverride }] = useDisclosure(false);
  // const [advancedSelectedExist, setAdvancedSelectedExist] = useState<CiAdvancedSearchModel>(ciAdvancedSearchDefault);

  useEffect(() => {
    CiAdvancedSearchApi.getAdvancedSearchOfCurrentUser(ciTypeId && ciTypeId !== 0 ? ciTypeId : undefined)
      .then((res) => {
        if (res.data) {
          setListData(res.data || []);
        }
      })
      .catch(() => {});
  }, [ciTypeId]);

  const updateAdvancedSearchFormData = (newFormData: CiAdvancedSearchDTO) => {
    setAdvancedSearchFormData(newFormData);
  };

  const deleteRows = (ids: number[]) => {
    CiAdvancedSearchApi.deleteByIds(ids, ciTypeId)
      .then(() => {
        NotificationSuccess({
          message: 'Deleted successfully',
        });
        const newData = listData.filter((item) => !ids.includes(item.id));
        updateShowLoadButton(false);
        setListData(newData);
      })
      .catch(() => {});
  };

  const deleteRow = (id: number) => {
    deleteRows([id]);
  };

  const executeEventConfirmUpdateAdvancedSearch = () => {
    CiAdvancedSearchApi.updateOrOverrideAdvancedSearch(advancedSearchFormData, advancedSearchFormData.id)
      .then((res) => {
        NotificationSuccess({
          message: 'Updated successfully',
        });
        setAdvancedSearchFormData(defaultData);

        const index = listData.findIndex((x) => x.id === res.data.id);
        const newData = [...listData];
        newData[index] = res.data;
        const updatedData = newData.filter((item) => !(item.name === advancedSearchFormData.name && item.id !== advancedSearchFormData.id));
        setListData(updatedData);
        closeModalUpdate();
      })
      .catch(() => {});
  };

  const onConfirmOverride = () => {
    executeEventConfirmUpdateAdvancedSearch();
    closeModalConfirmOverride();
  };

  const onConfirmUpdate = () => {
    CiAdvancedSearchApi.findByConditions(advancedSearchFormData.id, advancedSearchFormData.name, advancedSearchFormData.ciTypeId)
      .then((res) => {
        if (res.data) {
          openModalConfirmOverride();
        } else {
          executeEventConfirmUpdateAdvancedSearch();
        }
      })
      .catch(() => {});
  };

  return (
    <>
      <KanbanConfirmModal
        modalProps={{ size: 'lg' }}
        title={!isDetailView ? 'Update Advanced Search' : 'Advanced Search Detail'}
        onConfirm={!isDetailView ? onConfirmUpdate : undefined}
        onClose={closeModalUpdate}
        opened={openedModalUpdate}
        disabledConfirmButton={!advancedSearchFormData.name || advancedSearchFormData.name.trim() === ''}>
        <AdvancedSearchForm
          ciAdvancedSearchInfo={advancedSearchFormData}
          updateAdvancedSearchFormData={updateAdvancedSearchFormData}
          isDetailView={isDetailView}></AdvancedSearchForm>
      </KanbanConfirmModal>

      <KanbanConfirmModal
        modalProps={{ size: 'lg' }}
        title={'Confirm Override Data'}
        onConfirm={onConfirmOverride}
        onClose={closeModalConfirmOverride}
        opened={openedModalConfirmOverride}>
        <KanbanText>Name Advanced Search is existed. Would you like override data? </KanbanText>
      </KanbanConfirmModal>

      <KanbanTable
        columns={columns}
        key={1}
        data={listData}
        showNumericalOrderColumn={true}
        searchable={{
          enable: true,
        }}
        actions={{
          deletable: {
            onDeleted(data) {
              deleteRow(data.id);
            },
          },
          customAction: (data) => {
            return (
              <>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    setAdvancedSearchFormData({ ...data });
                    setIsDetailView(false);
                    openModalUpdate();
                  }}>
                  <IconEdit />
                </KanbanIconButton>
              </>
            );
          },
        }}
        selectableRows={{
          enable: true,
          onDeleted(rows) {
            deleteRows(rows.map((x) => x.id));
          },
          onSelectedRowsChanged(rows) {
            updateTotalSelectedLoadSearch(rows.length);
            if (rows.length === 0 || rows.length > 1) {
              updateShowLoadButton(false);
            } else {
              updateShowLoadButton(true);
              updateDataSelected(rows[0]);
            }
          },
        }}
        onRowClicked={(data) => {
          setAdvancedSearchFormData({ ...data });
          setIsDetailView(true);
          openModalUpdate();
        }}
        pagination={{
          enable: true,
        }}
      />
    </>
  );
};
export default ListAdvancedSearchComponent;
