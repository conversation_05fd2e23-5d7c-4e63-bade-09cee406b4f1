import { CIBussinessViewsApi, CIBussinessViewsResponse } from '@api/CIBussinessViewsApi';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { KanbanInput } from 'kanban-design-system';
import { KanbanTextarea } from 'kanban-design-system';
import { KanbanConfirmModal } from 'kanban-design-system';
import { KanbanTable, ColumnType, TableAffactedSafeType, KanbanTableProps } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import { IconPlus, IconEdit, IconEye } from '@tabler/icons-react';
import React, { useEffect, useState, useMemo, useCallback } from 'react';
// import { useDispatch } from 'react-redux';
import { KanbanIconButton } from 'kanban-design-system';
import { NotificationSuccess, NotificationError } from '@common/utils/NotificationUtils';
import type { ConfigItemResponse } from '@api/ConfigItemApi';
import { SelectCiPickerComponent } from '@components/commonCi/SelectCiComponent';
import { useNavigate } from 'react-router-dom';
import { buildCiUrl, buildViewCreateGraphUrl } from '@common/utils/RouterUtils';
import { renderDateTime } from 'kanban-design-system';
import equal from 'fast-deep-equal';
import { formatStandardName } from '@common/utils/StringUtils';

import { KanbanText } from 'kanban-design-system';
import { AclPermission } from '@models/AclPermission';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { CiDetailScreenType, CiDetailSubTabType } from '@common/constants/CiDetail';
import GuardComponent from '@components/GuardComponent';
import { BreadcrumbComponent } from '@pages/admins/breadcrumb/BreadcrumbComponent';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import { MAX_TEXT_LENGTH } from '@common/constants/FieldLengthConstants';

export const BussinessViews = () => {
  // const dispatch = useDispatch();
  const navigate = useNavigate();

  const columns: ColumnType<CIBussinessViewsResponse>[] = useMemo(() => {
    return [
      {
        title: 'View Name',
        name: 'viewName',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data) => {
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {data}
            </KanbanText>
          );
        },
        width: '20%',
      },
      // {
      //     title: 'Visibility',
      //     name: 'visibility',
      // },

      {
        title: 'Base CI',
        name: 'ciName',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data, rowData) => {
          // return <>{rowData.baseCiDTO? rowData.baseCiDTO.name :  ''}</>;
          return <>{rowData.ciName}</>;
        },
      },
      {
        title: 'Description',
        name: 'description',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data) => {
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {data}
            </KanbanText>
          );
        },
        width: '30%',
      },
      {
        title: 'View Created by',
        name: 'createdBy',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'View Created at',
        name: 'createdDate',
        advancedFilter: {
          variant: 'date',
          customProps: {
            popoverProps: {
              withinPortal: false,
            },
          },
        },
        customRender: renderDateTime,
      },
      {
        title: 'User Edited Graph',
        name: 'modifiedBy',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'View Edited at',
        name: 'modifiedDate',
        advancedFilter: {
          variant: 'date',
          customProps: {
            popoverProps: {
              withinPortal: false,
            },
          },
        },
        customRender: renderDateTime,
      },
    ];
  }, []);

  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);

  const [listData, setListData] = useState<CIBussinessViewsResponse[]>([]);
  const [newValue, setNewValue] = useState<ConfigItemResponse[]>([]);

  const fetchListBusinessView = useCallback(() => {
    if (!tableAffected) {
      return;
    }
    const dataSend = tableAffectedToMultiColumnFilterPaginationRequestModel<CIBussinessViewsResponse>(
      tableAffected.sortedBy ? tableAffected : { ...tableAffected, sortedBy: 'createdDate', isReverse: true },
    );

    CIBussinessViewsApi.getAll(dataSend)
      .then((res) => {
        if (res.data) {
          setListData(res.data?.content || []);
          setTotalRecords(res.data.totalElements);
        }
      })
      .catch(() => {});
  }, [tableAffected]);

  useEffect(() => {
    fetchListBusinessView();
  }, [fetchListBusinessView]);

  const [openedPopupBussiness, { close: closePopupBussiness, open: openPopupBussiness }] = useDisclosure(false);
  const [openedPopupSelectCI, { close: closePopupSelectCI, open: openPopupSelectCI }] = useDisclosure(false);
  const [isDetailView, setIsDetailView] = useState<boolean>(false);
  const defaultData: CIBussinessViewsResponse = {
    id: 0,
    viewName: '',
    visibility: '',
    description: '',
    share: true,
    graphView: '',
    ciId: 0,
    ciName: '',
  };
  const [dataView, setDataView] = useState<CIBussinessViewsResponse>({ ...defaultData });

  const deleteRow = useCallback(
    (id: number) => {
      CIBussinessViewsApi.deleteById(id)
        .then((res) => {
          if (res.data) {
            NotificationSuccess({
              message: 'Deleted successfully',
            });
            // const newData = listData.filter((x) => x.id !== id);
            // setListData(newData);
            fetchListBusinessView();
          }
        })
        .catch(() => {});
    },
    [fetchListBusinessView],
  );

  const deleteGraphView = useCallback(
    (ids: number[]) => {
      CIBussinessViewsApi.deleteByIds(ids)
        .then(() => {
          NotificationSuccess({
            message: 'Deleted successfully',
          });
          // const newData = listData.filter((item) => !ids.includes(item.id));
          // setListData(newData);
          fetchListBusinessView();
        })
        .catch(() => {});
    },
    [fetchListBusinessView],
  );

  const onConfirmCreateNew = () => {
    if (!dataView.viewName) {
      return;
    }
    // const ciId = dataView?.baseCiDTO?.id ;
    const ciId = dataView.ciId;
    CIBussinessViewsApi.saveOrUpdate(dataView, ciId)
      .then((res) => {
        NotificationSuccess({
          message: 'Updated successfully',
        });
        setDataView(defaultData);
        const index = listData.findIndex((x) => x.id === res.data.entity.id);
        const newData = [...listData];
        //[31/10/2024] fix not show base CI when edit
        newData[index] = { ...res.data.entity, ciName: newData[index].ciName };
        setListData(newData);
        closePopupBussiness();
      })
      .catch(() => {});
  };

  const onConfirmSelect = () => {
    const ciId = newValue[0]?.id;
    const ciTypeId = newValue[0]?.ciTypeId;

    if (!ciId) {
      NotificationError({
        message: 'Please choose Base CI.',
      });
    } else {
      navigate(buildCiUrl(ciTypeId, ciId, CiDetailScreenType.RELATIONSHIPS, CiDetailSubTabType.GRAPHVIEW));
    }
  };

  const handleChange = useCallback(
    (value: string) => {
      setDataView((prev) => {
        return { ...prev, viewName: value };
      });
    },
    [setDataView],
  );

  const tableViewListBusinessProps: KanbanTableProps<CIBussinessViewsResponse> = useMemo(() => {
    return {
      columns: columns,
      key: 1,
      data: listData,
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 300,
      },
      advancedFilterable: {
        enable: true,
        debounceTime: 1000,
        resetOnClose: true,
        compactMode: true,
      },
      actions: {
        deletable: isCurrentUserMatchPermissions([AclPermission.deleteBusinessView])
          ? {
              onDeleted: (data) => deleteRow(data.id),
            }
          : undefined,
        customAction: isCurrentUserMatchPermissions(AclPermission.actionTableBusinessViewPermissions)
          ? (data) => (
              <>
                {isCurrentUserMatchPermissions([AclPermission.viewDetailBusinessView]) && (
                  <KanbanIconButton
                    variant='transparent'
                    size='sm'
                    onClick={() => {
                      navigate(buildViewCreateGraphUrl(data.id));
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                )}
                {isCurrentUserMatchPermissions([AclPermission.updateBusinessView]) && (
                  <KanbanIconButton
                    variant='transparent'
                    size='sm'
                    onClick={() => {
                      setIsDetailView(false);
                      setDataView({ ...data });
                      openPopupBussiness();
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                )}
              </>
            )
          : undefined,
      },
      selectableRows: {
        enable: !!isCurrentUserMatchPermissions([AclPermission.deleteBusinessView]),
        onDeleted: isCurrentUserMatchPermissions([AclPermission.deleteBusinessView]) ? (rows) => deleteGraphView(rows.map((x) => x.id)) : undefined,
      },
      onRowClicked: (data) => {
        if (isCurrentUserMatchPermissions([AclPermission.viewDetailBusinessView])) {
          setIsDetailView(false);
          setDataView({ ...data });
          navigate(buildViewCreateGraphUrl(data.id));
        }
      },
      pagination: {
        enable: true,
      },
      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
    };
  }, [
    columns,
    listData,
    totalRecords,
    tableAffected,
    setTableAffected,
    setIsDetailView,
    setDataView,
    openPopupBussiness,
    deleteRow,
    deleteGraphView,
    navigate,
  ]);

  return (
    <>
      {/* 4736 bussiness view   */}
      <BreadcrumbComponent />

      <KanbanConfirmModal
        title={!isDetailView ? 'Update Bussiness View' : 'Bussiness View Detail'}
        onConfirm={!isDetailView ? onConfirmCreateNew : undefined}
        onClose={closePopupBussiness}
        opened={openedPopupBussiness}
        disabledConfirmButton={!dataView.viewName || dataView.viewName.trim() === ''}>
        <KanbanInput
          disabled={isDetailView}
          withAsterisk
          label='View Name'
          value={dataView.viewName || ''}
          onChange={(e) => {
            const value = e.target.value;
            handleChange(value);
          }}
          onBlur={(e) => {
            const value = e.target.value;
            handleChange(formatStandardName(value));
          }}
        />

        <KanbanTextarea
          disabled={isDetailView}
          label='Description'
          value={dataView.description || ''}
          onChange={(e) => {
            const value = e.target.value;
            setDataView((prev) => {
              const current = { ...prev };
              current.description = value;
              return current;
            });
          }}
        />
      </KanbanConfirmModal>

      <KanbanConfirmModal
        title='Select CI'
        onConfirm={onConfirmSelect}
        onClose={closePopupSelectCI}
        opened={openedPopupSelectCI}
        disabledConfirmButton={!newValue}
        modalProps={{
          size: '100%',
        }}>
        <SelectCiPickerComponent maxSelection={1} onChange={setNewValue} />
      </KanbanConfirmModal>

      <HeaderTitleComponent
        title='Business Views'
        rightSection={
          <GuardComponent requirePermissions={[AclPermission.createBusinessView]} hiddenOnUnSatisfy>
            <KanbanButton onClick={openPopupSelectCI} leftSection={<IconPlus />}>
              New Business view
            </KanbanButton>
          </GuardComponent>
        }
      />
      <div style={{ flex: 2 }}>
        <KanbanTable {...tableViewListBusinessProps} />
      </div>
    </>
  );
};
export default BussinessViews;
