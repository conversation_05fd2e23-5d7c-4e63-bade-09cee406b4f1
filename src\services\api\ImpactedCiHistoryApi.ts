import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import type { CiImpactedByRelationshipInformationResponseDto } from '@models/ChangeAssessment';

export type CiImpactedHistoryDto = {
  id?: number;
  ciRelationshipId?: number | null;
  requestMappingId?: number;
  ciImpactedId?: number;
  ciChangeId?: number;
  relationshipLevel?: number;
  version?: number;
  deleted?: boolean | null;
  flowNodes: string;
  attachedType?: string;
  ciChangeIds: number[];
  userComment?: string;
  impactedAssessmentLevel?: string;
};
export class ImpactedCiHistoryApi extends BaseApi {
  static baseUrl = BaseUrl.ciImpactedHistories;
  static findCiImpactedHistoryLatestByChangeId(changeAssessmentId: number) {
    //get list all instead of paging response : related to check if disable flag button in table ImpactedCiTableComponent
    return BaseApi.getData<CiImpactedByRelationshipInformationResponseDto[]>(`${this.baseUrl}?changeId=${changeAssessmentId}`);
  }

  static findCiImpactedHistoryWhenFlag(ciImpactedHistoryId: number) {
    return BaseApi.getData<CiImpactedHistoryDto[]>(`${this.baseUrl}/${ciImpactedHistoryId}/histories`);
  }
}
