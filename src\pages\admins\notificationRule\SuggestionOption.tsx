import { React<PERSON>enderer } from '@tiptap/react';
import tippy, { GetReferenceClientRect, Props, Instance as TippyInstance } from 'tippy.js';
import MentionList from './MentionList';
import type { SuggestionProps } from '@tiptap/suggestion';
import type { MentionListHandle, SuggestionType } from '@models/NotificationTemplate';
import { NotificationTemplateParamPrefix } from '@common/constants/NotificationTemplateConstants';

const suggestion: SuggestionType = {
  char: NotificationTemplateParamPrefix,
  items: () => {
    return ['sample'];
  },
  render: () => {
    let component: ReactRenderer<MentionListHandle, SuggestionProps> | undefined;
    let popup: TippyInstance[] | undefined;

    return {
      onStart: (prs: SuggestionProps) => {
        component = new ReactRenderer<MentionListHandle, SuggestionProps>(MentionList, {
          props: prs,
          editor: prs.editor,
        });

        if (!prs.clientRect) {
          return;
        }
        const rect: GetReferenceClientRect = () => {
          const rect = prs.clientRect && prs.clientRect();
          return rect ? rect : new DOMRect();
        };
        const optionalProps: Partial<Props> = {
          getReferenceClientRect: rect,
          appendTo: () => document.body,
          content: component.element,
          showOnCreate: true,
          interactive: true,
          trigger: 'manual',
          placement: 'bottom-start',
        };
        popup = tippy('body', optionalProps);
      },

      onUpdate(props: SuggestionProps) {
        if (component) {
          component.updateProps(props);
        }

        if (!props.clientRect || !popup) {
          return;
        }
        const rect: GetReferenceClientRect = () => {
          const rect = props.clientRect && props.clientRect();
          return rect ? rect : new DOMRect();
        };

        popup[0].setProps({
          getReferenceClientRect: rect,
        });
      },

      onKeyDown: (props: { event: KeyboardEvent }) => {
        if (props.event.key === 'Escape') {
          if (popup) {
            popup[0].hide();
          }
          return true;
        }
        if (component && component.ref) {
          return component.ref.onKeyDown(props);
        }
        return false;
      },

      onExit() {
        if (popup) {
          popup[0].destroy();
        }
        component?.destroy();
      },
    };
  },
};

export default suggestion;
