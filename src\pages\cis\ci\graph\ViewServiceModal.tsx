import { KanbanTabs } from 'kanban-design-system';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { ServiceMappingApi } from '@api/ServiceMappingApi';
import type { ServiceMapingDetailsModel } from '@models/ServiceMapping';
import ServiceMappingInfo from './tabViewService/ServiceMappingInfo';
import ServiceMappingRelatedTableTab from './tabViewService/ServiceMappingRelatedTableTab';
import { useDispatch } from 'react-redux';
import { ciRelationshipTypesSlice } from '@slices/CiRelationshipTypesSlice';
import { ciTypesSlice } from '@slices/CiTypesSlice';
import { ServiceMappingCiImpactGraphTab } from './tabViewService/ServiceMappingCiImpactGraphTab';
import { ServiceMappingGraphTab } from './tabViewService/ServiceMappingGraphTab';
import { NotificationError } from '@common/utils/NotificationUtils';

export interface ViewServiceMappingHandle {
  resizeDiagram: () => void;
}

type ViewServiceMappingProps = {
  serviceMapId: number;
  activeTab?: string;
};

export const ViewServiceModal = forwardRef<ViewServiceMappingHandle, ViewServiceMappingProps>((props, ref) => {
  const { serviceMapId } = props;
  const [serviceMapDetails, setServiceMapDetails] = useState<ServiceMapingDetailsModel | undefined>(undefined);
  const [activeTab, setActiveTab] = useState<string>(props.activeTab || '1');
  const designRef = useRef<{ getGoDiagram: () => go.Diagram | undefined }>(null);

  const dispatch = useDispatch();

  const resizeDiagram = useCallback(() => {
    designRef.current?.getGoDiagram()?.requestUpdate();
  }, []);

  useImperativeHandle(
    ref,
    () => ({
      resizeDiagram,
    }),
    [resizeDiagram],
  );

  /**
   * fetch relationship type
   */
  useEffect(() => {
    dispatch(ciRelationshipTypesSlice.actions.fetchForEmpty());
    dispatch(ciTypesSlice.actions.fetchForEmpty());
  }, [dispatch]);

  const fetchServiceMapCiImpactView = useCallback(() => {
    if (serviceMapId) {
      ServiceMappingApi.getDetailsById(serviceMapId)
        .then((res) => {
          if (res.data) {
            setServiceMapDetails(res.data);
            // view message error when delete start CI
            if (!res.data.startCiId) {
              NotificationError({
                message: 'Start CI has been deleted, please edit service map and set up the start CI again.',
              });
            }
          }
        })
        .catch(() => {});
    }
  }, [serviceMapId]);

  useEffect(() => {
    fetchServiceMapCiImpactView();
  }, [fetchServiceMapCiImpactView]);
  return (
    <>
      <br />
      <KanbanTabs
        configs={{
          value: activeTab,
          onChange: (value) => {
            setActiveTab(value || '1');
          },
        }}
        tabs={{
          '1': {
            title: 'View related CIs',
            content: <ServiceMappingRelatedTableTab listImpact={serviceMapDetails?.ciImpacts} />,
          },
          '2': {
            title: 'View map CIs',
            content: <>{<ServiceMappingCiImpactGraphTab ref={designRef} serviceMapDetails={serviceMapDetails} />}</>,
          },
          '3': {
            title: 'View service mapping',
            content: <>{<ServiceMappingGraphTab ref={designRef} serviceMapDetails={serviceMapDetails} />}</>,
          },
          '4': {
            content: <ServiceMappingInfo serviceMapDetails={serviceMapDetails} />,
            title: 'View service',
          },
        }}
      />
    </>
  );
});

ViewServiceModal.displayName = 'ViewServiceModal';
export default ViewServiceModal;
