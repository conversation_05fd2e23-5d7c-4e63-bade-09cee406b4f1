import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
import { JobDiscoveryModel } from '@models/JobDiscoveryConfig';
import { JobHistoryModel } from '@models/JobHistory';

export type JobResponseModel = PaginationResponseModel<JobDiscoveryModel>;
export type JobRequestModel = PaginationRequestModel<JobDiscoveryModel>;
export type JobHistoryResponseModel = PaginationResponseModel<JobHistoryModel>;
export type JobHistoryRequestModel = PaginationRequestModel<JobHistoryModel>;

export class JobDiscoveryConfigApi extends BaseApi {
  static baseUrl = BaseUrl.jobDiscoveryConfigs;

  static getAllJobByGroup(groupType: string, pagination: JobRequestModel) {
    return BaseApi.postData<JobResponseModel>(`${this.baseUrl}/${groupType}/groups`, pagination);
  }
  static getById(id: number) {
    return BaseApi.getData<JobDiscoveryModel>(`${this.baseUrl}/${id}`);
  }
  static deleteById(id: number) {
    return BaseApi.deleteData<boolean>(`${this.baseUrl}/${id}`);
  }
  static deleteByIds(ids: number[]) {
    return BaseApi.deleteData<number[]>(`${this.baseUrl}/batch`, {
      ids,
    });
  }
  static save(data: JobDiscoveryModel) {
    return BaseApi.postData<JobDiscoveryModel>(`${this.baseUrl}`, data);
  }
  static getAllJobHistoryByJobId(id: number, pagination: JobHistoryRequestModel) {
    return BaseApi.postData<JobHistoryResponseModel>(`${this.baseUrl}/${id}/job-histories`, pagination);
  }
}
