import React, { forwardRef, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import stylesCss from './CiType.module.scss';
import {
  ActionIcon,
  Alert,
  Box,
  Collapse,
  ComboboxItem,
  Container,
  Flex,
  Grid,
  GridCol,
  Group,
  InputBase,
  Pill,
  Stack,
  useMantineTheme,
} from '@mantine/core';
import { IconPlus, IconTrash, IconEdit, IconCaretRightFilled, IconCaretDownFilled, IconAlertTriangle } from '@tabler/icons-react';
import {
  CiTypeAttributeType,
  type CiTypeReferFieldsModel,
  type ConfigItemAttributeObject,
  type ConfigItemTypeAttrModel,
} from '@models/ConfigItemTypeAttr';
import { KanbanText } from 'kanban-design-system';
import { useDraggableInPortal } from '@common/hooks/useDraggableInPortal';
import { KanbanModal } from 'kanban-design-system';
import { CiTypeColumn, CiTypeAttributeDataType } from '@models/CiType';
import { useDisclosure } from '@mantine/hooks';
import { KanbanInput } from 'kanban-design-system';
import { KanbanCheckbox } from 'kanban-design-system';
import { KanbanSelect } from 'kanban-design-system';
import { KanbanButton } from 'kanban-design-system';
import { UseFormReturnType, useForm } from '@mantine/form';
import { NotificationError, NotificationSuccess } from '@common/utils/NotificationUtils';
import { KanbanTextarea } from 'kanban-design-system';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import { v4 as uuid } from 'uuid';
import type { ConfigItemTypeInfoModel } from '@models/ConfigItemTypeInfo';
import type { ConfigItemTypeAttrResponse } from '@api/ConfigItemTypeAttrApi';
import { CiTypePickerComponent } from '@components/commonCi/SelectCiTypeComponent';
import { CiTypeReferenceApi } from '@api/CiTypeReferenceApi';
import { getConfigs } from '@core/configs/Configs';
import { DiscoveryTransformMapApi } from '@api/DiscoveryTransformMapApi';
import { DiscoveryTransformMapModel } from '@models/DiscoveryTransformMap';
import { DragDropContext, Draggable, Droppable, DropResult } from '@hello-pangea/dnd';
import { swapElements } from '@common/utils/ArrayUtils';

export interface CiTypeAttributeDetailMethods {
  saveAllData: () => void;
}
export type CiTypeAttributeDetailProps = {
  ciTypeId: number;
  parentId?: number;
  ciTypeInfoUpdate: ConfigItemTypeInfoModel;
  setCiTypeInfoUpdate: React.Dispatch<React.SetStateAction<ConfigItemTypeInfoModel>>;
  setIsChange: (value: boolean) => void;
  setCiAttributes: React.Dispatch<React.SetStateAction<ConfigItemAttributeObject>>;
};

export type ConfigItemOptionPickerModel = {
  label: string;
  value: string;
};

type FormValues = {
  name: string;
  type: string;
  description: string;
  referField: string;
};

type FormAddOption = {
  label: string;
  value: string;
};

const sortData = (inputArray: ConfigItemTypeAttrResponse[]) => {
  return inputArray.slice().sort((a, b) => {
    const aOrder = a.order || 0;
    const bOrder = b.order || 0;

    if (a.id === 0 && b.id === 0) {
      return aOrder - bOrder;
    } else if (a.id === 0) {
      return -1;
    } else if (b.id === 0) {
      return 1;
    } else {
      return aOrder - bOrder;
    }
  });
};

const validateRequired = (value: string | undefined) => {
  const trimValue = value?.trim() || '';
  if (!trimValue) {
    return 'This field is required';
  }
  return undefined; // No error
};

const validateReferenceField = (value: string | undefined, values: FormValues) => {
  if (CiTypeAttributeDataType.REFERENCE === values.type && !value) {
    return 'Reference Field is required because Type is REFERENCE.';
  }
  return undefined;
};

const getListReferenceOption = (data: CiTypeReferFieldsModel[], ciTypeId: number): ComboboxItem[] => {
  return (data || [])
    .filter((item) => item.ciTypeId !== ciTypeId)
    .map((x) => ({
      value: `${x.id}`,
      label: `${x.ciTypeName} [ ${x.ciTypeAttributeName} ]`,
    }));
};

const buildDraggableIdById = (id: string) => {
  return `draggable-id-${id}`;
};
const buildDroppableIdByTypeAndSide = (type: CiTypeAttributeType, side: CiTypeColumn) => {
  return `droppable-id-${type}-${side}`;
};
const isFeatureReferenceAttribute = getConfigs().features.ciTypeReferenceAttribute;

const CiTypeAttributeDetail = forwardRef<CiTypeAttributeDetailMethods, CiTypeAttributeDetailProps>((props, _ref) => {
  const { ciTypeId, ciTypeInfoUpdate, parentId = -1, setCiAttributes, setCiTypeInfoUpdate, setIsChange } = props;
  const theme = useMantineTheme();
  const formRef = useRef<UseFormReturnType<FormValues> | null>(null);
  const formAddOptionRef = useRef<UseFormReturnType<FormAddOption> | null>(null);

  const [dataLeft, setDataLeft] = useState<ConfigItemTypeAttrModel[]>([]);
  const [dataRight, setDataRight] = useState<ConfigItemTypeAttrModel[]>([]);
  const [dataDefaultLeft, setDataDefaultLeft] = useState<ConfigItemTypeAttrModel[]>([]);
  const [dataDefaultRight, setDataDefaultRight] = useState<ConfigItemTypeAttrModel[]>([]);
  const [dataRemove, setDataRemove] = useState<ConfigItemTypeAttrModel[]>([]);
  const [ciTypeAttrs, setCiTypeAttrs] = useState<ConfigItemTypeAttrModel[]>([]);
  const [openedModal, { close: closePopup, open: openModal }] = useDisclosure(false);
  const [openedModalDel, { close: closePopupDel, open: openModalDel }] = useDisclosure(false);
  const [openedCopyOther, { close: closeCopyOther, open: openCopyOther }] = useDisclosure(false);
  const renderDraggable = useDraggableInPortal();

  const [newItem, setNewItem] = useState<ConfigItemTypeAttrModel>({
    id: 0,
    name: '',
    column: CiTypeColumn.LEFT,
    options: '[]',
  });
  const [deleteObject, setDeleteObject] = useState<ConfigItemTypeAttrModel>();
  const [location, setLocation] = useState('');
  const [ciTypeAttrsDefault, setCiTypeAttrsDefault] = useState<ConfigItemTypeAttrModel[]>([]);

  // for add picker option
  const [optionName, setOptionName] = useState('');
  const [optionContent, setOptionContent] = useState('');
  const [pillData, setPillData] = useState<ConfigItemOptionPickerModel[]>([]);
  const [ciIdCopy, setCiIdCopy] = useState(0);
  const [collapseOpened, { toggle }] = useDisclosure(false);

  const [listReferFields, setListReferFields] = useState<CiTypeReferFieldsModel[]>([]);
  const [referFieldsAllowSelect, setReferFieldsAllowSelect] = useState<ComboboxItem[]>([]);
  const [currentDataTypeAttributeIsReferenceField, setCurrentTypeAttributeIsReferenceField] = useState<boolean>(false);
  const [hasReferenceAttribute, setHasReferenceAttribute] = useState<boolean>(false);

  const [transformMapText, setTransformMapText] = useState<string | undefined>(undefined);
  const fetchCiTypesAttribute = useCallback(() => {
    ConfigItemTypeApi.getAllAttributes(ciTypeId)
      .then((res) => {
        const dataSorted = sortData(res.data);

        const dataDefault = dataSorted.filter((x) => x.ciTypeId === 0);
        const dataCurrent = dataSorted.filter((x) => x.ciTypeId !== 0);

        setHasReferenceAttribute(dataCurrent.some((x) => CiTypeAttributeDataType.REFERENCE === x.type));
        setCiTypeAttrsDefault(dataDefault);
        setCiTypeAttrs(dataCurrent);
      })
      .catch(() => {});
  }, [ciTypeId]);

  useEffect(() => {
    fetchCiTypesAttribute();
  }, [fetchCiTypesAttribute]);

  const fetchCiTypeReferFieldOptions = useCallback(() => {
    if ((hasReferenceAttribute || currentDataTypeAttributeIsReferenceField) && listReferFields.length === 0) {
      CiTypeReferenceApi.findAllByIdIn([])
        .then((res) => {
          setListReferFields(res.data);
          const dataRefer = getListReferenceOption(res.data, ciTypeId);
          setReferFieldsAllowSelect(dataRefer);
        })
        .catch(() => {});
    }
  }, [ciTypeId, currentDataTypeAttributeIsReferenceField, hasReferenceAttribute, listReferFields.length]);

  const fetchTransformMapByAttributeId = useCallback((attributeId: number) => {
    if (attributeId) {
      DiscoveryTransformMapApi.getByAttributeId(attributeId)
        .then((res) => {
          const datas: DiscoveryTransformMapModel[] = res.data || [];
          const names = datas.map((obj) => obj.name);
          const displayText = names.length > 3 ? `${names.slice(0, 3).join(', ')}...` : names.join(', ');
          setTransformMapText(displayText);
        })
        .catch(() => {});
    }
  }, []);

  useEffect(() => {
    fetchCiTypeReferFieldOptions();
  }, [fetchCiTypeReferFieldOptions]);

  useEffect(() => {
    const objects = (ciTypeAttrsDefault || []).map((item) => {
      return { ...item, tempId: uuid() };
    });
    const itemsLeft = objects.filter((obj) => obj.column === CiTypeColumn.LEFT);
    const itemsRight = objects.filter((obj) => obj.column === CiTypeColumn.RIGHT);
    if (ciTypeId > 0) {
      setDataDefaultLeft(itemsLeft);
      setDataDefaultRight(itemsRight);
      setCiAttributes((prev: ConfigItemAttributeObject) => ({
        ...prev,
        attributesDefault: [...itemsLeft, ...itemsRight],
      }));
    } else {
      setDataLeft(itemsLeft);
      setDataRight(itemsRight);
    }
  }, [ciTypeAttrsDefault, ciTypeId, setCiAttributes]);

  useEffect(() => {
    if (ciTypeAttrs && ciTypeAttrs.length > 0 && ciTypeId > 0) {
      const objects = (ciTypeAttrs || []).map((item) => {
        return { ...item, tempId: uuid() };
      });
      const itemsLeft = objects.filter((obj) => obj.column === CiTypeColumn.LEFT);
      const itemsRight = objects.filter((obj) => obj.column === CiTypeColumn.RIGHT);
      setDataLeft(itemsLeft);
      setDataRight(itemsRight);
    }
  }, [ciTypeAttrs, ciTypeId]);

  const form = useForm<FormValues>({
    initialValues: {
      name: '',
      description: '',
      type: '',
      referField: '',
    },
    validate: {
      name: validateRequired,
      type: validateRequired,
      referField: validateReferenceField,
    },
  });

  useEffect(() => {
    if (formRef.current && newItem) {
      const { setValues } = formRef.current;
      setValues({
        name: newItem.name,
        description: newItem.description,
        type: newItem.type?.toString(),
        referField: newItem.ciTypeReferenceId?.toString(),
      });
      setCurrentTypeAttributeIsReferenceField(CiTypeAttributeDataType.REFERENCE === newItem.type);
    }
  }, [newItem]);

  // reset value mandatory when edit change data type attribute field.
  useEffect(() => {
    if (currentDataTypeAttributeIsReferenceField && newItem.mandatory) {
      setNewItem((prev) => {
        return {
          ...prev,
          mandatory: false,
        };
      });
    }
  }, [currentDataTypeAttributeIsReferenceField, newItem.mandatory]);

  useEffect(() => {
    formRef.current = form;
  }, [form]);

  useEffect(() => {
    setCiTypeInfoUpdate((prev: ConfigItemTypeInfoModel) => ({
      ...prev,
      attributesDelete: dataRemove,
    }));
  }, [dataRemove, setCiTypeInfoUpdate]);

  useEffect(() => {
    const dataLeftOrder = (dataLeft || []).map((item, index) => {
      return { ...item, order: index + 1 };
    });
    const dataRightOrder = (dataRight || []).map((item, index) => {
      return { ...item, order: index + 1 };
    });

    const dataAttributes = [...dataLeftOrder, ...dataRightOrder];
    setCiTypeInfoUpdate((prev: ConfigItemTypeInfoModel) => ({
      ...prev,
      attributesUpdate: dataAttributes,
    }));

    setCiAttributes((prev: ConfigItemAttributeObject) => ({
      ...prev,
      attributes: dataAttributes,
    }));
  }, [dataLeft, dataRight, setCiAttributes, setCiTypeInfoUpdate]);

  // event action crud
  const closeModalUpdate = () => {
    setPillData([]);
    setOptionName('');
    setOptionContent('');
    closePopup();
  };

  const handleAdd = useCallback(
    (location: CiTypeColumn) => {
      const locationEnum = location === CiTypeColumn.LEFT ? CiTypeColumn.LEFT : CiTypeColumn.RIGHT;
      setNewItem({
        id: 0,
        name: '',
        mandatory: false,
        column: locationEnum,
        tempId: uuid(),
        ciTypeId: ciTypeId,
        options: '[]',
      });
      setLocation(location);
      // update list refer = all
      const dataRefer = getListReferenceOption(listReferFields, ciTypeId);

      setReferFieldsAllowSelect(dataRefer);
      openModal();
    },
    [ciTypeId, listReferFields, openModal],
  );

  const handleEdit = (obj: ConfigItemTypeAttrModel, location: CiTypeColumn) => {
    fetchTransformMapByAttributeId(obj.id);
    setNewItem(obj);

    const arrayOption = obj.options ? JSON.parse(obj.options) : [];
    setPillData(arrayOption);

    setLocation(location);
    if (obj.id > 0) {
      // update list refer = current ci type
      const currentSelectCiTypeId = listReferFields.find((x) => x.id === obj.ciTypeReferenceId)?.ciTypeId;
      const filterReferFieldAllow = listReferFields.filter((x) => x.ciTypeId === currentSelectCiTypeId);
      const dataRefer = getListReferenceOption(filterReferFieldAllow, ciTypeId);
      setReferFieldsAllowSelect(dataRefer);
    }

    openModal();
  };

  const handleDelete = (obj: ConfigItemTypeAttrModel, location: CiTypeColumn) => {
    setDeleteObject(obj);
    setLocation(location);
    fetchTransformMapByAttributeId(obj.id);
    openModalDel();
  };

  const onDelete = () => {
    const { id: deleteId } = deleteObject || {};
    if (deleteId && ciTypeInfoUpdate.attributesReferenceDelete?.every((obj) => obj.ciTypeAttributeId !== deleteId)) {
      // if record is old data in DB(has Id) => validate before delete
      CiTypeReferenceApi.findAllByCiTypeIdAndCiTypeAttributeIdIn(ciTypeId, [deleteId])
        .then((res) => {
          if (res.data && res.data.length > 0) {
            NotificationError({
              message: `The attribute you're trying to delete is currently being used in Tab Reference Fields.`,
            });
            closePopupDel();
          } else {
            processOnDelete();
          }
        })
        .catch(() => {});
    } else {
      processOnDelete();
    }
  };

  const onCreateUpdate = () => {
    form.validate();
    if (!form.isValid()) {
      NotificationError({
        message: 'Data is not valid',
      });
      return;
    }

    if (CiTypeAttributeDataType.PICK_LIST === newItem.type && !(pillData && pillData.length > 0)) {
      form.setFieldError('type', 'The PICK_LIST type must have a list of items');
      return;
    }

    const allCiTypeAttributes = [...(ciTypeId > 0 ? [...dataDefaultLeft, ...dataDefaultRight] : []), ...dataLeft, ...dataRight];
    const nameAttributeNew = newItem.name.trim();

    if (allCiTypeAttributes.some((x) => x.tempId !== newItem.tempId && x.name.toLowerCase() === nameAttributeNew.toLowerCase())) {
      form.setFieldError('name', 'Name is duplicate value');
      return;
    }

    if (CiTypeColumn.LEFT === location) {
      onCreateUpdateLeft();
    } else {
      onCreateUpdateRight();
    }
    setIsChange(true);
  };

  const onDeleteLeft = useCallback(() => {
    if (!deleteObject?.tempId) {
      return;
    }

    const removeItem = dataLeft.find((item) => item.tempId === deleteObject.tempId);
    if (removeItem && removeItem.id > 0) {
      setDataRemove((oldObj) => [...oldObj, removeItem]);
    }

    const updatedItems = dataLeft.filter((item) => item.tempId !== deleteObject.tempId);
    setDataLeft(updatedItems);
    closePopupDel();
  }, [closePopupDel, dataLeft, deleteObject?.tempId]);

  const onCreateUpdateLeft = () => {
    const objIndex = dataLeft.findIndex((obj) => obj.tempId === newItem.tempId);
    const updateNewItem = { ...newItem, name: newItem.name.trim() };
    if (objIndex >= 0) {
      const updatedArray = dataLeft.map((obj) => (obj.tempId === newItem.tempId ? updateNewItem : obj));
      setDataLeft(updatedArray);
    } else {
      setDataLeft((oldObj) => [...oldObj, updateNewItem]);
    }
    closeModalUpdate();
  };

  const onDeleteRight = useCallback(() => {
    if (!deleteObject?.tempId) {
      return;
    }
    const removeItem = dataRight.find((item) => item.tempId === deleteObject.tempId);
    if (removeItem && removeItem.id > 0) {
      setDataRemove((oldObj) => [...oldObj, removeItem]);
    }

    const updatedItems = dataRight.filter((item) => item.tempId !== deleteObject.tempId);
    setDataRight(updatedItems);
    closePopupDel();
  }, [closePopupDel, dataRight, deleteObject?.tempId]);

  const processOnDelete = useCallback(() => {
    if (CiTypeColumn.LEFT === location) {
      onDeleteLeft();
    } else {
      onDeleteRight();
    }
    setIsChange(true);
  }, [location, onDeleteLeft, onDeleteRight, setIsChange]);
  const onCreateUpdateRight = () => {
    const objIndex = dataRight.findIndex((obj) => obj.tempId === newItem.tempId);
    const updateNewItem = { ...newItem, name: newItem.name.trim() };
    if (objIndex >= 0) {
      const updatedArray = dataRight.map((obj) => (obj.tempId === newItem.tempId ? updateNewItem : obj));
      setDataRight(updatedArray);
    } else {
      setDataRight((oldObj) => [...oldObj, updateNewItem]);
    }
    closeModalUpdate();
  };

  const onChangeNewItem = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, name: string) => {
    const { target } = e;
    setNewItem((prev) => {
      return {
        ...prev,
        [name]: target.value,
      };
    });
  };

  const onChangeCheckbox = (e: React.ChangeEvent<HTMLInputElement>, name: string) => {
    const { currentTarget } = e;
    setNewItem((prev) => {
      return {
        ...prev,
        [name]: currentTarget.checked,
      };
    });
  };

  const onChangeSelect = (val: string | number | null, name: string) => {
    setNewItem((prev) => {
      return {
        ...prev,
        [name]: val,
      };
    });
  };

  const onCopyAttribute = (type: string) => {
    let idToCopy = 0;
    if (type === 'parent') {
      idToCopy = parentId;
    } else {
      idToCopy = ciIdCopy;
    }
    ConfigItemTypeApi.getAllAttributes(idToCopy)
      .then((res) => {
        const dataSorted = sortData(res.data);

        const dataCurrent = dataSorted.filter((x) => x.ciTypeId !== 0);
        const objectsCopy = (dataCurrent || []).map((item) => {
          return { ...item, id: 0, ciTypeId: ciTypeId };
        });

        // update list reference field when copy
        setHasReferenceAttribute(objectsCopy.some((x) => CiTypeAttributeDataType.REFERENCE === x.type));

        const listCurrentCiTypeAttribute = [...dataLeft, ...dataRight];
        const existingNames = listCurrentCiTypeAttribute.map((obj) => obj.name.toLowerCase());
        const newObjects = objectsCopy
          .filter((obj) => !existingNames.includes(obj.name.toLowerCase()))
          .map((item) => ({
            ...item,
            tempId: uuid(),
          }));

        const newItemsLeft = newObjects.filter((obj) => obj.column === CiTypeColumn.LEFT);
        const newItemsRight = newObjects.filter((obj) => obj.column === CiTypeColumn.RIGHT);

        setDataLeft((oldObj) => [...oldObj, ...newItemsLeft]);
        setDataRight((oldObj) => [...oldObj, ...newItemsRight]);

        NotificationSuccess({
          message: 'Copy data successfully',
        });
      })
      .catch(() => {});
    setIsChange(true);
    closeCopyOther();
    setCiIdCopy(0);
  };

  const pills = pillData.map((data) => (
    <Pill key={data.value} withRemoveButton onRemove={() => handleRemoveOption(data.value)}>
      {data.label}
    </Pill>
  ));

  const handleAddOption = () => {
    formAddOption.validate();
    if (!formAddOption.isValid()) {
      return;
    }
    const nameFilter = pillData.find((x) => x.label.toUpperCase() === optionName.toUpperCase());
    if (nameFilter) {
      formAddOption.setFieldError('label', 'Duplicate value detected (case-insensitive), cannot add.');
      return;
    }
    const valueFilter = pillData.find((x) => x.value.toUpperCase() === optionContent.toUpperCase());
    if (valueFilter) {
      formAddOption.setFieldError('value', 'Duplicate value detected (case-insensitive), cannot add.');
      return;
    }
    setPillData((ob) => [...ob, { label: optionName, value: optionContent }]);
    setOptionName('');
    setOptionContent('');
  };

  const handleRemoveOption = (val: string) => {
    const updatedPillData = pillData.filter((data) => data.value !== val);
    setPillData(updatedPillData);
  };

  const formAddOption = useForm<FormAddOption>({
    initialValues: {
      label: '',
      value: '',
    },

    validate: {
      label: validateRequired,
      value: validateRequired,
    },
  });

  useEffect(() => {
    formAddOptionRef.current = formAddOption;
  }, [formAddOption]);

  useEffect(() => {
    if (formAddOptionRef.current) {
      const { setValues } = formAddOptionRef.current;

      const nameInput = optionName ? optionName.trim() : '';
      const valueInput = optionContent ? optionContent.trim() : '';
      setValues({ label: nameInput, value: valueInput });
    }
  }, [optionName, optionContent]);

  useEffect(() => {
    const optionsStr = pillData ? JSON.stringify(pillData) : '[]';
    setNewItem((prev) => {
      return {
        ...prev,
        options: optionsStr,
      };
    });
  }, [pillData]);

  const ciColumnTypes: string[] = useMemo(() => {
    // ciTypeId = 0 => case create new attribute default attribute for all CI Type
    // ciTypeId > 0 => case create new attribute for current CI Type
    if (isFeatureReferenceAttribute && ciTypeId > 0) {
      return Object.values(CiTypeAttributeDataType);
    }

    return Object.values(CiTypeAttributeDataType).filter((x) => x !== CiTypeAttributeDataType.REFERENCE);
  }, [ciTypeId]);

  const onCloseCopyOther = () => {
    setCiIdCopy(0);
    closeCopyOther();
  };

  const handleDragEndAttributeCustom = useCallback(
    (result: DropResult) => {
      const { destination, source } = result;
      if (!destination) {
        return;
      }
      if (source.droppableId === destination.droppableId && source.index === destination.index) {
        return;
      }

      if (destination.droppableId === source.droppableId) {
        if (destination.droppableId === buildDroppableIdByTypeAndSide(CiTypeAttributeType.CUSTOM, CiTypeColumn.LEFT)) {
          setDataLeft((prev) => swapElements([...prev], destination.index, source.index));
        } else {
          setDataRight((prev) => swapElements([...prev], destination.index, source.index));
        }
      } else {
        if (
          source.droppableId === buildDroppableIdByTypeAndSide(CiTypeAttributeType.CUSTOM, CiTypeColumn.LEFT) &&
          destination.droppableId === buildDroppableIdByTypeAndSide(CiTypeAttributeType.CUSTOM, CiTypeColumn.RIGHT)
        ) {
          setDataLeft((prevLeft) => {
            const newLeft = [...prevLeft];

            setDataRight((prevRight) => {
              const newRight = [...prevRight];
              const [removed] = newLeft.splice(source.index, 1);

              newRight.splice(destination.index, 0, { ...removed, column: CiTypeColumn.RIGHT });
              setIsChange(true);
              return newRight;
            });
            return newLeft;
          });
        } else if (
          source.droppableId === buildDroppableIdByTypeAndSide(CiTypeAttributeType.CUSTOM, CiTypeColumn.RIGHT) &&
          destination.droppableId === buildDroppableIdByTypeAndSide(CiTypeAttributeType.CUSTOM, CiTypeColumn.LEFT)
        ) {
          setDataRight((prevRight) => {
            const newRight = [...prevRight];

            setDataLeft((prevLeft) => {
              const newLeft = [...prevLeft];
              const [removed] = newRight.splice(source.index, 1);
              newLeft.splice(destination.index, 0, { ...removed, column: CiTypeColumn.LEFT });
              setIsChange(true);

              return newLeft;
            });
            return newRight;
          });
        }
      }
    },
    [setIsChange],
  );
  const renderCiTypeAttributeDefault = (obj: ConfigItemTypeAttrModel, idx: number) => {
    return (
      <Flex align={'center'} gap={10} justify={'space-between'} className={stylesCss['draggable']} key={idx}>
        <Stack gap={3}>
          <Flex title={obj.description}>
            <KanbanText>{obj.name}</KanbanText>
            {obj.mandatory && <KanbanText c={'red'}>*</KanbanText>}
          </Flex>
          <Flex gap='md' justify='flex-start' align='center' direction='row'>
            <KanbanText c='dimmed' size='xs'>
              {obj.type}
            </KanbanText>
            <KanbanText c='dimmed' fs='italic' size='xs'>
              {obj.description}
            </KanbanText>
          </Flex>
        </Stack>
      </Flex>
    );
  };

  const renderCiTypeAttribute = (obj: ConfigItemTypeAttrModel, idx: number, location: CiTypeColumn) => {
    return (
      <Draggable index={idx} draggableId={buildDraggableIdById(obj.tempId || '')} key={obj.tempId}>
        {renderDraggable((provided) => (
          <Flex
            align={'center'}
            gap={10}
            justify={'space-between'}
            className={stylesCss['draggable']}
            key={obj.tempId}
            ref={provided.innerRef}
            {...provided.draggableProps}
            {...provided.dragHandleProps}>
            <Stack gap={3} maw={'80%'}>
              <Flex title={obj.description}>
                <KanbanText truncate='end'>{obj.name}</KanbanText>
                {obj.mandatory && <KanbanText c={'red'}>*</KanbanText>}
              </Flex>
              <Flex gap='md' justify='flex-start' align='center' direction='row'>
                <KanbanText c='dimmed' size='xs'>
                  {idx}: {obj.type}
                </KanbanText>
                <KanbanText c='dimmed' fs='italic' size='xs' truncate='end'>
                  {obj.description}
                </KanbanText>
              </Flex>
            </Stack>

            <Group justify='flex-end' gap='xs'>
              <ActionIcon
                onClick={(e) => {
                  e.stopPropagation();
                  handleEdit(obj, location);
                }}>
                <IconEdit size='1rem' />
              </ActionIcon>
              <ActionIcon
                color={theme.colors.red[5]}
                onClick={(e) => {
                  e.stopPropagation();
                  handleDelete(obj, location);
                }}>
                <IconTrash size='1rem' />
              </ActionIcon>
            </Group>
          </Flex>
        ))}
      </Draggable>
    );
  };

  const RenderCiTypeCustoms: React.FC<{ dataList: ConfigItemTypeAttrModel[]; columnType: CiTypeColumn }> = ({ columnType, dataList }) => {
    return dataList.length > 0 && <>{dataList.map((obj: ConfigItemTypeAttrModel, idx: number) => renderCiTypeAttribute(obj, idx, columnType))}</>;
  };

  return (
    <>
      {/* Modal create/update */}
      <KanbanModal
        size={'lg'}
        opened={openedModal}
        onClose={closeModalUpdate}
        title={'Create/Update Type Attribute'}
        actions={<KanbanButton onClick={onCreateUpdate}> {newItem.id > 0 ? 'Update' : 'Create'}</KanbanButton>}>
        {transformMapText && (
          <Alert my={'20'} variant='light' color='red' title={''} icon={<IconAlertTriangle />}>
            <KanbanText c={'red'} ml={8}>
              This attribute is being used in the transform map: {transformMapText}
            </KanbanText>
          </Alert>
        )}
        <KanbanInput
          label='Name'
          mt='md'
          withAsterisk
          maxLength={255}
          {...form.getInputProps('name')}
          value={newItem.name || ''}
          onChange={(e) => {
            onChangeNewItem(e, 'name');
          }}
        />
        <KanbanSelect
          label='Type'
          placeholder='Pick value'
          mt='md'
          withAsterisk
          {...form.getInputProps('type')}
          value={newItem.type}
          data={ciColumnTypes}
          disabled={newItem.id > 0}
          comboboxProps={{ zIndex: 999, withinPortal: true }}
          onChange={(e) => {
            onChangeSelect(e, 'type');
          }}
        />
        {newItem.type && CiTypeAttributeDataType.PICK_LIST === newItem.type && (
          <>
            <Flex justify='space-between' gap={'xs'} align={'center'}>
              <KanbanInput
                placeholder='Enter option name'
                {...formAddOption.getInputProps('label')}
                maxLength={50}
                m={0}
                w={'45%'}
                value={optionName}
                onChange={(e) => setOptionName(e.currentTarget.value)}
              />
              <KanbanInput
                placeholder='Enter option value'
                {...formAddOption.getInputProps('value')}
                maxLength={50}
                m={0}
                w={'45%'}
                value={optionContent}
                onChange={(e) => setOptionContent(e.currentTarget.value)}
              />
              <ActionIcon onClick={handleAddOption}>
                <IconPlus size='1rem' />
              </ActionIcon>
            </Flex>
            <InputBase component='div' mt={'xs'} multiline>
              <Pill.Group>{pills}</Pill.Group>
            </InputBase>
          </>
        )}
        {currentDataTypeAttributeIsReferenceField && (
          <>
            <KanbanSelect
              label='Reference Field'
              placeholder='Pick value'
              mt='md'
              searchable
              withAsterisk
              {...form.getInputProps('referField')}
              value={`${newItem.ciTypeReferenceId || ''}`}
              data={referFieldsAllowSelect}
              allowDeselect={false}
              onChange={(e) => {
                onChangeSelect(Number(e), 'ciTypeReferenceId');
              }}
            />
          </>
        )}
        <KanbanTextarea
          label='Description'
          mt='md'
          {...form.getInputProps('description')}
          value={newItem.description || ''}
          maxLength={255}
          onChange={(e) => {
            onChangeNewItem(e, 'description');
          }}
        />

        <KanbanCheckbox
          label='Mandatory'
          mt='md'
          checked={newItem.mandatory}
          labelPosition='left'
          disabled={currentDataTypeAttributeIsReferenceField}
          onChange={(e) => {
            onChangeCheckbox(e, 'mandatory');
          }}
        />
      </KanbanModal>

      {/* Modal confirm delete */}
      <KanbanModal
        size={'lg'}
        opened={openedModalDel}
        onClose={closePopupDel}
        title={'Confirm Delete'}
        actions={<KanbanButton onClick={onDelete}>OK</KanbanButton>}>
        {transformMapText && (
          <Alert my={'20'} variant='light' color='orange' title={''} icon={<IconAlertTriangle />}>
            <KanbanText c={'orange'} ml={8}>
              This attribute is being used in the Discovery Transform Map: {transformMapText}
            </KanbanText>
          </Alert>
        )}
        <p>Are you sure want to delete this attribute? By doing so, the associated field data will be removed from the CI(s).</p>
      </KanbanModal>

      {/* Modal copy other */}
      <KanbanModal
        size={'lg'}
        opened={openedCopyOther}
        onClose={onCloseCopyOther}
        padding={10}
        centered
        title={'Copy attribute from other CI Type'}
        actions={
          <KanbanButton
            disabled={!ciIdCopy}
            onClick={() => {
              onCopyAttribute('other');
            }}>
            Copy
          </KanbanButton>
        }>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
          <CiTypePickerComponent
            onChange={(e) => {
              setCiIdCopy(e || 0);
            }}></CiTypePickerComponent>
        </div>
      </KanbanModal>
      {ciTypeId > 0 && (
        <Container mt='lg'>
          <Group onClick={toggle} gap={2}>
            {collapseOpened ? <IconCaretDownFilled size={'1rem'} /> : <IconCaretRightFilled size={'1rem'} />}
            <KanbanText mt='xs' mb='xs' fw={500}>
              Default Attribute
            </KanbanText>
          </Group>
          <Collapse in={collapseOpened}>
            <Grid>
              <GridCol span={6} className={stylesCss['column']}>
                {dataDefaultLeft.length > 0 && (
                  <>{dataDefaultLeft.map((obj: ConfigItemTypeAttrModel, idx: number) => renderCiTypeAttributeDefault(obj, idx))}</>
                )}
              </GridCol>

              <GridCol span={6} className={stylesCss['column']}>
                {dataDefaultRight.length > 0 && (
                  <>{dataDefaultRight.map((obj: ConfigItemTypeAttrModel, idx: number) => renderCiTypeAttributeDefault(obj, idx))}</>
                )}
              </GridCol>
            </Grid>
          </Collapse>
        </Container>
      )}
      <DragDropContext onDragEnd={handleDragEndAttributeCustom}>
        <Container mt='lg' className={stylesCss['container']}>
          <Flex justify={'space-between'} my={10}>
            {ciTypeId > 0 && (
              <KanbanText mt={15} fw={500}>
                Custom Attribute
              </KanbanText>
            )}

            <Flex justify={'flex-end'} gap={10}>
              {ciTypeId > 0 && (
                <KanbanButton my='xs' size='xs' onClick={openCopyOther}>
                  Copy Other
                </KanbanButton>
              )}
              {parentId > 0 && (
                <KanbanButton
                  my='xs'
                  size='xs'
                  onClick={() => {
                    onCopyAttribute('parent');
                  }}>
                  Copy Parent
                </KanbanButton>
              )}
            </Flex>
          </Flex>

          <Grid>
            <Droppable droppableId={buildDroppableIdByTypeAndSide(CiTypeAttributeType.CUSTOM, CiTypeColumn.LEFT)}>
              {(provided) => (
                <GridCol span={6} className={stylesCss['column']} {...provided.droppableProps} ref={provided.innerRef}>
                  <Flex justify={'end'}>
                    <ActionIcon
                      mr={'xs'}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAdd(CiTypeColumn.LEFT);
                      }}>
                      <IconPlus size='1rem' />
                    </ActionIcon>
                  </Flex>

                  <Box>
                    <RenderCiTypeCustoms dataList={dataLeft} columnType={CiTypeColumn.LEFT} />
                  </Box>
                </GridCol>
              )}
            </Droppable>

            <Droppable droppableId={buildDroppableIdByTypeAndSide(CiTypeAttributeType.CUSTOM, CiTypeColumn.RIGHT)}>
              {(provided) => (
                <GridCol span={6} className={stylesCss['column']} {...provided.droppableProps} ref={provided.innerRef}>
                  <Flex justify={'end'}>
                    <ActionIcon
                      mr={'xs'}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAdd(CiTypeColumn.RIGHT);
                      }}>
                      <IconPlus size='1rem' />
                    </ActionIcon>
                  </Flex>

                  <Box>
                    <RenderCiTypeCustoms dataList={dataRight} columnType={CiTypeColumn.RIGHT} />
                  </Box>
                </GridCol>
              )}
            </Droppable>
          </Grid>
        </Container>
      </DragDropContext>
    </>
  );
});

CiTypeAttributeDetail.displayName = 'CiTypeAttributeDetail';
export default CiTypeAttributeDetail;
