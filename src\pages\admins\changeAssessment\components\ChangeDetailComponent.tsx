import { KanbanText } from 'kanban-design-system';
import { KanbanTitle } from 'kanban-design-system';
import { Title, Box, Flex } from '@mantine/core';
import type { ChangeResponseDto } from '@models/ChangeSdp';
import React from 'react';
import stylesCss from './Change.module.scss';
import { ChangeErrorCodeEnum, ChangeFieldEnum } from '@common/utils/ChangeAssessmentUtils';

export interface ChangeDetailProps {
  title: string;
  coordinator: string;
  stage: string;
  status: string;
}

export interface ChangeDataProps {
  data: ChangeResponseDto;
}

export interface ChangeDetailProps {
  title: string;
  coordinator: string;
  stage: string;
  status: string;
}

export interface InformationChangeProps {
  label: string;
  value: string;
  style?: React.CSSProperties;
}

export const InformationChange: React.FC<InformationChangeProps> = ({ label, style, value }) => {
  return (
    <Flex gap='xl' style={style}>
      <KanbanText w={'10%'} fw={500}>
        {label}
      </KanbanText>
      <KanbanText w={'60%'}>{value}</KanbanText>
    </Flex>
  );
};

const isErrorField = (field: ChangeFieldEnum, value?: string): boolean => {
  if (ChangeFieldEnum.COORDINATOR === field && ChangeErrorCodeEnum.CHANGE_COR_INVALID === value) {
    return true;
  }
  if (ChangeFieldEnum.STAGE === field && ChangeErrorCodeEnum.CHANGE_INCORRECT_STAGE_WHEN_CMDB_LINK_NEW_CHANGE_OR_DELETE === value) {
    return true;
  }
  return ChangeFieldEnum.STATUS === field && ChangeErrorCodeEnum.CHANGE_INVALID_STATUS === value;
};

export const ChangeDetailComponent: React.FC<ChangeDataProps> = ({ data }) => {
  return (
    <Box>
      <KanbanTitle order={3} mb='md' c='#1864AB'>
        SDP Link Info
      </KanbanTitle>
      <Title order={3} mb='md'>
        Current information in SDP (for review purpose and will not be saved to CMDB)
      </Title>
      <Flex direction='column' gap='sm'>
        <InformationChange label='Change Title:' value={data.changeTitle} />
        <div className={isErrorField(ChangeFieldEnum.COORDINATOR, data.cmdbErrorCode) ? stylesCss.invalidField : stylesCss.inheritField}>
          <InformationChange label='Change coordinator:' value={data.changeCor} />
        </div>
        <div className={isErrorField(ChangeFieldEnum.STAGE, data.cmdbErrorCode) ? stylesCss.invalidField : stylesCss.inheritField}>
          <InformationChange label='Change Stage:' value={data.changeStage} />
        </div>
        <div className={isErrorField(ChangeFieldEnum.STATUS, data.cmdbErrorCode) ? stylesCss.invalidField : stylesCss.inheritField}>
          <InformationChange label='Change Status:' value={data.changeStatus} />
        </div>
      </Flex>
    </Box>
  );
};
