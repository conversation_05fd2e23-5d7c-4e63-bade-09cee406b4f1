import type { ConfigItemTypeAttrResponse } from '@api/ConfigItemTypeAttrApi';
import { DD_MM_YYYY_FORMAT, dateToString, stringToDateWithFormat } from '@common/utils/DateUtils';
import { safetyParseObjectFromJson } from '@common/utils/Helpers';
import { KanbanIconButton } from 'kanban-design-system';
import { KanbanDateInput } from 'kanban-design-system';
import { KanbanInput } from 'kanban-design-system';
import { KanbanNumberInput } from 'kanban-design-system';
import { KanbanTextarea } from 'kanban-design-system';
import { KanbanConfirmModal } from 'kanban-design-system';
import { KanbanSelect } from 'kanban-design-system';
import { Container, Flex, Group, SimpleGrid } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import type { HistoryDescription } from '@models/CiHistory';
import { CiTypeColumn, CiTypeAttributeDataType } from '@models/CiType';
import type { ConfigItemModel } from '@models/ConfigItem';
import type { ConfigItemAttrModel } from '@models/ConfigItemAttr';
import type { ConfigItemAttrCustomModel } from '@models/ConfigItemAttrCustom';
import { type CiAttributeInfoModel, type CiTypeReferFieldsModel, type ConfigItemTypeAttrModel } from '@models/ConfigItemTypeAttr';
import type { ConfigItemOptionPickerModel } from '@pages/admins/ciType/CiTypeAttributeDetail';
import { IconCircleMinus, IconCirclePlus, IconX } from '@tabler/icons-react';
import { klona } from 'klona';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { formatStandardName } from '@common/utils/StringUtils';
import { KanbanText } from 'kanban-design-system';
import { UseFormReturnType, useForm } from '@mantine/form';
import { KanbanButton } from 'kanban-design-system';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import { AclPermission } from '@models/AclPermission';
import { PermissionAction } from '@common/constants/PermissionAction';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import CiReferenceFieldSelect from './component/CiReferenceFieldSelect';
import { getConfigs } from '@core/configs/Configs';
import { CiTypeReferenceApi } from '@api/CiTypeReferenceApi';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import { DataCiCompare } from '@models/CiDetailCompare';
import { DEFAULT_ID_FOR_CI_DESCRIPTION, DEFAULT_ID_FOR_CI_NAME } from '@common/constants/CommonConstants';

export type CiUpdateProps = {
  ci: ConfigItemModel;
  ciTypeAttributes: ConfigItemTypeAttrModel[];
  ciAttributes: Record<number, ConfigItemAttrModel>;
  ciAttributesCustom: ConfigItemAttrCustomModel[];
  hiddenCustomAttribute?: boolean;
  isCreateCi?: boolean;
  includeReferenceData?: boolean;
  onChangeCi?: (newCi: ConfigItemModel) => void;
  onChangeCiAttributes?: (newCiAttributes: Record<number, ConfigItemAttrModel>) => void;
  onChangeCiAttributeCustoms?: (newCiAttributeCustoms: ConfigItemAttrCustomModel[]) => void;
  onRemoveCiAttributeCustoms?: (ciAttributeCustom: ConfigItemAttrCustomModel) => void;
  onAddCiAttributeCustoms?: (ciAttributeCustom: ConfigItemAttrCustomModel) => void;

  onChangeHistoryDescription?: (historyDescription: HistoryDescription) => void;
  //previewdci: hight light change
  hightLightChange?: boolean | false;
  differenceObject?: DataCiCompare | null;
};

type FormCreateAttribute = {
  name: string;
  value: string;
};

export type CiTypeAttributeProps = {
  label: string;
  disabled: boolean;
  value: string;
  description?: string;
  withAsterisk: boolean;
  //previewdci: hight light change
  c?: string;
};

const isFeatureReferenceAttribute = getConfigs().features.ciTypeReferenceAttribute;

export const CiUpdate = ({
  ci: ciProps,
  ciAttributes: ciAttributesProps,
  ciAttributesCustom: ciAttributesCustomProps,
  ciTypeAttributes,
  differenceObject,
  hiddenCustomAttribute,
  hightLightChange,
  includeReferenceData,
  isCreateCi,
  onAddCiAttributeCustoms,
  onChangeCi,
  onChangeCiAttributeCustoms,
  onChangeCiAttributes,
  onChangeHistoryDescription,
  onRemoveCiAttributeCustoms,
}: CiUpdateProps) => {
  const [openedModalDeleteAttribute, { close: closeModalDeleteAttribute, open: openModalDeleteAttribute }] = useDisclosure(false);

  const [historyDescription, setHistoryDescription] = useState<HistoryDescription>({
    info: [],
    attributes: [],
    attributeCustoms: [],
  });

  const [ci, setCi] = useState(ciProps);
  const [ciAttributes, setCiAttributes] = useState(ciAttributesProps);

  const [ciAttributeCustoms, setCiAttributeCustoms] = useState(ciAttributesCustomProps);

  const [ciAttributeCustomDelete, setCiAttributeCustomDelete] = useState<ConfigItemAttrCustomModel | undefined>();

  const [showAddAttribute, setShowAddAttribute] = useState(false);
  const [attributeLabel, setAttributeLabel] = useState('');
  const [attributeContent, setAttributeContent] = useState('');
  const [listCiTypeAttribute, setListCiTypeAttribute] = useState<ConfigItemTypeAttrResponse[]>([]);
  const [listReferFields, setListReferFields] = useState<CiTypeReferFieldsModel[]>([]);
  const formAddOptionRef = useRef<UseFormReturnType<FormCreateAttribute> | null>(null);
  useEffect(() => {
    setCi(ciProps);
  }, [ciProps]);

  useEffect(() => {
    setCiAttributes(ciAttributesProps);
  }, [ciAttributesProps]);

  useEffect(() => {
    setCiAttributeCustoms(ciAttributesCustomProps);
  }, [ciAttributesCustomProps]);

  useEffect(() => {
    if (onChangeHistoryDescription) {
      onChangeHistoryDescription(historyDescription);
    }
  }, [historyDescription, onChangeHistoryDescription]);

  useEffect(() => {
    if (ci.ciTypeId > 0) {
      let ciTypeAttributesUpdate = [...ciTypeAttributes];
      const listReferenceIds = new Set(
        ciTypeAttributesUpdate
          .filter((x) => CiTypeAttributeDataType.REFERENCE === x.type && !!x.ciTypeReferenceId)
          .map((item) => item.ciTypeReferenceId ?? -1),
      );

      if (!includeReferenceData && listReferenceIds && listReferenceIds.size > 0) {
        ConfigItemTypeApi.getAllReferAttributeValueSuggestion(ci.ciTypeId)
          .then((res) => {
            if (res.data && res.data.length > 0) {
              const listSuggestReferFields: CiAttributeInfoModel[] = res.data;

              listReferenceIds.forEach((value) => {
                const listOptions: CiAttributeInfoModel[] = listSuggestReferFields.filter((x) => x.ciTypeReferenceId === value);
                ciTypeAttributesUpdate = ciTypeAttributesUpdate.map((obj) => {
                  return obj.ciTypeReferenceId === value ? { ...obj, ciTypeReferenceData: listOptions } : obj;
                });
              });

              setListCiTypeAttribute(ciTypeAttributesUpdate);
            } else {
              setListCiTypeAttribute(ciTypeAttributes);
            }
          })
          .catch(() => {});
      } else {
        setListCiTypeAttribute(ciTypeAttributes);
      }

      if (listReferenceIds && listReferenceIds.size > 0) {
        CiTypeReferenceApi.findAllByIdIn([...listReferenceIds])
          .then((res) => {
            const listFields: CiTypeReferFieldsModel[] = res.data || [];
            setListReferFields(listFields);
          })
          .catch(() => {});
      }
    }
  }, [ci.ciTypeId, ciTypeAttributes, includeReferenceData]);

  const isDisableCi = !onChangeCi;
  const isDisableCiAttribute = !onChangeCiAttributes;
  const isDisableCiAttributeCustom = !onChangeCiAttributeCustoms;

  const setValueAttribute = (attributeId: number, value: unknown) => {
    const ciAttributesClone = klona(ciAttributes);

    const current = ciAttributesClone[attributeId] || {
      id: 0,
      ciTypeAttributeId: attributeId,
      value: '',
    };
    ciAttributesClone[attributeId] = current;
    ciAttributesClone[attributeId].ciTypeAttributeId = attributeId;
    current.value = String(value || '');

    if (onChangeCiAttributes) {
      onChangeCiAttributes(ciAttributesClone);
    }

    setCiAttributes(ciAttributesClone);

    const ciTypeAttribute = listCiTypeAttribute.find((x) => x.id === attributeId);
    setHistoryDescription((prev) => {
      const result = klona(prev);

      let current = result.attributes.find((x) => x.id === attributeId);
      if (!current) {
        current = {
          id: attributeId,
          name: ciTypeAttribute?.name || '',
        };
        result.attributes.push(current);
      }
      current.toValue = String(value);

      return result;
    });
  };

  const RenderCiTypeAttribute = (ciTypeAttribute: ConfigItemTypeAttrResponse) => {
    //previewdci: hight light change
    const isUpdate = differenceObject?.attributes && differenceObject.attributes[ciTypeAttribute.id];

    const mappingProps: CiTypeAttributeProps = {
      label: ciTypeAttribute.name,
      disabled: isDisableCiAttribute,
      value: (ciAttributes[ciTypeAttribute.id] || {}).value || '',
      description: ciTypeAttribute.description,
      withAsterisk: !!ciTypeAttribute.mandatory,
      //previewdci: hight light change
      c: isUpdate && hightLightChange ? 'green' : '',
    };

    let options: ConfigItemOptionPickerModel[] = [];
    if (CiTypeAttributeDataType.PICK_LIST === ciTypeAttribute.type && ciTypeAttribute.options) {
      options = (safetyParseObjectFromJson(ciTypeAttribute.options) || []) as ConfigItemOptionPickerModel[];
    }

    const optionReferenceFields: CiAttributeInfoModel[] = ciTypeAttribute.ciTypeReferenceData || [];

    const mapping: Record<CiTypeAttributeDataType, React.ReactNode> = {
      [CiTypeAttributeDataType.TEXT]: (
        <KanbanTextarea
          {...mappingProps}
          maxLength={2000}
          minRows={1}
          maxRows={5}
          autosize
          title='This field supports a maximum of 2000 characters'
          onChange={(e) => {
            const { target } = e;
            setValueAttribute(ciTypeAttribute.id, target.value);
          }}
        />
      ),
      [CiTypeAttributeDataType.PICK_LIST]: (
        <KanbanSelect
          {...mappingProps}
          data={options.map((x) => ({
            value: x.value,
            label: x.value,
          }))}
          value={mappingProps.value || null}
          onChange={(value) => {
            setValueAttribute(ciTypeAttribute.id, value);
          }}
        />
      ),
      [CiTypeAttributeDataType.DATE]: (
        <KanbanDateInput
          {...mappingProps}
          value={stringToDateWithFormat((ciAttributes[ciTypeAttribute.id] || {}).value || '', DD_MM_YYYY_FORMAT) || null}
          valueFormat={DD_MM_YYYY_FORMAT}
          onChange={(e) => {
            setValueAttribute(ciTypeAttribute.id, dateToString(e, DD_MM_YYYY_FORMAT));
          }}
        />
      ),
      [CiTypeAttributeDataType.NUMBER]: (
        <KanbanNumberInput
          {...mappingProps}
          maxLength={38}
          onChange={(e) => {
            setValueAttribute(ciTypeAttribute.id, e);
          }}
        />
      ),
      [CiTypeAttributeDataType.REFERENCE]: isFeatureReferenceAttribute ? (
        <CiReferenceFieldSelect
          mappingProps={mappingProps}
          optionReferenceFields={optionReferenceFields}
          referenceFieldConfigs={listReferFields}
          ciTypeReferenceId={ciTypeAttribute.ciTypeReferenceId ?? 0}
          onChange={(val) => {
            setValueAttribute(ciTypeAttribute.id, val);
          }}
        />
      ) : (
        <></>
      ),
    };
    if (!ciTypeAttribute.type) {
      return <></>;
    }

    return mapping[ciTypeAttribute.type] || <></>;
  };
  const RenderAttributeCustom = (item: ConfigItemAttrCustomModel, key: number) => {
    return (
      <Flex align={'flex-end'}>
        <KanbanInput
          style={{ flex: 1 }}
          label={item.name}
          value={item.value}
          onChange={(e) => {
            const value = e.target.value;

            const currentCiAttributeCustoms = [...ciAttributeCustoms];
            const currentData = { ...currentCiAttributeCustoms[key] };
            currentData.value = value;
            currentCiAttributeCustoms[key] = currentData;
            if (onChangeCiAttributeCustoms) {
              onChangeCiAttributeCustoms(currentCiAttributeCustoms);
            }
            setCiAttributeCustoms(currentCiAttributeCustoms);

            const currentCiAttributeCustom = ciAttributeCustoms[key];
            setHistoryDescription((prev) => {
              const result = klona(prev);

              let current = result.attributeCustoms.find((x) => x.id === currentCiAttributeCustom?.id);
              if (!current) {
                current = {
                  id: currentCiAttributeCustom?.id,
                  name: currentCiAttributeCustom?.name || '',
                };
                result.attributeCustoms.push(current);
              }
              current.toValue = String(value);

              return result;
            });
          }}
          disabled={isDisableCiAttributeCustom}
          rightSection={
            onRemoveCiAttributeCustoms && (
              <>
                <KanbanIconButton
                  variant='transparent'
                  onClick={() => {
                    setCiAttributeCustomDelete(item);
                    openModalDeleteAttribute();
                  }}
                  c={'red'}>
                  <IconX></IconX>
                </KanbanIconButton>
              </>
            )
          }
        />
      </Flex>
    );
  };

  const validateRequired = (value: string | undefined) => {
    if (!value) {
      return 'This field is required';
    }
    return undefined; // No error
  };

  const formCreateAttributeCustom = useForm<FormCreateAttribute>({
    initialValues: {
      name: '',
      value: '',
    },
    validate: {
      name: validateRequired,
      value: validateRequired,
    },
  });

  useEffect(() => {
    formAddOptionRef.current = formCreateAttributeCustom;
  }, [formCreateAttributeCustom]);

  useEffect(() => {
    if (formAddOptionRef.current) {
      const { setValues } = formAddOptionRef.current;

      const nameInput = attributeLabel ? attributeLabel.trim() : '';
      const valueInput = attributeContent ? attributeContent.trim() : '';
      setValues({ name: nameInput, value: valueInput });
    }
  }, [attributeLabel, attributeContent]);

  const createAttribute = () => {
    formCreateAttributeCustom.validate();
    if (!formCreateAttributeCustom.isValid()) {
      return;
    }
    if (onAddCiAttributeCustoms) {
      onAddCiAttributeCustoms({ id: 0, ciId: 0, name: attributeLabel.trim(), value: attributeContent.trim() });
    }
    setAttributeLabel('');
    setAttributeContent('');
  };

  const ciTypes = useGetCiTypes();

  const checkViewInfoCi = useMemo(() => {
    const { ciTypeId, id: ciId } = ci;
    const viewBasicCiPermissions = AclPermission.createViewCiPermissions(ciId, ciTypeId);
    const viewAdvancedCiPermissions = AclPermission.createCiPermissions(PermissionAction.CI__VIEW_ADVANCED, ciId, ciTypeId);
    const isShowBasicInfoCi = isCreateCi || isCurrentUserMatchPermissions(viewBasicCiPermissions);
    const isShowAdvancedInfoCi = isCreateCi || isCurrentUserMatchPermissions(viewAdvancedCiPermissions);
    return { isShowBasicInfoCi, isShowAdvancedInfoCi };
  }, [ci, isCreateCi]);
  return (
    <>
      <KanbanConfirmModal
        title='Delete Attribute'
        onConfirm={() => {
          if (onRemoveCiAttributeCustoms && ciAttributeCustomDelete) {
            onRemoveCiAttributeCustoms(ciAttributeCustomDelete);
            setHistoryDescription((prev) => {
              const result = klona(prev);

              let current = result.attributeCustoms.find((x) => x.id === ciAttributeCustomDelete?.id);
              if (!current) {
                current = {
                  id: ciAttributeCustomDelete?.id,
                  name: ciAttributeCustomDelete?.name || '',
                };
                result.attributeCustoms.push(current);
              }
              current.toValue = 'Deleted';

              return result;
            });
          }
          setCiAttributeCustomDelete(undefined);
          closeModalDeleteAttribute();
        }}
        textConfirm='Delete'
        onClose={closeModalDeleteAttribute}
        opened={openedModalDeleteAttribute}>
        Are you sure to delete this Attribute?
      </KanbanConfirmModal>
      {checkViewInfoCi.isShowBasicInfoCi && (
        <SimpleGrid cols={2}>
          <div>
            <KanbanInput label='ID' defaultValue={ci.id || ''} disabled />
            <KanbanInput label='CI Type' defaultValue={ciTypes.data.find((x) => x.id === ci.ciTypeId)?.name || ''} disabled />
            <KanbanInput
              c={differenceObject?.attributes && differenceObject.attributes[DEFAULT_ID_FOR_CI_NAME] && hightLightChange ? 'green' : ''}
              label='Name'
              withAsterisk
              value={ci.name || ''}
              disabled={isDisableCi}
              urlDetect={true}
              maxLength={100}
              onBlur={(e) => {
                const { target } = e;
                const result = { ...ci, name: formatStandardName(target.value) };
                if (onChangeCi) {
                  onChangeCi(result);
                }
                setCi(result);

                setHistoryDescription((prev) => {
                  const result = klona(prev);

                  let current = result.info.find((x) => x.name === 'name');
                  if (!current) {
                    current = {
                      name: 'name',
                      toValue: '',
                    };
                    result.info.push(current);
                  }
                  current.toValue = String(target.value);

                  return result;
                });
              }}
              onChange={(e) => {
                const { target } = e;
                const result = { ...ci, name: target.value };
                setCi(result);
              }}
            />
          </div>

          <div>
            <KanbanTextarea
              label='Description'
              value={ci.description || ''}
              c={differenceObject?.attributes && differenceObject.attributes[DEFAULT_ID_FOR_CI_DESCRIPTION] && hightLightChange ? 'green' : ''}
              autosize
              minRows={3}
              maxRows={10}
              maxLength={2000}
              disabled={isDisableCi}
              onChange={(e) => {
                const { target } = e;

                const result = { ...ci, description: target.value };
                if (onChangeCi) {
                  onChangeCi(result);
                }
                setCi(result);
                setHistoryDescription((prev) => {
                  const result = klona(prev);

                  let current = result.info.find((x) => x.name === 'description');
                  if (!current) {
                    current = {
                      name: 'description',
                      toValue: '',
                    };
                    result.info.push(current);
                  }
                  current.toValue = String(target.value);

                  return result;
                });
              }}
            />
          </div>
        </SimpleGrid>
      )}

      {checkViewInfoCi.isShowAdvancedInfoCi && (
        <>
          <SimpleGrid cols={2}>
            <div>
              {listCiTypeAttribute.map((x, key) => {
                if (!x.column || CiTypeColumn.LEFT === x.column) {
                  return <React.Fragment key={key}>{RenderCiTypeAttribute(x)}</React.Fragment>;
                }
                return <React.Fragment key={key}></React.Fragment>;
              })}
            </div>

            <div>
              {listCiTypeAttribute.map((x, key) => {
                if (CiTypeColumn.RIGHT === x.column) {
                  return <React.Fragment key={key}>{RenderCiTypeAttribute(x)}</React.Fragment>;
                }
                return <React.Fragment key={key}></React.Fragment>;
              })}
            </div>
          </SimpleGrid>

          {!hiddenCustomAttribute && (
            <>
              {/* section create custom attribute */}
              <Container mt={'xl'} mb={'sm'} p={0} fluid style={{ backgroundColor: 'var(--mantine-color-gray-0)' }}>
                <Group gap={5}>
                  <KanbanText tt='uppercase' c='dimmed'>
                    Custom CI Attribute(s)
                  </KanbanText>
                  {onAddCiAttributeCustoms && (
                    <>
                      {showAddAttribute ? (
                        <IconCircleMinus
                          color='var(--mantine-color-dimmed)'
                          onClick={() => {
                            setShowAddAttribute(!showAddAttribute);
                          }}
                        />
                      ) : (
                        <IconCirclePlus
                          color='var(--mantine-color-dimmed)'
                          onClick={() => {
                            setShowAddAttribute(!showAddAttribute);
                          }}
                        />
                      )}
                    </>
                  )}
                </Group>
                {showAddAttribute && (
                  <>
                    <KanbanText size='sm' c='red'>
                      This attributes will be added for only this CI
                    </KanbanText>
                    <Flex justify='space-between' gap={'xs'}>
                      <KanbanInput
                        placeholder='Enter label'
                        {...formCreateAttributeCustom.getInputProps('name')}
                        w={'48%'}
                        value={attributeLabel}
                        maxLength={255}
                        onChange={(e) => setAttributeLabel(e.currentTarget.value)}
                      />
                      <KanbanInput
                        placeholder='Enter value'
                        {...formCreateAttributeCustom.getInputProps('value')}
                        w={'40%'}
                        value={attributeContent}
                        maxLength={2000}
                        onChange={(e) => setAttributeContent(e.currentTarget.value)}
                      />
                      <KanbanButton
                        size='xs'
                        mt={3}
                        onClick={() => {
                          createAttribute();
                        }}>
                        {'Add'}
                      </KanbanButton>
                    </Flex>
                  </>
                )}
              </Container>

              <SimpleGrid cols={2}>
                <div>
                  {ciAttributeCustoms.map((item, key) => {
                    if (key % 2 === 1) {
                      return <React.Fragment key={key}></React.Fragment>;
                    }
                    return <React.Fragment key={key}>{RenderAttributeCustom(item, key)}</React.Fragment>;
                  })}
                </div>
                <div>
                  {ciAttributeCustoms.map((item, key) => {
                    if (key % 2 === 0) {
                      return <React.Fragment key={key}></React.Fragment>;
                    }
                    return <React.Fragment key={key}>{RenderAttributeCustom(item, key)}</React.Fragment>;
                  })}
                </div>
              </SimpleGrid>
            </>
          )}
        </>
      )}
    </>
  );
};

export default CiUpdate;
