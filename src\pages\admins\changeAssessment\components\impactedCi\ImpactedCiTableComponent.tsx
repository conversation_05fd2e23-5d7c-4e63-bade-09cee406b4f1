import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { KanbanButton, KanbanCheckbox, KanbanTooltip, renderDateTime } from 'kanban-design-system';
import { KanbanTable, type ColumnType, type KanbanTableProps } from 'kanban-design-system';
import { useNavigate } from 'react-router-dom';
import { IconClipboardList } from '@tabler/icons-react';
import CiUpdateSole, { CiUpdateSoleMethods } from '@pages/cis/ci/CiUpdateSole';
import { KanbanConfirmModal } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import { KanbanIconButton } from 'kanban-design-system';
import type { CiManagementResponse } from '@api/CiManagementApi';
import CiManagementDetailViewPopup, { CiManagementDetailViewPopupMethods } from '@pages/cis/ciManagement/modal/CiManagementDetailViewPopup';
import type { CiImpactedByRelationshipInformationResponseDto, ImpactedCiTableComponentProps } from '@models/ChangeAssessment';
import { Badge, ScrollArea, Tooltip } from '@mantine/core';
import { IconEye } from '@tabler/icons-react';
import { IconAffiliate } from '@tabler/icons-react';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { keyImpactedCiAndMergedCisInChangeAndAttType, keyPairImpactedCiAndCiInChange } from '@common/utils/ChangeAssessmentUtils';
import { buildCiTypeUrl, buildCiUrl } from '@common/utils/RouterUtils';
import { ImpactedCiTableViewEnum } from '@common/constants/ChangeAssessmentConstants';
import { FlaggedImpactedCiAttachedType } from '@common/constants/CiManagement';
import CiRelationship from '@pages/cis/ci/relationship/CiRelationship';
import { CiInfoModel } from '@models/ConfigItem';
import { ImpactedCiRuleRelationApi } from '@api/ImpactedCiRuleRelationApi';
import { CiDetailScreenType, CiDetailSubTabType } from '@common/constants/CiDetail';
import { dateToString, DD_MM_YYYY_HH_MM_FORMAT, stringToDate } from '@common/utils/DateUtils';

const columnCountCiTitle = 'Count';
const flagColumnTitle = 'Flag';
export const ImpactedCiTableComponent: React.FC<ImpactedCiTableComponentProps> = ({
  allowEdit,
  level,
  //for disable/enable button flag ci
  listCiChange,
  listCiDraft,
  listFlagCi,
  listImpactedCi,
  listImpactedCiNew,
  setListImpactedCi,
  setListImpactedCiNew,
  showAllCiImpacted,
}) => {
  //avoid when whenever flag=>  dont call fetch unnessasry
  // const prevIdsRef = useRef<number[]>(listImpactedCi.map((item) => item.ciImpactedId ?? 0));
  const navigate = useNavigate();
  const [openedModalViewCi, { close: closeModalViewCi, open: openModalViewCi }] = useDisclosure(false);
  const [openedModalRelationship, { close: closeModalRelationship, open: openModalRelationship }] = useDisclosure(false);
  const [currentCiInfo, setCurrentCiInfo] = useState<CiInfoModel | undefined>();
  const [currentCiTemp, setCurrentCiTemp] = useState<CiManagementResponse>();

  const keySetPairImpactedCiAndCiInChange = useMemo(() => {
    return new Set(listFlagCi.map((item) => item.ciChangePlans?.map((it) => keyPairImpactedCiAndCiInChange(it.id, item.ciImpactedId))).flat());
  }, [listFlagCi]);
  const onClickViewDraft = useCallback(
    (ciId: number) => {
      const ciTempData = listCiDraft.find((x) => x.ciId === ciId);
      setCurrentCiTemp(ciTempData);
      childRefViewDetail.current?.openPopupView();
    },
    [listCiDraft],
  );

  const fetchListImpactedCis = useCallback(() => {
    const ciChangeIds: number[] = listCiChange.map((entity) => entity.id);
    ImpactedCiRuleRelationApi.fetchImpactedCisByLevelAndCiInChangePlans(level, ciChangeIds)
      .then((res) => {
        if (res.data) {
          const datas = res.data
            ? res.data.map((it) => {
                return {
                  ...it,
                  ciChangePlans: [
                    {
                      id: it.ciChangeId,
                      ciTypeId: it.ciTypeChangeId,
                      name: it.ciChangeName,
                      createdBy: it.ciChangeCreatedBy,
                      createdDate: it.ciChangeCreatedDate,
                      description: it.ciChangeDescription,
                    },
                  ],
                } as CiImpactedByRelationshipInformationResponseDto;
              })
            : [];
          setListImpactedCi(datas);
        }
      })
      .catch(() => {});
  }, [level, listCiChange, setListImpactedCi]);

  const columns: ColumnType<CiImpactedByRelationshipInformationResponseDto>[] = useMemo(() => {
    const cols = [
      {
        title: flagColumnTitle,
        customRenderHeader(dataRender: CiImpactedByRelationshipInformationResponseDto[]) {
          // Create a Set of impacted CI keys from listImpactedCiNew
          const listImpactedCiNewKeySet = new Set(
            listImpactedCiNew.map((item) => {
              return keyImpactedCiAndMergedCisInChangeAndAttType(item);
            }),
          );

          // Filter dataRender to find items that are not disabled (not in the keySetPairImpactedCiAndCiInChange)
          const listNotDisabled = dataRender.filter((item) => {
            return !keySetPairImpactedCiAndCiInChange.has(keyPairImpactedCiAndCiInChange(item.ciChangeId, item.ciImpactedId));
          });

          // Create a Set of keys for items in listNotDisabled
          const listNotDisabledKeySet = new Set(listNotDisabled.map((item) => keyImpactedCiAndMergedCisInChangeAndAttType(item)));

          // Filter listNotDisabled to find items that are not checked (not in the listImpactedCiNewKeySet)
          const listNotChecked = listNotDisabled.filter((item) => {
            return !listImpactedCiNewKeySet.has(keyImpactedCiAndMergedCisInChangeAndAttType(item));
          });

          // Determine if all items in listNotDisabled are disabled (no items in the list)
          const isDisableAll = listNotDisabled.length === 0;

          // Determine if all items in listNotChecked are checked (no items left to check)
          let isCheckedAll = listNotChecked.length === 0;

          // If all items are disabled, set isCheckedAll to false since no items are available for selection
          if (isDisableAll) {
            isCheckedAll = false;
          }
          return (
            <KanbanTooltip label={'Flag all impacted cis'}>
              <KanbanCheckbox
                disabled={isDisableAll}
                checked={isCheckedAll}
                onChange={(e) => {
                  const checked = e.target.checked;
                  let selectedCis = [...listImpactedCiNew];
                  if (checked) {
                    selectedCis = [...listNotChecked, ...selectedCis];
                  } else {
                    selectedCis = selectedCis.filter((item) => !listNotDisabledKeySet.has(keyImpactedCiAndMergedCisInChangeAndAttType(item)));
                  }
                  setListImpactedCiNew(selectedCis);
                }}
              />
            </KanbanTooltip>
          );
        },
        name: 'markRelated',
        sortable: false,
        advancedFilter: {
          enable: false,
        },
        width: '3%',
        customRender: (_, rowData: CiImpactedByRelationshipInformationResponseDto) => {
          const isDisable = keySetPairImpactedCiAndCiInChange.has(
            keyPairImpactedCiAndCiInChange(rowData.ciChangePlans ? rowData.ciChangePlans[0].id : undefined, rowData.ciImpactedId),
          );
          return (
            <KanbanTooltip label={'Flag impacted ci'}>
              <KanbanCheckbox
                checked={
                  isDisable
                    ? true
                    : listImpactedCiNew.some(
                        (item) => keyImpactedCiAndMergedCisInChangeAndAttType(item) === keyImpactedCiAndMergedCisInChangeAndAttType(rowData),
                      )
                }
                disabled={isDisable}
                onChange={(e) => {
                  const checked = e.target.checked;
                  let selectedCis = [...listImpactedCiNew];
                  if (checked) {
                    selectedCis = [rowData, ...selectedCis];
                  } else {
                    selectedCis = selectedCis.filter((item) => {
                      return keyImpactedCiAndMergedCisInChangeAndAttType(item) !== keyImpactedCiAndMergedCisInChangeAndAttType(rowData);
                    });
                  }
                  setListImpactedCiNew(selectedCis);
                }}
              />
            </KanbanTooltip>
          );
        },
      },
      {
        title: 'Impacted CI name',
        sortable: true,
        name: 'ciImpactedName',
        width: '20%',
        customRender: (data: string, rowData: CiImpactedByRelationshipInformationResponseDto) => {
          return (
            <KanbanButton
              size='compact-xs'
              radius={'lg'}
              variant={'subtle'}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(buildCiUrl(rowData.ciTypeImpactedId, rowData.ciImpactedId, CiDetailScreenType.RELATIONSHIPS, CiDetailSubTabType.LISTVIEW));
              }}>
              {rowData.ciImpactedName}
            </KanbanButton>
          );
        },
      },
      {
        title: 'Impacted CI type',
        sortable: true,
        name: 'ciTypeImpactedName',
        width: '10%',
        customRender: (data: string, rowData: CiImpactedByRelationshipInformationResponseDto) => {
          return (
            <KanbanButton
              size='compact-xs'
              radius={'lg'}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(buildCiTypeUrl(rowData.ciTypeImpactedId));
              }}>
              {rowData.ciTypeImpactedName}
            </KanbanButton>
          );
        },
      },
      {
        title: 'Has impact',
        sortable: true,
        name: 'hasImpact',
        width: '10%',
        customRender: () => {
          return 'Yes';
        },
      },

      {
        title: 'CI Name in change plan',
        sortable: true,
        name: 'ciChangeName',
        width: '20%',
        customRender: (_, rowData: CiImpactedByRelationshipInformationResponseDto) => {
          return (
            <KanbanButton
              size='compact-xs'
              radius={'lg'}
              variant={'subtle'}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(buildCiUrl(rowData.ciTypeChangeId, rowData.ciChangeId, CiDetailScreenType.RELATIONSHIPS, CiDetailSubTabType.LISTVIEW));
              }}>
              {rowData.ciChangeName}
            </KanbanButton>
          );
        },
      },
      {
        title: 'CI Type in change plan',
        sortable: true,
        name: 'ciTypeChangeName',
        hidden: true,
        width: '20%',
        customRender: (_, rowData: CiImpactedByRelationshipInformationResponseDto) => {
          return (
            <KanbanButton
              size='compact-xs'
              radius={'lg'}
              variant={'subtle'}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(buildCiTypeUrl(rowData.ciTypeChangeId));
              }}>
              {rowData.ciTypeChangeName}
            </KanbanButton>
          );
        },
      },

      {
        title: 'Created date',
        sortable: false,
        name: 'createdDateCiImpacted',
        width: '10%',
        hidden: true,
        customRender: renderDateTime,
      },
      {
        title: 'Created by',
        sortable: false,
        name: 'createdByCiImpacted',
        width: '10%',
        hidden: true,
      },
      {
        title: 'Attached type',
        sortable: false,
        name: 'attachedType',
        width: '10%',
        customRender: () => {
          return (
            <Badge mt='5' color={'var(--mantine-color-orange-5)'}>
              {FlaggedImpactedCiAttachedType.CALCULATED}
            </Badge>
          );
        },
      },
    ] as ColumnType<CiImpactedByRelationshipInformationResponseDto>[];

    return cols as ColumnType<CiImpactedByRelationshipInformationResponseDto>[];
  }, [listImpactedCiNew, keySetPairImpactedCiAndCiInChange, setListImpactedCiNew, navigate]);

  //impacted ci by rule: data from redis -> results is list, not page -> search client -> bug search by date raw , not the display date -> confuse
  const onSearchCiImpacted = useCallback((datas: CiImpactedByRelationshipInformationResponseDto[], search: string) => {
    if (!search || !search.trim()) {
      return datas;
    }
    const lowerCaseSearch = search.toLowerCase();
    return datas.filter((item) => {
      const impactedCiName = item.ciImpactedName;
      const impactedCiTypeName = item.ciTypeImpactedName;
      const inChangePlanCiName = item.ciChangeName;
      const createdDate = item.createdDateCiImpacted ? dateToString(stringToDate(item.createdDateCiImpacted), DD_MM_YYYY_HH_MM_FORMAT) : '';
      const createdBy = item.createdByCiImpacted || '';
      const attachedType = item.attachedType || '';
      return [impactedCiName, impactedCiTypeName, inChangePlanCiName, inChangePlanCiName, createdDate, createdBy, attachedType].some((item) =>
        (item || '').toLowerCase().includes(lowerCaseSearch),
      );
    });
  }, []);

  const tableProps: KanbanTableProps<CiImpactedByRelationshipInformationResponseDto> = useMemo(() => {
    let cols = columns;
    //fix not remove count in VIEW - option view all in tab impacted cis
    if (ImpactedCiTableViewEnum.ALL === showAllCiImpacted) {
      cols = cols.filter((item) => item.title !== columnCountCiTitle);
    }
    if (!allowEdit) {
      cols = cols.filter((item) => item.title !== flagColumnTitle);
    }
    return {
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 1000,
        onSearched: onSearchCiImpacted,
      },
      sortable: {
        enable: true,
      },
      advancedFilterable: {
        enable: true,
        debounceTime: 1000,
        resetOnClose: true,
        compactMode: true,
      },
      columns: cols,
      data: listImpactedCi,
      //set key to smth that change when ever change 'Select option to view' -> trigger table reload to cols
      key: cols,
      //3849 antht, api calculated relation return list
      // serverside: {
      //   totalRows: totalRecords,
      //   onTableAffected(dataSet) {
      //     if (!equal(tableAffected, dataSet)) {
      //       setTableAffected(dataSet);
      //     }
      //   },
      // },
      pagination: {
        enable: true,
      },
      actions: {
        customAction: (data) => {
          return (
            <>
              <Tooltip label='View change'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  disabled={listCiDraft.every((x) => x.ciId !== data.ciImpactedId)}
                  onClick={() => {
                    onClickViewDraft(data.ciImpactedId);
                  }}>
                  <IconClipboardList />
                </KanbanIconButton>
              </Tooltip>
              <GuardComponent requirePermissions={AclPermission.createViewCiPermissions(data.ciImpactedId, data.ciTypeImpactedId)} hiddenOnUnSatisfy>
                <Tooltip label='View info'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setCurrentCiInfo({
                        ciId: data.ciImpactedId,
                        ciTypeId: data.ciTypeImpactedId,
                      });
                      openModalViewCi();
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                </Tooltip>
              </GuardComponent>
              <Tooltip label='View relationship'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    setCurrentCiInfo({
                      ciId: data.ciImpactedId,
                      ciTypeId: data.ciTypeImpactedId,
                    });
                    openModalRelationship();
                  }}>
                  <IconAffiliate />
                </KanbanIconButton>
              </Tooltip>
            </>
          );
        },
      },
    };
  }, [
    allowEdit,
    columns,
    listCiDraft,
    listImpactedCi,
    onClickViewDraft,
    onSearchCiImpacted,
    openModalRelationship,
    openModalViewCi,
    showAllCiImpacted,
  ]);

  const customTableProps = useMemo(() => {
    const tablePropsUpdate = { ...tableProps };
    if (!allowEdit) {
      const { actions, selectableRows, ...rest } = tablePropsUpdate;
      if (selectableRows) {
        selectableRows.enable = false;
      }
      return { ...rest, selectableRows, actions };
    }
    return tablePropsUpdate;
  }, [allowEdit, tableProps]);

  useEffect(() => {
    fetchListImpactedCis();
  }, [fetchListImpactedCis]);

  const childRef = useRef<CiUpdateSoleMethods>(null);
  const childRefViewDetail = useRef<CiManagementDetailViewPopupMethods>(null);

  return (
    <>
      {/* modal view detail info of CI management draft */}
      <CiManagementDetailViewPopup ref={childRefViewDetail} initData={currentCiTemp} />

      <KanbanConfirmModal
        title={'CI Detail'}
        onClose={closeModalViewCi}
        onConfirm={undefined}
        opened={openedModalViewCi}
        modalProps={{
          size: '80%',
        }}>
        {currentCiInfo && (
          <ScrollArea.Autosize mah={1000} type='scroll'>
            <CiUpdateSole readOnly={true} ciId={currentCiInfo.ciId} ciTypeId={currentCiInfo.ciTypeId} forwardedRef={childRef} />
          </ScrollArea.Autosize>
        )}
      </KanbanConfirmModal>

      <KanbanConfirmModal
        title={'Relationship'}
        onConfirm={undefined}
        onClose={closeModalRelationship}
        opened={openedModalRelationship}
        modalProps={{
          size: '80%',
        }}>
        {currentCiInfo && <CiRelationship ciId={currentCiInfo.ciId} ciTypeId={currentCiInfo.ciTypeId} isView isFromBusinessView={true} />}
      </KanbanConfirmModal>

      <KanbanTable {...customTableProps} />
    </>
  );
};
