// /// <reference types="@welldone-software/why-did-you-render" />
// eslint-disable-next-line check-file/filename-naming-convention
import React from 'react';
import whyDidYouRender from '@welldone-software/why-did-you-render';
if (process.env.NODE_ENV === 'development') {
  const includeFileToTrack: RegExp[] = []; ///^Kanban.*/
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  //const whyDidYouRender = require('@welldone-software/why-did-you-render');

  whyDidYouRender(React, {
    trackAllPureComponents: true,
    include: includeFileToTrack,
  });
}
