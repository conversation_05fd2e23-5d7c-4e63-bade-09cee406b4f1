import { ColumnType, KanbanTable, TableAffactedSafeType } from 'kanban-design-system';
import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import equal from 'fast-deep-equal';
import { KanbanText } from 'kanban-design-system';
import { BusinessDomainApi } from '@api/BusinessDomainApi';
import { tableAffectedToPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import { BusinessDomainItem, BusinessDomainModel } from '@models/BusinessDomain';
import { useDisclosure } from '@mantine/hooks';
import { KanbanModal } from 'kanban-design-system';
import { BusinessFunctionModal } from './functionModal/index';
import styles from './BusinessDomainDetail.module.scss';

export const BusinessDomainDetailTabBusinessFunction = () => {
  const { ciId: ciIdParam } = useParams();
  const ciId = Number(ciIdParam);

  const [searchParams] = useSearchParams();
  const ciBusinessFunction = Number(searchParams.get('ciBusinessFunction'));
  const ciNameBusinessFunction = searchParams.get('ciNameBusinessFunction');

  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);

  const [listData, setListData] = useState<BusinessDomainItem[]>([]);
  const [isLoadingTable, setIsLoadingTable] = useState(false);
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);
  const [activeItem, setActiveItem] = useState<BusinessDomainItem>({
    id: ciBusinessFunction,
    name: ciNameBusinessFunction || '',
  });

  const columns: ColumnType<BusinessDomainItem>[] = useMemo(
    () => [
      {
        title: 'Name Function',
        name: 'name',
        width: '30%',
      },
      {
        title: 'ID',
        name: 'id',
        hidden: true,
      },
      {
        title: 'Description',
        name: 'description',
        customRender: (data: any) => {
          return <KanbanText lineClamp={2}>{data}</KanbanText>;
        },
        width: '60%',
      },
    ],
    [],
  );

  const fetchBusinessFunction = useCallback(() => {
    if (!tableAffected) {
      return;
    }
    setIsLoadingTable(true);
    const pagination = tableAffectedToPaginationRequestModel(tableAffected);
    pagination.sortBy = tableAffected.sortedBy || ('id' as keyof BusinessDomainModel);
    pagination.isReverse = !tableAffected.sortedBy ? false : tableAffected.isReverse;
    BusinessDomainApi.getBusinessFunctionByCiIdWithPaging(ciId, pagination)
      .then((res) => {
        if (res.data) {
          setListData(res.data?.content || []);
          setTotalRecords(res.data.totalElements);
        }
      })
      .catch(() => {})
      .finally(() => {
        setIsLoadingTable(false);
      });
  }, [tableAffected, ciId]);

  useEffect(() => {
    fetchBusinessFunction();
  }, [fetchBusinessFunction]);

  useEffect(() => {
    if (ciBusinessFunction > 0) {
      openModal();
    }
  }, [ciBusinessFunction, openModal]);

  return (
    <>
      <KanbanModal
        className={styles['wrapper']}
        showFullScreenAction={false}
        size={'100%'}
        opened={openedModal}
        onClose={closeModal}
        title={activeItem.name}>
        {<BusinessFunctionModal ciIdBusinessDomain={ciId} ciIdBusinessFunction={activeItem.id} />}
      </KanbanModal>
      <KanbanTable
        columns={columns}
        key={1}
        data={listData}
        isLoading={isLoadingTable}
        showNumericalOrderColumn={true}
        sortable={{
          enable: true,
        }}
        searchable={{
          enable: false,
          debounceTime: 800,
        }}
        onRowClicked={(data) => {
          setActiveItem(data);
          openModal();
        }}
        pagination={{
          enable: true,
        }}
        serverside={{
          totalRows: totalRecords,
          onTableAffected(dataSet) {
            if (!equal(tableAffected, dataSet)) {
              setTableAffected(dataSet);
            }
          },
        }}
      />
    </>
  );
};

export default BusinessDomainDetailTabBusinessFunction;
