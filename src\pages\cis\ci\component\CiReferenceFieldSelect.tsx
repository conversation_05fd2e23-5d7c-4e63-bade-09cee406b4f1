import { KanbanCheckbox, KanbanConfirmModal, KanbanSelect, KanbanText } from 'kanban-design-system';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import type { CiTypeAttributeProps } from '../CiUpdate';
import type { CiAttributeInfoModel, CiTypeReferFieldsModel } from '@models/ConfigItemTypeAttr';
import { ActionIcon, ComboboxData, Flex, Grid, GridCol, Stack, Tooltip } from '@mantine/core';
import { IconSettingsBolt } from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import CiUpdateSole, { CiUpdateSoleMethods } from '../CiUpdateSole';
import type { CiInfoModel } from '@models/ConfigItem';

export type CiReferenceFieldSelectProps = {
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  mappingProps: CiTypeAttributeProps;
  optionReferenceFields: CiAttributeInfoModel[];
  referenceFieldConfigs: CiTypeReferFieldsModel[];
  ciTypeReferenceId: number;
};

export type SuggestRelationshipSelectItemDTO = {
  relationshipId: number;
  relationshipTypeName?: string;
  toCiTypeName?: string;
};

export const CiReferenceFieldSelect = (props: CiReferenceFieldSelectProps) => {
  const { ciTypeReferenceId, mappingProps, onChange, optionReferenceFields, referenceFieldConfigs } = props;

  const [showCiName, setShowCiName] = useState<boolean>(true);
  const [referenceFieldConfig, setReferenceFieldConfig] = useState<CiTypeReferFieldsModel | undefined>();
  const [openedModalViewCi, { close: closeModalViewCi, open: openModalViewCi }] = useDisclosure(false);

  const childRef = useRef<CiUpdateSoleMethods | null>(null);

  const listValueReferenceFields: ComboboxData = useMemo(() => {
    return optionReferenceFields.map((x) => ({
      value: `${x.ciId}`,
      label: showCiName ? `${x.ciName} [ ${x.ciAttributeValue ?? 'Empty value'} ]` : `${x.ciAttributeValue ?? 'Empty value'}`,
    }));
  }, [optionReferenceFields, showCiName]);

  useEffect(() => {
    const referenceFieldConfig = referenceFieldConfigs.find((x) => x.id === ciTypeReferenceId);
    setReferenceFieldConfig(referenceFieldConfig);
  }, [ciTypeReferenceId, referenceFieldConfigs]);

  const currentCiInfo: CiInfoModel = useMemo(() => {
    const currentSelectedField = optionReferenceFields.find((x) => `${x.ciId}` === mappingProps.value);

    return {
      ciId: currentSelectedField?.ciId ?? 0,
      ciTypeId: currentSelectedField?.ciTypeId ?? 0,
    };
  }, [mappingProps, optionReferenceFields]);

  return (
    <>
      <KanbanConfirmModal
        title={'CI Detail'}
        onConfirm={undefined}
        onClose={closeModalViewCi}
        opened={openedModalViewCi}
        modalProps={{
          size: '80%',
        }}>
        {currentCiInfo && <CiUpdateSole readOnly={true} ciId={currentCiInfo.ciId} ciTypeId={currentCiInfo.ciTypeId} forwardedRef={childRef} />}
      </KanbanConfirmModal>

      <Grid align={'end'} gutter={'xs'}>
        <GridCol span={10}>
          <KanbanSelect
            {...mappingProps}
            label={
              <Tooltip label={'Reference field config'} openDelay={500}>
                <Flex gap={5}>
                  <KanbanText fw={500}>{mappingProps.label}</KanbanText>
                  <KanbanText size={'xs'} c={'dimmed'} mt={'2'} fw={500}>
                    {referenceFieldConfig
                      ? `${referenceFieldConfig.ciTypeName} [ ${referenceFieldConfig.ciTypeAttributeName} ]`
                      : `Unable to load configuration`}
                  </KanbanText>
                </Flex>
              </Tooltip>
            }
            data={listValueReferenceFields}
            value={mappingProps.value || null}
            w={''}
            searchable
            allowDeselect
            clearable
            onChange={(value) => {
              if (onChange) {
                onChange(value || '');
              }
            }}
          />
        </GridCol>
        <GridCol span={2}>
          <Stack gap={3} justify='space-between'>
            <ActionIcon
              variant={'light'}
              size={'xs'}
              m={0}
              title='CI Detail'
              disabled={currentCiInfo?.ciId === 0}
              onClick={() => {
                openModalViewCi();
              }}>
              <IconSettingsBolt size='1rem' />
            </ActionIcon>
            <KanbanCheckbox
              size={'xs'}
              m={0}
              label={
                <Tooltip label='Show CI Name' openDelay={500}>
                  <KanbanText size='xs' c={'dimmed'} m={0} lineClamp={1}>
                    Show CI Name
                  </KanbanText>
                </Tooltip>
              }
              checked={showCiName}
              onChange={() => {
                setShowCiName(!showCiName);
              }}
            />
          </Stack>
        </GridCol>
      </Grid>
    </>
  );
};
export default CiReferenceFieldSelect;
