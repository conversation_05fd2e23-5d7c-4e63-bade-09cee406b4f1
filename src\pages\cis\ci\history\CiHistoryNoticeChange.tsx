import type { ConfigItemTypeAttrResponse } from '@api/ConfigItemTypeAttrApi';
import { KanbanText } from 'kanban-design-system';
import { Alert, Container, Group, Table, Tooltip } from '@mantine/core';
import type { DataCiCompare } from '@models/CiDetailCompare';
import React, { useMemo } from 'react';
import { CiTypeAttributeDataType } from '@models/CiType';
import { IconEdit, IconPlus, IconRowRemove, IconTableImport } from '@tabler/icons-react';
import { CiHistoryAction, CiHistoryModel } from '@models/CiHistory';
type CiNoticeChangeProps = {
  ciHistory?: CiHistoryModel;
  differenceObject: DataCiCompare;
  listCiTypeAttribute: ConfigItemTypeAttrResponse[];
};

type DataChangeStyleObj = {
  title?: string;
  icon?: JSX.Element;
};

export type DataChangeObj = {
  key: string;
  oldValue?: string;
  newValue?: string | number;
  sourceChange?: string;
};

export const formatValueCiAttribute = (value: string | number | null, objAttribute: ConfigItemTypeAttrResponse | undefined): string => {
  if (objAttribute && CiTypeAttributeDataType.REFERENCE === objAttribute.type) {
    const objRefer = objAttribute.ciTypeReferenceData?.find((x) => `${x.ciId}` === `${value}`);
    return objRefer ? `${objRefer.ciName}[${objRefer.ciAttributeValue || 'Empty value'}]` : 'null';
  }
  return String(value);
};

export const CiHistoryNoticeChange = (props: CiNoticeChangeProps) => {
  const { ciHistory, differenceObject, listCiTypeAttribute } = props;
  const dataChangeStyleObj = useMemo((): DataChangeStyleObj => {
    const defaultValue = {};
    if (!ciHistory) {
      return defaultValue;
    }
    switch (ciHistory.action) {
      case CiHistoryAction.ADDED:
        return { title: 'Added', icon: <IconPlus /> };
      case CiHistoryAction.EDITED:
        return { title: 'Edited', icon: <IconEdit /> };
      case CiHistoryAction.IMPORT:
        return { title: 'Imported', icon: <IconTableImport /> };
      case CiHistoryAction.REMOVED:
        return { title: 'Removed', icon: <IconRowRemove /> };
      default:
        return defaultValue;
    }
  }, [ciHistory]);
  const mapChangeDataForRelationships = useMemo(() => {
    const result: DataChangeObj[] = [];
    const data = { ...differenceObject };
    for (const key in data.relationships) {
      const attribute = data.relationships[key];
      const keyName = key;

      const valueStr = String(attribute.oldValue);
      const newValue = String(attribute.newValue);
      result.push({ key: keyName, oldValue: valueStr, newValue: newValue });
    }

    return result;
  }, [differenceObject]);
  const mapChangeDataForCiData = useMemo(() => {
    const result: DataChangeObj[] = [];
    const data = { ...differenceObject };

    // Iterate through "ci" object
    for (const key in data.ci) {
      const oldValue = data.ci[key].oldValue || 'null';
      const newValue = data.ci[key].newValue || 'null';
      const sourceChange = String(data.ci[key].sourceChange || '');
      result.push({ key: key, oldValue: oldValue, newValue: newValue, sourceChange: sourceChange });
    }

    // Iterate through "attributes" object
    for (const key in data.attributes) {
      const attribute = data.attributes[key];
      const objAttribute = listCiTypeAttribute.find((x) => String(x.id) === key);
      const keyName = objAttribute ? objAttribute.name : key;

      const oldValue = String(attribute.oldValue);
      const valueStrOld = formatValueCiAttribute(oldValue, objAttribute);
      const newValue = String(attribute.newValue);
      const valueStrNew = formatValueCiAttribute(newValue, objAttribute);
      const sourceChange = String(attribute.sourceChange || '');
      result.push({ key: keyName, oldValue: valueStrOld, newValue: valueStrNew, sourceChange: sourceChange });
    }

    // Iterate through "attributes custom" object
    for (const key in data.attributesCustom) {
      const attribute = data.attributesCustom[key];
      const keyName = key;

      const valueStr = String(attribute.oldValue);
      const newValue = String(attribute.newValue);
      result.push({ key: keyName, oldValue: valueStr, newValue: newValue });
    }

    return result;
  }, [differenceObject, listCiTypeAttribute]);
  return (
    <>
      <Container maw={'100%'}>
        {mapChangeDataForCiData.length > 0 && (
          <Alert variant='light' color={'orange'} title={`${dataChangeStyleObj.title} CI data`} icon={dataChangeStyleObj.icon} p={'5'}>
            <Table withTableBorder withColumnBorders>
              <Table.Thead>
                <Table.Tr>
                  {['Attributes', 'Old value', 'New value', 'Data Source'].map((title, idx) => (
                    <Table.Th key={title} w={['20%', '25%', '25%', '25%'][idx]}>
                      <KanbanText size='xs' fw={500}>
                        {title}
                      </KanbanText>
                    </Table.Th>
                  ))}
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {mapChangeDataForCiData.map(({ key, newValue, oldValue, sourceChange }) => (
                  <Table.Tr key={key}>
                    {[key, oldValue, newValue, sourceChange || ''].map((value, idx) => (
                      <Table.Td key={idx} w={['20%', '25%', '25%', '25%'][idx]} maw={'200px'}>
                        <Tooltip label={value} multiline w={350} style={{ wordBreak: 'break-word' }}>
                          <KanbanText size='xs' fw={idx === 0 ? 500 : undefined} truncate='end' lineClamp={2} style={{ wordBreak: 'break-word' }}>
                            {value}
                          </KanbanText>
                        </Tooltip>
                      </Table.Td>
                    ))}
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Alert>
        )}
        {mapChangeDataForRelationships.length > 0 && (
          <Alert variant='light' color={'orange'} title={`${dataChangeStyleObj.title} CI relationships `} icon={dataChangeStyleObj.icon} p={'5'}>
            {mapChangeDataForRelationships.map((item) => (
              <Group key={item.key}>
                {item.newValue && (
                  <>
                    {/* {` -> `} */}
                    <KanbanText lineClamp={2}>{item.newValue}</KanbanText>
                  </>
                )}
                <br />
              </Group>
            ))}
          </Alert>
        )}
        {mapChangeDataForRelationships.length === 0 && mapChangeDataForCiData.length === 0 && <KanbanText>No data change</KanbanText>}
      </Container>
    </>
  );
};

export default CiHistoryNoticeChange;
