import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
import { DiscoveryStagingDataResponse } from '@models/discovery/DiscoveryStagingData';

export type DiscoveryStagingDataPagingResponse = PaginationResponseModel<DiscoveryStagingDataResponse>;
export class DiscoveryStagingDataApi extends BaseApi {
  static baseUrl = BaseUrl.discoveryStagingDatas;

  static getAll(pagination: PaginationRequestModel<DiscoveryStagingDataPagingResponse>) {
    return BaseApi.getData<DiscoveryStagingDataPagingResponse>(`${this.baseUrl}`, pagination);
  }

  static async getById(id: number) {
    return BaseApi.getData<DiscoveryStagingDataResponse>(`${this.baseUrl}/${id}`);
  }

  static discoverySourceByDiscoverySourceDataId(discoverySourceDataId: number) {
    return BaseApi.postData<DiscoveryStagingDataResponse>(`${this.baseUrl}?discoverySourceDataId=${discoverySourceDataId}`);
  }

  static deleteByIds(ids: number[]) {
    return BaseApi.deleteData<number[]>(`${this.baseUrl}`, {
      ids,
    });
  }
}
