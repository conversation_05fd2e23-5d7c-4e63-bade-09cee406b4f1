import { CLEAR_WHEN_LOGOUT_KEYS } from '@common/constants/LocalStorageKeyEnum';
import { getConfigs } from 'core/configs/Configs';
import Keycloak from 'keycloak-js';

const configs = getConfigs();

export const keycloak = new Keycloak({
  url: configs.keycloak.url,
  realm: configs.keycloak.realm,
  clientId: configs.keycloak.clientId,
});

function clearOnLogout() {
  Object.keys(localStorage)
    .filter((key) => CLEAR_WHEN_LOGOUT_KEYS.some((prefix) => key.startsWith(prefix)))
    .forEach((key) => localStorage.removeItem(key));
}

keycloak.onTokenExpired = () => {
  console.trace('token expired', keycloak.token);
  keycloak
    .updateToken(30)
    .then(() => {
      console.trace('token renew', keycloak.token);
    })
    .catch(() => {});
};

const getKeyCloack = () => keycloak;

const doLogin = keycloak.login;

const doLogout = () => {
  keycloak.logout({
    redirectUri: configs.deployUrl,
  });
  clearOnLogout();
};

const getToken = () => keycloak.token;

const isLoggedIn = () => keycloak.authenticated;

const getUsername = () => keycloak.tokenParsed?.realm_access;

const hasRole = (roles: string[]) => roles.some((role: string) => keycloak.hasRealmRole(role));

const KeycloakService = {
  doLogin,
  doLogout,
  isLoggedIn,
  getToken,
  getUsername,
  hasRole,
  getKeyCloack,
};

export default KeycloakService;
