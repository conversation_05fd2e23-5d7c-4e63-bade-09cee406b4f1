import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { KanbanButton, KanbanCheckbox, KanbanText, KanbanTooltip } from 'kanban-design-system';
import { KanbanTable, type ColumnType, type KanbanTableProps } from 'kanban-design-system';
import { useNavigate } from 'react-router-dom';
import { renderDateTime } from 'kanban-design-system';
import { IconClipboardList } from '@tabler/icons-react';
import CiUpdateSole, { CiUpdateSoleMethods } from '@pages/cis/ci/CiUpdateSole';
import { KanbanConfirmModal } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import { KanbanIconButton } from 'kanban-design-system';
import { CiManagementApi, type CiManagementResponse } from '@api/CiManagementApi';
import CiManagementDetailViewPopup, { CiManagementDetailViewPopupMethods } from '@pages/cis/ciManagement/modal/CiManagementDetailViewPopup';

import { ScrollArea, Tooltip } from '@mantine/core';
import { IconEye } from '@tabler/icons-react';
import { IconAffiliate } from '@tabler/icons-react';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { buildCiTypeUrl } from '@common/utils/RouterUtils';
import type { ConfigItemResponse } from '@api/ConfigItemApi';
import ViewServiceMappingDetail, { ViewServiceMappingDetailMethods } from '../ViewServiceMappingDetail';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import type { CiImpactedByRelationshipInformationResponseDto } from '@models/ChangeAssessment';
import { NumberMapEntry } from './ImpactedCiFlagTableComponent';
import { ConfigItemTypeModel } from '@models/ConfigItemType';
import { dateToString, DD_MM_YYYY_HH_MM_FORMAT } from '@common/utils/DateUtils';
import CiRelationship from '@pages/cis/ci/relationship/CiRelationship';
import { keyPairImpactedCiAndCiInChange } from '@common/utils/ChangeAssessmentUtils';
import { FlaggedImpactedCiAttachedType } from '@common/constants/CiManagement';

type ManualCiInChangePlanProps = {
  allowEdit: boolean;
  listCiChange: ConfigItemResponse[];
  currentFlagImpactedCi?: CiImpactedByRelationshipInformationResponseDto;
  currentListCiInChangeNew?: ConfigItemResponse[];
  setCurrentListCiInChangeNew: (vals: ConfigItemResponse[]) => void;
  //data table flag ci
  listFlagCi: CiImpactedByRelationshipInformationResponseDto[];
};
const flagColumnTitle = 'Config manual ci(s) in change plan';

const isSelfSelect = (val: number, val2?: number) => {
  return val === val2;
};

export const ManualCiInChangePlan: React.FC<ManualCiInChangePlanProps> = ({
  allowEdit,
  currentFlagImpactedCi,
  currentListCiInChangeNew,
  listCiChange,
  listFlagCi,
  setCurrentListCiInChangeNew,
}) => {
  const [listCiDraft, setListCiDraft] = useState<CiManagementResponse[]>([]);
  const allCiTypeMap = useGetCiTypes().data.reduce((acc: NumberMapEntry<ConfigItemTypeModel>, item) => {
    acc[item.id] = item; // Use the id as the key and the item as the value
    return acc;
  }, {});
  const navigate = useNavigate();

  const [openedModalViewCi, { close: closeModalViewCi, open: openModalViewCi }] = useDisclosure(false);
  const [openedModalRelationship, { close: closeModalRelationship, open: openModalRelationship }] = useDisclosure(false);
  const [currentCiInfo, setCurrentCiInfo] = useState<
    | {
        ciTypeId: number;
        ciId: number;
      }
    | undefined
  >();

  const keySetCalculatedImpactedCiPairs = useMemo(() => {
    return new Set(
      listFlagCi
        //filter only current impacted ci records( CALCULATED)
        .filter((it) => it.ciImpactedId === currentFlagImpactedCi?.ciImpactedId && FlaggedImpactedCiAttachedType.CALCULATED === it.attachedType)
        //create pair ciInChangeId###CiImpactedId
        .map((it) => it.ciChangePlans?.map((itx) => keyPairImpactedCiAndCiInChange(itx.id, it.ciImpactedId)))
        .flat(),
    );
  }, [currentFlagImpactedCi?.ciImpactedId, listFlagCi]);

  const [currentCiTemp, setCurrentCiTemp] = useState<CiManagementResponse>();

  const onClickViewDraft = useCallback(
    (ciId: number) => {
      const ciTempData = listCiDraft.find((x) => x.ciId === ciId);
      setCurrentCiTemp(ciTempData);
      childRefViewDetail.current?.openPopupView();
    },
    [listCiDraft],
  );
  // useEffect(() => {
  //   setCurrentListCiInChangeNew(currentFlagImpactedCi?.ciChangePlans);
  // }, [currentFlagImpactedCi?.ciChangePlans]);

  const renderSelectAll = useCallback(() => {
    if (!currentFlagImpactedCi) {
      return <></>;
    }
    const listOldSelectedCi = currentListCiInChangeNew || [];
    const oldSelectedCiKeySet = new Set(listOldSelectedCi.map((item) => item.id));

    const listNotSelectedCi = listCiChange.filter((item) => {
      return (
        !oldSelectedCiKeySet.has(item.id) &&
        !isSelfSelect(item.id, currentFlagImpactedCi?.ciImpactedId) &&
        !keySetCalculatedImpactedCiPairs.has(keyPairImpactedCiAndCiInChange(item.id, currentFlagImpactedCi.ciImpactedId))
      );
    });
    const isCheckedAll = listNotSelectedCi.length === 0;

    //find if exist not flagged ciImpacted-ciChangePlan, but if checkAll then not disable
    const isNotDisableAll = listCiChange.some(
      (it) => !keySetCalculatedImpactedCiPairs.has(keyPairImpactedCiAndCiInChange(it.id, currentFlagImpactedCi.ciImpactedId)),
    );

    return (
      <KanbanTooltip label={'Select all'}>
        <KanbanCheckbox
          checked={isCheckedAll}
          disabled={!isNotDisableAll}
          onChange={(e) => {
            const checked = e.target.checked;
            let listUpdatedNewSelectedCi = [...listOldSelectedCi];
            if (checked) {
              listUpdatedNewSelectedCi = [...listNotSelectedCi, ...listUpdatedNewSelectedCi];
            } else {
              listUpdatedNewSelectedCi = [];
            }
            setCurrentListCiInChangeNew(listUpdatedNewSelectedCi);
          }}
        />
      </KanbanTooltip>
    );
  }, [currentFlagImpactedCi, currentListCiInChangeNew, listCiChange, keySetCalculatedImpactedCiPairs, setCurrentListCiInChangeNew]);
  const columns: ColumnType<ConfigItemResponse>[] = useMemo(() => {
    return [
      {
        title: flagColumnTitle,
        customRenderHeader: () => {
          return renderSelectAll();
        },
        name: 'markRelated',
        sortable: false,
        advancedFilter: {
          enable: false,
        },
        width: '3%',
        customRender: (_, rowData: ConfigItemResponse) => {
          const currentListCisNew = [...(currentListCiInChangeNew || [])];
          //3849 : not allow add exist of pair (currentFlag manual Ci - manual ci in change )

          const currentRowPair = keyPairImpactedCiAndCiInChange(rowData.id, currentFlagImpactedCi?.ciImpactedId);
          const disableIfExistPair = keySetCalculatedImpactedCiPairs.has(currentRowPair);

          const isSelectSelf = isSelfSelect(rowData.id, currentFlagImpactedCi?.ciImpactedId);
          return (
            <KanbanTooltip
              label={
                isSelectSelf
                  ? 'Self-select!'
                  : disableIfExistPair
                    ? 'Exist a pair of impacted CI and CI in change plan!'
                    : 'Select manual CI in change plan'
              }>
              <KanbanCheckbox
                checked={currentListCisNew.some((item) => item.id === rowData.id)}
                disabled={isSelectSelf || disableIfExistPair}
                onChange={(e) => {
                  const checked = e.target.checked;
                  let selectedCis = [...currentListCisNew];
                  if (checked) {
                    selectedCis = [rowData, ...selectedCis];
                  } else {
                    selectedCis = selectedCis.filter((item) => {
                      return item.id !== rowData.id;
                    });
                  }
                  setCurrentListCiInChangeNew(selectedCis);
                }}
              />
            </KanbanTooltip>
          );
        },
      },
      {
        title: 'Id',
        name: 'id',
        hidden: true,
      },
      {
        title: 'Name',
        name: 'name',
        width: '10%',
      },
      {
        title: 'Description',
        name: 'description',
        customRender: (data) => {
          return <KanbanText lineClamp={2}>{data}</KanbanText>;
        },
        width: '40%',
      },
      {
        title: 'Ci Type',
        name: 'ciTypeId',
        customRender: (data: number) => {
          const ciType = allCiTypeMap[data];
          if (!ciType) {
            return <></>;
          }
          return (
            <KanbanButton
              size='compact-xs'
              radius={'lg'}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(buildCiTypeUrl(ciType.id));
              }}>
              {ciType.name}
            </KanbanButton>
          );
        },
        width: '10%',
      },
      {
        title: 'Created by',
        name: 'author',
        width: '10%',
      },
      {
        title: 'Created date',
        name: 'createdDate',
        customRender: renderDateTime,
        width: '10%',
      },
    ];
  }, [
    allCiTypeMap,
    currentFlagImpactedCi?.ciImpactedId,
    currentListCiInChangeNew,
    keySetCalculatedImpactedCiPairs,
    navigate,
    renderSelectAll,
    setCurrentListCiInChangeNew,
  ]);

  const onSearched = useCallback(
    (datas: ConfigItemResponse[], search: string): ConfigItemResponse[] => {
      const lowerCaseSearch = search.toLowerCase();
      return datas.filter((item) => {
        const ciName = item.name || '';
        const ciTypeName = allCiTypeMap[item.ciTypeId]?.name || '';
        const description = item.description || '';
        const createDate = item.createdDate ? dateToString(item.createdDate, DD_MM_YYYY_HH_MM_FORMAT) || '' : '';
        const createBy = item.createdBy || '';

        return [ciName, ciTypeName, description, createDate, createBy].some((item) => {
          return item.toLowerCase().includes(lowerCaseSearch);
        });
      });
    },
    [allCiTypeMap],
  );
  const tableProps: KanbanTableProps<ConfigItemResponse> = useMemo(() => {
    return {
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 300,
        onSearched: onSearched,
      },
      sortable: {
        enable: true,
      },
      columns: columns,
      data: listCiChange,
      pagination: {
        enable: true,
      },

      actions: {
        customAction: (data) => {
          return (
            <>
              <Tooltip label='View change'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  disabled={listCiDraft.every((x) => x.ciId !== data.id)}
                  onClick={() => {
                    onClickViewDraft(data.id);
                  }}>
                  <IconClipboardList />
                </KanbanIconButton>
              </Tooltip>
              <GuardComponent requirePermissions={AclPermission.createViewCiPermissions(data.id, data.ciTypeId)} hiddenOnUnSatisfy>
                <Tooltip label='View info'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setCurrentCiInfo({
                        ciId: data.id,
                        ciTypeId: data.ciTypeId,
                      });
                      openModalViewCi();
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                </Tooltip>
              </GuardComponent>

              <Tooltip label='View relationship'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    setCurrentCiInfo({
                      ciId: data.id,
                      ciTypeId: data.ciTypeId,
                    });
                    openModalRelationship();
                  }}>
                  <IconAffiliate />
                </KanbanIconButton>
              </Tooltip>
            </>
          );
        },
      },
    };
  }, [columns, listCiChange, listCiDraft, onClickViewDraft, onSearched, openModalRelationship, openModalViewCi]);

  const customTableProps = useMemo(() => {
    const tablePropsUpdate = { ...tableProps };
    if (!allowEdit) {
      const { actions, selectableRows, ...rest } = tablePropsUpdate;
      if (selectableRows) {
        selectableRows.enable = false;
      }
      if (actions && actions.deletable) {
        delete actions.deletable;
      }

      return { ...rest, selectableRows, actions };
    }

    return tablePropsUpdate;
  }, [allowEdit, tableProps]);

  const fetchDataDraft = useCallback(() => {
    const listIds = listCiChange.map((x) => x.id);
    if (listIds.length > 0) {
      CiManagementApi.getCiTempByCiIdIn(listIds)
        .then((res) => {
          if (res.data && res.data.length > 0) {
            setListCiDraft(res.data);
            return;
          }
        })
        .catch(() => {});
    }
  }, [listCiChange]);

  useEffect(() => {
    fetchDataDraft();
  }, [fetchDataDraft]);

  const childRef = useRef<CiUpdateSoleMethods | null>(null);
  const childRefViewDetail = useRef<CiManagementDetailViewPopupMethods | null>(null);
  const childRefViewServiceMapping = useRef<ViewServiceMappingDetailMethods | null>(null);

  return (
    <>
      {/* modal view detail info of CI management draft */}
      <CiManagementDetailViewPopup ref={childRefViewDetail} initData={currentCiTemp} />

      {/* Modal view service mapping */}
      <ViewServiceMappingDetail ref={childRefViewServiceMapping} />

      <KanbanConfirmModal
        title={'CI Detail'}
        onConfirm={undefined}
        onClose={closeModalViewCi}
        opened={openedModalViewCi}
        modalProps={{
          size: '80%',
        }}>
        {currentCiInfo && (
          <ScrollArea.Autosize mah={1000} type='scroll'>
            <CiUpdateSole readOnly={true} ciId={currentCiInfo.ciId} ciTypeId={currentCiInfo.ciTypeId} forwardedRef={childRef} />
          </ScrollArea.Autosize>
        )}
      </KanbanConfirmModal>

      <KanbanConfirmModal
        title={'Relationship'}
        onConfirm={undefined}
        onClose={closeModalRelationship}
        opened={openedModalRelationship}
        modalProps={{
          size: '80%',
        }}>
        {currentCiInfo && <CiRelationship ciId={currentCiInfo.ciId} ciTypeId={currentCiInfo.ciTypeId} isView isFromBusinessView={true} />}
      </KanbanConfirmModal>
      <KanbanTable {...customTableProps} />
    </>
  );
};
