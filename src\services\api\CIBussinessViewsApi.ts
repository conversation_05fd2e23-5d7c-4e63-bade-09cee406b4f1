import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import type { CIBusinessViewResponseDTO, CIBusinessViews } from '@models/CIBusinessViews';
import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';

export type CIBussinessViewsResponse = CIBusinessViews;
export type CIBussinessViewsResponsePagingResponse = PaginationResponseModel<CIBussinessViewsResponse>;

export class CIBussinessViewsApi extends BaseApi {
  static baseUrl = BaseUrl.ciBusinessViews;

  // static getAll() {
  //     return BaseApi.getData<CIBussinessViewsResponse[]>(`${this.baseUrl}/data`);
  // }

  // static getRelationShips(relationShip: RelationShipOfBusinessView[]) {
  //   return BaseApi.postData<CiRelationshipInfoModel[]>(`${this.baseUrl}/relationship`, relationShip);
  // }

  static getAll(pagination: PaginationRequestModel<CIBussinessViewsResponse>) {
    return BaseApi.postData<CIBussinessViewsResponsePagingResponse>(`${this.baseUrl}/filter`, pagination);
  }

  static async getById(id: number) {
    const response = await BaseApi.getData<CIBusinessViewResponseDTO>(`${this.baseUrl}/${id}`);
    if (response.data && response.data.entity.dataAction) {
      response.data.entity.dataActionParse = JSON.parse(response.data.entity.dataAction);
    }
    return response;
  }

  static saveOrUpdate(data: CIBusinessViews, ciId: number | undefined) {
    return BaseApi.postData<CIBusinessViewResponseDTO>(`${this.baseUrl}/cis/${ciId}`, data);
  }
  static deleteById(id: number) {
    return BaseApi.deleteData<boolean>(`${this.baseUrl}/${id}`);
  }
  static deleteByIds(ids: number[]) {
    return BaseApi.deleteData<number[]>(`${this.baseUrl}/batch`, {
      ids,
    });
  }
}
