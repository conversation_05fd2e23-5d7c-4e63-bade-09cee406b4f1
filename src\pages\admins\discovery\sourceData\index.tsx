import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanTable, ColumnType, TableAffactedSafeType, KanbanTableProps, KanbanConfirmModal } from 'kanban-design-system';
import { IconPlus, IconEdit, IconDatabaseImport, IconTrash } from '@tabler/icons-react';
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { buildSourceDataUrl, createOrUpdateDataSourcePath } from '@common/utils/RouterUtils';
import { renderDateTime } from 'kanban-design-system';
import equal from 'fast-deep-equal';

import { KanbanText, KanbanButton, KanbanIconButton } from 'kanban-design-system';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { FormatTypeEnum } from '@common/constants/FormatTypeEnum';
import { DiscoverySourceDataResponse } from '@models/discovery/DiscoverySourceData';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import { DiscoverySourceDataApi } from '@api/discovery/DiscoverySourceDataApi';
import { SourceDataAction } from '@common/constants/SourceDataActionEnum';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { DiscoveryStagingDataApi } from '@api/discovery/DiscoveryStagingDataApi';
import { BreadcrumbComponent } from '@pages/admins/breadcrumb/BreadcrumbComponent';
import { useDisclosure } from '@mantine/hooks';
import { MAX_TEXT_LENGTH } from '@common/constants/FieldLengthConstants';
const exampleDatas = [
  {
    id: 1,
    name: 'Source k8s',
    sourceId: 0,
    formatType: FormatTypeEnum.JSON,
    discoveryStagingId: 0,
    sourceName: 'K8s',
    discoveryStagingName: 'K8s Temp',
  },
];

export const DataSourcePage = () => {
  const navigate = useNavigate();
  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);
  const [listData, setListData] = useState<DiscoverySourceDataResponse[]>(exampleDatas);
  const [modalConfirmDelete, { close: closeModalConfirmDelete, open: openModalConfirmDelete }] = useDisclosure(false);
  const [deleteIds, setDeleteIds] = useState<number[]>([]);

  const columns: ColumnType<DiscoverySourceDataResponse>[] = useMemo(() => {
    return [
      {
        title: 'Data Source',
        name: 'name',
        customRender: (data) => {
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {data}
            </KanbanText>
          );
        },
        width: '20%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Source',
        name: 'discoverySourceName',
        customRender: (data) => {
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {data}
            </KanbanText>
          );
        },
        width: '30%',
        advancedFilter: {
          enable: false,
        },
      },
      {
        title: 'Format',
        name: 'formatType',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Staging Table ',
        name: 'discoveryStagingName',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Created by',
        name: 'createdBy',
        hidden: true,
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Created date',
        name: 'createdDate',
        customRender: renderDateTime,
        advancedFilter: {
          variant: 'date',
          customProps: {
            popoverProps: {
              withinPortal: false,
            },
          },
        },
      },
      {
        title: 'Updated by',
        name: 'modifiedBy',
        hidden: true,
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Updated date',
        name: 'modifiedDate',
        customRender: renderDateTime,
        advancedFilter: {
          variant: 'date',
          customProps: {
            popoverProps: {
              withinPortal: false,
            },
          },
        },
      },
    ];
  }, []);

  const getAllSourceDatas = useCallback(() => {
    if (!tableAffected) {
      return;
    }
    const dataSend = tableAffectedToMultiColumnFilterPaginationRequestModel<DiscoverySourceDataResponse>(
      tableAffected.sortedBy ? tableAffected : { ...tableAffected, sortedBy: 'createdDate', isReverse: true },
    );
    DiscoverySourceDataApi.getAll(dataSend)
      .then((res) => {
        if (res.data) {
          setListData(res.data?.content || []);
          setTotalRecords(res.data.totalElements);
        }
      })
      .catch(() => {});
  }, [tableAffected]);

  useEffect(() => {
    getAllSourceDatas();
  }, [getAllSourceDatas]);

  const deleteDataSources = useCallback(
    (ids: number[]) => {
      DiscoverySourceDataApi.deleteByIds(ids)
        .then(() => {
          NotificationSuccess({
            message: 'Deleted successfully',
          });
          // const newData = listData.filter((item) => !ids.includes(item.id));
          // setListData(newData);
          getAllSourceDatas();
        })
        .catch(() => {});
    },
    [getAllSourceDatas],
  );

  const autoDiscovery = useCallback((id: number) => {
    DiscoveryStagingDataApi.discoverySourceByDiscoverySourceDataId(id)
      .then(() => {
        NotificationSuccess({ message: 'Auto discovery source success' });
      })
      .catch(() => {});
  }, []);

  const tableViewListDataSourceProps: KanbanTableProps<DiscoverySourceDataResponse> = useMemo(() => {
    return {
      columns: columns,
      key: 1,
      data: listData,
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 300,
      },
      advancedFilterable: {
        enable: true,
        debounceTime: 1000,
        resetOnClose: true,
        compactMode: true,
      },
      actions: {
        customAction: isCurrentUserMatchPermissions([])
          ? (data) => (
              <>
                {/* {isCurrentUserMatchPermissions([]) && (
                  <KanbanIconButton
                    variant='transparent'
                    size='sm'
                    onClick={() => {
                      // navigate(buildViewDetailRoleUrl(data.id));
                    }}>
                    <IconHistory />
                  </KanbanIconButton>
                )} */}
                {isCurrentUserMatchPermissions([]) && (
                  <KanbanIconButton
                    variant='transparent'
                    size='sm'
                    onClick={() => {
                      autoDiscovery(data.id);
                    }}>
                    <IconDatabaseImport />
                  </KanbanIconButton>
                )}
                {isCurrentUserMatchPermissions([]) && (
                  <KanbanIconButton
                    variant='transparent'
                    size='sm'
                    onClick={() => {
                      // if (!isCurrentUserMatchPermissions([AclPermission.viewDetailRole])) {
                      //   // if has not permission view detail
                      //   NotificationError({
                      //     title: `Request error`,
                      //     message: `You don't have permissions: View detail role`,
                      //   });
                      //   return;
                      // }
                      navigate(buildSourceDataUrl(data.id, createOrUpdateDataSourcePath, SourceDataAction.UPDATE));
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                )}
                {isCurrentUserMatchPermissions([]) && (
                  <KanbanIconButton
                    key={3}
                    title='Delete'
                    color='red'
                    size='sm'
                    variant='transparent'
                    onClick={() => {
                      setDeleteIds([data.id]);
                      openModalConfirmDelete();
                    }}>
                    <IconTrash />
                  </KanbanIconButton>
                )}
              </>
            )
          : undefined,
      },
      // selectableRows: {
      //   enable: !!isCurrentUserMatchPermissions([]),
      //   onDeleted: isCurrentUserMatchPermissions([]) ? (rows) => deleteDataSources(rows.map((x) => x.id)) : undefined,
      // },
      onRowClicked: (data) => {
        if (isCurrentUserMatchPermissions([])) {
          navigate(buildSourceDataUrl(data.id, createOrUpdateDataSourcePath, SourceDataAction.VIEW));
        }
      },
      pagination: {
        enable: true,
      },
      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
    };
  }, [columns, listData, totalRecords, autoDiscovery, navigate, openModalConfirmDelete, tableAffected]);

  return (
    <>
      {/* Modal confirm delete */}
      <KanbanConfirmModal
        opened={modalConfirmDelete}
        onClose={closeModalConfirmDelete}
        title='Confirm delete'
        onConfirm={() => {
          deleteDataSources(deleteIds);
          closeModalConfirmDelete();
        }}>
        Are you sure you want to delete this item? Deleting it will also remove all configured Scheduled Jobs.
      </KanbanConfirmModal>

      {/* 4763 Datasource list*/}
      <BreadcrumbComponent />
      <HeaderTitleComponent
        title='Data Sources'
        rightSection={
          <GuardComponent requirePermissions={[AclPermission.createRole]} hiddenOnUnSatisfy>
            <KanbanButton
              onClick={() => {
                navigate(buildSourceDataUrl(0, createOrUpdateDataSourcePath, SourceDataAction.CREATE));
              }}
              leftSection={<IconPlus />}>
              Create Data Source
            </KanbanButton>
          </GuardComponent>
        }
      />
      <div style={{ flex: 2 }}>
        <KanbanTable {...tableViewListDataSourceProps} />
      </div>
    </>
  );
};
export default DataSourcePage;
