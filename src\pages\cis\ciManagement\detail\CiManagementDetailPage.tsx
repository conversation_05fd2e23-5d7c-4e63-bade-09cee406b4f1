import { ConfigItemApi } from '@api/ConfigItemApi';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanTabs, KanbanTooltip } from 'kanban-design-system';
import type { CiChangeModel, ConfigItemInfoModel, ConfigItemModel } from '@models/ConfigItem';
import React, { Fragment, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Alert, Anchor, Box, Breadcrumbs, Flex, ScrollArea, Space } from '@mantine/core';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import type { ConfigItemTypeAttrResponse } from '@api/ConfigItemTypeAttrApi';
import type { ConfigItemAttrModel } from '@models/ConfigItemAttr';
import { useDisclosure } from '@mantine/hooks';
import { KanbanButton } from 'kanban-design-system';
import GuardComponent from '@components/GuardComponent';
import { IconEdit, IconInfoSquare, IconMessage, IconTrash } from '@tabler/icons-react';
import { NotificationSuccess, NotificationWarning } from '@common/utils/NotificationUtils';
import { KanbanConfirmModal } from 'kanban-design-system';
import { buildCiManageUrl } from '@common/utils/RouterUtils';
import type { ConfigItemAttrCustomModel } from '@models/ConfigItemAttrCustom';
import { KanbanText } from 'kanban-design-system';
import { configItemTypeAttrSorted } from '@common/utils/CiUtils';
import { CiManagementExecute } from '@service/CiManagementExecute';
import { CiManagementApi, CiManagementResponse } from '@api/CiManagementApi';
import { ActionType, ScreenTypeManagement, StatusCiManagement } from '@common/constants/CiManagement';
import CiRequestDetailPopup, { CiRequestDetailPopupMethods } from '../modal/CiRequestDetailPopup';
import CiUpdate from '../../ci/CiUpdate';
import CiInfo from '../../ci/CiInfo';
import CiRelationship from '../../ci/relationship/CiRelationship';
import CiNoticeChange from './CiNoticeChange';
import CiRequestDetailPopupSpecial, { CiRequestDetailPopupSpecialMethods } from '../modal/CiRequestDetailPopupSpecial';
import { useSelector } from 'react-redux';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import type { DataCiCompare } from '@models/CiDetailCompare';
import LeftHeaderView from './LeftHeaderView';
import { CiTypeAttributeDataType } from '@models/CiType';
import { IconArrowsExchange } from '@tabler/icons-react';
import { CHANGE_STAGES } from '@common/constants/ChangeAssessmentConstants';
import { PermissionAction } from '@common/constants/PermissionAction';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';

export type CiManagementDetailPageProps = {
  initData?: CiManagementResponse;
  isView?: boolean;
};

const defaultCiInfo: ConfigItemModel = {
  id: 0,
  ciTypeId: 0,
  name: '',
};

const listStatusFinal = [StatusCiManagement.APPROVED, StatusCiManagement.REJECTED];

const getNoticeCommentByStatus = (status: StatusCiManagement, isColor: boolean) => {
  if (isColor) {
    return StatusCiManagement.APPROVED === status ? 'blue' : 'red';
  } else {
    return StatusCiManagement.APPROVED === status ? 'Approved comment' : 'Rejected comment';
  }
};

const getColorByStage = (stg?: CHANGE_STAGES) => {
  switch (stg) {
    case CHANGE_STAGES.SUBMISSION:
      return 'gray';
    case CHANGE_STAGES.PLANNING:
      return 'cyan';
    case CHANGE_STAGES.APPROVAL:
      return 'yellow';
    case CHANGE_STAGES.IMPLEMENTATION:
      return 'green';
    case CHANGE_STAGES.REVIEW:
      return 'purple';
    case CHANGE_STAGES.CLOSE:
      return 'red';
    default:
      return undefined;
  }
};

export const CiManagementDetailPage = (props: CiManagementDetailPageProps) => {
  const navigate = useNavigate();
  const { ciTempId } = useParams();

  const { initData, isView } = props;

  const [openedModalEdit, { close: closeModalEdit, open: openModalEdit }] = useDisclosure(false);
  const [openedModalDelete, { close: closeModalDelete, open: openModalDelete }] = useDisclosure(false);

  const [listCiTypeAttribute, setListCiTypeAttribute] = useState<ConfigItemTypeAttrResponse[]>([]);
  const [ciTypeAttributes, setCiTypeAttributes] = useState<ConfigItemTypeAttrResponse[]>([]);
  const [ciAttributes, setCiAttributes] = useState<ConfigItemAttrModel[]>([]);
  const [ciAttributesCustom, setCiAttributesCustom] = useState<ConfigItemAttrCustomModel[]>([]);
  const [openedModalAfterUpdate, { close: closeModalAfterUpdate, open: openModalAfterUpdate }] = useDisclosure(false);

  const [ciInfo, setCiInfo] = useState<ConfigItemModel>(defaultCiInfo);

  const [ciInfoUpdate, setCiInfoUpdate] = useState(ciInfo);
  const [ciAttributeMappingAttributeUpdate, setCiAttributeMappingAttributeUpdate] = useState<Record<number, ConfigItemAttrModel>>({});
  const [ciAttributeCustomUpdate, setCiAttributeCustomUpdate] = useState<ConfigItemAttrCustomModel[]>([]);

  const [oldDataCi, setOldDataCi] = useState<ConfigItemInfoModel | undefined>();

  const [status, setStatus] = useState<StatusCiManagement>(StatusCiManagement.DRAFT);
  const [verifyUser, setVerifyUser] = useState('');
  const [actionType, setActionType] = useState<ActionType>(ActionType.SEND);

  const [ciTypeId, setCiTypeId] = useState(0);
  const [dataView, setDataView] = useState<CiManagementResponse>();
  const [viewId, setViewId] = useState(0);
  const [isOwnerView, setIsOwnerView] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [currentCiTypeExistsAttributeReference, setCurrentCiTypeExistsAttributeReference] = useState<boolean>(false);

  const currentUser = useSelector(getCurrentUser);
  const isSuperAdmin = currentUser.data?.isSuperAdmin === true;
  const [listFlatCiChangeData, setListFlatCiChangeData] = useState<CiChangeModel[]>();
  const ciRequestDetailRef = useRef<CiRequestDetailPopupMethods | null>(null);
  const ciRequestDetailPopupSpecialRef = useRef<CiRequestDetailPopupSpecialMethods | null>(null);
  const [showMoreCiChanges, setIsShowMoreCiChanges] = useState(true);

  const handleShowMore = (val: boolean) => {
    setIsShowMoreCiChanges(val);
  };
  const fetchCiChanges = useCallback(() => {
    //case press eye btn then take ciDraft id from props , case click on row then take from url
    let draftId;
    if (props.initData?.id) {
      draftId = props.initData.id;
    } else if (ciTempId) {
      draftId = Number.parseInt(ciTempId);
    } else {
      return;
    }
    CiManagementApi.findChangeInfoByCiTempIds([draftId])
      .then((res) => {
        if (!res || !res.data) {
          return;
        }
        setListFlatCiChangeData(res.data);
        // setListFlatCiChangeData(genListCiChangeData());
      })
      .catch(() => {});
  }, [ciTempId, props.initData?.id]);

  useEffect(() => {
    fetchCiChanges();
  }, [fetchCiChanges]);

  const renderCiChangeInfos = useCallback(() => {
    if (!listFlatCiChangeData || listFlatCiChangeData.length === 0) {
      return <></>;
    }
    const maxEle = 20;
    const isTooLong = listFlatCiChangeData.length > maxEle;
    const shortList = isTooLong && showMoreCiChanges ? listFlatCiChangeData.slice(0, maxEle) : listFlatCiChangeData;
    const viewChangeAssessment = (val?: string) => {
      window.open(val, '_blank', 'noopener,noreferrer');
    };
    const renderRes = shortList.map((it) => {
      return (
        CHANGE_STAGES.CLOSE !== it.changeInfo?.stage && (
          <Box mb={'xs'} mr={'xs'} key={it.cmdbChangeDraftId}>
            <span key={it.cmdbChangeDraftId}>
              {it.sdpChangeId && (
                <KanbanTooltip label={it.changeInfo?.stage}>
                  <KanbanButton
                    bg={getColorByStage(it.changeInfo?.stage as CHANGE_STAGES)}
                    size={'compact-xs'}
                    onClick={() => viewChangeAssessment(it.sdpChangeLink)}>
                    {it.sdpChangeId}
                  </KanbanButton>
                </KanbanTooltip>
              )}
              <KanbanButton
                bg={'var(--mantine-color-gray-4)'}
                variant={'subtle'}
                size={'compact-xs'}
                onClick={() => viewChangeAssessment(it.cmdbChangeLink)}>
                {it.cmdbChangeDraftId}
              </KanbanButton>
              {/* {idx < shortList.length - 1 ? '' : ''} */}
            </span>
          </Box>
        )
      );
    });
    return (
      <ScrollArea.Autosize mah={300}>
        <Alert color='var(--mantine-color-gray-7)' mt={'20'} variant='light' title={'SDP Change ID / CMDB Change plan'} icon={<IconArrowsExchange />}>
          <Flex>
            <Box w={'80%'}>
              <Flex justify={'flex-start'} wrap={'wrap'}>
                {renderRes}
              </Flex>{' '}
              {isTooLong && (
                <KanbanButton size={'compact-xs'} variant={'subtle'} onClick={() => handleShowMore(!showMoreCiChanges)}>
                  {showMoreCiChanges ? 'Show more' : 'Show less'}
                </KanbanButton>
              )}
            </Box>
            <Box w={'20%'}>
              {/* <KanbanText fw={'bold'} color='var(--mantine-color-gray-7)'>
                Change Stages
              </KanbanText> */}
              {Object.values(CHANGE_STAGES).map((stage, index) => {
                return CHANGE_STAGES.CLOSE !== stage ? (
                  <KanbanButton key={index} bg={getColorByStage(stage as CHANGE_STAGES)} size={'compact-xs'}>
                    {stage}
                  </KanbanButton>
                ) : (
                  <></>
                );
              })}
            </Box>
          </Flex>
        </Alert>
      </ScrollArea.Autosize>
    );
  }, [listFlatCiChangeData, showMoreCiChanges]);
  const fetchCiDetail = useCallback(() => {
    CiManagementApi.getById(Number(ciTempId))
      .then((res) => {
        const resData = res.data;
        if (resData) {
          setCiTypeId(resData.ciTypeId);
          setDataView(resData);
        }
      })
      .catch(() => {
        navigate(buildCiManageUrl(ScreenTypeManagement.DRAFT));
      });
  }, [ciTempId, navigate]);

  useEffect(() => {
    if (!isView) {
      fetchCiDetail();
    } else {
      if (initData) {
        setCiTypeId(initData.ciTypeId);
        setDataView(initData);
      }
    }
  }, [fetchCiDetail, initData, isView]);

  useEffect(() => {
    setIsOwnerView(currentUser.data?.username === dataView?.owner);
  }, [currentUser, dataView]);

  const shouldDisplayEditDraftButton = useCallback(
    (data?: CiManagementResponse) => {
      if (isSuperAdmin) {
        return true;
      }
      if (!data) {
        return false;
      }
      const hasEditPermission = isCurrentUserMatchPermissions([
        AclPermission.createCiTypePermission(PermissionAction.CI_DRAFT__UPDATE, data.ciTypeId),
      ]);

      return currentUser.data?.username === data.owner && hasEditPermission;
    },
    [currentUser.data?.username, isSuperAdmin],
  );

  const shouldDisplayDeleteDraftButton = useCallback(
    (data?: CiManagementResponse) => {
      if (isSuperAdmin) {
        return true;
      }
      if (!data) {
        return false;
      }
      const hasDeletePermission = isCurrentUserMatchPermissions([
        AclPermission.createCiTypePermission(PermissionAction.CI_DRAFT__DELETE, data.ciTypeId),
      ]);

      return hasDeletePermission && currentUser.data?.username === data.owner;
    },
    [currentUser.data?.username, isSuperAdmin],
  );

  useEffect(() => {
    setIsUpdate('UPDATE' === dataView?.action);
  }, [dataView]);

  useEffect(() => {
    if (dataView && dataView.dataParse) {
      const dataCi = dataView.dataParse;
      setCiAttributes(dataCi.attributes);
      setCiAttributesCustom(dataCi.attributeCustoms);
      setStatus(dataView.status);
      setVerifyUser(dataView.verifyUser || '');
      if (dataCi.ci && dataCi.ci.id) {
        ConfigItemApi.getInfoById(dataCi.ci.id)
          .then((res) => {
            if (res.data) {
              setOldDataCi(res.data);
            }
          })
          .catch(() => {});
      }
      // update ciId after create
      if (dataCi.ci) {
        dataCi.ci.id = dataView.ciId;
      }
      setCiInfo(dataCi.ci || defaultCiInfo);
    }
  }, [dataView]);

  const fetchCiTypesAttribute = useCallback(() => {
    if (ciTypeId) {
      ConfigItemTypeApi.getAllAttributes(ciTypeId)
        .then((res) => {
          const ciTypeAttributesResponse = res.data;
          setCiTypeAttributes(ciTypeAttributesResponse);
          setListCiTypeAttribute(configItemTypeAttrSorted(ciTypeAttributesResponse));
          setCurrentCiTypeExistsAttributeReference(ciTypeAttributesResponse.some((x) => CiTypeAttributeDataType.REFERENCE === x.type));
        })
        .catch(() => {});
    }
  }, [ciTypeId]);

  useEffect(() => {
    fetchCiTypesAttribute();
  }, [fetchCiTypesAttribute]);

  const updateListCiTypeAttribute = useCallback(() => {
    if (currentCiTypeExistsAttributeReference) {
      let ciTypeAttributesUpdate = [...ciTypeAttributes];
      const listReferenceIds = new Set(
        ciTypeAttributesUpdate
          .filter((x) => CiTypeAttributeDataType.REFERENCE === x.type && !!x.ciTypeReferenceId)
          .map((item) => item.ciTypeReferenceId ?? -1),
      );

      if (listReferenceIds && listReferenceIds.size > 0) {
        ConfigItemTypeApi.getAllReferAttributeValueSuggestion(ciTypeId)
          .then((res) => {
            if (res.data && res.data.length > 0) {
              const listSuggestReferFields = res.data;

              listReferenceIds.forEach((value) => {
                const listOptions = listSuggestReferFields.filter((x) => x.ciTypeReferenceId === value);
                ciTypeAttributesUpdate = ciTypeAttributesUpdate.map((obj) => {
                  return obj.ciTypeReferenceId === value ? { ...obj, ciTypeReferenceData: listOptions } : obj;
                });
              });
              setListCiTypeAttribute(ciTypeAttributesUpdate);
            }
          })
          .catch(() => {});
      }
    }
  }, [currentCiTypeExistsAttributeReference, ciTypeAttributes, ciTypeId]);

  useEffect(() => {
    updateListCiTypeAttribute();
  }, [updateListCiTypeAttribute]);

  const differenceObject: DataCiCompare | null = useMemo(() => {
    if (listStatusFinal.includes(status) && dataView?.dataChange) {
      return JSON.parse(dataView.dataChange) as DataCiCompare;
    } else {
      if (oldDataCi && oldDataCi.ci && dataView) {
        return {
          ci: CiManagementExecute.compareObjects(oldDataCi.ci, ciInfo),
          attributes: CiManagementExecute.compareAttributes(oldDataCi.attributes, ciAttributes),
          attributesCustom: !dataView.isImport ? CiManagementExecute.compareAttributesCustom(oldDataCi.attributeCustoms, ciAttributesCustom) : {},
        };
      }
    }

    return null;
  }, [status, oldDataCi, dataView, ciInfo, ciAttributes, ciAttributesCustom]);

  const ciAttributeMapping = useMemo(() => {
    const result: Record<number, ConfigItemAttrModel> = {};

    for (const ciTypeAttribute of listCiTypeAttribute) {
      result[ciTypeAttribute.id] = ciAttributes.find((x) => x.ciTypeAttributeId === ciTypeAttribute.id) || {
        ciTypeAttributeId: ciTypeAttribute.id,
        id: 0,
        ciId: 0,
        value: '',
      };
    }

    return result;
  }, [ciAttributes, listCiTypeAttribute]);

  const actionOpenModalUpdateInfo = () => {
    setCiInfoUpdate({ ...ciInfo });
    setCiAttributeMappingAttributeUpdate({ ...ciAttributeMapping });
    setCiAttributeCustomUpdate([...ciAttributesCustom]);
    openModalEdit();
  };

  const onUpdateCi = () => {
    const data: ConfigItemInfoModel = {
      ci: ciInfoUpdate,
      attributes: Object.values(ciAttributeMappingAttributeUpdate),
      attributeCustoms: ciAttributeCustomUpdate,
      tempId: dataView?.id || 0,
    };

    ConfigItemApi.saveInfo(data)
      .then((res) => {
        NotificationSuccess({
          message: 'Updated successfully',
        });
        if (res && res.status === 200 && res.errorDescription) {
          NotificationWarning({
            message: res.errorDescription,
          });
        }

        fetchCiDetail();
        closeModalEdit();
        openModalAfterUpdate();
        const newTempId = res.data?.tempId || 0;
        if (newTempId !== Number(ciTempId)) {
          setViewId(newTempId);
        }
      })
      .catch(() => {})
      .finally(() => {});
  };

  const onDeleteCI = () => {
    CiManagementApi.deleteById(dataView?.id || 0)
      .then(() => {
        NotificationSuccess({
          message: 'Deleted successfully',
        });
        navigate(buildCiManageUrl(ScreenTypeManagement.DRAFT));
      })
      .catch(() => {})
      .finally(() => {});
  };

  const onRemoveCiAttributeCustoms = (item: ConfigItemAttrCustomModel) => {
    setCiAttributeCustomUpdate((prev) => {
      return prev.filter((x) => x !== item);
    });
  };

  const onAddCiAttributeCustoms = (item: ConfigItemAttrCustomModel) => {
    setCiAttributeCustomUpdate((prev) => {
      return [...prev, item];
    });
  };

  const isValid = useMemo(() => {
    if (!ciInfoUpdate.name) {
      return false;
    }
    for (const attr of listCiTypeAttribute) {
      if (attr.mandatory) {
        const currentAttr = ciAttributeMappingAttributeUpdate[attr.id];
        if (!currentAttr || !currentAttr.value) {
          return false;
        }
      }
    }

    return true;
  }, [listCiTypeAttribute, ciAttributeMappingAttributeUpdate, ciInfoUpdate]);

  const onOpenModalReview = (action: ActionType) => {
    setActionType(action);
    if (listStatusFinal.includes(status)) {
      ciRequestDetailPopupSpecialRef.current?.openPopupReview();
    } else {
      ciRequestDetailRef.current?.openPopupReview();
    }
  };

  const onCloseModalAfterUpdate = () => {
    NotificationSuccess({
      message: 'The CI has been modified but not approved yet. Please submit for approval at Tab CIs Management -> CIs Draft',
    });
    closeModalAfterUpdate();
  };

  const permissionApprovals = useMemo(() => {
    return StatusCiManagement.WAITING === status && currentUser.data?.username === dataView?.verifyUser;
  }, [status, dataView, currentUser]);

  // build menu link
  const handleNavigation = useCallback(
    (type: string) => {
      if ('home' === type) {
        let statusView = status.toString();
        if (StatusCiManagement.WAITING === status && isOwnerView) {
          statusView = ScreenTypeManagement.SENT;
        }
        navigate(buildCiManageUrl(statusView));
      }
    },
    [isOwnerView, navigate, status],
  );

  const listItemMenus = useMemo(() => {
    const menuItems = [
      { title: 'CIs Management', type: 'home' },
      { title: 'CI Detail', type: 'child' },
    ];

    return menuItems.map((item) => (
      <Anchor onClick={() => handleNavigation(item.type)} key={item.title} underline='hover'>
        {item.title}
      </Anchor>
    ));
  }, [handleNavigation]);

  return (
    <>
      <KanbanConfirmModal
        title={'Update request'}
        onConfirm={onUpdateCi}
        textConfirm='Update'
        onClose={closeModalEdit}
        opened={openedModalEdit}
        disabledConfirmButton={!isValid}
        modalProps={{
          size: '60%',
        }}>
        <CiUpdate
          ci={ciInfoUpdate}
          ciAttributes={ciAttributeMappingAttributeUpdate}
          ciTypeAttributes={listCiTypeAttribute}
          onChangeCi={setCiInfoUpdate}
          onChangeCiAttributes={setCiAttributeMappingAttributeUpdate}
          ciAttributesCustom={ciAttributeCustomUpdate}
          onChangeCiAttributeCustoms={setCiAttributeCustomUpdate}
          onRemoveCiAttributeCustoms={onRemoveCiAttributeCustoms}
          onAddCiAttributeCustoms={onAddCiAttributeCustoms}
          hiddenCustomAttribute={dataView?.isImport}
          includeReferenceData={true}
        />
      </KanbanConfirmModal>
      <KanbanConfirmModal title='Delete CI' onConfirm={onDeleteCI} textConfirm='Delete' onClose={closeModalDelete} opened={openedModalDelete}>
        {'Are you sure to delete this request?'}
      </KanbanConfirmModal>
      {/* modal view detail request */}
      {dataView && (
        <CiRequestDetailPopup ref={ciRequestDetailRef} listData={[dataView]} screenAction={actionType} onCloseModal={fetchCiDetail} isFromDetail />
      )}
      {/* modal view detail request */}
      <CiRequestDetailPopupSpecial
        ref={ciRequestDetailPopupSpecialRef}
        viewId={viewId}
        screenAction={actionType}
        onCloseModal={fetchCiDetail}
        isFromDetail
      />

      {/* modal notice after update CI draft*/}
      <KanbanConfirmModal
        title={'Updated successfully'}
        onConfirm={() => {
          onCloseModalAfterUpdate();
          onOpenModalReview(ActionType.SEND);
        }}
        textConfirm='OK'
        onClose={onCloseModalAfterUpdate}
        opened={openedModalAfterUpdate}
        modalProps={{
          size: 'lg',
        }}>
        {`The CI record has been modified but hasn't been approved yet. Do you want to submit this record for review?`}
      </KanbanConfirmModal>
      {!isView && <Breadcrumbs mb={20}>{listItemMenus}</Breadcrumbs>}
      <HeaderTitleComponent
        title={`CI: ${oldDataCi?.ci?.name || ciInfo?.name}`}
        rightSection={
          <>
            {!isView && (
              //TODO Permission => create new component JSX
              <GuardComponent requirePermissions={[]} hiddenOnUnSatisfy>
                <Flex>
                  {shouldDisplayEditDraftButton(dataView) && (
                    <>
                      <KanbanButton leftSection={<IconEdit />} onClick={actionOpenModalUpdateInfo}>
                        Edit
                      </KanbanButton>
                    </>
                  )}
                  {shouldDisplayDeleteDraftButton(dataView) && (
                    <>
                      {[StatusCiManagement.DRAFT, StatusCiManagement.WAITING].includes(status) && (
                        <>
                          <Space w={'sm'} />
                          <KanbanButton onClick={openModalDelete} leftSection={<IconTrash />} color='red'>
                            Delete
                          </KanbanButton>
                        </>
                      )}
                    </>
                  )}

                  {StatusCiManagement.DRAFT === status && isOwnerView && (
                    <>
                      <Space w={'sm'} />
                      <KanbanButton
                        onClick={() => {
                          onOpenModalReview(ActionType.SEND);
                        }}>
                        Send request for approval
                      </KanbanButton>
                    </>
                  )}
                  {permissionApprovals && (
                    <>
                      <Space w={'sm'} />
                      <KanbanButton
                        onClick={() => {
                          onOpenModalReview(ActionType.APPROVE);
                        }}>
                        Approvals
                      </KanbanButton>
                    </>
                  )}
                </Flex>
              </GuardComponent>
            )}
          </>
        }
        leftSection={<LeftHeaderView dataView={dataView} isOwnerView={isOwnerView} isUpdate={isUpdate} status={status} verifyUser={verifyUser} />}
      />
      {listStatusFinal.includes(status) && (
        <Alert variant='light' color={getNoticeCommentByStatus(status, true)} title={getNoticeCommentByStatus(status, false)} icon={<IconMessage />}>
          <KanbanText>{dataView?.approvalComment || ''}</KanbanText>
        </Alert>
      )}
      {isUpdate && (!oldDataCi || !oldDataCi.ci) && (
        <Alert
          my={'20'}
          variant='light'
          color='red'
          title={'Notice: Current Ci has not permission view detail or deleted.'}
          icon={<IconInfoSquare />}>
          <KanbanText ml={8} style={{ whiteSpace: 'break-spaces' }}>
            Current CI has not permission view detail or deleted, current update cannot be submitted for review/approval
          </KanbanText>
        </Alert>
      )}
      {renderCiChangeInfos()}
      {differenceObject && <CiNoticeChange differenceObject={differenceObject} listCiTypeAttribute={listCiTypeAttribute} />}
      <KanbanTabs
        configs={{
          defaultValue: '1',
        }}
        tabs={{
          '1': {
            title: 'Info',
            content: (
              <div>
                <CiInfo
                  ci={ciInfo}
                  ciTypeAttributes={listCiTypeAttribute}
                  ciAttributes={ciAttributeMapping}
                  ciAttributesCustom={ciAttributesCustom}
                  hiddenCustomAttribute={dataView?.isImport}
                  includeReferenceData={true}
                />
              </div>
            ),
          },
          '2': {
            title: 'Relationships',
            disabled: !isUpdate,
            content: <CiRelationship isView={true} ciId={ciInfo.id} ciTypeId={ciTypeId} isFromBusinessView={true} />,
          },
        }}
      />
    </>
  );
};
CiManagementDetailPage.whyDidYouRender = true;
CiManagementDetailPage.displayName = 'CiManagementDetailPage';
export default CiManagementDetailPage;
