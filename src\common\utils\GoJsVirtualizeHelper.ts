import { GoJs } from '@common/libs';
import { callBackSaveFile, GRAPH_VIEW_EXPORT_TYPE } from './GoJsHelper';
import { CiRelationshipInfoWithImpact } from '@models/CiRelationship';

export type GraphCountResult = {
  nodeCount: number;
  linkCount: number;
};

const go = GoJs;

function computeAllNodesBounds(diagram: go.Diagram): go.Rect {
  const it = diagram.nodes;
  let bounds: go.Rect | null = null;

  while (it.next()) {
    const part = it.value;
    if (!part || !part.actualBounds) {
      continue;
    }

    if (bounds === null) {
      bounds = part.actualBounds.copy();
    } else {
      bounds.unionRect(part.actualBounds);
    }
  }

  // Nếu không có node nào thì trả về Rect rỗng
  return bounds || new go.Rect(0, 0, 0, 0);
}

export const handleDownloadImageWithVitualized = (goDiagram: go.Diagram | undefined, type: GRAPH_VIEW_EXPORT_TYPE) => {
  if (!goDiagram) {
    return;
  }

  const extension = type === GRAPH_VIEW_EXPORT_TYPE.JPG ? 'jpg' : 'png';
  const bounds = computeAllNodesBounds(goDiagram);

  goDiagram.makeImageData({
    type: 'blob',
    scale: 1,
    background: 'white',
    position: bounds.position,
    size: bounds.size,
    callback: (blob) => callBackSaveFile(blob, `diagram.${extension}`),
  });
};

export const countNodesAndLinks = (relationships: CiRelationshipInfoWithImpact[]): GraphCountResult => {
  const nodeSet = new Set<number>();
  let linkCount = 0;

  for (const item of relationships ?? []) {
    if (!nodeSet.has(item.fromCi)) {
      nodeSet.add(item.fromCi);
    }
    if (item.toCi && !nodeSet.has(item.toCi)) {
      nodeSet.add(item.toCi);
    }
    linkCount++;
  }

  return {
    nodeCount: nodeSet.size,
    linkCount,
  };
};
