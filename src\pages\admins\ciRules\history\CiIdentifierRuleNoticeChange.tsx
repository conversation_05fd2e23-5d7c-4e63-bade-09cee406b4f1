import type { ConfigItemTypeAttrResponse } from '@api/ConfigItemTypeAttrApi';
import { KanbanAccordion, KanbanAccordionData, KanbanButton, KanbanText, KanbanTooltip } from 'kanban-design-system';
import { Box, Flex, Group } from '@mantine/core';
import React, { useMemo } from 'react';
import { CiTypeAttributeDataType } from '@models/CiType';
import { EntityAction } from '@models/AuditLog';
import { DataChangeLstWithActionObj, DataCiRuleCompare } from '@models/CiIdentifierRule';
import styled from './CiIdenfierRuleNoticeChange.module.scss';
import { RenderHighlightValue } from '@components/commonCi/RenderHighlightValue';

export const formatValueCiAttribute = (value: string | number | null, objAttribute: ConfigItemTypeAttrResponse | undefined): string => {
  if (objAttribute && CiTypeAttributeDataType.REFERENCE === objAttribute.type) {
    return objAttribute.ciTypeReferenceData?.find((x) => `${x.ciAttributeId}` === `${value}`)?.ciAttributeValue || 'null';
  }
  return String(value);
};

const renderActionColor = (action?: EntityAction | EntityAction.UNKNOWN): string => {
  const defaultValue = 'gray';
  if (!action) {
    return defaultValue;
  }
  switch (action) {
    case EntityAction.CREATE:
      return 'green';
    case EntityAction.UPDATE:
      return 'orange';
    case EntityAction.DELETE:
      return 'red';
    default:
      return defaultValue;
  }
};
const RuleInfoTitle: React.FC<DataChangeLstWithActionObj> = (entry) => {
  return (
    <Flex gap='xs' align={'flex-start'}>
      <KanbanTooltip label={`${entry.titleAction}`}>
        <KanbanButton
          size='compact-xs'
          variant={'subtle'}
          c={'white'}
          fw={'600'}
          bg={renderActionColor(entry.action)}
          onClick={() => {
            // navigate(buildImpactedRuleDetailUrl(0, RuleAction.ADD));
          }}>
          {/* {entry.titleAction} */}
          <RenderHighlightValue text={entry.titleAction} />
        </KanbanButton>
      </KanbanTooltip>
    </Flex>
  );
};

const RuleChangeAccordion: React.FC<DataChangeLstWithActionObj> = (ruleDataChange) => {
  const dataAcc = useMemo((): KanbanAccordionData => {
    const res: KanbanAccordionData = {
      title: <RuleInfoTitle {...ruleDataChange} />,
      className: styled.accordion_content,
      content: (
        <Flex direction={'column'}>
          {ruleDataChange.dataChange &&
            ruleDataChange.dataChange.length > 0 &&
            ruleDataChange.dataChange.map((item) => (
              <Group key={item.key}>
                {/* -{' '} */}
                <KanbanText fw={700}>
                  <RenderHighlightValue text={item.key} />
                </KanbanText>
                :{' '}
                {!(EntityAction.CREATE === ruleDataChange.action) && (
                  <KanbanText>
                    <RenderHighlightValue text={item.oldValue} />
                  </KanbanText>
                )}
                {/* {item.newValue && (
                  <> */}
                {EntityAction.UPDATE === ruleDataChange.action && ` -> `}
                <KanbanText>
                  <RenderHighlightValue text={String(item.newValue)} />
                </KanbanText>
                {/* </>
                )} */}
                <br />
              </Group>
            ))}
        </Flex>
      ),
    };

    return { ...res, key: 'ruleChange' };
  }, [ruleDataChange]);
  return (
    ruleDataChange.dataChange &&
    ruleDataChange.dataChange.length > 0 && (
      <>
        <KanbanText fw='500'>Rule information</KanbanText>
        <KanbanAccordion
          mb={'xs'}
          className={styled.accordion}
          chevronPosition='left'
          chevronSize={'xs'}
          //control when init, which is opened
          defaultValue={['ruleChange']}
          data={[dataAcc]}
        />
      </>
    )
  );
};

const RuleEntriesChangeAccordion = (entry: DataChangeLstWithActionObj) => {
  const dataAcc = useMemo((): KanbanAccordionData => {
    const res: KanbanAccordionData = {
      title: <RuleInfoTitle {...entry} />,
      className: styled.accordion_content,
      content: (
        <Box>
          {entry.dataChange &&
            entry.dataChange.map((item) => {
              return (
                <Group key={item.key}>
                  <KanbanText fw={700}>
                    <RenderHighlightValue text={item.key} />
                  </KanbanText>
                  :{' '}
                  {!(EntityAction.CREATE === entry.action) && (
                    <KanbanText className={styled.accordion_content_text}>{<RenderHighlightValue text={item.oldValue} />}</KanbanText>
                  )}
                  {EntityAction.UPDATE === entry.action && ` -> `}
                  {!(EntityAction.DELETE === entry.action) && (
                    <>
                      {item.newValue && (
                        <KanbanText className={styled.accordion_content_text}>
                          <RenderHighlightValue text={String(item.newValue)} />
                        </KanbanText>
                      )}
                    </>
                  )}
                  <br />
                </Group>
              );
            })}
        </Box>
      ),
    };

    return { ...res, key: 'ruleEntryChange' };
  }, [entry]);

  return (
    entry.dataChange && <KanbanAccordion className={styled.accordion} chevronPosition='left' defaultValue={[`ruleEntryChange`]} data={[dataAcc]} />
  );
};

export const CiIdentifierRuleNoticeChange = ({ dataChange }: { dataChange: string }) => {
  const parsedData: DataCiRuleCompare = useMemo(() => {
    try {
      return JSON.parse(dataChange);
    } catch (e) {
      return {};
    }
  }, [dataChange]);
  const ciIdentifierRule = parsedData.ciIdentifierRule;
  const ciIdentifierRuleEntry = parsedData.ciIdentifierRuleEntry;
  const ruleDataChange = useMemo(() => {
    if (!ciIdentifierRule) {
      return {};
    }
    const result: DataChangeLstWithActionObj = {
      action: ciIdentifierRule.action,
      oldInfo: ciIdentifierRule.oldInfo,
      newInfo: ciIdentifierRule.newInfo,
      titleAction: ciIdentifierRule.titleAction,
    };
    result.dataChange = [];

    const data = { ...ciIdentifierRule };

    //rule change chi co 1 accordion nen chi can check co du lieu hightlight 1 lan de expand
    for (const key in data.mapSelfChange) {
      const valueStr = String(data.mapSelfChange[key].oldValue);
      const newValue = String(data.mapSelfChange[key].newValue);

      result.dataChange.push({
        key: key,
        oldValue: valueStr,
        newValue: newValue,
      });
    }

    return result;
  }, [ciIdentifierRule]);
  //
  const entriesDataChange = useMemo(() => {
    const mapEntryIdWithData: { [key: string]: DataChangeLstWithActionObj } = {};
    const allEntryData = { ...ciIdentifierRuleEntry };

    if (!allEntryData?.mapChildren) {
      return mapEntryIdWithData;
    }
    // Iterate through "ci" object
    for (const entryId in allEntryData.mapChildren) {
      const oneEntry = { ...allEntryData.mapChildren[entryId] };
      mapEntryIdWithData[entryId] = {
        action: oneEntry.action,
        oldInfo: oneEntry.oldInfo,
        newInfo: oneEntry.newInfo,
        titleAction: oneEntry.titleAction,
      };
      mapEntryIdWithData[entryId].dataChange = [];

      for (const entryFieldName in oneEntry.mapSelfChange) {
        const oneEntryField = oneEntry.mapSelfChange[entryFieldName];
        const oldValue = String(oneEntryField.oldValue);
        const newValue = String(oneEntryField.newValue);

        mapEntryIdWithData[entryId].dataChange.push({
          key: entryFieldName,
          oldValue: oldValue,
          newValue: newValue,
        });
      }
    }

    return mapEntryIdWithData;
  }, [ciIdentifierRuleEntry]);

  return (
    <>
      <RuleChangeAccordion {...ruleDataChange} />
      {entriesDataChange && Object.keys(entriesDataChange).length > 0 && (
        <Flex direction={'column'} align={'flex-start'}>
          <KanbanText fw='500'>Rule entries</KanbanText>

          {Object.keys(entriesDataChange).map((entryId) => {
            const entry = entriesDataChange[entryId];
            return <RuleEntriesChangeAccordion {...entry} key={entryId} />;
          })}
        </Flex>
      )}
    </>
  );
};

export default CiIdentifierRuleNoticeChange;
