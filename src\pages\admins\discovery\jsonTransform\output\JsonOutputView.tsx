import React from 'react';
import { allExpanded, darkStyles, JsonView } from 'react-json-view-lite';
import 'react-json-view-lite/dist/index.css';
import { JSONNode } from '../helper/JsonTransformHelper';
import './JsonOutputView.scss';

export type JsonOutputViewProps = {
  jsonTransform: JSONNode[];
};

const JsonOutputView: React.FC<JsonOutputViewProps> = ({ jsonTransform }) => {
  return (
    <div>
      <JsonView data={jsonTransform} shouldExpandNode={allExpanded} style={darkStyles} />
    </div>
  );
};

export default JsonOutputView;
