import type { ApiResponseDataBase } from '@core/api/ApiResponse';
import { BaseUrl } from '@core/api/BaseUrl';
import { BaseApi } from 'core/api/BaseApi';

export type SqlExecutionRequest = {
  password: string;
  sqlQuery: string;
};

export type SqlExecutionModel = {
  isNonQuery: boolean;
  listColumns: string[];
  listDataMappings: {
    listSqlMappingColumnDatas: {
      column: string;
      value: string;
    }[];
  }[];
};

export type SqlExecutionResponse = ApiResponseDataBase & SqlExecutionModel;

export class SuperiorsApi extends BaseApi {
  static baseUrl = BaseUrl.superiors;

  static sqlExection(request: SqlExecutionRequest) {
    return BaseApi.postData<SqlExecutionModel>(`${this.baseUrl}/sql-execution`, request);
  }
}
