import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export class VropsApi extends BaseApi {
  static baseUrl = BaseUrl.vrops;

  static findAllClusters() {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/clusters`);
  }

  static findAllResources() {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/resources`);
  }
}
