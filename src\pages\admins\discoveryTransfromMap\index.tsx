import { DiscoveryTransformMapApi } from '@api/DiscoveryTransformMapApi';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { buildCreateOrUpdateDiscoveryTransfromMapUrl } from '@common/utils/RouterUtils';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { DiscoveryTransformMapModel } from '@models/DiscoveryTransformMap';
import { IconDatabaseExclamation, IconEdit, IconPlus } from '@tabler/icons-react';
import equal from 'fast-deep-equal';
import {
  ColumnType,
  KanbanButton,
  KanbanIconButton,
  KanbanTable,
  KanbanTableProps,
  renderDateTime,
  TableAffactedSafeType,
} from 'kanban-design-system';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { BreadcrumbComponent } from '../breadcrumb/BreadcrumbComponent';
import { MAX_TEXT_LENGTH } from '@common/constants/FieldLengthConstants';

const columns: ColumnType<DiscoveryTransformMapModel>[] = [
  {
    title: 'Name',
    name: 'name',
    width: '10%',
    advancedFilter: {
      variant: 'text',
      customProps: { maxLength: MAX_TEXT_LENGTH },
    },
  },
  {
    title: 'Source (Staging table)',
    name: 'stagingTableName',
    advancedFilter: {
      variant: 'text',
      customProps: { maxLength: MAX_TEXT_LENGTH },
    },
  },
  {
    title: 'Target (CI Type)',
    name: 'ciTypeName',
    advancedFilter: {
      variant: 'text',
      customProps: { maxLength: MAX_TEXT_LENGTH },
    },
  },
  {
    name: 'createdBy',
    title: 'Created by',
    advancedFilter: {
      variant: 'text',
      customProps: { maxLength: MAX_TEXT_LENGTH },
    },
  },
  {
    name: 'createdDate',
    title: 'Created date',
    customRender: renderDateTime,
    advancedFilter: {
      variant: 'date',
      customProps: {
        popoverProps: {
          withinPortal: false,
        },
      },
    },
  },
  {
    name: 'modifiedBy',
    title: 'Updated by',
    advancedFilter: {
      variant: 'text',
      customProps: { maxLength: MAX_TEXT_LENGTH },
    },
  },
  {
    name: 'modifiedDate',
    title: 'Updated date',
    customRender: renderDateTime,
    advancedFilter: {
      variant: 'date',
      customProps: {
        popoverProps: {
          withinPortal: false,
        },
      },
    },
  },
  {
    name: 'runBy',
    title: 'Run by',
    advancedFilter: {
      variant: 'text',
      customProps: { maxLength: MAX_TEXT_LENGTH },
    },
  },
  {
    name: 'runDate',
    title: 'Run date',
    advancedFilter: {
      variant: 'date',
      customProps: {
        popoverProps: {
          withinPortal: false,
        },
      },
    },
    customRender: (data: Date) => {
      if (data) {
        return renderDateTime(data);
      }
    },
  },
];

export const DiscoveryTransfromMap = () => {
  const navigate = useNavigate();
  const [totalRecords, setTotalRecords] = useState(0);
  const [listData, setListData] = useState<DiscoveryTransformMapModel[]>([]);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);

  const fetchAllTransformMap = useCallback(() => {
    if (!tableAffected) {
      return;
    }
    const dataSend = tableAffectedToMultiColumnFilterPaginationRequestModel<DiscoveryTransformMapModel>(
      tableAffected.sortedBy ? tableAffected : { ...tableAffected, sortedBy: 'createdDate', isReverse: true },
    );

    DiscoveryTransformMapApi.getAll(dataSend)
      .then((res) => {
        if (res.data) {
          setListData(res.data?.content || []);
          setTotalRecords(res.data.totalElements);
        }
      })
      .catch(() => {});
  }, [tableAffected]);

  const deleteById = useCallback(
    (id: number) => {
      DiscoveryTransformMapApi.deleteById(id)
        .then(() => {
          NotificationSuccess({
            message: 'Deleted successfully',
          });
          fetchAllTransformMap();
        })
        .catch(() => {});
    },
    [fetchAllTransformMap],
  );

  useEffect(() => {
    fetchAllTransformMap();
  }, [fetchAllTransformMap]);

  /**
   * run trigger job
   */
  const runJob = useCallback(
    (transFormMapId: number) => {
      DiscoveryTransformMapApi.runJob(transFormMapId)
        .then((res) => {
          if (res.data) {
            fetchAllTransformMap();
            NotificationSuccess({ title: 'Success', message: 'Run job successfully' });
          }
        })
        .catch(() => {});
    },
    [fetchAllTransformMap],
  );

  const tableViewProps: KanbanTableProps<DiscoveryTransformMapModel> = useMemo(() => {
    return {
      columns: columns,
      key: 1,
      data: listData,
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 300,
      },
      advancedFilterable: {
        enable: true,
        debounceTime: 1000,
        resetOnClose: true,
        compactMode: true,
      },
      sortable: {
        enable: true,
      },
      actions: {
        deletable: {
          onDeleted: (data) => deleteById(data.id || 0),
        },
        customAction: (item: DiscoveryTransformMapModel) => (
          <>
            <KanbanIconButton
              variant='transparent'
              size='sm'
              onClick={() => {
                if (item.id) {
                  runJob(item.id);
                }
              }}>
              <IconDatabaseExclamation />
            </KanbanIconButton>
            <KanbanIconButton
              variant='transparent'
              size='sm'
              onClick={() => {
                navigate(buildCreateOrUpdateDiscoveryTransfromMapUrl(item.id || 0));
              }}>
              <IconEdit />
            </KanbanIconButton>
          </>
        ),
      },

      selectableRows: {
        enable: false,
      },
      onRowClicked: (data) => {
        navigate(buildCreateOrUpdateDiscoveryTransfromMapUrl(data.id || 0));
      },
      pagination: {
        enable: true,
      },

      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
    };
  }, [listData, totalRecords, deleteById, runJob, navigate, tableAffected]);

  return (
    <>
      <BreadcrumbComponent />
      <HeaderTitleComponent
        title='Transform Maps'
        rightSection={
          <KanbanButton onClick={() => navigate(buildCreateOrUpdateDiscoveryTransfromMapUrl(0))} leftSection={<IconPlus />}>
            Add New
          </KanbanButton>
        }
      />
      <KanbanTable {...tableViewProps} />
    </>
  );
};
