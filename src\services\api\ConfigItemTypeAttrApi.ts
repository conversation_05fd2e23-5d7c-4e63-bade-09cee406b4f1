import type { ApiResponseDataBase } from '@core/api/ApiResponse';
import { BaseUrl } from '@core/api/BaseUrl';
import type { CiTypeAttributeReferInfoModel, ConfigItemTypeAttrModel } from '@models/ConfigItemTypeAttr';
import { BaseApi } from 'core/api/BaseApi';

export type ConfigItemTypeAttrResponse = ApiResponseDataBase & ConfigItemTypeAttrModel;

export class ConfigItemTypeAttrApi extends BaseApi {
  static baseUrl = BaseUrl.ciTypeAttrs;

  static getSuggestCiTypeAttribute(ciTypeId?: number) {
    return BaseApi.getData<ConfigItemTypeAttrResponse[]>(`${this.baseUrl}/suggestion`, { ciTypeId });
  }
  static findAllByCiTypeReferenceIdIn(ciTypeReferenceIds: number[]) {
    return BaseApi.getData<CiTypeAttributeReferInfoModel[]>(`${this.baseUrl}`, { ciTypeReferenceIds });
  }
}
