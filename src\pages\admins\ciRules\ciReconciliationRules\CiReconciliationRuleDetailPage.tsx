import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton, KanbanConfirmModal, KanbanInput, KanbanSelect, KanbanTabs, KanbanText, KanbanTextarea } from 'kanban-design-system';
import { IconEdit } from '@tabler/icons-react';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import { Box, ComboboxItem, Flex, Group } from '@mantine/core';
import CiReconciliationEntryTableComponent from './CiReconciliationEntryTableComponent';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import {
  CiReconciliationEntryDto,
  CiReconciliationRuleAction,
  CiReconciliationRuleDto,
  DATA_SOURCE_MANUAL,
  RuleStatus,
} from '@models/CiReconciliationRule';
import { buildCiReconciliationRuleDetailUrl, buildCiReconciliationRuleUrl, navigateTo } from '@common/utils/RouterUtils';
import { CiReconciliationRuleApi } from '@api/CiReconciliationRuleApi';
import { NotificationError, NotificationSuccess } from '@common/utils/NotificationUtils';
import { v4 as uuid } from 'uuid';
import { useDisclosure } from '@mantine/hooks';
import { formatStandardName } from '@common/utils/StringUtils';
import { CiTypeAttributeDataType } from '@models/CiType';
import { DEFAULT_ID_FOR_CI_NAME } from '@common/constants/CommonConstants';
import { CiTypeAttributeDto } from '../DnDAttributesComponent';
import { BreadcrumbComponent, UrlBaseCrumbData } from '@pages/admins/breadcrumb/BreadcrumbComponent';
import CiReconciliationAttributeTableComponent from './CiReconciliationAttributeTableComponent';
import { DiscoverySourceDataApi } from '@api/discovery/DiscoverySourceDataApi';
import { DiscoverySourceDataModel } from '@models/discovery/DiscoverySourceData';
import { FormatTypeEnum } from '@common/constants/FormatTypeEnum';

export const listStatus: ComboboxItem[] = [
  {
    value: RuleStatus.ENABLE,
    label: 'Enable',
  },
  {
    value: RuleStatus.DISABLE,
    label: 'Disable',
  },
];

const defaultRuleInfo: CiReconciliationRuleDto = {
  id: 0,
  name: '',
  active: RuleStatus.ENABLE,
  ciTypeId: 0,
  skipDuplicate: true,
  applyToChild: false,
};

type CiReconciliationRuleError = {
  name?: string;
  applyCiType?: string;
};

const validateName = (value: string | undefined) => {
  if (!value || value.trim().length === 0) {
    return 'Name cannot be empty';
  } else if (value.length > 255) {
    return 'Name must be less than 255 characters';
  }
  return undefined; // No error
};

const listExcludeType: CiTypeAttributeDataType[] = [
  CiTypeAttributeDataType.DATE,
  CiTypeAttributeDataType.PICK_LIST,
  CiTypeAttributeDataType.REFERENCE,
];

export const CiReconciliationRuleDetailPage = () => {
  const { ruleId } = useParams();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const action = searchParams.get('action') || CiReconciliationRuleAction.VIEW;

  const ruleIdNumber = Number(ruleId);

  const [allowEdit, setAllowEdit] = useState(false);
  const [ciTypeId, setCiTypeId] = useState(0);
  const [listAttributes, setListAttributes] = useState<CiTypeAttributeDto[]>([]);
  const [ruleInfo, setRuleInfo] = useState<CiReconciliationRuleDto>(defaultRuleInfo);
  const [listDataEntry, setListDataEntry] = useState<CiReconciliationEntryDto[]>([]);
  const [listDataEntryUpdate, setListDataEntryUpdate] = useState<CiReconciliationEntryDto[]>([]);
  const [listDataEntryDelete, setListDataEntryDelete] = useState<CiReconciliationEntryDto[]>([]);
  const [errorMessage, setErrorMessage] = useState<CiReconciliationRuleError>();
  const [openedModalConfirmChange, { close: closeModalConfirmChange, open: openModalConfirmChange }] = useDisclosure(false);
  const [discoverySourceData, setDiscoverySourceData] = useState<DiscoverySourceDataModel[]>([]);

  useEffect(() => {
    setAllowEdit(CiReconciliationRuleAction.VIEW !== action);
  }, [action]);

  const ciTypes = useGetCiTypes();

  const listCiTypeCombobox: ComboboxItem[] = useMemo(() => {
    const ciTypeData = ciTypes.data || [];
    const sortedCiTypeData = [...ciTypeData];

    sortedCiTypeData.sort((a, b) => a.name.localeCompare(b.name));

    return sortedCiTypeData.map((item) => {
      return {
        value: `${item.id}`,
        label: item.name,
      };
    });
  }, [ciTypes]);

  const fetchCiTypesAttribute = useCallback(() => {
    if (ciTypeId > 0) {
      ConfigItemTypeApi.getAllAttributes(
        ciTypeId,
        {
          useLoading: false,
        },
        true,
      )
        .then((res) => {
          if (res && res.data) {
            const attributes: CiTypeAttributeDto[] = res.data
              .filter((x) => x.type && !listExcludeType.includes(x.type))
              .map((item) => ({
                attributeId: item.id,
                nameAttribute: item.name,
                deleted: item.deleted,
              }));
            const defaultAttribute: CiTypeAttributeDto = { attributeId: DEFAULT_ID_FOR_CI_NAME, nameAttribute: 'CI Name' };
            setListAttributes([defaultAttribute, ...attributes]);
          }
        })
        .catch(() => {});
    }
  }, [ciTypeId]);

  useEffect(() => {
    fetchCiTypesAttribute();
  }, [fetchCiTypesAttribute]);

  useEffect(() => {
    if (ruleInfo && ruleInfo.ciTypeId > 0) {
      setCiTypeId(ruleInfo.ciTypeId);
    }
  }, [ruleInfo]);

  const findCiReconciliationRuleById = useCallback(() => {
    if (ruleIdNumber > 0) {
      CiReconciliationRuleApi.findCiReconciliationRuleById(ruleIdNumber)
        .then((res) => {
          if (res && res.data) {
            const { listEntries, ...ruleDetail } = res.data;
            const dataEntries: CiReconciliationEntryDto[] = (listEntries || []).map((item) => ({
              ...item,
              tempId: uuid(),
            }));
            setRuleInfo({ ...ruleDetail, listEntries: dataEntries });
            setListDataEntry(dataEntries);
          }
        })
        .catch(() => {});
    }
  }, [ruleIdNumber]);

  useEffect(() => {
    findCiReconciliationRuleById();
  }, [findCiReconciliationRuleById]);

  const getAllSourceDatas = useCallback(() => {
    DiscoverySourceDataApi.getAllDataSource(true)
      .then((res) => {
        if (res.data) {
          const datas = res.data || [];
          const manualData = DATA_SOURCE_MANUAL;
          const enhancedDatas: DiscoverySourceDataModel[] = [
            {
              id: Number(manualData.id),
              name: String(manualData.name),
              formatType: FormatTypeEnum.JSON,
              sourceId: 0,
              discoveryStagingId: 0,
              deleted: false,
            },
            ...datas,
          ];
          setDiscoverySourceData(enhancedDatas);
        }
      })
      .catch(() => {});
  }, []);

  useEffect(() => {
    getAllSourceDatas();
  }, [getAllSourceDatas]);

  const onSaveData = () => {
    const errorName = validateName(ruleInfo.name);
    const errorCiType = !ruleInfo.ciTypeId ? 'Field is required.' : undefined;
    setErrorMessage({ name: errorName, applyCiType: errorCiType });
    if (errorName || errorCiType) {
      return;
    }
    const listEntriesCreate = listDataEntry.filter((x) => x.id === 0);
    const updatedData: CiReconciliationRuleDto = {
      ...ruleInfo,
      listEntriesCreate: listEntriesCreate,
      listEntriesUpdate: listDataEntryUpdate,
      listEntriesDelete: listDataEntryDelete,
    };

    if (ruleIdNumber > 0) {
      CiReconciliationRuleApi.updateRule(updatedData, ruleIdNumber)
        .then((res) => {
          if (res.data) {
            NotificationSuccess({
              message: 'Updated successfully.',
            });
            navigateTo(buildCiReconciliationRuleUrl());
          } else {
            NotificationError({
              message: 'Error when update rule.',
            });
          }
        })
        .catch(() => {});
    } else {
      CiReconciliationRuleApi.createNew(updatedData)
        .then((res) => {
          if (res.data) {
            NotificationSuccess({
              message: 'Created successfully.',
            });
            navigateTo(buildCiReconciliationRuleUrl());
          } else {
            NotificationError({
              message: 'Error when create new rule.',
            });
          }
        })
        .catch(() => {});
    }

    return;
  };

  const onChangeCiType = (val: string | null) => {
    const result = { ...ruleInfo, ciTypeId: Number(val) };
    setRuleInfo(result);
    setErrorMessage({ ...errorMessage, applyCiType: undefined });

    if (ruleIdNumber === 0 && listDataEntry.length > 0) {
      openModalConfirmChange();
    }
  };

  const actionChangeCiType = () => {
    closeModalConfirmChange();
    setListDataEntry([]);
    setListDataEntryUpdate([]);
    setListDataEntryDelete([]);
  };

  const locationCustomPaths = useMemo((): UrlBaseCrumbData => {
    const originPath = buildCiReconciliationRuleDetailUrl(Number(ruleId), CiReconciliationRuleAction.VIEW);
    let detailBread = '';
    if (Number(ruleId)) {
      detailBread = `${formatStandardName(action)} ${ruleInfo.name}`;
    } else if (CiReconciliationRuleAction.CREATE === action) {
      detailBread = `${formatStandardName(action)}`;
    }

    return {
      [`/${ruleId}`]: {
        title: detailBread,
        href: originPath,
      },
    };
  }, [action, ruleId, ruleInfo.name]);

  return (
    <>
      {/* 4736 ci rule detail  */}
      <BreadcrumbComponent locationCustomPaths={locationCustomPaths} />
      <KanbanConfirmModal
        title={'Confirm change Ci Type'}
        onConfirm={actionChangeCiType}
        textConfirm={'OK'}
        onClose={closeModalConfirmChange}
        opened={openedModalConfirmChange}
        modalProps={{
          size: 'sm',
        }}>
        <KanbanText>When changing CI Type, the entry list will be cleared. Are you sure?</KanbanText>
      </KanbanConfirmModal>

      <KanbanTabs
        configs={{
          variant: 'outline',
          defaultValue: 'INFO',
        }}
        tabs={{
          INFO: {
            title: 'Reconcile Rule',
            content: (
              <>
                <HeaderTitleComponent
                  title={''}
                  rightSection={
                    <Flex gap={10}>
                      <KanbanButton
                        variant='outline'
                        onClick={() => {
                          navigateTo(buildCiReconciliationRuleUrl());
                        }}>
                        Cancel
                      </KanbanButton>
                      {!allowEdit && (
                        <>
                          <KanbanButton
                            leftSection={<IconEdit />}
                            onClick={() => {
                              navigate(buildCiReconciliationRuleDetailUrl(ruleIdNumber, CiReconciliationRuleAction.UPDATE));
                            }}>
                            Edit
                          </KanbanButton>
                        </>
                      )}
                      {allowEdit && (
                        <KanbanButton
                          onClick={() => {
                            onSaveData();
                          }}>
                          Save
                        </KanbanButton>
                      )}
                    </Flex>
                  }
                />

                <Box>
                  <KanbanInput
                    label='Name'
                    value={ruleInfo?.name || ''}
                    withAsterisk
                    disabled
                    maxLength={255}
                    error={errorMessage?.name}
                    onChange={(el) => {
                      const value = el.target.value ?? '';
                      const result = { ...ruleInfo, name: formatStandardName(value) };
                      setRuleInfo(result);
                      setErrorMessage({ ...errorMessage, name: undefined });
                    }}
                  />
                  <Group grow>
                    <KanbanSelect
                      label='Apply CI Type'
                      placeholder='Select CI Type'
                      searchable
                      allowDeselect={false}
                      withAsterisk
                      disabled
                      autoChangeValueByOptions={false}
                      error={errorMessage?.applyCiType}
                      data={listCiTypeCombobox}
                      value={`${ruleInfo.ciTypeId}`}
                      onChange={(x) => {
                        onChangeCiType(x);
                      }}
                    />
                  </Group>
                  <KanbanTextarea
                    label='Description'
                    value={ruleInfo?.description || ''}
                    autosize
                    minRows={2}
                    maxRows={5}
                    maxLength={2000}
                    disabled={!allowEdit}
                    onChange={(el) => {
                      const { target } = el;
                      const result = { ...ruleInfo, description: target.value };
                      setRuleInfo(result);
                    }}
                  />
                </Box>
                <KanbanTabs
                  configs={{
                    variant: 'outline',
                    defaultValue: 'VIEW_ENTRIES',
                  }}
                  tabs={{
                    VIEW_ENTRIES: {
                      title: 'View entries',
                      content: (
                        <>
                          <Box mt={20}>
                            <CiReconciliationEntryTableComponent
                              listEntry={listDataEntry}
                              setListEntry={setListDataEntry}
                              listEntryUpdate={listDataEntryUpdate}
                              setListEntryUpdate={setListDataEntryUpdate}
                              listEntryDelete={listDataEntryDelete}
                              setListEntryDelete={setListDataEntryDelete}
                              allowEdit={allowEdit}
                              listAttributes={listAttributes}
                              discoverySourceData={discoverySourceData}
                            />
                          </Box>
                        </>
                      ),
                    },
                    VIEW_ATTRIBUTES: {
                      title: 'View attributes',
                      content: (
                        <>
                          <Box mt={20}>
                            <CiReconciliationAttributeTableComponent
                              listEntry={listDataEntry}
                              allowEdit={allowEdit}
                              listAttributes={listAttributes}
                              discoverySourceData={discoverySourceData}
                            />
                          </Box>
                        </>
                      ),
                    },
                  }}></KanbanTabs>
              </>
            ),
          },
          // #5056: comment
          // HISTORY: {
          //   title: 'Config History',
          //   content: <AuditLogPage entityId={ruleIdNumber} auditLogType={EntityAuditLogTypeEnum.CI_RECONCILIATION_RULE} />,
          // },
          // LOG: {
          //   title: 'Rule Reconciliation Log',
          //   content: (
          //     <>
          //       <CiReconciliationRuleLogPage></CiReconciliationRuleLogPage>
          //     </>
          //   ),
          // },
        }}
      />
    </>
  );
};
CiReconciliationRuleDetailPage.whyDidYouRender = true;
CiReconciliationRuleDetailPage.displayName = 'CiReconciliationRuleDetailPage';
export default CiReconciliationRuleDetailPage;
