import { SolarWindsSourceEnum } from '@common/constants/SolarWindsSourceEnum';
import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export class SolarwindApi extends BaseApi {
  static baseUrl = BaseUrl.solarwinds;

  static findAllByApi(sourceType: SolarWindsSourceEnum) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}`, { sourceType: sourceType });
  }

  static findAllByDb() {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/databases`);
  }
}
