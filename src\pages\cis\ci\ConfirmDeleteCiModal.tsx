import { ServiceMappingApi } from '@api/ServiceMappingApi';
import { Alert } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IconAlertTriangle } from '@tabler/icons-react';
import { KanbanConfirmModal, KanbanText } from 'kanban-design-system';
import React, { useCallback, useState } from 'react';
import { forwardRef, useImperativeHandle } from 'react';

type ConfirmDeleteCiModalProps = {
  onConfirmDelete: () => void;
};

export type ConfirmDeleteCiModalMethods = {
  openConfirmDelete: (ciId: number) => void;
};

export const ConfirmDeleteCiModal = forwardRef<ConfirmDeleteCiModalMethods, ConfirmDeleteCiModalProps>((props, ref) => {
  const [serviceNames, setServiceNames] = useState('');
  const [openedModalDelete, { close: closeModalDelete, open: openModalDelete }] = useDisclosure(false);

  const fetchServiceInfoByStartCiId = useCallback(
    (ciId: number) => {
      if (ciId > 0) {
        ServiceMappingApi.getAllByStartCiId(ciId)
          .then((res) => {
            if (res.data && res.data.length > 0) {
              const serviceNames = res.data.slice(0, 3).join(', ');
              const displayServiceName = res.data.length > 3 ? `${serviceNames}, ..` : serviceNames;
              setServiceNames(displayServiceName);
            } else {
              setServiceNames('');
            }
            openModalDelete();
          })
          .catch(() => {})
          .finally(() => {});
      }
    },
    [openModalDelete],
  );

  useImperativeHandle<any, ConfirmDeleteCiModalMethods>(
    ref,
    () => ({
      openConfirmDelete: (ciId: number) => {
        fetchServiceInfoByStartCiId(ciId);
      },
    }),
    [fetchServiceInfoByStartCiId],
  );

  const onActionConfirm = () => {
    props.onConfirmDelete();
    closeModalDelete();
  };

  return (
    <>
      <KanbanConfirmModal title='Delete CI' onConfirm={onActionConfirm} textConfirm='Delete' onClose={closeModalDelete} opened={openedModalDelete}>
        {serviceNames ? (
          <Alert variant='light' color='yellow' title={''} icon={<IconAlertTriangle />}>
            <KanbanText ml={8}>
              This CI is in use for service(s): {serviceNames}. <br />
              Are you sure to delete this CI?
            </KanbanText>
          </Alert>
        ) : (
          <KanbanText>Are you sure to delete this CI?</KanbanText>
        )}
      </KanbanConfirmModal>
    </>
  );
});
ConfirmDeleteCiModal.displayName = 'ConfirmDeleteCiModal';
export default ConfirmDeleteCiModal;
