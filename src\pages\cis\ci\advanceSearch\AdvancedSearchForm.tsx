import React from 'react';
import { KanbanInput } from 'kanban-design-system';
import { KanbanTextarea } from 'kanban-design-system';
import type { CiAdvancedSearchDTO } from '@models/CiAdvancedSearch';
import { formatStandardName } from '@common/utils/StringUtils';

type AdvancedSearchFormProps = {
  ciAdvancedSearchInfo: CiAdvancedSearchDTO;
  isDetailView?: boolean;
  updateAdvancedSearchFormData: (newFormData: CiAdvancedSearchDTO) => void;
};

export const AdvancedSearchForm = (props: AdvancedSearchFormProps) => {
  const { ciAdvancedSearchInfo, isDetailView, updateAdvancedSearchFormData } = props;
  return (
    <>
      <KanbanInput
        maxLength={100}
        disabled={isDetailView}
        withAsterisk
        label='Name'
        value={ciAdvancedSearchInfo.name || ''}
        onChange={(e) => {
          const value = e.target.value;
          const data = { ...ciAdvancedSearchInfo, name: value };
          updateAdvancedSearchFormData(data);
        }}
        onBlur={(e) => {
          const value = e.target.value;
          const data = { ...ciAdvancedSearchInfo, name: formatStandardName(value) };
          updateAdvancedSearchFormData(data);
        }}
      />

      <KanbanTextarea
        maxLength={2000}
        maxRows={15}
        autosize
        disabled={isDetailView}
        label='Description'
        value={ciAdvancedSearchInfo.description || ''}
        onChange={(e) => {
          const value = e.target.value;
          const data = { ...ciAdvancedSearchInfo, description: value };
          updateAdvancedSearchFormData(data);
        }}
      />
    </>
  );
};

AdvancedSearchForm.displayName = 'AdvancedSearchForm';
export default AdvancedSearchForm;
