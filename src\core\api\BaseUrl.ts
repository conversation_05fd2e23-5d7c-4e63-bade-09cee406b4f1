import { getConfigs } from '@core/configs/Configs';

const config = getConfigs();

export const DEFAULT_VERSION = 'v1';
export const SERVER_URL = `${config.buildApiBaseUrl('server')}/${DEFAULT_VERSION}`;
export const DISCOVERY_URL = `${config.buildApiBaseUrl('discovery')}/${DEFAULT_VERSION}`;

export const BaseUrl = {
  //Server API
  superiors: `${SERVER_URL}/admin/superiors`,
  users: `${SERVER_URL}/systems/users`,
  usersAdmin: `${SERVER_URL}/systems/users/admin`,
  roles: `${SERVER_URL}/systems/roles`,
  groups: `${SERVER_URL}/systems/groups`,
  configParams: `${SERVER_URL}/systems/config-params`,
  businessDomains: `${SERVER_URL}/business-domains`,
  applications: `${SERVER_URL}/applications`,
  auditLogs: `${SERVER_URL}/audit-logs`,
  changeAssessments: `${SERVER_URL}/change-assessments`,
  changes: `${SERVER_URL}/changes`,
  ciAdvancedSearches: `${SERVER_URL}/ci-advanced-searches`,
  ciBusinessViews: `${SERVER_URL}/ci-business-views`,
  ciIdentifierRules: `${SERVER_URL}/ci-identifier-rules`,
  ciReconciliationRules: `${SERVER_URL}/ci-reconciliation-rules`,
  ciManagements: `${SERVER_URL}/ci-managements`,
  ciRelationships: `${SERVER_URL}/ci-relationships`,
  ciRelationshipTypes: `${SERVER_URL}/ci-relationship-types`,
  ciTypeReferenceFields: `${SERVER_URL}/ci-type-reference-fields`,
  ciTypeRelationshipAttributes: `${SERVER_URL}/ci-type/relationship/attributes`,
  cis: `${SERVER_URL}/cis`,
  ciTypes: `${SERVER_URL}/ci-types`,
  ciTypeAttrs: `${SERVER_URL}/ci-type-attrs`,
  discoveryStagings: `${SERVER_URL}/discovery-stagings`,
  discoveryTransformMaps: `${SERVER_URL}/discovery-transform-maps`,
  ciImpactedHistories: `${SERVER_URL}/ci-impacted-histories`,
  impactedCiRuleRelationships: `${SERVER_URL}/impacted-ci-rule-relationships`,
  impactedRules: `${SERVER_URL}/impacted-rules`,
  incidentRequests: `${SERVER_URL}/incident-requests`,
  notificationTemplates: `${SERVER_URL}/notification-templates`,
  outgoingMailConfigs: `${SERVER_URL}/outgoing-mail-configs`,
  serviceMaps: `${SERVER_URL}/service-maps`,
  sysPermissionCis: `${SERVER_URL}/sys-permission-cis`,
  sysPermissionOthers: `${SERVER_URL}/sys-permission-others`,
  discoveryPreviewDataCis: `${SERVER_URL}/discovery-preview-data-cis`,
  discoverySources: `${SERVER_URL}/discovery-sources`,
  discoverySourceDatas: `${SERVER_URL}/discovery-source-datas`,
  discoveryStagingDatas: `${SERVER_URL}/discovery-staging-datas`,
  jobDiscoveryConfigs: `${SERVER_URL}/job-discovery-configs`,

  // Discovery API

  netboxs: `${DISCOVERY_URL}/netboxs`,
  ranchers: `${DISCOVERY_URL}/ranchers`,
  aixHmcs: `${DISCOVERY_URL}/aix-hmcs`,
  vrops: `${DISCOVERY_URL}/vrops`,
  solarwinds: `${DISCOVERY_URL}/solarwinds`,
  snmps: `${DISCOVERY_URL}/snmps`,
  activeIq: `${DISCOVERY_URL}/active-iqs`,
  f5s: `${DISCOVERY_URL}/f5s`,
  k8s: `${DISCOVERY_URL}/k8s`,
};
