import React, { useEffect, useMemo, useState } from 'react';

import { KanbanTable2, KanbanTable2ColumnDef, KanbanTable2SortingState, useKanbanTable2 } from 'kanban-design-system';

export type Person = {
  firstName: string;
  middleName: string;
  lastName: string;
};

export const makeData = (numberOfRows: number) =>
  [...Array(numberOfRows).fill(null)].map((name, index) => ({
    firstName: `First name ${index}`,
    middleName: `Middle name ${index}`,
    lastName: `LastName name ${index}`,
  }));

const Example5 = () => {
  const columns = useMemo<KanbanTable2ColumnDef<Person>[]>(
    () => [
      {
        accessorKey: 'firstName',
        header: 'First Name',
        size: 150,
      },
      {
        accessorKey: 'middleName',
        header: 'Middle Name',
        size: 150,
      },
      {
        accessorKey: 'lastName',
        header: 'Last Name',
        size: 150,
      },
    ],
    [],
  );

  const [data, setData] = useState<Person[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [sorting, setSorting] = useState<KanbanTable2SortingState>([]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setData(makeData(10_000));
      setIsLoading(false);
    }
  }, []);

  const table = useKanbanTable2({
    columns,
    data, //10,000 rows
    enableBottomToolbar: false,
    enableGlobalFilterModes: true,
    enablePagination: false,
    enableRowNumbers: true,
    enableRowVirtualization: true,
    onSortingChange: setSorting,
    state: { isLoading, sorting },
    rowVirtualizerOptions: { overscan: 8 },
  });

  return (
    <>
      Virtualizer
      <KanbanTable2 table={table} />
    </>
  );
};

export default Example5;
