import { countersSlice, getCounters } from 'store/slices/CountersSlice';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { KanbanButton } from 'kanban-design-system';

export const DemoUseStorePage = () => {
  const count = useSelector(getCounters);
  const dispatch = useDispatch();
  return (
    <>
      Counter from Store: {count.value}
      <div>
        <KanbanButton
          onClick={() => {
            dispatch(countersSlice.actions.increment());
          }}>
          Increase
        </KanbanButton>
        <KanbanButton
          onClick={() => {
            dispatch(countersSlice.actions.decrement());
          }}>
          Decrease
        </KanbanButton>
      </div>
      <br />
      Node: Only use store in cases where you need information to be reused everywhere, for example: user information, administrative
      configuration,... In other cases, use context
    </>
  );
};
export default DemoUseStorePage;
