import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useParams } from 'react-router-dom';

import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { Flex, Box } from '@mantine/core';
import { BusinessDomainTreeDetailComponent } from '../component/BusinessDomainTreeDetailComponent';
import styles from './BusinessDomainDetail.module.scss';
import { KanbanTabs, KanbanTabsType } from 'kanban-design-system';
import { BusinessDomainDetailTab } from '@common/constants/BusinessDomainConstants';
import { useNavigate, useSearchParams } from 'react-router-dom';

import { BusinessDomainDetailTabBusinessFunction } from './BusinessDomainDetailTabBusinessFunction';
import { BusinessDomainDetailTabInformation } from './BusinessDomainDetailTabInformation';

import { buildBusinessDomainDetailUrl } from '@common/utils/RouterUtils';
import { BusinessDomainApi } from '@api/BusinessDomainApi';
import type { BusinessDomainDetailModel } from '@models/BusinessDomain';
import { NotificationError } from '@common/utils/NotificationUtils';
import { BreadcrumbComponent, UrlBaseCrumbData } from '@pages/admins/breadcrumb/BreadcrumbComponent';

const defaultValue: BusinessDomainDetailModel = { content: { id: 0, isLastChild: false } };

export const BusinessDomainDetailPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState<string>(BusinessDomainDetailTab.DEFAULT);

  const [isLastLeafLevel, setIsLastLeafLevel] = useState<boolean | undefined>(undefined);

  const [businessDomainDetailModel, setBusinessDomainDetailModel] = useState<BusinessDomainDetailModel>(defaultValue);
  const { ciId: ciIdParam } = useParams();
  const ciId = Number(ciIdParam);

  const fetchData = useCallback(() => {
    BusinessDomainApi.getByCiId(ciId)
      .then((res) => {
        setIsLastLeafLevel(res.data.content.isLastChild);
        setBusinessDomainDetailModel(res.data);
      })
      .catch(() => {
        setBusinessDomainDetailModel(defaultValue);
        setIsLastLeafLevel(defaultValue.content.isLastChild);
      });
  }, [ciId]);

  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab) {
      setActiveTab(tab);
    } else {
      setActiveTab(BusinessDomainDetailTab.INFORMATION);
    }
  }, [searchParams]);

  useEffect(() => {
    if (isLastLeafLevel !== undefined && activeTab !== BusinessDomainDetailTab.DEFAULT) {
      if (activeTab === BusinessDomainDetailTab.BUSINESS_FUNCTION && isLastLeafLevel === false) {
        navigate(buildBusinessDomainDetailUrl(ciId, BusinessDomainDetailTab.INFORMATION, undefined, undefined, true));
      }
    }
  }, [activeTab, isLastLeafLevel, ciId, navigate]);

  useEffect(() => {
    if (activeTab === BusinessDomainDetailTab.INFORMATION && searchParams.get('noRelationshipBusinessFunctionError') === 'true') {
      NotificationError({
        title: 'Error Business Domain',
        message: 'Business function is not in the relationship with the lowest business domain',
      });
    }
  }, [activeTab, searchParams]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const tabInfo = useMemo(() => {
    const tabs: KanbanTabsType = {
      INFORMATION: {
        title: 'Infomation',
        content: <BusinessDomainDetailTabInformation isShowLevelLeaf={true} data={businessDomainDetailModel} />,
      },
    };
    if (isLastLeafLevel) {
      tabs['BUSINESS_FUNCTION'] = {
        title: 'Business Function',
        content: <BusinessDomainDetailTabBusinessFunction />,
      };
    }
    return tabs;
  }, [businessDomainDetailModel, isLastLeafLevel]);

  const controlOpenedCiTypes = [...(businessDomainDetailModel.content.parents?.map((item) => item.id) || []), ciId];
  const locationCustomPaths = useMemo((): UrlBaseCrumbData => {
    const originPath = buildBusinessDomainDetailUrl(ciId, BusinessDomainDetailTab.INFORMATION);

    let detailBread = '';
    if (ciId) {
      detailBread = `View ${businessDomainDetailModel.content.name}`;
    }
    return {
      [`/${ciId}`]: {
        title: detailBread,
        href: originPath,
      },
    };
  }, [businessDomainDetailModel.content.name, ciId]);
  return (
    <>
      <BreadcrumbComponent locationCustomPaths={locationCustomPaths} />

      <Flex className={styles['wrapper']}>
        {/* 4736 bussiness domain detail */}

        <Flex w={300} style={{ borderRight: '1px solid var(--app-shell-border-color)' }}>
          {
            <BusinessDomainTreeDetailComponent
              data={businessDomainDetailModel.treeContents || []}
              onChange={(_, item) => {
                if (item) {
                  navigate(buildBusinessDomainDetailUrl(item.id, BusinessDomainDetailTab.INFORMATION));
                }
              }}
              customActive={(item) => {
                const isActive = item.id === ciId;
                return isActive;
              }}
              controlOpenedCiTypes={controlOpenedCiTypes}
            />
          }
        </Flex>
        <Box p={'sm'} flex={1}>
          <HeaderTitleComponent title={`View Detail`} />
          <KanbanTabs
            configs={{
              defaultValue: BusinessDomainDetailTab.INFORMATION,
              value: activeTab,
              variant: 'outline',
              onChange: (value) => {
                navigate(buildBusinessDomainDetailUrl(ciId, value || BusinessDomainDetailTab.INFORMATION));
              },
            }}
            tabs={tabInfo}
          />
        </Box>
      </Flex>
    </>
  );
};

export default BusinessDomainDetailPage;
