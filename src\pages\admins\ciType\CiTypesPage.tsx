import { KanbanButton, KanbanTabsType } from 'kanban-design-system';
import { KanbanModal } from 'kanban-design-system';
import { ActionIcon, Flex, Group, Space, Alert, Box } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IconPlus, IconSettings, IconAlertTriangle, IconEdit, IconTrash } from '@tabler/icons-react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import type { ConfigItemTypeModel } from '@models/ConfigItemType';
import CiTypeAttributeDetail, { CiTypeAttributeDetailMethods } from './CiTypeAttributeDetail';
import { useDispatch } from 'react-redux';
import { ciTypesSlice, useGetCiTypes } from '@slices/CiTypesSlice';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { ConfigItemTypeApi, ConfigItemTypeResponse } from '@api/ConfigItemTypeApi';
import { KanbanText } from 'kanban-design-system';
import { CiTypePickerComponent } from '@components/commonCi/SelectCiTypeComponent';
import { KanbanIconButton } from 'kanban-design-system';
import ConfigItemTypeRelation from './CiTypeRelationship';
import { KanbanTabs } from 'kanban-design-system';
import type { ConfigItemTypeInfoModel } from '@models/ConfigItemTypeInfo';
import { Link, useSearchParams } from 'react-router-dom';
import { KanbanCheckbox } from 'kanban-design-system';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import type { ConfigItemAttributeObject } from '@models/ConfigItemTypeAttr';
import ConfigItemTypeReference from './CiTypeReferenceField';
import CiTypeBasicInfo, { CiTypeBasicInfoMethods } from './CiTypeBasicInfo';
import { getConfigs } from '@core/configs/Configs';
import { ApiResponse } from '@core/api/ApiResponse';
import { DiscoveryTransformMapApi } from '@api/DiscoveryTransformMapApi';
import { DiscoveryTransformMapModel } from '@models/DiscoveryTransformMap';
import { BreadcrumbComponent } from '../breadcrumb/BreadcrumbComponent';

const CINAME_RECORD_MAX_SIZE = 5;

export enum CI_TYPE_CONFIG_TABS {
  INFORMATION = 'INFORMATION',
  ATTRIBUTES = 'ATTRIBUTES',
  RELATIONSHIPS = 'RELATIONSHIPS',
  REFERENCE_FIELDS = 'REFERENCE_FIELDS',
}

const isFeatureReferenceAttribute = getConfigs().features.ciTypeReferenceAttribute;

export const CiTypesPage = () => {
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();

  const ciTypeIdParam = searchParams.get('ciTypeId');
  const activeTab = searchParams.get('activeTab');
  const ciTypeIdNumber = Number(ciTypeIdParam);

  const [expandAll, setExpandAll] = useState(false);
  const [openedModal, { close: closePopup, open: openModal }] = useDisclosure(false);
  const [openedModalDel, { close: closePopupDel, open: openModalDel }] = useDisclosure(false);
  const [openedModalDelAll, { close: closePopupDelAll, open: openModalDelAll }] = useDisclosure(false);
  const [openedModalDetail, { close: closePopupDetail, open: openModalDetail }] = useDisclosure(false);
  const [openedModalConfirmChange, { close: closePopupConfirmChange, open: openModalConfirmChange }] = useDisclosure(false);
  const [newItem, setNewItem] = useState<ConfigItemTypeModel>({
    id: 0,
    name: '',
    active: true,
  });
  const [deleteId, setDeleteId] = useState<number>(0);
  const [isAddByParent, setIsAddByParent] = useState(false);
  const [itemSelect, setItemSelect] = useState<ConfigItemTypeModel>({
    id: 0,
    name: '',
  });
  const refDetail = useRef<CiTypeAttributeDetailMethods | null>(null);
  const [listNameCI, setListNameCI] = useState('');
  const [listCiTypeHasRelationship, setListCiTypeHasRelationship] = useState('');
  const [isChange, setIsChange] = useState(false);
  const [isChangeBasicInfo, setIsChangeBasicInfo] = useState(false);
  const [ciTypeInfoUpdate, setCiTypeInfoUpdate] = useState<ConfigItemTypeInfoModel>({});
  const [controlCheckedCiTypes, setControlCheckedCiTypes] = useState<number[]>([]);
  const [defaultTabDetail, setDefaultTabDetail] = useState<string>(CI_TYPE_CONFIG_TABS.ATTRIBUTES);
  const [isLoad, setIsLoad] = useState<boolean>(true);
  const [transformMapText, setTransformMapText] = useState<string | undefined>(undefined);
  const [ciAttributes, setCiAttributes] = useState<ConfigItemAttributeObject>({ attributesDefault: [], attributes: [] });

  const ciTypes = useGetCiTypes();

  const resetCiTypeInfo = () => {
    setNewItem({
      id: 0,
      name: '',
      parentId: 0,
      active: true,
      icon: '',
    });
    setIsAddByParent(false);
    setListNameCI('');
    setIsChangeBasicInfo(false);
  };

  const closeModal = () => {
    resetCiTypeInfo();
    closePopup();
  };

  const closeModalDel = () => {
    setDeleteId(0);
    setListNameCI('');
    setListCiTypeHasRelationship('');
    closePopupDel();
  };

  const closeModalDelAll = () => {
    closePopupDelAll();
  };

  const closeModalDetail = () => {
    closePopupConfirmChange();
    closePopupDetail();

    resetCiTypeInfo();
    setIsChange(false);
    setCiTypeInfoUpdate({});
  };

  const handleCloseModalDetail = () => {
    if (isChange) {
      openModalConfirmChange();
      return;
    }
    closeModalDetail();
  };

  const updateCiWarning = (ciTypeId: number) => {
    ConfigItemTypeApi.getTopCiNamesByCiTypeId(ciTypeId, CINAME_RECORD_MAX_SIZE)
      .then((res) => {
        if (res?.data?.length > 0) {
          let limitedNames = res.data.slice(0, CINAME_RECORD_MAX_SIZE - 1).join(', ');
          if (res.data.length > CINAME_RECORD_MAX_SIZE - 1) {
            limitedNames += ', ..';
          }
          setListNameCI(limitedNames);
        } else {
          setListNameCI('');
        }
      })
      .catch(() => {});
  };

  const getTransformMapByCiTypeId = (ciTypeId: number) => {
    if (ciTypeId) {
      DiscoveryTransformMapApi.getByciTypeId(ciTypeId)
        .then((res) => {
          const datas: DiscoveryTransformMapModel[] = res.data || [];
          setTransformMapText(datas.map((obj) => obj.name).join(', '));
        })
        .catch(() => {});
    }
  };

  const updateCiTypeRelationshipWarning = (ciTypeId: number) => {
    ConfigItemTypeApi.getAllCiTypeHasRelationship(ciTypeId)
      .then((res) => {
        if (res?.data?.length > 0) {
          const nameCiTypes = res.data.map((item) => item.name);
          const limit = 3;
          let limitedNames = nameCiTypes.slice(0, limit).join(', ');
          if (nameCiTypes.length > limit) {
            limitedNames += ', ..';
          }
          setListCiTypeHasRelationship(limitedNames);
        } else {
          setListCiTypeHasRelationship('');
        }
      })
      .catch(() => {});
  };

  const handleDelete = useCallback(
    (node: ConfigItemTypeModel) => {
      setDeleteId(node.id);
      updateCiWarning(node.id);
      getTransformMapByCiTypeId(node.id);
      updateCiTypeRelationshipWarning(node.id);
      openModalDel();
    },
    [openModalDel],
  );

  const handleAdd = useCallback(
    (node: ConfigItemTypeModel) => {
      setNewItem({
        id: 0,
        name: '',
        parentId: node.id,
        active: true,
      });
      setIsAddByParent(true);
      openModal();
    },
    [openModal],
  );

  const handleViewDetail = useCallback(
    (node: ConfigItemTypeModel) => {
      setItemSelect(node);
      // updateCiWarning(node.id);
      openModalDetail();
    },
    [openModalDetail],
  );

  const handleEdit = useCallback(
    (node: ConfigItemTypeModel) => {
      setNewItem(node);
      setIsAddByParent(true);
      updateCiWarning(node.id);
      handleViewDetail(node);
      getTransformMapByCiTypeId(node.id);
    },
    [handleViewDetail],
  );

  const handleViewDefault = () => {
    setItemSelect({
      id: 0,
      name: '',
      parentId: 0,
    });
    setDefaultTabDetail(CI_TYPE_CONFIG_TABS.ATTRIBUTES);
    openModalDetail();
  };

  useEffect(() => {
    if (isLoad && ciTypeIdNumber > 0 && activeTab) {
      setIsLoad(false);
      const ciType = ciTypes.data.find((x) => x.id === ciTypeIdNumber);
      if (ciType) {
        setDefaultTabDetail(activeTab);
        handleEdit(ciType);
      }
    }
  }, [activeTab, ciTypeIdNumber, ciTypes.data, handleEdit, isLoad]);

  const onDelete = () => {
    dispatch(
      ciTypesSlice.actions.deleteData({
        data: deleteId,
        onSuccessCallback(_res) {
          NotificationSuccess({
            message: 'Delete data successfully',
          });
          setControlCheckedCiTypes((prev) => prev.filter((item) => item !== deleteId));
        },
      }),
    );
    closeModalDel();
  };

  const onDeleteAll = () => {
    if (!controlCheckedCiTypes) {
      return;
    }
    dispatch(
      ciTypesSlice.actions.deleteAllData({
        data: controlCheckedCiTypes,
        onSuccessCallback(_res) {
          NotificationSuccess({
            message: 'Delete data successfully',
          });
          setControlCheckedCiTypes([]);
        },
      }),
    );
    closeModalDelAll();
  };

  const onSaveDetail = () => {
    if (isChangeBasicInfo) {
      onSaveChangeCiTypeUpdate();
    }
    if (isChange) {
      ConfigItemTypeApi.createOrUpdateCiTypeDetailInfos(itemSelect.id, ciTypeInfoUpdate)
        .then((_res) => {
          NotificationSuccess({
            message: 'Save data successfully',
          });
          closeModalDetail();
        })
        .catch(() => {});
    }
    if (!isChangeBasicInfo && !isChange) {
      closeModalDetail();
    }
  };

  const [listExpanded, setListExpanded] = useState<number[]>([]);

  const leftIcon: (item: ConfigItemTypeModel) => React.ReactNode = useCallback((node) => {
    return (
      <GuardComponent requirePermissions={[AclPermission.deleteCiType]} hiddenOnUnSatisfy>
        <KanbanCheckbox
          mb={0}
          onClick={(e) => {
            e.stopPropagation();
            setControlCheckedCiTypes((prev) => {
              const opened = prev.includes(node.id);
              if (opened) {
                return prev.filter((x) => x !== node.id);
              }
              return [...prev, node.id];
            });
          }}
        />
      </GuardComponent>
    );
  }, []);
  const onChange: (newValue?: number | undefined, item?: ConfigItemTypeModel | undefined) => boolean = useCallback(
    (_value, item) => {
      if (item && isCurrentUserMatchPermissions([AclPermission.viewDetailCiType])) {
        // handleViewDetail(item);
        handleEdit(item);
        setDefaultTabDetail(CI_TYPE_CONFIG_TABS.ATTRIBUTES);
      }
      return false;
    },
    [handleEdit],
  );
  const onChangeExpand: (item: ConfigItemTypeModel, state: boolean) => void = useCallback((data, state) => {
    setListExpanded((prev) => {
      if (state) {
        return [...prev, data.id];
      }
      return prev.filter((x) => x !== data.id);
    });
  }, []);
  const customExpand: (item: ConfigItemTypeModel) => boolean = useCallback(
    (data) => {
      return listExpanded.includes(data.id);
    },
    [listExpanded],
  );
  const rightSection: (item: ConfigItemTypeModel) => React.ReactNode = useCallback(
    (item) => {
      return (
        <>
          <GuardComponent requirePermissions={[AclPermission.createCiType]} hiddenOnUnSatisfy>
            <KanbanIconButton
              size={'sm'}
              onClick={(e) => {
                e.stopPropagation();
                handleAdd(item);
              }}>
              <IconPlus size='1rem' />
            </KanbanIconButton>
          </GuardComponent>
          <Space w={5} />
          <GuardComponent requirePermissions={[AclPermission.updateCiType]} hiddenOnUnSatisfy>
            <KanbanIconButton
              size={'sm'}
              onClick={(e) => {
                e.stopPropagation();
                handleEdit(item);
                setDefaultTabDetail(CI_TYPE_CONFIG_TABS.INFORMATION);
              }}>
              <IconEdit size='1rem' />
            </KanbanIconButton>
          </GuardComponent>
          <Space w={5} />
          <GuardComponent requirePermissions={[AclPermission.deleteCiType]} hiddenOnUnSatisfy>
            <KanbanIconButton
              color='red'
              size={'sm'}
              onClick={(e) => {
                e.stopPropagation();
                handleDelete(item);
              }}>
              <IconTrash size='1rem' />
            </KanbanIconButton>
          </GuardComponent>
          <Space w={'sm'} />
        </>
      );
    },
    [handleAdd, handleDelete, handleEdit],
  );

  const childRefCiTypeBasicInfo = useRef<CiTypeBasicInfoMethods | null>(null);

  const onCreateCiType = () => {
    onSaveChangeCiTypeCreate();
  };

  const onSaveChangeCiTypeCreate = () => {
    const updateObject: ConfigItemTypeModel | undefined = childRefCiTypeBasicInfo.current?.onCreateUpdate();
    if (updateObject) {
      dispatch(
        ciTypesSlice.actions.createOrUpdate({
          data: updateObject,
          onSuccessCallback(res) {
            const response = res as ApiResponse<ConfigItemTypeResponse>;
            closeModal();
            NotificationSuccess({
              message: 'Create CI Type data successfully',
            });
            setDefaultTabDetail(CI_TYPE_CONFIG_TABS.ATTRIBUTES);
            handleEdit(response.data);
          },
        }),
      );
    }
  };

  const onSaveChangeCiTypeUpdate = () => {
    const updateObject: ConfigItemTypeModel | undefined = childRefCiTypeBasicInfo.current?.onCreateUpdate();
    if (updateObject) {
      dispatch(
        ciTypesSlice.actions.createOrUpdate({
          data: updateObject,
          onSuccessCallback(_res) {
            closeModalDetail();
            NotificationSuccess({
              message: 'Update CI Type data successfully',
            });
          },
        }),
      );
    }
  };

  const tabsDetail: KanbanTabsType = {
    ...(itemSelect.id > 0 && {
      INFORMATION: {
        title: 'Information',
        content: (
          <>
            <CiTypeBasicInfo ref={childRefCiTypeBasicInfo} ciTypeInfo={newItem} isAddByParent={isAddByParent} setIsChange={setIsChangeBasicInfo} />
          </>
        ),
      },
    }),
    ATTRIBUTES: {
      title: 'Attributes',
      content: (
        <>
          {/* 4763 draggable ci type custom atts */}
          <CiTypeAttributeDetail
            ref={refDetail}
            ciTypeId={itemSelect.id}
            parentId={itemSelect.parentId}
            ciTypeInfoUpdate={ciTypeInfoUpdate}
            setCiTypeInfoUpdate={setCiTypeInfoUpdate}
            setIsChange={setIsChange}
            setCiAttributes={setCiAttributes}
          />
        </>
      ),
    },
    ...(itemSelect.id > 0 && {
      RELATIONSHIPS: {
        title: 'Relationships',
        disabled: !(itemSelect.id > 0),
        content: (
          <>
            <ConfigItemTypeRelation setCiTypeInfoUpdate={setCiTypeInfoUpdate} setIsChange={setIsChange} ciTypeId={itemSelect.id} />
          </>
        ),
      },
      ...(isFeatureReferenceAttribute && {
        REFERENCE_FIELDS: {
          title: 'Reference fields',
          disabled: !(itemSelect.id > 0),
          content: (
            <>
              <ConfigItemTypeReference
                ciTypeId={itemSelect.id}
                ciAttributes={ciAttributes}
                setCiTypeInfoUpdate={setCiTypeInfoUpdate}
                setIsChange={setIsChange}
              />
            </>
          ),
        },
      }),
    }),
  };

  return (
    <Box mr={'sm'}>
      {/* 4763 ci type list*/}
      <BreadcrumbComponent />
      {/* Modal confirm delete all */}
      <KanbanModal
        size={'lg'}
        opened={openedModalDelAll}
        onClose={closeModalDelAll}
        title={'Confirm Delete'}
        actions={<KanbanButton onClick={onDeleteAll}>OK</KanbanButton>}>
        <p>
          Are you sure you want to clear the selected CI Types? You must ensure that the above list does not contain any CIs or CI Type Relationship.
        </p>
      </KanbanModal>

      {/* Modal confirm delete */}
      <KanbanModal
        size={'lg'}
        opened={openedModalDel}
        onClose={closeModalDel}
        title={'Confirm Delete'}
        actions={
          <KanbanButton onClick={onDelete} disabled={!!listNameCI || !!listCiTypeHasRelationship}>
            OK
          </KanbanButton>
        }>
        <p>Are you sure you want to delete this item?</p>
        {listNameCI && (
          <Alert my={'20'} variant='light' color='red' title={''} icon={<IconAlertTriangle />}>
            <KanbanText c={'red'} ml={8}>
              CI Type is currently being used in the following CIs: {listNameCI}. You must delete all CIs before continuing, follow the link:{' '}
              <Link to={`/ci-types/${deleteId}`}>CIs Management</Link>.
            </KanbanText>
          </Alert>
        )}

        {listCiTypeHasRelationship && (
          <Alert my={'20'} variant='light' color='red' title={''} icon={<IconAlertTriangle />}>
            <KanbanText c={'red'} ml={8}>
              CI Type/Sub CI Type exists in the CI Type Relationship. <br />
              You must delete all relationship(Detail {'->'} tab Relationship) before continuing, follow CI Type: {listCiTypeHasRelationship}
            </KanbanText>
          </Alert>
        )}
        {transformMapText && (
          <Alert my={'20'} variant='light' color='orange' title={''} icon={<IconAlertTriangle />}>
            <KanbanText c={'orange'} ml={8}>
              This CI Type is being used in the Discovery Transform Map: {transformMapText}
            </KanbanText>
          </Alert>
        )}
      </KanbanModal>

      {/* Modal create CI Type */}
      <KanbanModal
        size={'lg'}
        opened={openedModal}
        onClose={closeModal}
        title={'Create CI Type'}
        actions={<KanbanButton onClick={onCreateCiType}> {'Create'}</KanbanButton>}>
        <CiTypeBasicInfo ref={childRefCiTypeBasicInfo} ciTypeInfo={newItem} isAddByParent={isAddByParent} setIsChange={setIsChangeBasicInfo} />
      </KanbanModal>

      {/* Modal crud detail attribute/relationship */}
      <KanbanModal
        size='70%'
        opened={openedModalDetail}
        onClose={handleCloseModalDetail}
        title={itemSelect.id > 0 ? `Detail Config Type Attribute: ${itemSelect.name}` : 'Default Attributes - Common to all the CI Types'}
        actions={
          <GuardComponent requirePermissions={[AclPermission.configAttributeOfCiType]} hiddenOnUnSatisfy>
            <KanbanButton onClick={onSaveDetail}>Save</KanbanButton>{' '}
          </GuardComponent>
        }>
        {listNameCI && itemSelect.id > 0 && (
          <Alert my={'20'} variant='light' color='red' title={''} icon={<IconAlertTriangle />}>
            <KanbanText c={'red'} ml={8}>
              CI Type is currently being used in the following CIs: {listNameCI}. Updating information here will result in changes to existing CIs
              data.
            </KanbanText>
          </Alert>
        )}

        {transformMapText && (
          <Alert my={'20'} variant='light' color='orange' title={''} icon={<IconAlertTriangle />}>
            <KanbanText c={'orange'} ml={8}>
              This CI Type is being used in the Discovery Transform Map: {transformMapText}
            </KanbanText>
          </Alert>
        )}
        <KanbanTabs
          configs={{
            defaultValue: defaultTabDetail,
            keepMounted: true,
          }}
          tabs={tabsDetail}
        />
      </KanbanModal>

      {/* Modal confirm close after change */}
      <KanbanModal
        size={'lg'}
        opened={openedModalConfirmChange}
        onClose={closePopupConfirmChange}
        title={'Confirm Discard Changes'}
        actions={<KanbanButton onClick={closeModalDetail}>OK</KanbanButton>}>
        <p>Data has changed. Do you want to cancel?</p>
      </KanbanModal>

      <Flex justify={'space-between'}>
        <KanbanButton
          onClick={() => {
            setListExpanded((_prev) => {
              if (expandAll) {
                return [];
              }
              return ciTypes.data.map((x) => x.id);
            });
            setExpandAll(!expandAll);
          }}>
          {expandAll ? 'Collapse All' : 'Expand All'}
        </KanbanButton>
        <Group justify='center'>
          <GuardComponent requirePermissions={[AclPermission.deleteCiType]} hiddenOnUnSatisfy>
            <KanbanButton leftSection={<IconTrash />} onClick={openModalDelAll} disabled={!controlCheckedCiTypes.length}>
              Delete All
            </KanbanButton>
          </GuardComponent>
          <GuardComponent requirePermissions={[AclPermission.createCiType]} hiddenOnUnSatisfy>
            <KanbanButton leftSection={<IconPlus />} onClick={openModal}>
              Create new
            </KanbanButton>
          </GuardComponent>
          <GuardComponent requirePermissions={[AclPermission.configAttributeOfCiType]} hiddenOnUnSatisfy>
            <ActionIcon
              size='lg'
              title='Config Default Attribute'
              onClick={() => {
                handleViewDefault();
              }}>
              <IconSettings size='1rem' />
            </ActionIcon>
          </GuardComponent>
        </Group>
      </Flex>

      <br />

      <CiTypePickerComponent
        showInfo={true}
        leftSection={leftIcon}
        onChange={onChange}
        onChangeExpand={onChangeExpand}
        customExpand={customExpand}
        rightSection={rightSection}
      />
    </Box>
  );
};

export default CiTypesPage;
