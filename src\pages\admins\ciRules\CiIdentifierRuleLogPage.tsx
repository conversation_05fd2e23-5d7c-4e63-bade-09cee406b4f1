import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { KanbanButton, KanbanTable, TableAffactedSafeType, type ColumnType, type KanbanTableProps } from 'kanban-design-system';
import { Divider, Space } from '@mantine/core';
import { CiIdentifierLogDto } from '@models/CiIdentifierRule';
import { DiscoveryPreviewDataCiApi } from '@api/discovery/DiscoveryPreviewDataCiApi';
import { useParams } from 'react-router-dom';
import { tableAffectedToPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import equal from 'fast-deep-equal';

export const CiIdentifierRuleLogPage = () => {
  const { ruleId } = useParams();
  const ruleIdNumber = Number(ruleId);
  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType>();
  const [listData, setListData] = useState<CiIdentifierLogDto[]>([]);
  const [isLoadingTable, setIsLoadingTable] = useState(false);

  const findAllDiscoveryDataCiLogByRuleId = useCallback(() => {
    if (!tableAffected) {
      return;
    }

    setIsLoadingTable(true);
    const pagination = tableAffectedToPaginationRequestModel(tableAffected);
    DiscoveryPreviewDataCiApi.findAllDiscoveryDataCiLogByRuleId(pagination, ruleIdNumber)
      .then((res) => {
        if (res.data) {
          setListData(res.data?.content || []);
          setTotalRecords(res.data.totalElements);
        }
      })
      .catch(() => {})
      .finally(() => {
        setIsLoadingTable(false);
      });
  }, [ruleIdNumber, tableAffected]);

  useEffect(() => {
    findAllDiscoveryDataCiLogByRuleId();
  }, [findAllDiscoveryDataCiLogByRuleId]);

  const columns: ColumnType<CiIdentifierLogDto>[] = useMemo(() => {
    return [
      {
        title: 'Id',
        name: 'id',
        hidden: true,
        width: '5%',
      },
      {
        title: 'Time',
        name: 'createdDate',
        width: '10%',
      },
      {
        title: 'Log Type',
        name: 'logType',
        width: '5%',
      },
      {
        title: 'Level',
        name: 'logLevel',
        width: '5%',
      },
      {
        title: 'CI Name',
        name: 'ciName',
        width: '10%',
      },
      {
        title: 'CI Id',
        name: 'ciId',
        width: '5%',
      },
      {
        title: 'Messages',
        name: 'message',
        width: '60%',
        customRender: (data) => {
          return <pre>{data}</pre>;
        },
      },
    ];
  }, []);

  const tableProps: KanbanTableProps<CiIdentifierLogDto> = useMemo(() => {
    return {
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 300,
        // onSearched: onSearched,
      },
      sortable: {
        enable: true,
      },
      columns: columns,
      data: listData,
      isLoading: isLoadingTable,
      selectableRows: {
        enable: false,
      },
      serverside: {
        totalRows: totalRecords,
        onTableAffected(dataSet) {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
      pagination: {
        enable: true,
      },
    };
  }, [columns, isLoadingTable, listData, tableAffected, totalRecords]);

  return (
    <>
      <Space h={'xs'} />
      <Divider />
      <Space h={'xs'} />
      <KanbanButton
        onClick={() => {
          findAllDiscoveryDataCiLogByRuleId();
        }}>
        Refresh
      </KanbanButton>
      <KanbanTable {...tableProps} />
    </>
  );
};

CiIdentifierRuleLogPage.whyDidYouRender = true;
CiIdentifierRuleLogPage.displayName = 'CiIdentifierRuleLogPage';
export default CiIdentifierRuleLogPage;
