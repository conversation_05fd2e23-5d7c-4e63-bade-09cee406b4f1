import React from 'react';

export type ProtectedRouteProps = {
  children: React.ReactNode;
  //One of those role
  requirePermissions: string[];
  userPermissions: string[];
  errorElement: React.ReactNode;
  hiddenOnUnSatisfy?: boolean;
  allMatchPermissions?: boolean;
  isSuperAdmin: boolean;
};

export const ProtectedComponent: React.FC<ProtectedRouteProps> = ({
  allMatchPermissions,
  children,
  errorElement,
  hiddenOnUnSatisfy,
  isSuperAdmin,
  requirePermissions,
  userPermissions,
}) => {
  const isAuthorized = () => {
    return (
      !requirePermissions.length ||
      (allMatchPermissions
        ? requirePermissions.every((permission) => userPermissions.includes(permission))
        : requirePermissions.some((permission) => userPermissions.includes(permission)))
    );
  };

  return <>{isSuperAdmin || isAuthorized() ? children : hiddenOnUnSatisfy ? null : errorElement}</>;
};
