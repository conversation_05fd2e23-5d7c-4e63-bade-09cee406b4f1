import type { StatusCiManagement } from '@common/constants/CiManagement';
import type { ConfigItemInfoModel } from './ConfigItem';
import { CiAttributeInfoModel } from './ConfigItemTypeAttr';

export type CiManagementModel = {
  id: number;
  data: string;
  dataParse?: ConfigItemInfoModel;
  owner: string;
  verifyUser: string;
  ciId: number;
  description: string;
  approvalComment?: string;
  status: StatusCiManagement;
  action: string;
  ciTypeId: number;
  ciName?: string;
  dataChange?: string;
  isImport?: boolean;
  sysId?: string;
  ciAttributes?: CiAttributeInfoModel[];
};
export default 1;
