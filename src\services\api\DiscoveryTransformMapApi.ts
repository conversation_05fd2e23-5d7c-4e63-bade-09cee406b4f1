import { <PERSON><PERSON><PERSON> } from '@core/api/BaseApi';
import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
import { DiscoveryTransformMapModel } from '@models/DiscoveryTransformMap';
import { ApiResponseDataBase } from '@core/api/ApiResponse';
import { BaseUrl } from '@core/api/BaseUrl';

export type DiscoveryTransformMapResponse = DiscoveryTransformMapModel & ApiResponseDataBase;
export type DiscoveryTransformMapPagingResponse = PaginationResponseModel<DiscoveryTransformMapModel>;
export type DiscoveryTransformMapRequestModel = PaginationRequestModel<DiscoveryTransformMapModel>;

export class DiscoveryTransformMapApi extends BaseApi {
  static baseUrl = BaseUrl.discoveryTransformMaps;

  static getAll(pagination: DiscoveryTransformMapRequestModel) {
    return BaseApi.postData<DiscoveryTransformMapPagingResponse>(`${this.baseUrl}/filter`, pagination);
  }

  static save(data: DiscoveryTransformMapModel) {
    return BaseApi.postData<DiscoveryTransformMapModel>(`${this.baseUrl}`, data);
  }

  static getById(id: number) {
    return BaseApi.getData<DiscoveryTransformMapResponse>(`${this.baseUrl}/${id}`);
  }

  static deleteById(id: number) {
    return BaseApi.deleteData<boolean>(`${this.baseUrl}/${id}`);
  }

  static deleteByIds(ids: number[]) {
    return BaseApi.deleteData<boolean[]>(`${this.baseUrl}/batch`, {
      ids,
    });
  }

  static runJob(id: number) {
    return BaseApi.postData<string>(`${this.baseUrl}/${id}/execution`);
  }

  static getByAttributeId(attributeId: number) {
    return BaseApi.getData<DiscoveryTransformMapModel[]>(`${this.baseUrl}`, {
      attributeId,
    });
  }

  static getByciTypeId(ciTypeId: number) {
    return BaseApi.getData<DiscoveryTransformMapModel[]>(`${this.baseUrl}`, {
      ciTypeId,
    });
  }
}
