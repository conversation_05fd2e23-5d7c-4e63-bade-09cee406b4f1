import { KanbanInput } from 'kanban-design-system';
import { KanbanTextarea } from 'kanban-design-system';
import { SimpleGrid } from '@mantine/core';
import type { ServiceMapingDetailsModel } from '@models/ServiceMapping';
import React from 'react';

type ServiceMappingInfoProps = {
  serviceMapDetails?: ServiceMapingDetailsModel;
};

export const ServiceMappingInfo = (props: ServiceMappingInfoProps) => {
  const { serviceMapDetails } = props;
  return (
    <>
      <SimpleGrid cols={4}>
        <KanbanInput radius='md' label='ID:' defaultValue={serviceMapDetails?.id} mb={2} disabled />
        <KanbanInput radius='md' label='CI Type:' defaultValue={serviceMapDetails?.name} disabled />
        <KanbanInput radius='md' label='Name:' defaultValue={serviceMapDetails?.name} disabled />
        <KanbanInput radius='md' label='Cost' defaultValue={'TODO no document'} disabled />
      </SimpleGrid>
      <SimpleGrid cols={4}>
        <KanbanInput radius='md' label='Availability target (%)' defaultValue={1000} disabled />
        <KanbanInput radius='md' label='Service support hours' defaultValue={'TODO no document'} disabled />
        <KanbanInput radius='md' label='Business impact' defaultValue={'TODO no document'} disabled />
        <KanbanInput radius='md' label='Business Criticality' defaultValue={'TODO no document'} disabled />
      </SimpleGrid>

      <KanbanTextarea w={'100%'} label='Service Description' defaultValue={serviceMapDetails?.description} disabled />
    </>
  );
};

export default ServiceMappingInfo;
