import React, { useState, useCallback, useEffect } from 'react';
import type { BusinessDomainDetailModel } from '@models/BusinessDomain';
import { BusinessDomainApi } from '@api/BusinessDomainApi';
import { BusinessDomainDetailTabInformation } from '../BusinessDomainDetailTabInformation';

const defaultValue: BusinessDomainDetailModel = { content: { id: 0, isLastChild: false } };
export type BusinessFunctionModalTabInfomationProps = {
  ciIdBusinessDomain: number;
  ciIdBusinessFunction: number;
};

export const BusinessFunctionModalTabInfomation = (props: BusinessFunctionModalTabInfomationProps) => {
  const [businessDomainDetailModel, setBusinessDomainDetailModel] = useState<BusinessDomainDetailModel>(defaultValue);

  const fetchData = useCallback(() => {
    BusinessDomainApi.getBusinessFunctionInfoByCiIdBusinessDomainAndCiIdBusinessFunction(props.ciIdBusinessDomain, props.ciIdBusinessFunction)
      .then((res) => {
        setBusinessDomainDetailModel(res.data);
      })
      .catch(() => {
        setBusinessDomainDetailModel(defaultValue);
      });
  }, [props.ciIdBusinessDomain, props.ciIdBusinessFunction]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return (
    <>
      <BusinessDomainDetailTabInformation data={businessDomainDetailModel} />
    </>
  );
};

export default BusinessFunctionModalTabInfomation;
