import type { ApiResponseDataBase } from '@core/api/ApiResponse';
import { BaseUrl } from '@core/api/BaseUrl';
import type { ApplicationModel } from '@models/Application';
import { BaseApi } from 'core/api/BaseApi';

export type ApplicationResponse = ApiResponseDataBase & ApplicationModel;

export class ApplicationsApi extends BaseApi {
  static baseUrl = BaseUrl.applications;

  static getAllApplications() {
    return BaseApi.getData<ApplicationResponse[]>(ApplicationsApi.baseUrl);
  }
  static createOrUpdateApplication(data: ApplicationModel) {
    return BaseApi.postData<ApplicationResponse>(ApplicationsApi.baseUrl, data);
  }
  static deleteApplication(id: number) {
    return BaseApi.deleteData<boolean>(`${ApplicationsApi.baseUrl}/${id}`);
  }
}
