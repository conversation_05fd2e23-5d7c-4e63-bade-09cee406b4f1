import { Draggable, DraggableProvided } from '@hello-pangea/dnd';
import { Paper, PaperProps, Tooltip } from '@mantine/core';
import React, { ReactNode, useCallback, useMemo } from 'react';
import { useDraggableInPortal } from '@common/hooks/useDraggableInPortal';
import { DragTranfromMapType } from '@models/DiscoveryTransformMap';

export interface DraggableItemBaseProps {
  item: DragTranfromMapType;
  index: number;
  classes: Record<string, string>;
  onClick?: () => void;

  // Customization props
  draggableIdPrefix: string;
  paperProps?: PaperProps;
  paperStyle?: React.CSSProperties;

  // Render props for content customization
  renderContent: (item: DragTranfromMapType) => ReactNode;
  dragableRef?: React.Ref<HTMLDivElement | null>;
}

export const DraggableItemBase: React.FC<DraggableItemBaseProps> = ({
  classes,
  dragableRef,
  draggableIdPrefix,
  index,
  item,
  onClick,
  paperProps,
  paperStyle,
  renderContent,
}) => {
  const renderDraggable = useDraggableInPortal();

  const renderItem = useCallback(
    (provided: DraggableProvided) => {
      const paperContent = (
        <Tooltip label={item.label} multiline withArrow openDelay={700}>
          <Paper
            className={item.selected ? classes.active : undefined}
            {...provided.draggableProps}
            {...provided.dragHandleProps}
            ref={(node) => {
              provided.innerRef(node);

              if (dragableRef) {
                if (typeof dragableRef === 'function') {
                  dragableRef(node);
                } else {
                  (dragableRef as React.MutableRefObject<HTMLDivElement | null>).current = node;
                }
              }
            }}
            p='xs'
            mb='10'
            radius='sm'
            shadow='md'
            withBorder
            onClick={onClick}
            style={{
              ...provided.draggableProps.style,
              ...paperStyle,
            }}
            {...paperProps}>
            {renderContent(item)}
          </Paper>
        </Tooltip>
      );

      return paperContent;
    },
    [item, classes.active, onClick, paperStyle, paperProps, renderContent, dragableRef],
  );

  const draggableChildren = useMemo(() => renderDraggable(renderItem), [renderDraggable, renderItem]);

  return (
    <Draggable key={`${item.id}_${item.hashId || ''}`} index={index} draggableId={`${draggableIdPrefix}${item.id}_${item.hashId ?? ''}`}>
      {draggableChildren}
    </Draggable>
  );
};
