import React, { useEffect } from 'react';
import '@mantine/core/styles.css';
import '@mantine/dates/styles.css';
import '@mantine/notifications/styles.css';
import '@mantine/spotlight/styles.css';
import '@mantine/dropzone/styles.css';
import { MantineProvider } from '@mantine/core';
import { Notifications } from '@mantine/notifications';
import Index from './pages';
import { BrowserRouter } from 'react-router-dom';
import { Default } from 'core/themes/Default';
import PageLoadingComponent from '@components/PageLoadingComponent';
import { KanbanModalProvider } from 'kanban-design-system';
import { ZIndexNotification } from '@common/constants/ZIndexConstants';
import { getConfigs } from '@core/configs/Configs';

const appName = getConfigs().fullname;
function App() {
  useEffect(() => {
    const styles = ['font-size: 18px', 'color: red'];
    // eslint-disable-next-line no-console
    console.log(
      `%cWelcome to the \`${appName}\` project, developed by \`<PERSON><PERSON>ban\` team. Please notify the administrator if there is a problem`,
      styles.join(';'),
    );
  }, []);
  return (
    <>
      <MantineProvider theme={Default}>
        <KanbanModalProvider>
          <Notifications limit={5} zIndex={ZIndexNotification} />
          <PageLoadingComponent />
          <BrowserRouter>
            <Index />
          </BrowserRouter>
        </KanbanModalProvider>
      </MantineProvider>
    </>
  );
}

export default App;
