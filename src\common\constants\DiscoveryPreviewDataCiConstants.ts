export enum DiscoveryPreviewDataCiAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DUPLICATE = 'DUPLICATE',
}

export enum DiscoveryPreviewDataCiStatus {
  PENDING = 'PENDING',
}

export enum DiscoveryPreviewDataCiStatusIdentifier {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
}

export enum DiscoveryDataCiTransformStatus {
  DRAFT = 'DRAFT',
  WAITING = 'WAITING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  PENDING = 'PENDING',
  FAILED = 'FAILED',
  DELETED = 'DELETED',
}
