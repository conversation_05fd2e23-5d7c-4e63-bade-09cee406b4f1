.item {
    display: flex;
    align-items: center;
    border-radius: var(--mantine-radius-md);
    padding: var(--mantine-spacing-sm) var(--mantine-spacing-xl);
    background-color: light-dark(var(--mantine-color-white), var(--mantine-color-dark-5));
    margin-bottom: var(--mantine-spacing-sm);
}

.itemDragging {
    box-shadow: var(--mantine-shadow-sm);
}

.active {
    border: solid 1px var(--mantine-color-blue-7);
}

.table {
    height: 100%;
}

.table-th {
    position: 'sticky';
    top: 0;
    background-color: 'white';
    z-index: 1
}

.table-td {
    max-width: 50%;
    width: 50%;
    height: auto;
    padding: 0;
}

.table-tr {
    max-width: 50%;
    width: 50%;
    height: 100%;
}

.table-data {
    min-height: 509px;
    height: 100%;
}