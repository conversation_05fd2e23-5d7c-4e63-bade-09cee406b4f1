import type { CiRequestTypeEnum } from '@common/constants/CiDetail';
import type { ApiResponseDataBase } from '@core/api/ApiResponse';
import { BaseApi, ExtraConfigApiType } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import type { CiSdpRequestModel } from '@models/ChangeSdp';
import type { RelationShipOfBusinessView } from '@models/CIBusinessViews';
import type { CiHistoryModel } from '@models/CiHistory';
import type { CiRelationshipCountModel, CiRelationshipInfoModel } from '@models/CiRelationship';
import type { ConfigItemInfoModel, ConfigItemModel } from '@models/ConfigItem';
import type { ConfigItemAttrModel } from '@models/ConfigItemAttr';
import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
import type { FileUploadRequest, ImportFileCiResponse } from '@models/ImportFileResponse';

export type ConfigItemResponse = ApiResponseDataBase & ConfigItemModel;
export type ConfigItemResponsePagingResponse = PaginationResponseModel<ConfigItemResponse>;
export type CiRequestsPagingResponse = PaginationResponseModel<CiSdpRequestModel>;
export type ConfigItemInfoResponse = ApiResponseDataBase & ConfigItemInfoModel;

export class ConfigItemApi extends BaseApi {
  static baseUrl = BaseUrl.cis;

  static importFileCI(request: FileUploadRequest) {
    const formData = new FormData();
    formData.append('file', request.file);
    formData.append('mappingFields', request.mappingFields);
    formData.append('ciTypeId', request.ciTypeId.toString());
    if (request.rowImportSelected) {
      formData.append('rowImportSelected', JSON.stringify(request.rowImportSelected));
    }
    formData.append('importAll', String(request.importAll));
    formData.append('finalize', String(request.finalize));
    formData.append('formatDate', request.formatDate);
    if (request.isImportOldVersion) {
      return BaseApi.postData<ImportFileCiResponse>(`${this.baseUrl}/import/old`, formData);
    } else {
      return BaseApi.postData<ImportFileCiResponse>(`${this.baseUrl}/import`, formData);
    }
  }

  static saveInfo(data: ConfigItemInfoModel) {
    return BaseApi.postData<ConfigItemInfoResponse>(`${this.baseUrl}/info`, data);
  }
  static getById(id: number) {
    return BaseApi.getData<ConfigItemResponse>(`${this.baseUrl}/${id}`);
  }
  static getInfoById(id: number) {
    return BaseApi.getData<ConfigItemInfoModel>(`${this.baseUrl}/${id}/info`);
  }
  static getInfoByName(pagination: PaginationRequestModel<ConfigItemResponse>, ciTypeId?: number) {
    const url = ciTypeId ? `${this.baseUrl}/info?ciTypeId=${ciTypeId}` : `${this.baseUrl}/info`;

    return BaseApi.getData<ConfigItemResponsePagingResponse>(url, pagination, {}, { useLoading: false, useErrorNotification: true });
  }
  static getAllAttributes(id: number) {
    return BaseApi.getData<ConfigItemAttrModel[]>(`${this.baseUrl}/${id}/attributes`);
  }
  // static deleteById(id: number, extraConfigs?: ExtraConfigApiType) {
  //   return BaseApi.deleteData<ConfigItemResponse[]>(`${this.baseUrl}/${id}`, undefined, undefined, extraConfigs);
  // }
  static deleteByIds(ids: number[], extraConfigs?: ExtraConfigApiType) {
    return BaseApi.deleteData<ConfigItemResponse[]>(
      `${this.baseUrl}/batch`,
      {
        ids,
      },
      undefined,
      extraConfigs,
    );
  }
  // static saveNewAttributeCustoms(id: number, data: ConfigItemAttrCustomModel[]) {
  //   return BaseApi.postData<ConfigItemAttrCustomModel[]>(`${this.baseUrl}/${id}/attribute-customs`, data);
  // }

  static getAllHistories(id: number) {
    return BaseApi.getData<CiHistoryModel[]>(`${this.baseUrl}/${id}/histories`);
  }

  static getAllHistoriesByPaging(ciId: number, pagination: PaginationRequestModel<CiHistoryModel>) {
    return BaseApi.getData<PaginationResponseModel<CiHistoryModel>>(`${this.baseUrl}/${ciId}/histories/all`, { ...pagination }, {}, {});
  }
  static getAllDirectRelationship(id: number, level = 1) {
    return BaseApi.getData<CiRelationshipInfoModel[]>(`${this.baseUrl}/${id}/relationships/all?level=${level}`);
  }

  static getAllRelationshipByCiId(id: number, isFindFrom: boolean) {
    return BaseApi.getData<CiRelationshipInfoModel[]>(
      `${this.baseUrl}/${id}/relationships?isFindFrom=${isFindFrom}`,
      {},
      {},
      { useLoading: false, useErrorNotification: true },
    );
  }

  static ciCountRelationships(ciIds: number[]) {
    return BaseApi.postData<CiRelationshipCountModel[]>(`${this.baseUrl}/relationships/count`, ciIds);
  }

  static getDetailRelationships(ciRelationships: RelationShipOfBusinessView[]) {
    return BaseApi.postData<CiRelationshipInfoModel[]>(`${this.baseUrl}/relationships/detail`, ciRelationships);
  }
  static getAllRequestByCiId(
    id: number,
    pagination: PaginationRequestModel<CiSdpRequestModel>,
    requestTypeEnum: CiRequestTypeEnum,
    useLoading: boolean,
  ) {
    return BaseApi.getData<CiRequestsPagingResponse>(
      `${this.baseUrl}/${id}/sdp-requests`,
      { ...pagination, requestTypeEnum },
      {},
      {
        useLoading: useLoading,
        useErrorNotification: true,
      },
    );
  }
}
