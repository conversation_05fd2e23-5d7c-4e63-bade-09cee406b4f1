$background-color: var(--mantine-color-gray-0);
$border-color: var(--mantine-color-gray-3);
$key-bg-color: var(--mantine-color-blue-1);
$key-value-font-size: var(--text-fz, var(--mantine-font-size-sm));

.node-key {
    background-color: $key-bg-color;
    padding: 2px 5px;
    border-radius: 4px;
    font-weight: bold;
    font-size: $key-value-font-size;
}

.node-value {
    word-wrap: break-word;
    word-break: break-word;
    overflow: hidden;
    white-space: normal;
    text-overflow: ellipsis;
    display: inline-block;
    font-size: $key-value-font-size;
}



.rc-tree-scroll-container {
    background-color: var(--mantine-color-gray-0);
    min-height: 70vh;
    max-height: 70vh;
    overflow-y: scroll;
}


.custom-tabs {
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .mantine-Tabs-panel {
        flex: 1;
        height: 100%;
    }
}