import { AxiosError, type AxiosInterceptorManager, type AxiosResponse } from 'axios';
import type { AxiosResponseApi } from './ApiResponse';
import { isNil } from 'lodash';
import KeycloakService from '@core/auth/Keycloak';

export const handleResponseInterceptor = (response: AxiosInterceptorManager<AxiosResponse<any, any>>) => {
  response.use(
    (response: AxiosResponseApi<any>) => {
      if (!response?.data?.data && response?.data?.errorCode) {
        const axiosError = new AxiosError();
        axiosError.code = response.data.errorCode;
        axiosError.message = `${response.data.errorDescription}`;
        axiosError.response = response;
        return Promise.reject(axiosError);
      }
      return response;
    },
    (error: AxiosError) => {
      const response: any = error.response;
      if (response) {
        if (!isNil(response.data?.errorCode)) {
          error.code = response.data.errorCode;
          error.message = `${response.data.errorDescription}`;
        }
      }
      if (error.status === 401) {
        KeycloakService.doLogout();
      }
      return Promise.reject(error);
    },
  );
};
