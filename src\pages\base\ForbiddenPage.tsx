import React from 'react';
import styled from 'styled-components';

const Container = styled.div`
  text-align: center;
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: center;
`;
const Title = styled.div`
  font-size: 5rem;
  div {
    display: inline-block;
    span {
      margin-right: 1px;
      font-weight: 900;
      color: var(--mantine-color-primary-5);
    }
  }
`;
const Description = styled.div``;
export default function ForbiddenPage() {
  return (
    <>
      <Container>
        <Title>
          <div>
            <span>403</span>
          </div>
        </Title>
        <Description>{"You don't have permission to view this page"}</Description>
      </Container>
    </>
  );
}
