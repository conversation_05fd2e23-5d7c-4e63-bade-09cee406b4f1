import HeaderTitleComponent from '@components/HeaderTitleComponent';
import {
  KanbanButton,
  KanbanInput,
  KanbanNumberInput,
  KanbanSelect,
  KanbanSwitch,
  KanbanTabs,
  KanbanText,
  KanbanTextarea,
} from 'kanban-design-system';
import { IconEdit } from '@tabler/icons-react';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { Box, ComboboxItem, Flex, Group } from '@mantine/core';
import { buildCiAbsentRuleDetailUrl, ciAbsentRulePath, navigateTo } from '@common/utils/RouterUtils';
import { NotificationError, NotificationSuccess } from '@common/utils/NotificationUtils';
import { formatStandardName } from '@common/utils/StringUtils';
import { BreadcrumbComponent, UrlBaseCrumbData } from '@pages/admins/breadcrumb/BreadcrumbComponent';
import { CiAbsentRuleApi } from '@api/CiAbsentRuleApi';
import AuditLogPage from '../AuditLogPage';
import { EntityAuditLogTypeEnum } from '@models/AuditLog';
import { CiAbsentRule, CiAbsentRuleAction, getComboboxIntervalUnitEnum, IntervalUnitEnum, RuleStatus } from '@models/CiAbsentRule';
import { useGetCiTypes } from '@slices/CiTypesSlice';

const defaultRuleInfo: CiAbsentRule = {
  id: 0,
  name: '',
  status: RuleStatus.ENABLE,
  ciTypeId: 0,
  absenceThreshold: 0,
  disposedThreshold: 0,
  absenceThresholdUnit: IntervalUnitEnum.DAY,
  disposedThresholdUnit: IntervalUnitEnum.DAY,
};

type CiAbsentRuleError = {
  name?: string;
  absenceThreshold?: string;
  disposedThreshold?: string;
};

const validateName = (value: string | undefined) => {
  if (!value || value.trim().length === 0) {
    return 'Name cannot be empty';
  } else if (value.length > 255) {
    return 'Name must be less than 255 characters';
  }
  return undefined; // No error
};

const validateThreshold = (value: number | null | undefined): string | undefined => {
  if (value === null || value === undefined || value.toString().trim().length === 0) {
    return 'Priority cannot be empty';
  }
  const numValue = Number(value);
  if (isNaN(numValue) || numValue < 1 || numValue > 999) {
    return 'Invalid input: Priority value must be a number between 1 and 999';
  }
};

const titleView = (idNumber: number, action: string | null): string => {
  if (idNumber <= 0) {
    return 'Add new CI Absent Rule';
  }

  if (CiAbsentRuleAction.UPDATE === action) {
    return 'Update CI Absent Rule detail';
  }

  return 'CI Absent Rule detail';
};

export const CiAbsentRuleDetailPage = () => {
  const { ruleId } = useParams();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const action = searchParams.get('action') || CiAbsentRuleAction.VIEW;

  const ruleIdNumber = Number(ruleId);

  const [allowEdit, setAllowEdit] = useState(false);
  const [ruleInfo, setRuleInfo] = useState<CiAbsentRule>(defaultRuleInfo);
  const [errorMessage, setErrorMessage] = useState<CiAbsentRuleError>();

  useEffect(() => {
    setAllowEdit(CiAbsentRuleAction.VIEW !== action);
  }, [action]);

  const ciTypes = useGetCiTypes();

  const listCiTypeCombobox: ComboboxItem[] = useMemo(() => {
    const ciTypeData = ciTypes.data || [];
    const sortedCiTypeData = [...ciTypeData];

    sortedCiTypeData.sort((a, b) => a.name.localeCompare(b.name));

    return sortedCiTypeData.map((item) => {
      return {
        value: `${item.id}`,
        label: item.name,
      };
    });
  }, [ciTypes]);

  const findCiAbsentRuleById = useCallback(() => {
    if (ruleIdNumber > 0) {
      CiAbsentRuleApi.findCiAbsentRuleById(ruleIdNumber)
        .then((res) => {
          if (res && res.data) {
            setRuleInfo(res.data);
          }
        })
        .catch(() => {});
    }
  }, [ruleIdNumber]);

  useEffect(() => {
    findCiAbsentRuleById();
  }, [findCiAbsentRuleById]);

  const onSaveData = () => {
    const errorName = validateName(ruleInfo.name);
    const errorAbsenceThreshold = validateThreshold(ruleInfo.absenceThreshold);
    const errorDisposedThreshold = validateThreshold(ruleInfo.disposedThreshold);
    setErrorMessage({ name: errorName, absenceThreshold: errorAbsenceThreshold, disposedThreshold: errorDisposedThreshold });
    if (errorName || errorAbsenceThreshold || errorDisposedThreshold) {
      return;
    }

    if (ruleIdNumber > 0) {
      CiAbsentRuleApi.updateRule(ruleInfo, ruleIdNumber)
        .then((res) => {
          if (res.data) {
            NotificationSuccess({
              message: 'Updated successfully.',
            });
            navigate(buildCiAbsentRuleDetailUrl(ruleIdNumber, CiAbsentRuleAction.VIEW));
          } else {
            NotificationError({
              message: 'Error when update rule.',
            });
          }
        })
        .catch(() => {});
    } else {
      CiAbsentRuleApi.createNew(ruleInfo)
        .then((res) => {
          if (res.data) {
            NotificationSuccess({
              message: 'Created successfully.',
            });
            navigateTo(ciAbsentRulePath);
          } else {
            NotificationError({
              message: 'Error when create new rule.',
            });
          }
        })
        .catch(() => {});
    }

    return;
  };

  const locationCustomPaths = useMemo((): UrlBaseCrumbData => {
    const originPath = buildCiAbsentRuleDetailUrl(Number(ruleId), CiAbsentRuleAction.VIEW);
    let detailBread = '';
    if (Number(ruleId)) {
      detailBread = `${formatStandardName(action)} ${ruleInfo.name}`;
    } else if (CiAbsentRuleAction.CREATE === action) {
      detailBread = `${formatStandardName(action)}`;
    }

    return {
      [`/${ruleId}`]: {
        title: detailBread,
        href: originPath,
      },
    };
  }, [action, ruleId, ruleInfo.name]);

  return (
    <>
      {/* 4736 ci rule detail  */}
      <BreadcrumbComponent locationCustomPaths={locationCustomPaths} />

      <KanbanTabs
        configs={{
          variant: 'outline',
          defaultValue: 'INFO',
        }}
        tabs={{
          INFO: {
            title: 'Absent Rule',
            content: (
              <>
                <HeaderTitleComponent
                  title={titleView(ruleIdNumber, action)}
                  rightSection={
                    <Flex gap={10}>
                      <KanbanButton
                        variant='outline'
                        onClick={() => {
                          if (allowEdit) {
                            navigate(buildCiAbsentRuleDetailUrl(ruleIdNumber, CiAbsentRuleAction.VIEW));
                            return;
                          }
                          navigateTo(ciAbsentRulePath);
                        }}>
                        Cancel
                      </KanbanButton>
                      {!allowEdit && (
                        <>
                          <KanbanButton
                            leftSection={<IconEdit />}
                            onClick={() => {
                              navigate(buildCiAbsentRuleDetailUrl(ruleIdNumber, CiAbsentRuleAction.UPDATE));
                            }}>
                            Edit
                          </KanbanButton>
                        </>
                      )}
                      {allowEdit && (
                        <KanbanButton
                          onClick={() => {
                            onSaveData();
                          }}>
                          Save
                        </KanbanButton>
                      )}
                    </Flex>
                  }
                />

                <Box>
                  <KanbanInput
                    label='Name'
                    value={ruleInfo?.name || ''}
                    withAsterisk
                    disabled
                    maxLength={255}
                    error={errorMessage?.name}
                    onChange={(el) => {
                      const value = el.target.value ?? '';
                      const result = { ...ruleInfo, name: formatStandardName(value) };
                      setRuleInfo(result);
                      setErrorMessage({ ...errorMessage, name: undefined });
                    }}
                  />
                  <KanbanSelect
                    label='Apply CI Type'
                    placeholder='Select CI Type'
                    searchable
                    allowDeselect={false}
                    withAsterisk
                    disabled
                    autoChangeValueByOptions={false}
                    data={listCiTypeCombobox}
                    value={`${ruleInfo.ciTypeId}`}
                  />
                  <Box mb={'6'}>
                    <KanbanText fw={500}>Active</KanbanText>
                    <KanbanSwitch
                      color={'green'}
                      mt={'3'}
                      // disabled={!allowEdit}
                      checked={RuleStatus.ENABLE === ruleInfo.status}
                      onChange={(e) => {
                        if (!allowEdit) {
                          // disable but need show green color
                          return;
                        }
                        const value = e.currentTarget.checked;
                        const result = { ...ruleInfo, status: value ? RuleStatus.ENABLE : RuleStatus.DISABLE };
                        setRuleInfo(result);
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                    />
                  </Box>
                  <KanbanTextarea
                    label='Description'
                    value={ruleInfo?.description || ''}
                    autosize
                    minRows={2}
                    maxRows={5}
                    maxLength={2000}
                    disabled={!allowEdit}
                    onChange={(el) => {
                      const { target } = el;
                      const result = { ...ruleInfo, description: target.value };
                      setRuleInfo(result);
                    }}
                  />
                  <Group grow>
                    <KanbanNumberInput
                      label='Absence Threshold'
                      placeholder='Input number value'
                      description='Days after last discovery before CI is marked Absent.'
                      value={ruleInfo.absenceThreshold}
                      disabled={!allowEdit}
                      allowNegative={false}
                      allowDecimal={false}
                      clampBehavior='strict'
                      min={1}
                      max={999}
                      error={errorMessage?.absenceThreshold}
                      withAsterisk
                      onChange={(x) => {
                        const result = { ...ruleInfo, absenceThreshold: Number(x) };
                        setRuleInfo(result);
                        setErrorMessage({ ...errorMessage, absenceThreshold: undefined });
                      }}
                    />
                    <KanbanSelect
                      allowDeselect={false}
                      withAsterisk
                      mt={'xl'}
                      mb={'0'}
                      disabled={!allowEdit}
                      autoChangeValueByOptions={false}
                      data={getComboboxIntervalUnitEnum()}
                      defaultValue={IntervalUnitEnum.DAY}
                      value={ruleInfo.absenceThresholdUnit}
                      onChange={(value) => {
                        const valueEnum = value as IntervalUnitEnum;
                        const result = { ...ruleInfo, absenceThresholdUnit: valueEnum };
                        setRuleInfo(result);
                      }}
                    />
                  </Group>
                  <Group grow>
                    <KanbanNumberInput
                      label='Disposed Threshold'
                      placeholder='Input number value'
                      description='Days after being Absent before CI is marked Disposed.'
                      value={ruleInfo.disposedThreshold}
                      disabled={!allowEdit}
                      allowNegative={false}
                      allowDecimal={false}
                      clampBehavior='strict'
                      min={1}
                      max={999}
                      error={errorMessage?.disposedThreshold}
                      withAsterisk
                      onChange={(x) => {
                        const result = { ...ruleInfo, disposedThreshold: Number(x) };
                        setRuleInfo(result);
                        setErrorMessage({ ...errorMessage, disposedThreshold: undefined });
                      }}
                    />
                    <KanbanSelect
                      allowDeselect={false}
                      withAsterisk
                      mt={'xl'}
                      mb={'0'}
                      disabled={!allowEdit}
                      autoChangeValueByOptions={false}
                      data={getComboboxIntervalUnitEnum()}
                      defaultValue={IntervalUnitEnum.DAY}
                      value={ruleInfo.disposedThresholdUnit}
                      onChange={(value) => {
                        const valueEnum = value as IntervalUnitEnum;
                        const result = { ...ruleInfo, disposedThresholdUnit: valueEnum };
                        setRuleInfo(result);
                      }}
                    />
                  </Group>
                </Box>
              </>
            ),
          },
          HISTORY: {
            title: 'Config History',
            content: <AuditLogPage entityId={ruleIdNumber} auditLogType={EntityAuditLogTypeEnum.CI_ABSENT_RULE} />,
          },
        }}
      />
    </>
  );
};
CiAbsentRuleDetailPage.whyDidYouRender = true;
CiAbsentRuleDetailPage.displayName = 'CiAbsentRuleDetailPage';
export default CiAbsentRuleDetailPage;
