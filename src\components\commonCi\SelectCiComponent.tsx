import { ColumnType, KanbanComponentWith<PERSON>abel, KanbanTooltip, type KanbanComponentWithLabelProps } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { CiTypePickerComponent } from './SelectCiTypeComponent';
import { Container, Flex, Group, Space } from '@mantine/core';
import { KanbanButton } from 'kanban-design-system';
import { KanbanText } from 'kanban-design-system';
import type { ConfigItemResponse } from '@api/ConfigItemApi';
import CiListComponent, { CiListCustomTablePropsType } from './CiListComponent';
import { renderDateTime } from 'kanban-design-system';
import { KanbanCheckbox } from 'kanban-design-system';
import { KanbanModal } from 'kanban-design-system';
import { useGetCiTypes } from '@slices/CiTypesSlice';

export type SelectCiPickerComponentProps = {
  maxSelection?: number;
  value?: ConfigItemResponse[];
  onChange?: (items: ConfigItemResponse[]) => void;
  excludeSelectCiIds?: number[];
};

export const SelectCiPickerComponent = ({ excludeSelectCiIds, maxSelection, onChange, value }: SelectCiPickerComponentProps) => {
  const [currentCITypeId, setCurrentCiTypeId] = useState<number | undefined>(0);
  const [currentCiSelected, setCurrentCiSelected] = useState<ConfigItemResponse[]>([]);
  const allCiTypes = useGetCiTypes();
  useEffect(() => {
    if (value) {
      setCurrentCiSelected(value);
    }
  }, [value]);
  const columns: ColumnType<ConfigItemResponse>[] = useMemo(() => {
    return [
      {
        title: '',
        name: 'checkbox',
        sortable: false,
        customRender: (_, row) => {
          return (
            <>
              {!excludeSelectCiIds?.includes(row.id) && (
                <KanbanCheckbox
                  checked={currentCiSelected.some((x) => x.id === row.id)}
                  onChange={(e) => {
                    const checked = e.target.checked;
                    if (checked && maxSelection && currentCiSelected.length >= maxSelection) {
                      return;
                    }
                    let ciSelected = [...currentCiSelected];
                    if (checked) {
                      ciSelected.push(row);
                    } else {
                      ciSelected = ciSelected.filter((x) => x.id !== row.id);
                    }

                    if (onChange) {
                      onChange(ciSelected);
                    }
                    setCurrentCiSelected(ciSelected);
                  }}
                />
              )}
            </>
          );
        },
      },
      {
        title: 'Id',
        name: 'id',
        hidden: true,
      },
      {
        title: 'Name',
        name: 'name',
        width: '20%',
      },
      {
        title: 'Description',
        name: 'description',
        customRender: (data) => {
          return (
            <KanbanTooltip label={data} w={'500'} style={{ wordBreak: 'break-word' }} multiline>
              <KanbanText lineClamp={2} style={{ wordBreak: 'break-word' }}>
                {data}
              </KanbanText>
            </KanbanTooltip>
          );
        },
        width: '50%',
      },
      {
        title: 'Ci Type',
        name: 'ciTypeId',
        customRender: (data) => {
          const ciType = allCiTypes.data.find((x) => x.id === data);
          if (!ciType) {
            return <></>;
          }
          return (
            <Container maw={'200px'}>
              <KanbanTooltip label={ciType.name} maw={'500'} style={{ wordBreak: 'break-word' }} multiline>
                <KanbanButton size='compact-xs' radius={'lg'} maw={'100%'}>
                  <KanbanText truncate='end'>{ciType.name}</KanbanText>
                </KanbanButton>
              </KanbanTooltip>
            </Container>
          );
        },
      },
      {
        title: 'Created by',
        name: 'author',
      },
      {
        title: 'Created date',
        name: 'createdDate',
        customRender: renderDateTime,
      },
    ];
  }, [excludeSelectCiIds, currentCiSelected, maxSelection, onChange, allCiTypes.data]);
  const customTableProps: CiListCustomTablePropsType = useCallback(
    (currentProps) => {
      currentProps.actions = {};
      currentProps.columns = columns;
      return currentProps;
    },
    [columns],
  );

  return (
    <Group align='flex-start' w={'100%'}>
      <div style={{ flex: 1, maxHeight: '100%', maxWidth: '20%', overflow: 'auto' }}>
        <CiTypePickerComponent showAllCIs value={currentCITypeId} onChange={setCurrentCiTypeId} />
      </div>
      <div style={{ flex: 2, maxWidth: '80%' }}>
        <CiListComponent ciTypeId={currentCITypeId} customTableProps={customTableProps} />
      </div>
    </Group>
  );
};
SelectCiPickerComponent.whyDidYouRender = true;

export type SelectCiComponentProps = Omit<KanbanComponentWithLabelProps, 'onChange'> & {
  maxSelection?: number;
  onChange?: (items: ConfigItemResponse[]) => void;
  excludeSelectCiIds?: number[];
  withinPortal?: boolean;
};
export const SelectCiComponent = ({ excludeSelectCiIds, onChange: onChangeProps, withinPortal = true, ...props }: SelectCiComponentProps) => {
  const [openedSelect, { close: closeSelect, open: openSelect }] = useDisclosure(false);

  const [value, setValue] = useState<ConfigItemResponse[]>([]);

  const [newValue, setNewValue] = useState<ConfigItemResponse[]>([]);

  const onConfirmSelect = () => {
    setValue(newValue);
    if (onChangeProps) {
      onChangeProps(newValue);
    }
    closeSelect();
  };

  const onOpenSelect = () => {
    setNewValue(value);
    openSelect();
  };
  const names = useMemo(() => {
    const max = 3;
    let result = '';
    const length = Math.min(value.length, max);
    for (let i = 0; i < length; i++) {
      if (i !== 0) {
        result += ', ';
      }
      result += value[i].name;
    }
    if (value.length > max) {
      result += ',..';
    }

    return result;
  }, [value]);

  return (
    <>
      <KanbanModal
        withinPortal={withinPortal}
        onClose={closeSelect}
        size={'100%'}
        opened={openedSelect}
        title='Select CI'
        contentAbandon
        actions={
          <>
            <KanbanButton
              disabled={newValue.length === 0}
              color={'red'}
              onClick={() => {
                setValue([]);
                setNewValue([]);
              }}>
              Deselect all
            </KanbanButton>
            <KanbanButton disabled={newValue.length === 0} onClick={onConfirmSelect}>
              Confirm
            </KanbanButton>
          </>
        }>
        <SelectCiPickerComponent value={value} maxSelection={props.maxSelection} onChange={setNewValue} excludeSelectCiIds={excludeSelectCiIds} />
      </KanbanModal>

      <KanbanComponentWithLabel {...props}>
        <Flex align={'center'}>
          <KanbanButton size='xs' variant='outline' onClick={onOpenSelect}>
            Select Value
          </KanbanButton>
          <Space w={'md'} />
          <KanbanText size='xs' fw={'bold'}>
            {names || ''}
          </KanbanText>
        </Flex>
      </KanbanComponentWithLabel>
    </>
  );
};
SelectCiComponent.whyDidYouRender = true;
export default SelectCiComponent;
