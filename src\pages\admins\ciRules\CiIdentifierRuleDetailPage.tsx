import HeaderTitleComponent from '@components/HeaderTitleComponent';
import {
  KanbanButton,
  KanbanCheckbox,
  KanbanConfirmModal,
  KanbanInput,
  KanbanSelect,
  KanbanTabs,
  KanbanText,
  KanbanTextarea,
} from 'kanban-design-system';
import { IconEdit } from '@tabler/icons-react';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import { Box, ComboboxItem, Flex, Group } from '@mantine/core';
import CiIdentifierEntryTableComponent from './CiIdentifierEntryTableComponent';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import type { CiTypeAttributeDto } from './DnDAttributesComponent';
import { CiIdentifierEntryDto, CiIdentifierRuleAction, CiIdentifierRuleDto, RuleStatus } from '@models/CiIdentifierRule';
import { buildCiIdentifierRuleDetailUrl, buildCiIdentifierRuleUrl, navigateTo } from '@common/utils/RouterUtils';
import { CiIdentifierRuleApi } from '@api/CiIdentifierRuleApi';
import { NotificationError, NotificationSuccess } from '@common/utils/NotificationUtils';
import { v4 as uuid } from 'uuid';
import { useDisclosure } from '@mantine/hooks';
import { formatStandardName } from '@common/utils/StringUtils';
import { CiTypeAttributeDataType } from '@models/CiType';
import { DEFAULT_ID_FOR_CI_NAME } from '@common/constants/CommonConstants';
import CiIdentifierRuleLogPage from './CiIdentifierRuleLogPage';
import AuditLogPage from './AuditLogPage';
import { BreadcrumbComponent, UrlBaseCrumbData } from '../breadcrumb/BreadcrumbComponent';
import { EntityAuditLogTypeEnum } from '@models/AuditLog';

export const listStatus: ComboboxItem[] = [
  {
    value: RuleStatus.ENABLE,
    label: 'Enable',
  },
  {
    value: RuleStatus.DISABLE,
    label: 'Disable',
  },
];

const defaultRuleInfo: CiIdentifierRuleDto = {
  id: 0,
  name: '',
  active: RuleStatus.ENABLE,
  ciTypeId: 0,
  skipDuplicate: true,
  applyToChild: false,
};

type CiIdentifierRuleError = {
  name?: string;
  applyCiType?: string;
};

const validateName = (value: string | undefined) => {
  if (!value || value.trim().length === 0) {
    return 'Name cannot be empty';
  } else if (value.length > 255) {
    return 'Name must be less than 255 characters';
  }
  return undefined; // No error
};

const titleView = (idNumber: number, action: string | null): string => {
  if (idNumber <= 0) {
    return 'Add new CI Rule';
  }

  if (CiIdentifierRuleAction.UPDATE === action) {
    return 'Update CI Rule detail';
  }

  return 'CI Rule detail';
};

const listExcludeType: CiTypeAttributeDataType[] = [
  CiTypeAttributeDataType.DATE,
  CiTypeAttributeDataType.PICK_LIST,
  CiTypeAttributeDataType.REFERENCE,
];

export const CiIdentifierRuleDetailPage = () => {
  const { ruleId } = useParams();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const action = searchParams.get('action') || CiIdentifierRuleAction.VIEW;

  const ruleIdNumber = Number(ruleId);

  const [allowEdit, setAllowEdit] = useState(false);
  const [ciTypeId, setCiTypeId] = useState(0);
  const [listAttributes, setListAttributes] = useState<CiTypeAttributeDto[]>([]);
  const [ruleInfo, setRuleInfo] = useState<CiIdentifierRuleDto>(defaultRuleInfo);
  const [listDataEntry, setListDataEntry] = useState<CiIdentifierEntryDto[]>([]);
  const [listDataEntryUpdate, setListDataEntryUpdate] = useState<CiIdentifierEntryDto[]>([]);
  const [listDataEntryDelete, setListDataEntryDelete] = useState<CiIdentifierEntryDto[]>([]);
  const [errorMessage, setErrorMessage] = useState<CiIdentifierRuleError>();
  const [openedModalConfirmChange, { close: closeModalConfirmChange, open: openModalConfirmChange }] = useDisclosure(false);

  useEffect(() => {
    setAllowEdit(CiIdentifierRuleAction.VIEW !== action);
  }, [action]);

  const ciTypes = useGetCiTypes();

  const listCiTypeCombobox: ComboboxItem[] = useMemo(() => {
    const ciTypeData = ciTypes.data || [];
    const sortedCiTypeData = [...ciTypeData];

    sortedCiTypeData.sort((a, b) => a.name.localeCompare(b.name));

    return sortedCiTypeData.map((item) => {
      return {
        value: `${item.id}`,
        label: item.name,
      };
    });
  }, [ciTypes]);

  const fetchCiTypesAttribute = useCallback(() => {
    if (ciTypeId > 0) {
      ConfigItemTypeApi.getAllAttributes(
        ciTypeId,
        {
          useLoading: false,
        },
        true,
      )
        .then((res) => {
          if (res && res.data) {
            const attributes: CiTypeAttributeDto[] = res.data
              .filter((x) => x.type && !listExcludeType.includes(x.type))
              .map((item) => ({
                attributeId: item.id,
                nameAttribute: item.name,
                deleted: item.deleted,
              }));
            const defaultAttribute: CiTypeAttributeDto = { attributeId: DEFAULT_ID_FOR_CI_NAME, nameAttribute: 'CI Name' };
            setListAttributes([defaultAttribute, ...attributes]);
          }
        })
        .catch(() => {});
    }
  }, [ciTypeId]);

  useEffect(() => {
    fetchCiTypesAttribute();
  }, [fetchCiTypesAttribute]);

  useEffect(() => {
    if (ruleInfo && ruleInfo.ciTypeId > 0) {
      setCiTypeId(ruleInfo.ciTypeId);
    }
  }, [ruleInfo]);

  const findCiIdentifierRuleById = useCallback(() => {
    if (ruleIdNumber > 0) {
      CiIdentifierRuleApi.findCiIdentifierRuleById(ruleIdNumber)
        .then((res) => {
          if (res && res.data) {
            const { listEntries, ...ruleDetail } = res.data;
            const dataEntries: CiIdentifierEntryDto[] = (listEntries || []).map((item) => ({
              ...item,
              tempId: uuid(),
            }));
            setRuleInfo({ ...ruleDetail, listEntries: dataEntries });
            setListDataEntry(dataEntries);
          }
        })
        .catch(() => {});
    }
  }, [ruleIdNumber]);

  useEffect(() => {
    findCiIdentifierRuleById();
  }, [findCiIdentifierRuleById]);

  const onSaveData = () => {
    const errorName = validateName(ruleInfo.name);
    const errorCiType = !ruleInfo.ciTypeId ? 'Field is required.' : undefined;
    setErrorMessage({ name: errorName, applyCiType: errorCiType });
    if (errorName || errorCiType) {
      return;
    }
    const listEntriesCreate = listDataEntry.filter((x) => x.id === 0);
    const updatedData: CiIdentifierRuleDto = {
      ...ruleInfo,
      listEntriesCreate: listEntriesCreate,
      listEntriesUpdate: listDataEntryUpdate,
      listEntriesDelete: listDataEntryDelete,
    };

    if (ruleIdNumber > 0) {
      CiIdentifierRuleApi.updateRule(updatedData, ruleIdNumber)
        .then((res) => {
          if (res.data) {
            NotificationSuccess({
              message: 'Updated successfully.',
            });
            navigateTo(buildCiIdentifierRuleUrl());
          } else {
            NotificationError({
              message: 'Error when update rule.',
            });
          }
        })
        .catch(() => {});
    } else {
      CiIdentifierRuleApi.createNew(updatedData)
        .then((res) => {
          if (res.data) {
            NotificationSuccess({
              message: 'Created successfully.',
            });
            navigateTo(buildCiIdentifierRuleUrl());
          } else {
            NotificationError({
              message: 'Error when create new rule.',
            });
          }
        })
        .catch(() => {});
    }

    return;
  };

  const onChangeCiType = (val: string | null) => {
    const result = { ...ruleInfo, ciTypeId: Number(val) };
    setRuleInfo(result);
    setErrorMessage({ ...errorMessage, applyCiType: undefined });

    if (ruleIdNumber === 0 && listDataEntry.length > 0) {
      openModalConfirmChange();
    }
  };

  const actionChangeCiType = () => {
    closeModalConfirmChange();
    setListDataEntry([]);
    setListDataEntryUpdate([]);
    setListDataEntryDelete([]);
  };

  const locationCustomPaths = useMemo((): UrlBaseCrumbData => {
    const originPath = buildCiIdentifierRuleDetailUrl(Number(ruleId), CiIdentifierRuleAction.VIEW);
    let detailBread = '';
    if (Number(ruleId)) {
      detailBread = `${formatStandardName(action)} ${ruleInfo.name}`;
    } else if (CiIdentifierRuleAction.CREATE === action) {
      detailBread = `${formatStandardName(action)}`;
    }

    return {
      [`/${ruleId}`]: {
        title: detailBread,
        href: originPath,
      },
    };
  }, [action, ruleId, ruleInfo.name]);

  return (
    <>
      {/* 4736 ci rule detail  */}
      <BreadcrumbComponent locationCustomPaths={locationCustomPaths} />
      <KanbanConfirmModal
        title={'Confirm change Ci Type'}
        onConfirm={actionChangeCiType}
        textConfirm={'OK'}
        onClose={closeModalConfirmChange}
        opened={openedModalConfirmChange}
        modalProps={{
          size: 'sm',
        }}>
        <KanbanText>When changing CI Type, the entry list will be cleared. Are you sure?</KanbanText>
      </KanbanConfirmModal>

      <KanbanTabs
        configs={{
          variant: 'outline',
          defaultValue: 'INFO',
        }}
        tabs={{
          INFO: {
            title: 'CI Rule Detail',
            content: (
              <>
                <HeaderTitleComponent
                  title={titleView(ruleIdNumber, action)}
                  rightSection={
                    <Flex gap={10}>
                      <KanbanButton
                        variant='outline'
                        onClick={() => {
                          navigateTo(buildCiIdentifierRuleUrl());
                        }}>
                        Cancel
                      </KanbanButton>
                      {!allowEdit && (
                        <>
                          <KanbanButton
                            leftSection={<IconEdit />}
                            onClick={() => {
                              navigate(buildCiIdentifierRuleDetailUrl(ruleIdNumber, CiIdentifierRuleAction.UPDATE));
                            }}>
                            Edit
                          </KanbanButton>
                        </>
                      )}
                      {allowEdit && (
                        <KanbanButton
                          onClick={() => {
                            onSaveData();
                          }}>
                          Save
                        </KanbanButton>
                      )}
                    </Flex>
                  }
                />

                <Box>
                  <KanbanText my={'xs'}>General Information</KanbanText>
                  <KanbanInput
                    label='Name'
                    value={ruleInfo?.name || ''}
                    withAsterisk
                    disabled
                    maxLength={255}
                    error={errorMessage?.name}
                    onChange={(el) => {
                      const value = el.target.value ?? '';
                      const result = { ...ruleInfo, name: formatStandardName(value) };
                      setRuleInfo(result);
                      setErrorMessage({ ...errorMessage, name: undefined });
                    }}
                  />
                  <Group grow>
                    <KanbanSelect
                      label='Apply CI Type'
                      placeholder='Select CI Type'
                      searchable
                      allowDeselect={false}
                      withAsterisk
                      disabled
                      autoChangeValueByOptions={false}
                      error={errorMessage?.applyCiType}
                      data={listCiTypeCombobox}
                      value={`${ruleInfo.ciTypeId}`}
                      onChange={(x) => {
                        onChangeCiType(x);
                      }}
                    />
                  </Group>
                  <KanbanTextarea
                    label='Description'
                    value={ruleInfo?.description || ''}
                    autosize
                    minRows={2}
                    maxRows={5}
                    maxLength={2000}
                    disabled={!allowEdit}
                    onChange={(el) => {
                      const { target } = el;
                      const result = { ...ruleInfo, description: target.value };
                      setRuleInfo(result);
                    }}
                  />
                  {/* TODO */}
                  <Group grow display={'none'}>
                    <KanbanCheckbox
                      label='Apply to all child CI Type'
                      checked={ruleInfo.applyToChild}
                      disabled={!allowEdit}
                      onChange={(val) => {
                        const { target } = val;
                        const result = { ...ruleInfo, applyToChild: target.checked };
                        setRuleInfo(result);
                      }}
                    />
                    <KanbanCheckbox
                      label='Skip duplicate'
                      checked={ruleInfo.skipDuplicate}
                      disabled={!allowEdit}
                      onChange={(val) => {
                        const { target } = val;
                        const result = { ...ruleInfo, skipDuplicate: target.checked };
                        setRuleInfo(result);
                      }}
                    />
                  </Group>
                </Box>
                <Box mt={20}>
                  <CiIdentifierEntryTableComponent
                    listEntry={listDataEntry}
                    setListEntry={setListDataEntry}
                    listEntryUpdate={listDataEntryUpdate}
                    setListEntryUpdate={setListDataEntryUpdate}
                    listEntryDelete={listDataEntryDelete}
                    setListEntryDelete={setListDataEntryDelete}
                    allowEdit={allowEdit}
                    listAttributes={listAttributes}
                  />
                </Box>
              </>
            ),
          },
          HISTORY: {
            title: 'Config History',
            content: <AuditLogPage entityId={ruleIdNumber} auditLogType={EntityAuditLogTypeEnum.CI_IDENTIFIER_RULE} />,
          },
          LOG: {
            title: 'Rule Identify Log',
            content: (
              <>
                <CiIdentifierRuleLogPage></CiIdentifierRuleLogPage>
              </>
            ),
          },
        }}
      />
    </>
  );
};
CiIdentifierRuleDetailPage.whyDidYouRender = true;
CiIdentifierRuleDetailPage.displayName = 'CiIdentifierRuleDetailPage';
export default CiIdentifierRuleDetailPage;
