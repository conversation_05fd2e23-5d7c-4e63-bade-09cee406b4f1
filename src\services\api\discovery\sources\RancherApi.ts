import { ResourceConfigTypeEnum } from '@common/constants/ResourceConfigTypeEnum';
import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export class RancherApi extends BaseApi {
  static baseUrl = BaseUrl.ranchers;

  static findAllDeploymentByProjectId(projectId: string, resouceType: ResourceConfigTypeEnum) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/deployments/projects/${projectId}?configType=${resouceType}`);
  }

  static findAllNodes(resouceType: ResourceConfigTypeEnum) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/nodes?configType=${resouceType}`);
  }
  static findAllClusters(resouceType: ResourceConfigTypeEnum) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/clusters?configType=${resouceType}`);
  }
}
