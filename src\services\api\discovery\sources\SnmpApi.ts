import { DeviceTypeEnum } from '@common/constants/DeviceTypeEnum';
import { SnmpVersionEnum } from '@common/constants/SnmpVersionEnum';
import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export class SnmpApi extends BaseApi {
  static baseUrl = BaseUrl.snmps;

  static findAllDevice(deviceType: DeviceTypeEnum, snmpVersion: SnmpVersionEnum = SnmpVersionEnum.SNMP_V3) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/devices`, { deviceType: deviceType, snmpVersion: snmpVersion });
  }
}
