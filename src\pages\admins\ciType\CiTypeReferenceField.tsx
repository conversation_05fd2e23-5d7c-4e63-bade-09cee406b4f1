import { Box, ComboboxItem, ComboboxItemGroup, Group } from '@mantine/core';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { KanbanConfirmModal, KanbanIconButton, KanbanSelect, KanbanText } from 'kanban-design-system';
import { IconCircleMinus, IconCirclePlus } from '@tabler/icons-react';
import type { ConfigItemAttributeObject, CiTypeReferenceModel, ConfigItemTypeAttrModel } from '@models/ConfigItemTypeAttr';
import { CiTypeAttributeDataType, CiTypeColumn } from '@models/CiType';
import type { ConfigItemTypeInfoModel } from '@models/ConfigItemTypeInfo';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import { useDisclosure } from '@mantine/hooks';
import { ConfigItemTypeAttrApi } from '@api/ConfigItemTypeAttrApi';
import { NotificationError } from '@common/utils/NotificationUtils';
import { DEFAULT_ID_FOR_CI_NAME } from '@common/constants/CommonConstants';

type CiTypeReferenceFieldProps = {
  ciTypeId: number;
  ciAttributes: ConfigItemAttributeObject;
  setCiTypeInfoUpdate: React.Dispatch<React.SetStateAction<ConfigItemTypeInfoModel>>;
  setIsChange: (value: boolean) => void;
};

const ciTypeAttributeDataTypeExclude: CiTypeAttributeDataType[] = [CiTypeAttributeDataType.REFERENCE];

const mapAttributes = (attributes: ConfigItemTypeAttrModel[], referFields: CiTypeReferenceModel[]): ComboboxItem[] => {
  return attributes
    .filter((x) => x.type && !ciTypeAttributeDataTypeExclude.includes(x.type))
    .map((x) => ({
      label: x.name,
      value: x.id !== 0 ? `${x.id}` : x.tempId || '',
      disabled: referFields.some(
        (obj) => obj.ciTypeAttributeTempId && (obj.ciTypeAttributeTempId === `${x.id}` || obj.ciTypeAttributeTempId === x.tempId),
      ),
    }));
};

const defaultAttributeName: ConfigItemTypeAttrModel = {
  id: DEFAULT_ID_FOR_CI_NAME,
  name: 'CI Name',
  column: CiTypeColumn.LEFT,
  type: CiTypeAttributeDataType.TEXT,
};

const defaultReferField: CiTypeReferenceModel = {
  id: 0,
  ciTypeId: 0,
  ciTypeAttributeId: 0,
};

export const ConfigItemTypeReference = (props: CiTypeReferenceFieldProps) => {
  const { ciAttributes, ciTypeId, setCiTypeInfoUpdate, setIsChange } = props;
  const [referFields, setReferFields] = useState<CiTypeReferenceModel[]>([]);
  const [itemsDel, setItemsDel] = useState<CiTypeReferenceModel[]>([]);
  const [indexRemove, setIndexRemove] = useState<number>(-1);
  const [openedModalConfirmDel, { close: closeModalConfirmDel, open: openModalConfirmDel }] = useDisclosure(false);

  const fetchCiTypeReferenceFields = useCallback(() => {
    if (ciTypeId > 0) {
      ConfigItemTypeApi.getAllReferenceFields(ciTypeId)
        .then((res) => {
          if (res?.data && res.data.length > 0) {
            const resData: CiTypeReferenceModel[] = (res.data || []).map((item) => ({
              ...item,
              ciTypeAttributeTempId: `${item.ciTypeAttributeId}`,
            }));
            setReferFields(resData);
          } else {
            setReferFields([defaultReferField]);
          }
        })
        .catch(() => {});
    }
  }, [ciTypeId]);

  useEffect(() => {
    fetchCiTypeReferenceFields();
  }, [fetchCiTypeReferenceFields]);

  const attributes: ComboboxItemGroup[] = useMemo(() => {
    const defaultAttributes = [defaultAttributeName, ...ciAttributes.attributesDefault];
    const attributeItemsDefault: ComboboxItem[] = mapAttributes(defaultAttributes, referFields);
    const attributeItems: ComboboxItem[] = mapAttributes(ciAttributes.attributes, referFields);
    return [
      { group: 'Default attribute', items: attributeItemsDefault },
      { group: 'Custom attribute', items: attributeItems },
    ];
  }, [ciAttributes, referFields]);

  const onChangeSelect = (val: string | null, index: number) => {
    setIsChange(true);
    setReferFields((prevItems) => prevItems.map((item, idx) => (idx === index ? { ...item, ciTypeAttributeTempId: val || undefined } : item)));
  };

  const onDeleteReferenceField = (idxDel: number) => {
    const objDelete = referFields[idxDel];
    const valueDelete = objDelete.id !== 0 ? `${objDelete.ciTypeAttributeId}` : objDelete.ciTypeAttributeTempId;
    if (valueDelete) {
      setIndexRemove(idxDel);
      openModalConfirmDel();
    } else {
      onRemoveItem(idxDel);
    }
  };

  const onRemoveItem = (idxDel: number) => {
    const removeItem = referFields[idxDel];
    if (removeItem?.id > 0) {
      ConfigItemTypeAttrApi.findAllByCiTypeReferenceIdIn([removeItem.id])
        .then((res) => {
          if (res.data && res.data.length > 0) {
            const attributeReferInfo = res.data[0];
            NotificationError({
              message: `This Reference Field exists in used. Please, remove config field ${attributeReferInfo.name} in CI Type:  ${attributeReferInfo.ciTypeName}, before continue.`,
            });
            closeModalConfirmDel();
          } else {
            setItemsDel((oldObj) => [...oldObj, removeItem]);
            setIsChange(true);
            processOnRemoveItem(idxDel);
          }
        })
        .catch(() => {});
    } else {
      processOnRemoveItem(idxDel);
    }
  };

  const processOnRemoveItem = (idxDel: number) => {
    // remove item in reference fields
    if (referFields.length !== 1) {
      setReferFields((prevItems) => prevItems.filter((_, index) => index !== idxDel));
    } else {
      // reset value if is last element
      setReferFields([defaultReferField]);
    }

    setIndexRemove(-1);
    closeModalConfirmDel();
  };

  useEffect(() => {
    setCiTypeInfoUpdate((prev: ConfigItemTypeInfoModel) => ({
      ...prev,
      attributesReferenceDelete: itemsDel,
    }));
  }, [itemsDel, setCiTypeInfoUpdate]);

  useEffect(() => {
    setCiTypeInfoUpdate((prev: ConfigItemTypeInfoModel) => ({
      ...prev,
      attributesReferenceUpdate: referFields,
    }));
  }, [referFields, setCiTypeInfoUpdate]);

  return (
    <>
      {/* Modal confirm delete */}
      <KanbanConfirmModal
        title={'Confirm Delete'}
        onConfirm={() => {
          onRemoveItem(indexRemove);
        }}
        textConfirm={'OK'}
        onClose={closeModalConfirmDel}
        opened={openedModalConfirmDel}
        modalProps={{
          size: 'lg',
        }}>
        <p>Are you sure you want to delete this item?</p>
      </KanbanConfirmModal>
      {referFields.length > 0 ? (
        referFields.map((x, idx) => {
          const value = x.id !== 0 ? `${x.ciTypeAttributeId}` : x.ciTypeAttributeTempId;
          const isLastElement = idx === referFields.length - 1;
          const isDisableDelete = referFields.length === 1 && !value;
          return (
            <Box m={'sm'} key={idx}>
              <Group>
                <KanbanText>Referenced Field</KanbanText>
                <KanbanSelect
                  mb={0}
                  label=''
                  placeholder='Pick value'
                  searchable
                  data={attributes}
                  value={value || null}
                  autoChangeValueByOptions={false}
                  disabled={!!value}
                  onChange={(value) => {
                    onChangeSelect(value, idx);
                  }}
                />
                <KanbanIconButton
                  key={1}
                  title='Add'
                  variant='transparent'
                  disabled={!isLastElement} // disable Add if is not last element
                  onClick={() => {
                    setReferFields([...referFields, defaultReferField]);
                  }}>
                  <IconCirclePlus />
                </KanbanIconButton>
                <KanbanIconButton
                  key={2}
                  title='Remove'
                  variant='transparent'
                  disabled={isDisableDelete}
                  onClick={() => {
                    onDeleteReferenceField(idx);
                  }}>
                  <IconCircleMinus />
                </KanbanIconButton>
              </Group>
            </Box>
          );
        })
      ) : (
        <></>
      )}
    </>
  );
};
// ConfigItemTypeReference.whyDidYouRender = true;
export default ConfigItemTypeReference;
