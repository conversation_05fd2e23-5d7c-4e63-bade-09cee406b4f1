import React from 'react';
import { useLocation, useParams } from 'react-router-dom';
import CiDetailPage from './CiDetailPage';

export const CiPage = () => {
  const { ciId: ciIdParam, ciTypeId: ciTypeIdParam } = useParams();
  const ciTypeId = Number(ciTypeIdParam);
  const ciId = Number(ciIdParam);
  const location = useLocation();

  // Parse the query string to get query parameters
  const queryParams = new URLSearchParams(location.search);
  const isBusinessViewParam = queryParams.get('isBusinessView');
  const isBusinessView = isBusinessViewParam === 'true';
  return (
    <>
      <CiDetailPage ciTypeId={ciTypeId} ciId={ciId} isFromBusinessView={isBusinessView}></CiDetailPage>
    </>
  );
};

export default CiPage;
