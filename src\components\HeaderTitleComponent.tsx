import { KanbanTitle } from 'kanban-design-system';
import { Divider, Flex, Space } from '@mantine/core';
import type { TablerIconsProps } from '@tabler/icons-react';
import React from 'react';
import styled from 'styled-components';

const TitleWrapper = styled.div`
  & > svg {
    vertical-align: middle;
  }
  & > h4 {
    display: inline-block;
    margin-left: var(--mantine-spacing-xs);
    vertical-align: middle;
  }
`;
const RightSectionWrapper = styled.div``;
export type HeaderTitleComponentProps = {
  title: string;
  icon?: {
    icon: React.ComponentType<TablerIconsProps>;
    props?: TablerIconsProps;
  };
  rightSection?: React.ReactNode;
  leftSection?: React.ReactNode;
};

export const HeaderTitleComponent = (props: HeaderTitleComponentProps) => {
  return (
    <>
      <Flex justify={'space-between'}>
        <TitleWrapper>
          {props.icon && <props.icon.icon {...props.icon.props} />}
          <Flex justify={'space-between'} gap={'xs'}>
            <KanbanTitle order={4}>{props.title}</KanbanTitle>
            {props.leftSection && <>{props.leftSection}</>}
          </Flex>
        </TitleWrapper>
        {props.rightSection && <RightSectionWrapper>{props.rightSection}</RightSectionWrapper>}
      </Flex>
      <Space h={'xs'} />
      <Divider />
      <Space h={'xs'} />
    </>
  );
};

export default HeaderTitleComponent;
