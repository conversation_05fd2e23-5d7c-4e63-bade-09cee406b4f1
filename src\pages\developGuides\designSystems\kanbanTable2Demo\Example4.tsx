import React, { useMemo, useState } from 'react';
import { KanbanTable2, KanbanTable2ColumnDef, KanbanTable2Row, useKanbanTable2 } from 'kanban-design-system';

export type Person = {
  firstName: string;
  lastName: string;
  city: string;
};

export const initData = [
  {
    firstName: 'Dylan',
    lastName: 'Murray',
    city: 'East Daphne',
  },
  {
    firstName: 'Raquel',
    lastName: 'Kohler',
    city: 'Columbus',
  },
  {
    firstName: 'Ervin',
    lastName: 'Reinger',
    city: 'South Linda',
  },
  {
    firstName: 'Brittany',
    lastName: 'McCullough',
    city: 'Lincoln',
  },
  {
    firstName: 'Branson',
    lastName: 'Frami',
    city: 'Charleston',
  },
  {
    firstName: 'Dylan',
    lastName: 'Murray',
    city: 'East Daphne',
  },
  {
    firstName: 'Raquel',
    lastName: 'Kohler',
    city: 'Columbus',
  },
  {
    firstName: '<PERSON>rvin',
    lastName: 'Reinger',
    city: 'South Linda',
  },
  {
    firstName: 'Brittany',
    lastName: 'McCullough',
    city: 'Lincoln',
  },
  {
    firstName: 'Branson',
    lastName: 'Frami',
    city: 'Charleston',
  },
  {
    firstName: '<PERSON>',
    lastName: 'Murray',
    city: 'East Daphne',
  },
  {
    firstName: 'Raquel',
    lastName: 'Kohler',
    city: 'Columbus',
  },
  {
    firstName: 'Ervin',
    lastName: 'Reinger',
    city: 'South Linda',
  },
  {
    firstName: 'Brittany',
    lastName: 'McCullough',
    city: 'Lincoln',
  },
  {
    firstName: 'Branson',
    lastName: 'Frami',
    city: 'Charleston',
  },
  {
    firstName: 'Dylan',
    lastName: 'Murray',
    city: 'East Daphne',
  },
  {
    firstName: 'Raquel',
    lastName: 'Kohler',
    city: 'Columbus',
  },
  {
    firstName: 'Ervin',
    lastName: 'Reinger',
    city: 'South Linda',
  },
  {
    firstName: 'Brittany',
    lastName: 'McCullough',
    city: 'Lincoln',
  },
  {
    firstName: 'Branson',
    lastName: 'Frami',
    city: 'Charleston',
  },
  {
    firstName: 'Dylan',
    lastName: 'Murray',
    city: 'East Daphne',
  },
  {
    firstName: 'Raquel',
    lastName: 'Kohler',
    city: 'Columbus',
  },
  {
    firstName: 'Ervin',
    lastName: 'Reinger',
    city: 'South Linda',
  },
  {
    firstName: 'Brittany',
    lastName: 'McCullough',
    city: 'Lincoln',
  },
  {
    firstName: 'Branson',
    lastName: 'Frami',
    city: 'Charleston',
  },
];

const Example = () => {
  const columns = useMemo<KanbanTable2ColumnDef<Person>[]>(
    () => [
      {
        accessorKey: 'firstName',
        header: 'First Name',
      },
      {
        accessorKey: 'lastName',
        header: 'Last Name',
      },
      {
        accessorKey: 'city',
        header: 'City',
      },
    ],
    [],
  );

  const [data, setData] = useState(() => initData);

  const table = useKanbanTable2({
    columns,
    data,
    enableColumnActions: false,
    enableColumnFilters: false,
    enablePagination: false,
    enableSorting: false,

    enableTopToolbar: false,
    mantineTableProps: {
      highlightOnHover: false,
      withColumnBorders: true,
      style: {
        'thead > tr': {
          backgroundColor: 'inherit',
        },
        'thead > tr > th': {
          backgroundColor: 'inherit',
        },
        'tbody > tr > td': {
          backgroundColor: 'inherit',
        },
      },
    },
  });
  const table2 = useKanbanTable2({
    columns,
    data,
    autoResetPageIndex: true,
    enableSorting: false,
    enableRowOrdering: true,
    mantineTableContainerProps: { style: { maxHeight: 700 } },
    mantineRowDragHandleProps: ({ table }) => {
      return {
        onDragEnd: () => {
          const { draggingRow, hoveredRow } = table.getState();
          if (hoveredRow && draggingRow) {
            data.splice((hoveredRow as KanbanTable2Row<Person>).index, 0, data.splice(draggingRow.index, 1)[0]);
            setData([...data]);
          }
        },
      };
    },
  });

  return (
    <>
      <KanbanTable2 table={table2} />
      <KanbanTable2 table={table} />
    </>
  );
};

export default Example;
