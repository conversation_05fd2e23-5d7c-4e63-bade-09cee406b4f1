import React, { useState, useImperativeHandle, forwardRef, useEffect, useCallback } from 'react';
import { ActionIcon, Box, Center, Divider, Flex, Paper, ScrollArea, Text } from '@mantine/core';
import { IconSearch, IconSquareArrowLeft, IconSquareArrowRight } from '@tabler/icons-react';
import { KanbanInput, KanbanText } from 'kanban-design-system';
import styleCss from './CiIdentifierRule.module.css';
import { useDraggableInPortal } from '@common/hooks/useDraggableInPortal';
import { DragDropContext, Draggable, Droppable, DropResult } from '@hello-pangea/dnd';

export type CiTypeAttributeDto = {
  attributeId: number;
  nameAttribute: string;
  selected?: boolean;
  deleted?: boolean;
};

type DnDAttributesComponentProps = {
  attributeOfCis: CiTypeAttributeDto[];
  selectedAttributes: CiTypeAttributeDto[];
  isView?: boolean;
  errorMessage?: string;
};

const SOURCE_FIELD = 'SOURCE_FIELD';
const TARGET_FIELD = 'TARGET_FIELD';

export type DnDAttributesComponentMethods = {
  getSelectedAttributes: () => CiTypeAttributeDto[];
};

export const DnDAttributesComponent = forwardRef<DnDAttributesComponentMethods, DnDAttributesComponentProps>((props, ref) => {
  const { attributeOfCis, errorMessage, isView, selectedAttributes } = props;

  const [availableColumns, setAvailableColumns] = useState<CiTypeAttributeDto[]>([]);
  const [selectedColumns, setSelectedColumns] = useState<CiTypeAttributeDto[]>(selectedAttributes);
  const [availableSearchColumns, setAvailableSearchColumns] = useState<CiTypeAttributeDto[]>([]);
  const [selectedSearchColumns, setselectedSearchColumns] = useState<CiTypeAttributeDto[]>([]);

  const renderDraggable = useDraggableInPortal();

  const getSelectedAttributes = useCallback(() => {
    return selectedColumns;
  }, [selectedColumns]);

  useImperativeHandle<any, DnDAttributesComponentMethods>(
    ref,
    () => ({
      getSelectedAttributes: getSelectedAttributes,
    }),
    [getSelectedAttributes],
  );

  useEffect(() => {
    if (attributeOfCis.length > 0) {
      let availableColumnsFilter: CiTypeAttributeDto[] = attributeOfCis;
      if (selectedAttributes && selectedAttributes.length > 0) {
        setSelectedColumns(selectedAttributes);
        setselectedSearchColumns(selectedAttributes);
        availableColumnsFilter = availableColumnsFilter.filter((item) => selectedAttributes.every((obj) => obj.attributeId !== item.attributeId));
      }
      availableColumnsFilter = availableColumnsFilter.filter((x) => !x.deleted);
      availableColumnsFilter.sort((a, b) => a.attributeId - b.attributeId);
      setAvailableColumns(availableColumnsFilter);
      setAvailableSearchColumns(availableColumnsFilter);
    }
  }, [attributeOfCis, selectedAttributes]);

  const handleClickAvailableColumns = (attributeId: number) => {
    if (isView) {
      return;
    }

    setAvailableSearchColumns((prevState) =>
      prevState.map((column) => (column.attributeId === attributeId ? { ...column, selected: !column.selected } : column)),
    );
  };

  const handleClickSelectedColumns = (attributeId: number) => {
    if (isView) {
      return;
    }

    setselectedSearchColumns((prevState) =>
      prevState.map((column) => (column.attributeId === attributeId ? { ...column, selected: !column.selected } : column)),
    );
  };

  const availableColumnDatas = availableSearchColumns.map((item, index) => (
    <Draggable key={item.attributeId} index={index} draggableId={`source_attribute_${item.attributeId}`} isDragDisabled={isView}>
      {renderDraggable((provided) => (
        <Paper
          key={item.attributeId}
          className={item.selected ? styleCss.active : styleCss['attribute-item']}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          ref={provided.innerRef}
          p='xs'
          mb='sm'
          radius='sm'
          shadow='md'
          withBorder
          onClick={() => handleClickAvailableColumns(item.attributeId)}
          onDoubleClick={() => {
            handleOnDoubleClick(item.attributeId, true);
          }}>
          {item.deleted ? <Text c={'red'}>{item.nameAttribute} - (Deleted)</Text> : <Text>{item.nameAttribute}</Text>}
        </Paper>
      ))}
    </Draggable>
  ));

  const selectedColumnDatas = selectedSearchColumns.map((item, index) => (
    <Draggable key={item.attributeId} index={index} draggableId={`target_attribute_${item.attributeId}`} isDragDisabled={isView}>
      {renderDraggable((provided) => (
        <Paper
          key={item.attributeId}
          className={item.selected ? styleCss.active : styleCss['attribute-item']}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          ref={provided.innerRef}
          p='xs'
          mb='sm'
          radius='sm'
          shadow='md'
          withBorder
          onClick={() => handleClickSelectedColumns(item.attributeId)}
          onDoubleClick={() => {
            handleOnDoubleClick(item.attributeId, false);
          }}>
          {item.deleted ? <Text c={'red'}>{item.nameAttribute} - (Deleted)</Text> : <Text>{item.nameAttribute}</Text>}
        </Paper>
      ))}
    </Draggable>
  ));

  // event click button move column
  const handleClickBtn = (isAvailableColumns: boolean) => {
    if (isAvailableColumns) {
      const newlySelectedColumns = availableSearchColumns
        .filter((x) => x.selected)
        .map((x) => {
          return { ...x, selected: false };
        });

      setSelectedColumns((prevSelectedColumns) => [...prevSelectedColumns, ...newlySelectedColumns]);
      setselectedSearchColumns((prevSelectedColumns) => [...prevSelectedColumns, ...newlySelectedColumns]);

      const remainingAvailableColumns = availableColumns.filter(
        (column) => !newlySelectedColumns.some((newColumn) => newColumn.attributeId === column.attributeId),
      );
      setAvailableColumns(remainingAvailableColumns);

      const remainingAvailableSearchColumns = availableSearchColumns.filter(
        (column) => !newlySelectedColumns.some((newColumn) => newColumn.attributeId === column.attributeId),
      );
      setAvailableSearchColumns(remainingAvailableSearchColumns);
    } else {
      const newlyAvailableColumns = selectedSearchColumns
        .filter((x) => x.selected)
        .map((x) => {
          return { ...x, selected: false };
        });
      setAvailableColumns((prevAvailableColumns) => [...prevAvailableColumns, ...newlyAvailableColumns]);
      setAvailableSearchColumns((prevAvailableColumns) => [...prevAvailableColumns, ...newlyAvailableColumns]);

      const remainingSelectedColumns = selectedColumns.filter(
        (column) => !newlyAvailableColumns.some((newColumn) => newColumn.attributeId === column.attributeId),
      );
      setSelectedColumns(remainingSelectedColumns);

      const remainingSelectedSearchColumns = selectedSearchColumns.filter(
        (column) => !newlyAvailableColumns.some((newColumn) => newColumn.attributeId === column.attributeId),
      );
      setselectedSearchColumns(remainingSelectedSearchColumns);
    }
  };

  // event drag drop
  const handleDragOrDropDestinationEnd = (result: DropResult) => {
    const { destination, source } = result;

    if (isView || !destination) {
      return;
    }
    if (source.droppableId === destination.droppableId && source.index === destination.index) {
      return;
    }

    const isSourceAvailable = SOURCE_FIELD === source.droppableId;
    const isDestinationSelected = TARGET_FIELD === destination.droppableId;

    let sourceList = isSourceAvailable ? [...availableSearchColumns] : [...selectedSearchColumns];
    let destinationList = isDestinationSelected ? [...selectedSearchColumns] : [...availableSearchColumns];

    const [movedItem] = sourceList.splice(source.index, 1);

    const isSortItem = source.droppableId === destination.droppableId;
    if (isSortItem) {
      sourceList = sourceList.filter((item) => item.attributeId !== movedItem.attributeId);
      sourceList.splice(destination.index, 0, movedItem);
    } else {
      destinationList = destinationList.filter((item) => item.attributeId !== movedItem.attributeId);
      destinationList.splice(destination.index, 0, movedItem);
    }

    if (isDestinationSelected) {
      if (isSortItem) {
        setSelectedColumns(sourceList);
      } else {
        setSelectedColumns(destinationList);
        setAvailableColumns((prev) => prev.filter((item) => item.attributeId !== movedItem.attributeId));
        setselectedSearchColumns(destinationList);
        setAvailableSearchColumns(sourceList);
      }
    } else {
      if (isSortItem) {
        setAvailableColumns(sourceList);
      } else {
        setAvailableColumns(destinationList);
        setSelectedColumns((prev) => prev.filter((item) => item.attributeId !== movedItem.attributeId));
        setselectedSearchColumns(sourceList);
        setAvailableSearchColumns(destinationList);
      }
    }
  };

  // event double click
  const handleOnDoubleClick = (attributeId: number, isAvailableColumn: boolean) => {
    if (isView) {
      return;
    }
    if (isAvailableColumn) {
      const newlySelectedColumns = availableSearchColumns
        .filter((x) => x.attributeId === attributeId)
        .map((x) => {
          return { ...x, selected: false };
        });
      setSelectedColumns((prevSelectedColumns) => [...prevSelectedColumns, ...newlySelectedColumns]);
      setselectedSearchColumns((prevSelectedColumns) => [...prevSelectedColumns, ...newlySelectedColumns]);

      const remainingAvailableColumns = availableColumns.filter(
        (column) => !newlySelectedColumns.some((newColumn) => newColumn.attributeId === column.attributeId),
      );
      setAvailableColumns(remainingAvailableColumns);
      setAvailableSearchColumns(remainingAvailableColumns);
    } else {
      const newlyAvailableColumns = selectedSearchColumns
        .filter((x) => x.attributeId === attributeId)
        .map((x) => {
          return { ...x, selected: false };
        });
      setAvailableColumns((prevAvailableColumns) => [...prevAvailableColumns, ...newlyAvailableColumns]);
      setAvailableSearchColumns((prevAvailableColumns) => [...prevAvailableColumns, ...newlyAvailableColumns]);

      const remainingSelectedColumns = selectedSearchColumns.filter(
        (column) => !newlyAvailableColumns.some((newColumn) => newColumn.attributeId === column.attributeId),
      );
      setSelectedColumns(remainingSelectedColumns);
      setselectedSearchColumns(remainingSelectedColumns);
    }
  };

  return (
    <>
      <Divider my={10} />
      <DragDropContext onDragEnd={handleDragOrDropDestinationEnd}>
        <Flex>
          <Paper w={'50%'}>
            <Text fw={500} mb='sm'>
              Available attribute
            </Text>
            <Box>
              <Droppable droppableId={SOURCE_FIELD}>
                {(provided) => (
                  <Paper withBorder p='xs' {...provided.droppableProps} ref={provided.innerRef}>
                    <KanbanInput
                      placeholder='Search here'
                      leftSection={<IconSearch size='1rem' />}
                      size='xs'
                      onChange={(data) => {
                        if (data.target.value?.trim()?.length) {
                          const columnSearch = availableColumns.filter(
                            (obj) =>
                              !selectedColumns.some((map) => map.attributeId === obj.attributeId) &&
                              obj.nameAttribute.toLowerCase().includes(data.target.value.toLowerCase()),
                          );

                          setAvailableSearchColumns(columnSearch);
                        } else {
                          setAvailableSearchColumns([
                            ...availableColumns.filter((obj) => !selectedColumns.some((map) => map.attributeId === obj.attributeId)),
                          ]);
                        }
                      }}></KanbanInput>
                    <ScrollArea h={350}>
                      {availableColumnDatas}
                      {provided.placeholder}
                    </ScrollArea>
                  </Paper>
                )}
              </Droppable>
            </Box>
          </Paper>

          <Center w={'8%'}>
            <Flex direction='column'>
              <ActionIcon size={'lg'} variant='transparent' onClick={() => handleClickBtn(true)} disabled={isView}>
                <IconSquareArrowRight size={'lg'} />
              </ActionIcon>
              <ActionIcon size={'lg'} variant='transparent' onClick={() => handleClickBtn(false)} disabled={isView}>
                <IconSquareArrowLeft size={'lg'} />
              </ActionIcon>
            </Flex>
          </Center>
          <Paper w={'50%'}>
            <Text fw={500} mb='sm'>
              Selected attribute
            </Text>
            <Box>
              <Droppable droppableId={TARGET_FIELD}>
                {(provided) => (
                  <Paper
                    className={errorMessage ? styleCss['invalid-selected'] : styleCss['selected-attributes']}
                    p='xs'
                    {...provided.droppableProps}
                    ref={provided.innerRef}>
                    <KanbanInput
                      placeholder='Search here'
                      leftSection={<IconSearch size='1rem' />}
                      size='xs'
                      onChange={(data) => {
                        if (data.target.value?.trim()?.length) {
                          const columnSearch = selectedColumns.filter(
                            (obj) =>
                              !availableColumns.some((map) => map.attributeId === obj.attributeId) &&
                              obj.nameAttribute.toLowerCase().includes(data.target.value.toLowerCase()),
                          );

                          setselectedSearchColumns(columnSearch);
                        } else {
                          setselectedSearchColumns([
                            ...selectedColumns.filter((obj) => !availableColumns.some((map) => map.attributeId === obj.attributeId)),
                          ]);
                        }
                      }}></KanbanInput>
                    <ScrollArea h={350}>
                      {selectedColumnDatas}
                      {provided.placeholder}
                    </ScrollArea>
                  </Paper>
                )}
              </Droppable>
            </Box>
            {errorMessage && (
              <KanbanText size='xs' mt={3} c={'red'}>
                {errorMessage}
              </KanbanText>
            )}
          </Paper>
        </Flex>
      </DragDropContext>
    </>
  );
});

DnDAttributesComponent.whyDidYouRender = false;
DnDAttributesComponent.displayName = 'DnDAttributesComponent';
export default DnDAttributesComponent;
