import type { CiTypeAttributeDataType } from './CiType';

export type CiTypeRelationModel = {
  id: number;
  fromCiType: number;
  toCiType: number;
  relationshipId: number;
  fromCiTypeName?: string;
  toCiTypeName?: string;
  tempId?: string;
  referCiTypeId: number;
};

export type CiTypeRelationAttrModel = {
  id: number;
  ciTypeRelationshipId: number;
  name: string;
  description?: string;
  type?: CiTypeAttributeDataType;
  options?: string;
  tempId?: string;
  referId?: string;
};

export default 1;
