import { DiscoveryPreviewDataCiApi } from '@api/discovery/DiscoveryPreviewDataCiApi';
import { buildDiscoveryDataCiDetailPage } from '@common/utils/RouterUtils';
import { DiscoveryPreviewDataCiDetailModel } from '@models/DiscoveryPreviewDataCi';
import { ColumnType, KanbanButton, KanbanTable, KanbanTableProps, renderDateTime } from 'kanban-design-system';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';

interface CiDiscoveryProps {
  ciId: number;
  ciTypeId: number;
  isViewPage: boolean;
}

const CiDiscovery = ({ ciId }: CiDiscoveryProps) => {
  const [listCiAttr, setListCiAttr] = useState<DiscoveryPreviewDataCiDetailModel[]>([]);
  const navigate = useNavigate();

  const fetchDiscoveryDataCiDetail = useCallback(() => {
    DiscoveryPreviewDataCiApi.getLatestCiAttributeChange(Number(ciId))
      .then((res) => {
        const resData = res.data;
        if (resData) {
          setListCiAttr(resData);
        }
      })
      .catch(() => {});
  }, [ciId]);
  useEffect(() => {
    fetchDiscoveryDataCiDetail();
  }, [fetchDiscoveryDataCiDetail]);
  const columns: ColumnType<DiscoveryPreviewDataCiDetailModel>[] = useMemo(() => {
    const cols: ColumnType<DiscoveryPreviewDataCiDetailModel>[] = [
      {
        title: 'Attribute',
        name: 'ciTypeAttributeName',
        width: '10%',
      },
      {
        title: 'Live value',
        name: 'ciAttributeLiveValue',
      },

      {
        title: 'Discovered value',
        name: 'ciAttributeValue',
      },
      {
        title: 'Time',
        name: 'discoveredTime',

        customRender: renderDateTime,
      },
      {
        title: 'Discovery Preview CI',
        name: 'discoveryDataCiId',
        hidden: true,
        customRender: (_: string, rowData: DiscoveryPreviewDataCiDetailModel) => {
          return (
            <KanbanButton
              size='compact-xs'
              variant={'subtle'}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(buildDiscoveryDataCiDetailPage(rowData.discoveryDataCiId || 0));
              }}>
              {rowData.discoveryDataCiId}
            </KanbanButton>
          );
        },
      },
    ];

    return cols;
  }, [navigate]);
  const tableProps: KanbanTableProps<DiscoveryPreviewDataCiDetailModel> = useMemo(() => {
    return {
      showNumericalOrderColumn: true,
      columns: columns,
      data: listCiAttr,
      key: columns,
    };
  }, [columns, listCiAttr]);
  return (
    <>
      <KanbanTable {...tableProps} />
    </>
  );
};

export default CiDiscovery;
