export type DiscoveryTransformMapModel = {
  id?: number;
  name: string;
  stagingTableId: number;
  stagingTableName: string;
  ciTypeId: number;
  ciTypeName: string;
  enforceMandatoryType?: string;
  isActive?: boolean;
  details?: DiscoveryTransformMapDetailModel[];
};

export type DiscoveryTransformMapDetailModel = {
  id?: number;
  transformMapId: number;
  discoveryStagingStructureId: number;
  columnName: string;
  attributeId: number;
  attributeName: string;
  position: number;
  attributeDeleted?: boolean;
  isStatic: boolean;
  staticValue?: string;
  listChildren?: DiscoveryTransformMapDetailModel[];
  _targetRowSpan?: number;
};

export type DragTranfromMapType = {
  id: number | string;
  label: string;
  selected: boolean;
  hashId?: string;
  deleted?: boolean;
  isStatic: boolean;
  staticValue?: string;
  listChildren?: DragTranfromMapType[];
};
