import { escapeRegex } from '@common/utils/CommonUtils';
import React from 'react';

export const DEFAULT_HIGHTLIGHT_PRETAG = '[[[hl]]]';
export const DEFAULT_HIGHTLIGHT_POSTTAG = '[[[/hl]]]';

export type RenderHightLightValueProps = {
  text?: string;
  highlightStyle?: React.CSSProperties;

  preTag?: string;
  postTag?: string;
};
export const RenderHighlightValue = ({
  highlightStyle,
  postTag = DEFAULT_HIGHTLIGHT_POSTTAG,
  preTag = DEFAULT_HIGHTLIGHT_PRETAG,
  text,
}: RenderHightLightValueProps) => {
  if (!text) {
    return null;
  }
  const preTagEscape = escapeRegex(preTag);
  const postTagEscape = escapeRegex(postTag);
  const parts1 = text.split(new RegExp(`(${preTagEscape}|${postTagEscape})`, 'g'));
  let isHighlight = false;
  return (
    <>
      {parts1.map((part, index) => {
        if (part === preTag) {
          isHighlight = true;
          return null;
        }
        if (part === postTag) {
          isHighlight = false;
          return null;
        }
        return isHighlight ? (
          <mark key={index} color='yellow' style={highlightStyle}>
            {part}
          </mark>
        ) : (
          <span key={index}>{part}</span>
        );
      })}
    </>
  );
};
