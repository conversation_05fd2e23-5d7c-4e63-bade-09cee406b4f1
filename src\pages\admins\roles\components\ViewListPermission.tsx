import { Box, Flex, Grid, ScrollArea } from '@mantine/core';
import { KanbanCheckbox, KanbanInput } from 'kanban-design-system';
import React, { useMemo, useState } from 'react';
import { IconSearch } from '@tabler/icons-react';
import type { ConfigItemPermissionOtherDto } from '@models/ConfigItemTypePermission';
export type ListPermissionProps = {
  permissions: ConfigItemPermissionOtherDto[];
  updateOtherPermissions: (adminPermissions: ConfigItemPermissionOtherDto[]) => void;
};
export const ViewListPermission: React.FC<ListPermissionProps> = ({ permissions, updateOtherPermissions }) => {
  const [search, setSearch] = useState('');
  const [selectAll, setSelectAll] = useState(false);
  const handleCheckboxChange = (changedPermission: ConfigItemPermissionOtherDto) => {
    const newPermissions = permissions.map((item) => (item.action === changedPermission.action ? { ...item, isChecked: !item.isChecked } : item));
    updateOtherPermissions(newPermissions);
    const allChecked = newPermissions.every((item) => item.isChecked);
    setSelectAll(allChecked);
  };
  const handleSelectAllChange = () => {
    const newSelectAllState = !selectAll;
    setSelectAll(newSelectAllState);

    // Only update the filtered permissions
    const updatedPermissions = permissions.map((item) => {
      const isFiltered = filteredPermissions.some((filteredItem) => filteredItem.action === item.action);
      // Update only the filtered items
      return isFiltered ? { ...item, isChecked: newSelectAllState } : item;
    });

    updateOtherPermissions(updatedPermissions);
  };

  const filteredPermissions = useMemo(
    () =>
      permissions.filter(
        (permission) =>
          permission.action.toLowerCase().includes(search.toLowerCase()) || permission.meaning.toLowerCase().includes(search.toLowerCase()),
      ),
    [permissions, search],
  );

  return (
    <Box p={20}>
      <Flex justify='space-between' mb={'xs'}>
        <KanbanCheckbox checked={selectAll} label={'Select all'} onChange={handleSelectAllChange} />
        <KanbanInput
          w={'30%'}
          placeholder='Search here'
          value={search}
          onChange={(e) => {
            const value = e.target.value;
            setSearch(value);
          }}
          leftSection={<IconSearch />}
        />
      </Flex>
      <ScrollArea h={600} scrollbars='y'>
        <Grid>
          {filteredPermissions.map(({ action: permission, isChecked, meaning, type: permissionType }) => (
            <Grid.Col span={{ base: 12, md: 12, lg: 6, sm: 12, xl: 4, xs: 12 }} key={permission}>
              <KanbanCheckbox
                checked={isChecked}
                value={permission}
                label={permission}
                description={meaning}
                onChange={() => handleCheckboxChange({ action: permission, meaning, type: permissionType, isChecked })}
              />
            </Grid.Col>
          ))}
        </Grid>
      </ScrollArea>
    </Box>
  );
};

export default ViewListPermission;
