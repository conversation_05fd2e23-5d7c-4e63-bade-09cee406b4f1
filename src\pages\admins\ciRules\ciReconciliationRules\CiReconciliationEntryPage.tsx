import { KanbanInput, KanbanNumberInput, KanbanSelect, KanbanTooltip } from 'kanban-design-system';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { Box, ComboboxItem, Group, Radio, RadioGroup } from '@mantine/core';
import { CiReconciliationEntryAttributeDto, CiReconciliationEntryDto, DATA_SOURCE_MANUAL, RuleStatus } from '@models/CiReconciliationRule';
import { formatStandardName } from '@common/utils/StringUtils';
import DnDAttributesComponent, { CiTypeAttributeDto, DnDAttributesComponentMethods } from '../DnDAttributesComponent';
import { IconInfoCircle } from '@tabler/icons-react';
import { DiscoverySourceDataModel } from '@models/discovery/DiscoverySourceData';
import { listStatus } from '../CiIdentifierRuleDetailPage';
import { TimeUnitEnum, getComboboxTimeUnitEnum } from '@common/constants/TimeUnitEnum';

type CiReconciliationEntryPageProps = {
  entryInfo: CiReconciliationEntryDto;
  listAttributes: CiTypeAttributeDto[];
  isView: boolean;
  listDiscoverySourceData: DiscoverySourceDataModel[];
};

export type CiReconciliationEntryPageMethods = {
  onSaveData: () => CiReconciliationEntryDto;
  validateData: (listEntry: CiReconciliationEntryDto[]) => boolean;
};

type ReconciliationEntryError = {
  name?: string;
  priority?: string;
  selectedAttribute?: string;
  discoverySourceData?: string;
  timeoutData?: string;
  timeUnit?: string;
};

const validateName = (dataEntry: CiReconciliationEntryDto, listEntry: CiReconciliationEntryDto[]): string | undefined => {
  const value = dataEntry.name;
  if (!value || value.trim().length === 0) {
    return 'Name cannot be empty';
  } else if (value.length > 255) {
    return 'Name must be less than 255 characters';
  }
  const standardizedName = formatStandardName(value);
  const index = listEntry.findIndex((x) => x.name === standardizedName && x.tempId !== dataEntry.tempId);
  if (index >= 0) {
    return 'Duplicate name found. Please enter a unique name.';
  }
};

const validatePriority = (dataEntry: CiReconciliationEntryDto): string | undefined => {
  const value = dataEntry.priority;
  if (value === null || value === undefined || value.toString().trim().length === 0) {
    return 'Priority cannot be empty';
  }
  const numValue = Number(value);
  if (isNaN(numValue) || numValue < 1 || numValue > 999) {
    return 'Invalid input: Priority value must be a number between 1 and 999';
  }
};

const validateTimeoutData = (dataEntry: CiReconciliationEntryDto): string | undefined => {
  if (dataEntry.discoverySourceDataId !== DATA_SOURCE_MANUAL.id) {
    const value = dataEntry.timeoutData;
    if (value === null || value === undefined || value.toString().trim().length === 0) {
      return 'Timeout data cannot be empty';
    }
    const numValue = Number(value);
    if (isNaN(numValue) || numValue < 1 || numValue > 99) {
      return 'Invalid input: Timeout data value must be a number between 1 and 99';
    }
  }
};

const validateTimeUnit = (dataEntry: CiReconciliationEntryDto): string | undefined => {
  if (dataEntry.discoverySourceDataId !== DATA_SOURCE_MANUAL.id) {
    const value = dataEntry.timeUnit;
    if (value === null || value === undefined || value.toString().trim().length === 0) {
      return 'Time unit cannot be empty';
    }
  }
};

const validateAttributes = (attributes: CiTypeAttributeDto[], applyAllAttribute: boolean): string | undefined => {
  if (!applyAllAttribute) {
    if (!attributes?.length) {
      return 'Selected attribute cannot be empty';
    }
    const hasInvalidAttribute = attributes.some((item) => item.deleted);
    if (hasInvalidAttribute) {
      return 'Selection is not allowed for attributes that are marked as deleted.';
    }
  }
};

export const CiReconciliationEntryPage = forwardRef<CiReconciliationEntryPageMethods, CiReconciliationEntryPageProps>((props, ref) => {
  const { entryInfo, isView, listAttributes, listDiscoverySourceData } = props;
  const [dataEntry, setDataEntry] = useState<CiReconciliationEntryDto>(entryInfo);
  const [errorMessage, setErrorMessage] = useState<ReconciliationEntryError>();

  const dnDAttributesRef = useRef<DnDAttributesComponentMethods | null>(null);
  const [dataSourceComboxItem, setDataSourceComboxItem] = useState<ComboboxItem[]>([]);

  const selectedAttributes: CiTypeAttributeDto[] = useMemo(() => {
    const selectedCiTypeAttributeIds = (entryInfo?.attributes || []).map((x) => x.ciTypeAttributeId);

    return (listAttributes || []).filter((x) => selectedCiTypeAttributeIds.includes(x.attributeId));
  }, [entryInfo?.attributes, listAttributes]);

  const setDataSourceComboboxItems: ComboboxItem[] = useMemo(
    () => [
      ...listDiscoverySourceData
        .filter((item) => !item.deleted)
        .map(({ id, name }) => ({
          value: String(id),
          label: name,
        })),
    ],
    [listDiscoverySourceData],
  );

  useEffect(() => {
    setDataSourceComboxItem(setDataSourceComboboxItems);
  }, [setDataSourceComboboxItems]);

  const onSaveData = useCallback(() => {
    const selectedAttributesDnD: CiTypeAttributeDto[] = dnDAttributesRef.current?.getSelectedAttributes() || [];

    // convert data
    const attributes: CiReconciliationEntryAttributeDto[] = selectedAttributesDnD.map((x) => {
      return { id: 0, ciReconciliationEntryId: 0, ciTypeAttributeId: x.attributeId, name: x.nameAttribute };
    });

    return { ...dataEntry, name: formatStandardName(dataEntry.name), attributes: attributes };
  }, [dataEntry]);

  const validateDiscoverySourceDeleted = useCallback(
    (id?: number) => {
      if (id) {
        const item = listDiscoverySourceData.find((item) => item.deleted && item.id === id);
        if (item) {
          return 'Discovery Source is not allowed for sources that are marked as deleted.';
        }
      }
    },
    [listDiscoverySourceData],
  );

  const validateData = useCallback(
    (listEntry: CiReconciliationEntryDto[]) => {
      const errorName = validateName(dataEntry, listEntry);
      const errorPriority = validatePriority(dataEntry);
      const errorTimeoutData = validateTimeoutData(dataEntry);
      const errorDiscoverySourceData = validateDiscoverySourceDeleted(dataEntry.discoverySourceDataId);
      const errorTimeUnit = validateTimeUnit(dataEntry);

      const selectedAttributesDnD: CiTypeAttributeDto[] = dnDAttributesRef.current?.getSelectedAttributes() || [];
      const errorAttribute = validateAttributes(selectedAttributesDnD, dataEntry.applyAllAttribute || false);
      setErrorMessage({
        name: errorName,
        priority: errorPriority,
        selectedAttribute: errorAttribute,
        timeoutData: errorTimeoutData,
        discoverySourceData: errorDiscoverySourceData,
        timeUnit: errorTimeUnit,
      });

      return !(errorName || errorPriority || errorAttribute || errorTimeoutData || errorDiscoverySourceData || errorTimeUnit);
    },
    [dataEntry, validateDiscoverySourceDeleted],
  );

  const getDataSoureceComboxItem = useCallback(
    (id?: number, dataSourceComboxItem: ComboboxItem[] = []) => {
      if (id) {
        const item = listDiscoverySourceData.find((item) => item.deleted && item.id === id);
        if (item) {
          dataSourceComboxItem = [...dataSourceComboxItem, { value: String(id), label: `${item.name} - (Deleted)` }];
        }
      }
      return dataSourceComboxItem;
    },
    [listDiscoverySourceData],
  );

  useImperativeHandle<any, CiReconciliationEntryPageMethods>(
    ref,
    () => ({
      onSaveData: onSaveData,
      validateData: validateData,
    }),
    [onSaveData, validateData],
  );

  return (
    <>
      <Box>
        <KanbanInput
          label='Entry Name'
          placeholder='Input entry name'
          value={dataEntry.name || ''}
          maxLength={255}
          withAsterisk
          error={errorMessage?.name}
          disabled={isView || dataEntry.defaultEntry}
          onChange={(event) => {
            const value = event.target.value ?? '';
            const result = { ...dataEntry, name: value };
            setDataEntry(result);
            setErrorMessage({ ...errorMessage, name: undefined });
          }}
        />
        <KanbanSelect
          disabled={isView || dataEntry.defaultEntry}
          required
          searchable
          error={errorMessage?.discoverySourceData}
          value={dataEntry.discoverySourceDataId ? String(dataEntry.discoverySourceDataId) : String(DATA_SOURCE_MANUAL.id)}
          data={getDataSoureceComboxItem(dataEntry.discoverySourceDataId, dataSourceComboxItem)}
          label='Data Source'
          onChange={(value) => {
            if (!value) {
              return;
            }
            const result =
              value === String(DATA_SOURCE_MANUAL.id)
                ? { ...dataEntry, discoverySourceDataId: Number(value), timeoutData: undefined, timeUnit: undefined }
                : { ...dataEntry, discoverySourceDataId: Number(value) };
            setDataEntry(result);
            setErrorMessage({ ...errorMessage, discoverySourceData: undefined });
          }}
        />

        <Group gap={6} style={{ width: '100%' }}>
          <KanbanNumberInput
            label='Priority'
            placeholder='Input number value'
            value={dataEntry.priority ?? ''}
            disabled={isView || dataEntry.defaultEntry}
            allowNegative={false}
            allowDecimal={false}
            clampBehavior='strict'
            min={1}
            max={999}
            error={errorMessage?.priority}
            style={{ flex: 1 }}
            withAsterisk
            onChange={(value) => {
              const numberValue = value === '' ? undefined : Number(value);

              const result = { ...dataEntry, priority: numberValue };
              setDataEntry(result);
              setErrorMessage({ ...errorMessage, priority: undefined });
            }}
          />
          <KanbanTooltip
            maw={'40%'}
            bd={'1px solid rgba(0, 119, 255, 0.8)'}
            bg={'white'}
            fs={'italic'}
            c={'var(--mantine-color-blue-4)'}
            label={'Lower Priority values are given higher precedence during attribute data updates. Priority value is between 1 and 999'}
            style={{ flexShrink: 0 }}
            multiline>
            <Box c={'blue'}>
              <IconInfoCircle size={'20px'} />
            </Box>
          </KanbanTooltip>
          <KanbanSelect
            label='Active'
            placeholder='Select value'
            withAsterisk
            data={listStatus}
            defaultValue={RuleStatus.ENABLE}
            value={dataEntry.active}
            allowDeselect={false}
            disabled={isView || dataEntry.defaultEntry}
            onChange={(val) => {
              const ruleStatus = val && val in RuleStatus ? (val as RuleStatus) : RuleStatus.ENABLE;
              const result = { ...dataEntry, active: ruleStatus };
              setDataEntry(result);
            }}
            style={{ flex: 1 }}
          />
        </Group>
        <Group gap={6} style={{ width: '100%' }}>
          <KanbanNumberInput
            label='Timeout data'
            placeholder={dataEntry.defaultEntry ? '(none)' : 'Input number value'}
            value={dataEntry.discoverySourceDataId !== DATA_SOURCE_MANUAL.id ? dataEntry.timeoutData || '' : ''}
            disabled={isView || dataEntry.defaultEntry || dataEntry.discoverySourceDataId === DATA_SOURCE_MANUAL.id}
            allowNegative={false}
            allowDecimal={false}
            clampBehavior='strict'
            min={1}
            max={99}
            error={errorMessage?.timeoutData}
            style={{ flex: 1 }}
            withAsterisk
            onChange={(value) => {
              const numberValue = value === '' ? undefined : Number(value);

              const result = { ...dataEntry, timeoutData: numberValue };
              setDataEntry(result);
              setErrorMessage({ ...errorMessage, timeoutData: undefined });
            }}
          />
          <KanbanTooltip
            maw={'45%'}
            bd={'1px solid rgba(0, 119, 255, 0.8)'}
            bg={'white'}
            fs={'italic'}
            c={'var(--mantine-color-blue-4)'}
            label={
              'If data from higher-priority sources is not received within the specified timeframe, data from lower-priority sources will be accepted'
            }
            style={{ flexShrink: 0 }}
            multiline>
            <Box c={'blue'}>
              <IconInfoCircle size={'20px'} />
            </Box>
          </KanbanTooltip>
          <KanbanSelect
            label='Time Unit'
            placeholder={dataEntry.defaultEntry ? '(none)' : 'Select value'}
            withAsterisk
            data={getComboboxTimeUnitEnum([TimeUnitEnum.MINUTE, TimeUnitEnum.HOUR, TimeUnitEnum.DAY])}
            value={dataEntry.discoverySourceDataId !== DATA_SOURCE_MANUAL.id ? dataEntry.timeUnit : undefined}
            allowDeselect={false}
            disabled={isView || dataEntry.defaultEntry || dataEntry.discoverySourceDataId === DATA_SOURCE_MANUAL.id}
            error={errorMessage?.timeUnit}
            onChange={(value) => {
              if (!value) {
                return;
              }
              const timeUnitValue = value as TimeUnitEnum;
              setDataEntry({ ...dataEntry, timeUnit: timeUnitValue });
              setErrorMessage({ ...errorMessage, timeUnit: undefined });
            }}
            style={{ flex: 1 }}
          />
        </Group>
        <RadioGroup
          name='applyAllAttribute'
          label=''
          mb='lg'
          withAsterisk={false}
          value={dataEntry.applyAllAttribute ? RuleStatus.ENABLE : RuleStatus.DISABLE}
          defaultValue={RuleStatus.DISABLE}
          onChange={(value) => {
            const isEnabled = value === RuleStatus.ENABLE;
            const result = { ...dataEntry, applyAllAttribute: isEnabled };
            setDataEntry(result);
          }}>
          <Radio
            mb='sm'
            value={RuleStatus.ENABLE}
            disabled={isView || dataEntry.defaultEntry}
            label={
              <Box style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                <span>Apply to all attributes</span>
                <KanbanTooltip
                  maw='45%'
                  bd='1px solid rgba(0, 119, 255, 0.8)'
                  bg='white'
                  fs='italic'
                  c='var(--mantine-color-blue-4)'
                  label='Applies to all existing and future attributes of this CI type.'
                  style={{ flexShrink: 0 }}
                  multiline>
                  <Box c='blue'>
                    <IconInfoCircle size={20} />
                  </Box>
                </KanbanTooltip>
              </Box>
            }
          />

          <Radio
            value={RuleStatus.DISABLE}
            disabled={isView || dataEntry.defaultEntry}
            label={
              <Box style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                <span>Apply to specific attributes</span>
                <KanbanTooltip
                  maw='45%'
                  bd='1px solid rgba(0, 119, 255, 0.8)'
                  bg='white'
                  fs='italic'
                  c='var(--mantine-color-blue-4)'
                  label='Applies to selected specific attributes only.'
                  style={{ flexShrink: 0 }}
                  multiline>
                  <Box c='blue'>
                    <IconInfoCircle size={20} />
                  </Box>
                </KanbanTooltip>
              </Box>
            }
          />
        </RadioGroup>
        <DnDAttributesComponent
          ref={dnDAttributesRef}
          attributeOfCis={listAttributes}
          selectedAttributes={selectedAttributes}
          isView={isView || dataEntry.defaultEntry || dataEntry.applyAllAttribute}
          errorMessage={errorMessage?.selectedAttribute}
        />
      </Box>
    </>
  );
});
CiReconciliationEntryPage.whyDidYouRender = true;
CiReconciliationEntryPage.displayName = 'CiReconciliationEntryPage';
export default CiReconciliationEntryPage;
