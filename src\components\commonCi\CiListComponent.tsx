import { ConfigItemApi, ConfigItemResponse } from '@api/ConfigItemApi';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { buildCiTypeUrl } from '@common/utils/RouterUtils';
import { KanbanButton, KanbanTableSelectHandleMethods, KanbanTooltip } from 'kanban-design-system';
import { ColumnType, KanbanTable, KanbanTableProps, TableAffactedSafeType } from 'kanban-design-system';
import { renderDateTime } from 'kanban-design-system';
import { KanbanText } from 'kanban-design-system';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import equal from 'fast-deep-equal';
import { klona } from 'klona';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { IconListSearch } from '@tabler/icons-react';
import { KanbanIconButton } from 'kanban-design-system';
import type { QueryRequestModel } from '@models/EntityModelBase';
import AdvanceSearchCi, { AdvanceSearchCiMethod } from './advanceSearch/AdvanceSearchCi';
import { type AdvanceSearchData } from './advanceSearch/AdvanceSearchComponent';
import { tableAffectedToPaginationRequestModel, tableAffectedToQueryRequestModel } from '@common/utils/KanbanTableUtils';
import { AclPermission } from '@models/AclPermission';
import { useDispatch } from 'react-redux';
import { PermissionAction } from '@common/constants/PermissionAction';
import { checkAllCiPermissions } from '@common/utils/AclPermissionUtils';
import { ciHasPermissionsSlice } from '@slices/CiHasPermissionSlice';
import { Container } from '@mantine/core';
import { ConfigItemTypeAttrModel } from '@models/ConfigItemTypeAttr';
import AddColumnListCiComponent from './AddColumnListCiComponent';
import { LocalStorageKeyEnum } from '@common/constants/LocalStorageKeyEnum';

export type CiListCustomTablePropsType = (
  tableProps: KanbanTableProps<ConfigItemResponse>,
  methods: CiListComponentMethod,
) => KanbanTableProps<ConfigItemResponse>;

export type CiListComponentMethod = {
  fetchCis: () => void;
  deleteCi: (id: number) => void;
  deleteCis: (ids: number[]) => void;
  resetSelectedItems: () => void;
};

export type CiListComponentProps = {
  ciTypeId: number | undefined;
  customTableProps?: CiListCustomTablePropsType;
};

export const CiListComponent = forwardRef<CiListComponentMethod, CiListComponentProps>(
  ({ ciTypeId, customTableProps }: CiListComponentProps, ref) => {
    const navigate = useNavigate();
    const tableRef = useRef<KanbanTableSelectHandleMethods>(null);
    const [listCI, setListCI] = useState<ConfigItemResponse[]>([]);
    const [totalRecords, setTotalRecords] = useState(0);
    const [advanceSearch, setAdvanceSearch] = useState<AdvanceSearchData | undefined>(undefined);
    const [showAdvanceSearch, setShowAdvanceSearch] = useState(false);

    const [tableAffected, setTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);

    const exportComponentRef = useRef<AdvanceSearchCiMethod | null>(null);
    const dispatch = useDispatch();
    const [queryRequestModel, setQueryRequestModel] = useState<QueryRequestModel<any>>({
      search: '',
      sortBy: undefined,
      isReverse: false,
    });
    const [isLoadingTable, setIsLoadingTable] = useState(false);
    const [dataAttributeSelected, setDataAttributeSelected] = useState<ConfigItemTypeAttrModel[]>([]);
    const localStorageKey = useMemo(() => {
      return `${LocalStorageKeyEnum.ATTRIBUTE_LIST_CI}_${ciTypeId}`;
    }, [ciTypeId]);

    const fetchCis = useCallback(
      (controller?: AbortController) => {
        if (!tableAffected) {
          return;
        }
        if (ciTypeId === undefined) {
          setTotalRecords(0);
          setListCI([]);
          return;
        }
        const ciTypeAttributeIds: number[] = Array.from(
          new Set(
            dataAttributeSelected
              .filter((item) => item.tempId)
              .flatMap((item) => item.tempId?.split(','))
              .map((str) => Number(str?.trim()))
              .filter((num) => !isNaN(num)),
          ),
        );
        setIsLoadingTable(true);
        const pagination = tableAffectedToPaginationRequestModel(tableAffected);
        const queryRequest = tableAffectedToQueryRequestModel(tableAffected);
        setQueryRequestModel(queryRequest);
        ConfigItemTypeApi.getAllCisWithChildrenPaging(ciTypeId, pagination, advanceSearch, false, controller, ciTypeAttributeIds)
          .then((res) => {
            setTotalRecords(res.data.totalElements);
            const cis = res.data.content;
            setListCI(cis);
            const deleteCiPermissions: AclPermission[][] = cis.map((item) =>
              AclPermission.createCiPermissions(PermissionAction.CI__DELETE, item.id, item.ciTypeId),
            );
            const hasDeletePermissions = checkAllCiPermissions(deleteCiPermissions);

            dispatch(ciHasPermissionsSlice.actions.updateCiHasPermissions(hasDeletePermissions));

            exportComponentRef.current?.showBtnExport(true);
          })
          .catch(() => {})
          .finally(() => {
            setIsLoadingTable(false);
          });
      },
      [tableAffected, ciTypeId, dataAttributeSelected, advanceSearch, dispatch],
    );
    useEffect(() => {
      const controller = new AbortController();
      fetchCis(controller);
      return () => controller.abort();
    }, [fetchCis]);

    const deleteCi = useCallback(
      (id: number) => {
        setIsLoadingTable(true);
        ConfigItemApi.deleteByIds([id], {
          useLoading: false,
        })
          .then(() => {
            NotificationSuccess({
              message: 'Delete successfully',
            });
            fetchCis();
          })
          .catch(() => {})
          .finally(() => {
            setIsLoadingTable(false);
          });
      },
      [fetchCis],
    );
    const deleteCis = useCallback(
      (ids: number[]) => {
        setIsLoadingTable(true);
        ConfigItemApi.deleteByIds(ids, {
          useLoading: false,
        })
          .then(() => {
            NotificationSuccess({
              message: 'Delete successfully',
            });
            fetchCis();
          })
          .catch(() => {})
          .finally(() => {
            setIsLoadingTable(false);
          });
      },
      [fetchCis],
    );

    const updateDataAttributeSelected = (datas: ConfigItemTypeAttrModel[]) => {
      setDataAttributeSelected(datas);
    };

    useEffect(() => {
      const data = localStorage.getItem(localStorageKey);
      if (data) {
        try {
          const parsedData = JSON.parse(data);
          setDataAttributeSelected(parsedData);
        } catch (error) {
          setDataAttributeSelected([]);
        }
      } else {
        setDataAttributeSelected([]);
      }
    }, [localStorageKey]);

    const allCiTypes = useGetCiTypes();

    const columns: ColumnType<ConfigItemResponse>[] = useMemo(() => {
      return [
        {
          title: 'Id',
          name: 'id',
          hidden: true,
        },
        {
          title: 'Name',
          name: 'name',
          width: '20%',
        },
        {
          title: 'Description',
          name: 'description',
          customRender: (data) => {
            return (
              <KanbanTooltip label={data} w={'500'} style={{ wordBreak: 'break-word' }} multiline>
                <KanbanText lineClamp={2} style={{ wordBreak: 'break-word' }}>
                  {data}
                </KanbanText>
              </KanbanTooltip>
            );
          },
          width: '40%',
        },
        {
          title: 'Ci Type',
          name: 'ciTypeId',
          customRender: (data) => {
            const ciType = allCiTypes.data.find((x) => x.id === data);
            if (!ciType) {
              return <></>;
            }
            return (
              <Container maw={'200px'}>
                <KanbanTooltip label={ciType.name} maw={'500'} multiline>
                  <KanbanButton
                    size='compact-xs'
                    radius={'lg'}
                    maw={'100%'}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      navigate(buildCiTypeUrl(ciType.id));
                    }}>
                    <KanbanText truncate='end'>{ciType.name}</KanbanText>
                  </KanbanButton>
                </KanbanTooltip>
              </Container>
            );
          },
        },
        {
          title: 'Created by',
          name: 'author',
        },
        {
          title: 'Created date',
          name: 'createdDate',
          customRender: renderDateTime,
        },
      ];
    }, [navigate, allCiTypes.data]);

    const updatedColumns = useMemo(() => {
      const newColumns = [...columns];

      const insertIndex = newColumns.length - 1;
      const newArrayColumns = dataAttributeSelected.map((obj) => ({
        title: obj.name,
        name: `${obj.hashId}`,
        sortable: false,
        customRenderHeader: () => (
          <>
            <KanbanTooltip label={obj.ciTypeId === 0 ? '(Default Attribute)' : obj.ciTypeName || ''}>
              <KanbanText truncate='end' fw={700} maw={'200px'}>
                {obj.name}
              </KanbanText>
            </KanbanTooltip>
          </>
        ),
        customRender: (_data: any, rowData: ConfigItemResponse) => {
          const matchedAttr = rowData?.ciAttributes?.find((item) => obj.hashId === item.hashId);
          const valueView = matchedAttr?.ciAttributeValue || '';

          return (
            <KanbanTooltip label={valueView} multiline w={350}>
              <KanbanText lineClamp={2}>{valueView}</KanbanText>
            </KanbanTooltip>
          );
        },
      }));

      newColumns.splice(insertIndex, 0, ...newArrayColumns);

      return newColumns;
    }, [columns, dataAttributeSelected]);

    useEffect(() => {
      tableRef.current?.columnPropsChangesNotify({ resetHiddenColumnByTheNew: true, resetAdvancedFilterMapping: true });
    }, [updatedColumns]);

    const tableProps: KanbanTableProps<ConfigItemResponse> = useMemo(() => {
      return {
        title: 'List CI',
        showNumericalOrderColumn: true,
        searchable: {
          enable: true,
          debounceTime: 900,
        },
        sortable: {
          enable: true,
        },

        columns: updatedColumns,
        data: listCI,

        pagination: {
          enable: true,
        },
        // onRowClicked: (data) => {
        //     navigate(buildCiUrl(data.ciTypeId, data.id));
        // },
        serverside: {
          totalRows: totalRecords,
          onTableAffected(dataSet) {
            if (!equal(tableAffected, dataSet)) {
              setTableAffected(dataSet);
            }
          },
        },
        customAction: () => (
          <>
            <AddColumnListCiComponent
              ciTypeId={ciTypeId}
              dataAttributeSelected={dataAttributeSelected}
              updateDataAttributeSelected={updateDataAttributeSelected}
              localStorageKey={localStorageKey}
            />

            <KanbanIconButton
              ml={'sm'}
              variant={showAdvanceSearch ? 'filled' : 'outline'}
              title='Advance search'
              onClick={() => {
                if (showAdvanceSearch) {
                  setAdvanceSearch(undefined);
                }
                setShowAdvanceSearch(!showAdvanceSearch);
              }}>
              <IconListSearch />
            </KanbanIconButton>
          </>
        ),
      };
    }, [updatedColumns, listCI, totalRecords, tableAffected, ciTypeId, dataAttributeSelected, localStorageKey, showAdvanceSearch]);

    const resetSelectedItems = () => {
      tableRef.current?.deselectAll();
    };
    const methods: CiListComponentMethod = useMemo(
      () => ({
        fetchCis,
        deleteCi,
        deleteCis,
        resetSelectedItems,
      }),
      [fetchCis, deleteCi, deleteCis],
    );

    const finalTableProps = useMemo(() => {
      if (!customTableProps) {
        return tableProps;
      }
      const current = klona(tableProps);
      return customTableProps(current, methods);
    }, [tableProps, customTableProps, methods]);

    useImperativeHandle<any, CiListComponentMethod>(ref, () => methods, [methods]);

    useEffect(() => {
      setAdvanceSearch(undefined);
      setShowAdvanceSearch(false);
      exportComponentRef.current?.showBtnExport(false);
    }, [ciTypeId]);

    return (
      <>
        {showAdvanceSearch && (
          <AdvanceSearchCi
            onSearch={(val) => {
              setAdvanceSearch(val);
            }}
            ref={exportComponentRef}
            queryRequestModel={queryRequestModel}
            ciTypeId={ciTypeId}
          />
        )}
        <KanbanTable ref={tableRef} isLoading={isLoadingTable} {...finalTableProps} />
      </>
    );
  },
);
CiListComponent.whyDidYouRender = true;
CiListComponent.displayName = 'CiListComponent';
export default CiListComponent;
