import { Text } from '@mantine/core';
import React from 'react';
import { DragTranfromMapType } from '@models/DiscoveryTransformMap';
import { DraggableItemBase } from './DraggableItemBase';

interface TargetAttributeSearchItemProps {
  item: DragTranfromMapType;
  index: number;
  classes: Record<string, string>;
  onClick: () => void;
}

export const TargetAttributeSearchItem = ({ classes, index, item, onClick }: TargetAttributeSearchItemProps) => {
  return (
    <DraggableItemBase
      item={item}
      index={index}
      classes={classes}
      onClick={onClick}
      paperStyle={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}
      draggableIdPrefix='target_attribute'
      paperProps={{
        c: item.deleted ? 'red' : 'black',
      }}
      renderContent={(item) => (
        <Text w={200} truncate='end'>
          {item.label}
        </Text>
      )}
    />
  );
};
