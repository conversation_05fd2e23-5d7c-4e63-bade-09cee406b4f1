import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export class ActiveIqApi extends BaseApi {
  static baseUrl = BaseUrl.activeIq;

  static findAllClusterNodes() {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/data-center/clusters/nodes`);
  }

  static findAllStorageVolumes() {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/data-center/storage/volumes`);
  }
}
