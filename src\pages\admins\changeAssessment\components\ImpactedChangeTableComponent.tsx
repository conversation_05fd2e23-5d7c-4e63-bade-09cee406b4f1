import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { KanbanButton } from 'kanban-design-system';
import type { ConfigItemResponse } from '@api/ConfigItemApi';
import { KanbanTable, type ColumnType, type KanbanTableProps } from 'kanban-design-system';
import { KanbanText } from 'kanban-design-system';
import { useNavigate } from 'react-router-dom';
import { buildCiTypeUrl } from '@common/utils/RouterUtils';
import { renderDateTime } from 'kanban-design-system';
import { ImpactedTypeTable } from '@models/ChangeAssessment';
import { IconAffiliate, IconClipboardList, IconEye, IconLayoutList } from '@tabler/icons-react';
import CiUpdateSole, { CiUpdateSoleMethods } from '@pages/cis/ci/CiUpdateSole';
import CiRelationship from '@pages/cis/ci/relationship/CiRelationship';
import { KanbanConfirmModal } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import { KanbanIconButton } from 'kanban-design-system';
import { Badge, ScrollArea, Tooltip } from '@mantine/core';
import { ViewServiceMappingDetail, type ViewServiceMappingDetailMethods } from './ViewServiceMappingDetail';
import { CI_TYPE_ID_OF_SERVICE_MAP } from '@common/constants/CommonConstants';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { CiManagementApi, CiManagementResponse } from '@api/CiManagementApi';
import CiManagementDetailViewPopup, { CiManagementDetailViewPopupMethods } from '@pages/cis/ciManagement/modal/CiManagementDetailViewPopup';
import type { CiInfoModel } from '@models/ConfigItem';
import { ServiceAttachedTypeEnum } from '@common/constants/CiDetail';
interface ImpactedChangeTableComponentProps {
  typeTable: ImpactedTypeTable;
  cis: ConfigItemResponse[];
  onDelete: (val: ConfigItemResponse[]) => void;
  allowEdit: boolean;
}

export const ImpactedChangeTableComponent: React.FC<ImpactedChangeTableComponentProps> = ({ allowEdit, cis, onDelete, typeTable }) => {
  const [listCi, setListCi] = useState<ConfigItemResponse[]>([]);
  const [listCiDraft, setListCiDraft] = useState<CiManagementResponse[]>([]);
  const allCiTypes = useGetCiTypes();
  const navigate = useNavigate();

  const [openedModalViewCi, { close: closeModalViewCi, open: openModalViewCi }] = useDisclosure(false);
  const [openedModalRelationship, { close: closeModalRelationship, open: openModalRelationship }] = useDisclosure(false);
  const [currentCiInfo, setCurrentCiInfo] = useState<CiInfoModel | undefined>();
  const [currentCiTemp, setCurrentCiTemp] = useState<CiManagementResponse>();

  const onClickViewDraft = useCallback(
    (ciId: number) => {
      const ciTempData = listCiDraft.find((x) => x.ciId === ciId);
      setCurrentCiTemp(ciTempData);
      childRefViewDetail.current?.openPopupView();
    },
    [listCiDraft],
  );

  const columns: ColumnType<ConfigItemResponse>[] = useMemo(() => {
    return [
      {
        title: 'Id',
        name: 'id',
        hidden: true,
      },
      {
        title: 'Name',
        name: 'name',
        width: '10%',
      },
      {
        title: 'Description',
        name: 'description',
        customRender: (data) => {
          return <KanbanText lineClamp={2}>{data}</KanbanText>;
        },
        width: '40%',
      },
      //1615: Not affect to incident page, only for change assessment
      {
        title: 'Attached type',
        name: 'attachedType',
        width: '40%',
        customRender: (val: string) => {
          const isManual = ServiceAttachedTypeEnum.MANUAL === val;
          return (
            <Badge mt='5' color={isManual ? 'var(--mantine-color-green-8)' : 'var(--mantine-color-orange-5)'}>
              {val}
            </Badge>
          );
        },
        hidden: typeTable !== ImpactedTypeTable.SERVICE,
      },
      {
        title: 'Ci Type',
        name: 'ciTypeId',
        customRender: (data: number) => {
          const ciType = allCiTypes.data.find((x) => x.id === data);
          if (!ciType) {
            return <></>;
          }
          return (
            <KanbanButton
              size='compact-xs'
              radius={'lg'}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(buildCiTypeUrl(ciType.id));
              }}>
              {ciType.name}
            </KanbanButton>
          );
        },
        width: '10%',
      },
      {
        title: 'Created by',
        name: 'author',
        width: '10%',
      },
      {
        title: 'Created date',
        name: 'createdDate',
        customRender: renderDateTime,
        width: '10%',
      },
    ];
  }, [navigate, allCiTypes.data, typeTable]);

  const tableProps: KanbanTableProps<ConfigItemResponse> = useMemo(() => {
    return {
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 300,
      },
      sortable: {
        enable: true,
      },
      columns: columns,
      data: listCi,

      pagination: {
        enable: true,
      },
      selectableRows: {
        enable: true,
        onDeleted(rows) {
          //1615 : delete with attachedType + ciId
          onDelete(rows);
        },
      },
      actions: {
        deletable: {
          onDeleted(data) {
            onDelete([data]);
          },
        },
        customAction: (data) => {
          return (
            <>
              <Tooltip label='View change'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  disabled={listCiDraft.every((x) => x.ciId !== data.id)}
                  onClick={() => {
                    onClickViewDraft(data.id);
                  }}>
                  <IconClipboardList />
                </KanbanIconButton>
              </Tooltip>
              <GuardComponent requirePermissions={AclPermission.createViewCiPermissions(data.id, data.ciTypeId)} hiddenOnUnSatisfy>
                <Tooltip label='View info'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setCurrentCiInfo({
                        ciId: data.id,
                        ciTypeId: data.ciTypeId,
                      });
                      openModalViewCi();
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                </Tooltip>
              </GuardComponent>

              <Tooltip label='View relationship'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    setCurrentCiInfo({
                      ciId: data.id,
                      ciTypeId: data.ciTypeId,
                    });
                    openModalRelationship();
                  }}>
                  <IconAffiliate />
                </KanbanIconButton>
              </Tooltip>

              {data.ciTypeId === CI_TYPE_ID_OF_SERVICE_MAP && (
                <Tooltip label='View service info'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      childRefViewServiceMapping.current?.viewServiceMappingDetail(data.id);
                    }}>
                    <IconLayoutList />
                  </KanbanIconButton>
                </Tooltip>
              )}
            </>
          );
        },
      },
    };
  }, [columns, listCi, listCiDraft, onClickViewDraft, onDelete, openModalRelationship, openModalViewCi]);

  const customTableProps = useMemo(() => {
    const tablePropsUpdate = { ...tableProps };
    if (!allowEdit) {
      const { actions, selectableRows, ...rest } = tablePropsUpdate;
      if (selectableRows) {
        selectableRows.enable = false;
      }
      if (actions && actions.deletable) {
        delete actions.deletable;
      }

      return { ...rest, selectableRows, actions };
    }

    return tablePropsUpdate;
  }, [allowEdit, tableProps]);

  const fetchDataDraft = useCallback(() => {
    const listIds = listCi.map((x) => x.id);
    if (listIds.length > 0) {
      CiManagementApi.getCiTempByCiIdIn(listIds)
        .then((res) => {
          if (res.data && res.data.length > 0) {
            setListCiDraft(res.data);
            return;
          }
        })
        .catch(() => {});
    }
  }, [listCi]);

  useEffect(() => {
    fetchDataDraft();
  }, [fetchDataDraft]);

  useEffect(() => {
    setListCi(cis);
  }, [cis]);

  const childRef = useRef<CiUpdateSoleMethods | null>(null);
  const childRefViewDetail = useRef<CiManagementDetailViewPopupMethods | null>(null);
  const childRefViewServiceMapping = useRef<ViewServiceMappingDetailMethods | null>(null);

  return (
    <>
      {/* modal view detail info of CI management draft */}
      <CiManagementDetailViewPopup ref={childRefViewDetail} initData={currentCiTemp} />

      {/* Modal view service mapping */}
      <ViewServiceMappingDetail ref={childRefViewServiceMapping} />

      <KanbanConfirmModal
        title={'CI Detail'}
        onConfirm={undefined}
        onClose={closeModalViewCi}
        opened={openedModalViewCi}
        modalProps={{
          size: '80%',
        }}>
        {currentCiInfo && (
          <ScrollArea.Autosize mah={1000} type='scroll'>
            <CiUpdateSole readOnly={true} ciId={currentCiInfo.ciId} ciTypeId={currentCiInfo.ciTypeId} forwardedRef={childRef} />
          </ScrollArea.Autosize>
        )}
      </KanbanConfirmModal>

      <KanbanConfirmModal
        title={'Relationship'}
        onConfirm={undefined}
        onClose={closeModalRelationship}
        opened={openedModalRelationship}
        modalProps={{
          size: '80%',
        }}>
        {currentCiInfo && <CiRelationship ciId={currentCiInfo.ciId} ciTypeId={currentCiInfo.ciTypeId} isView isFromBusinessView={true} />}
      </KanbanConfirmModal>
      <KanbanTable {...customTableProps} />
    </>
  );
};
