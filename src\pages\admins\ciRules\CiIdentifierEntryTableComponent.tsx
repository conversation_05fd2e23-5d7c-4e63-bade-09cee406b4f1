import React, { useCallback, useMemo, useRef, useState } from 'react';
import { KanbanButton, KanbanNumberInput, KanbanSwitch, KanbanTable, type ColumnType, type KanbanTableProps } from 'kanban-design-system';
import { KanbanText } from 'kanban-design-system';
import { IconCheck, IconEdit, IconEye, IconPencil, IconPlus, IconTrash } from '@tabler/icons-react';
import { KanbanConfirmModal } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import { KanbanIconButton } from 'kanban-design-system';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { Badge, Divider, Group, rem, Space } from '@mantine/core';
import { v4 as uuid } from 'uuid';
import CiIdentifierEntryPage, { CiIdentifierEntryPageMethods } from './CiIdentifierEntryPage';
import type { CiTypeAttributeDto } from './DnDAttributesComponent';
import { CiIdentifierEntryAttributeDto, RuleStatus, type CiIdentifierEntryDto } from '@models/CiIdentifierRule';

interface ImpactedChangeTableComponentProps {
  listEntry: CiIdentifierEntryDto[];
  setListEntry: (val: CiIdentifierEntryDto[]) => void;
  listEntryUpdate: CiIdentifierEntryDto[];
  setListEntryUpdate: (val: CiIdentifierEntryDto[]) => void;
  listEntryDelete: CiIdentifierEntryDto[];
  setListEntryDelete: (val: CiIdentifierEntryDto[]) => void;
  listAttributes: CiTypeAttributeDto[];
  allowEdit: boolean;
}

export const CiIdentifierEntryTableComponent = (props: ImpactedChangeTableComponentProps) => {
  const { allowEdit, listAttributes, listEntry, listEntryDelete, listEntryUpdate, setListEntry, setListEntryDelete, setListEntryUpdate } = props;

  const [openedModalIdentifierEntry, { close: closeModalIdentifierEntry, open: openModalIdentifierEntry }] = useDisclosure(false);
  const [modalConfirmDelete, { close: closeModalConfirmDelete, open: openModalConfirmDelete }] = useDisclosure(false);
  const [dataEntry, setDataEntry] = useState<CiIdentifierEntryDto>();
  const [isViewEntry, setIsViewEntry] = useState(false);
  const [deleteIds, setDeleteIds] = useState<string[]>([]);
  const [priorityChange, setPriorityChange] = useState<number>(0);

  const onSearched = useCallback(
    (datas: CiIdentifierEntryDto[], search: string): CiIdentifierEntryDto[] => {
      const lowerCaseSearch = search.toLowerCase();

      return datas.filter((item) => {
        const listId = (item.attributes || []).map((x) => x.ciTypeAttributeId);

        const listKeyAttribute = listAttributes.filter((attr) => listId.includes(attr.attributeId));

        const dataAttributes = (listKeyAttribute || [])
          .map((x) => (x.deleted ? `${x.nameAttribute} - (Deleted)` : x.nameAttribute))
          .join(', ')
          .toLowerCase();
        return (
          item.name?.toLowerCase().includes(lowerCaseSearch) ||
          item.active?.toLowerCase().includes(lowerCaseSearch) ||
          dataAttributes?.includes(lowerCaseSearch) ||
          item.id?.toString().toLowerCase().includes(lowerCaseSearch)
        );
      });
    },
    [listAttributes],
  );

  const onDelete = useCallback(
    (ids: string[]) => {
      if (!ids) {
        return;
      }
      // filter list data old (has Id)
      const listDeleted = listEntry.filter((item) => item.id && item.tempId && ids.includes(item.tempId));
      const deletedObject = [...listEntryDelete, ...listDeleted];
      setListEntryDelete(deletedObject);

      // remove in list all data
      const updatedEntry = listEntry.filter((item) => item.tempId && !ids.includes(item.tempId));
      setListEntry(updatedEntry);

      // remove in list update data
      const newListUpdate = listEntryUpdate.filter((item) => item.tempId && !ids.includes(item.tempId));
      setListEntryUpdate(newListUpdate);

      setDeleteIds([]);
    },
    [listEntry, listEntryDelete, listEntryUpdate, setListEntry, setListEntryDelete, setListEntryUpdate],
  );

  const handleUpdateOrCreate = useCallback(
    (newItem: CiIdentifierEntryDto | undefined) => {
      if (!newItem) {
        return;
      }

      const index = listEntry.findIndex((item) => item.tempId === newItem.tempId);
      if (index !== -1) {
        // case create
        const updatedList = listEntry.map((item) => (item.tempId === newItem.tempId ? { ...item, ...newItem } : item));
        setListEntry(updatedList);
      } else {
        // case update
        setListEntry([...listEntry, newItem]);
      }

      if (newItem.id > 0) {
        const indexUpdate = listEntryUpdate.findIndex((item) => item.tempId === newItem.tempId);
        if (indexUpdate !== -1) {
          const updatedList = listEntryUpdate.map((item) => (item.tempId === newItem.tempId ? { ...item, ...newItem } : item));
          setListEntryUpdate(updatedList);
        } else {
          setListEntryUpdate([...listEntryUpdate, newItem]);
        }
      }
    },
    [listEntry, listEntryUpdate, setListEntry, setListEntryUpdate],
  );

  const handleUpdatePriority = useCallback(
    (rowData: CiIdentifierEntryDto) => {
      const updatedList = listEntry.map((item) => (item.tempId === rowData.tempId ? { ...item, onEdit: false, priority: priorityChange } : item));
      setListEntry(updatedList);

      const updatedObj = updatedList.find((x) => x.tempId === rowData.tempId);
      handleUpdateOrCreate(updatedObj);
      setPriorityChange(0);
    },
    [handleUpdateOrCreate, listEntry, priorityChange, setListEntry],
  );

  const renderListKeyAttributes = useCallback(
    (data: CiIdentifierEntryAttributeDto[]) => {
      const listId = data.map((item) => item.ciTypeAttributeId);

      const listKeyAttribute = listAttributes.filter((attr) => listId.includes(attr.attributeId));

      return listKeyAttribute.map((item, index) => {
        const isDeleted = item.deleted;
        const badgeColor = isDeleted ? 'red' : 'blue';
        const badgeText = isDeleted ? `${item.nameAttribute} - (Deleted)` : item.nameAttribute;

        return (
          <Badge key={index} variant='light' color={badgeColor} radius='sm' mr='5'>
            <KanbanText fw={500} size='sm' tt='initial'>
              {badgeText}
            </KanbanText>
          </Badge>
        );
      });
    },
    [listAttributes],
  );

  const columns: ColumnType<CiIdentifierEntryDto>[] = useMemo(() => {
    return [
      {
        title: 'Id',
        name: 'id',
        hidden: true,
        width: '10%',
      },
      {
        title: 'Entry Name',
        name: 'name',
        width: '10%',
      },
      {
        title: 'Key attributes',
        name: 'attributes',
        customRender: (data: CiIdentifierEntryAttributeDto[]) => {
          return renderListKeyAttributes(data);
        },
      },
      {
        title: 'Priority',
        name: 'priority',
        width: '10%',
        customRender: (data, rowData) => {
          return (
            <>
              {allowEdit && rowData?.onEdit ? (
                <KanbanNumberInput
                  value={data}
                  disabled={!allowEdit}
                  allowNegative={false}
                  allowDecimal={false}
                  clampBehavior='strict'
                  autoFocus
                  min={1}
                  max={999}
                  rightSectionPointerEvents='auto'
                  rightSection={
                    <IconCheck
                      style={{ width: rem(20), height: rem(20) }}
                      color='var(--mantine-color-green-5)'
                      onClick={(e) => {
                        e.stopPropagation();
                        handleUpdatePriority(rowData);
                      }}
                    />
                  }
                  onChange={(e) => {
                    setPriorityChange(Number(e));
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  onBlur={() => {
                    handleUpdatePriority(rowData);
                  }}
                />
              ) : (
                <Group grow>
                  <KanbanText>{data}</KanbanText>
                  {allowEdit && (
                    <IconPencil
                      style={{ width: rem(20), height: rem(20) }}
                      color='var(--mantine-color-gray-5)'
                      onClick={(e) => {
                        e.stopPropagation();
                        const updatedList = listEntry.map((item) => (item.tempId === rowData.tempId ? { ...item, onEdit: true } : item));
                        setListEntry(updatedList);
                        setPriorityChange(data);
                      }}
                    />
                  )}
                </Group>
              )}
            </>
          );
        },
      },
      {
        title: 'Active',
        name: 'active',
        width: '10%',
        customRender: (data) => {
          const isCheck = RuleStatus.ENABLE === data;
          return <KanbanText c={isCheck ? 'green' : 'red'}>{isCheck ? 'Active' : 'Inactive'}</KanbanText>;
        },
      },
    ];
  }, [allowEdit, renderListKeyAttributes, handleUpdatePriority, listEntry, setListEntry]);

  const tableProps: KanbanTableProps<CiIdentifierEntryDto> = useMemo(() => {
    return {
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 300,
        onSearched: onSearched,
      },
      sortable: {
        enable: true,
      },

      columns: columns,
      data: listEntry,

      pagination: {
        enable: true,
      },
      selectableRows: {
        enable: false,
        onDeleted(rows) {
          const ids = rows.map((x) => x.tempId);
          onDelete(ids);
        },
      },
      onRowClicked: (data) => {
        setDataEntry(data);
        setIsViewEntry(true);
        openModalIdentifierEntry();
      },
      actions: {
        customAction: (data) => {
          return (
            <>
              {allowEdit && (
                <KanbanSwitch
                  size='xs'
                  mr='xs'
                  color={'green'}
                  checked={RuleStatus.ENABLE === data.active}
                  onChange={(e) => {
                    const value = e.currentTarget.checked;
                    const updatedList = listEntry.map((item) =>
                      item.tempId === data.tempId ? { ...item, active: value ? RuleStatus.ENABLE : RuleStatus.DISABLE } : item,
                    );
                    setListEntry(updatedList);
                    const updatedObj = updatedList.find((x) => x.tempId === data.tempId);
                    handleUpdateOrCreate(updatedObj);
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                />
              )}
              {/* <GuardComponent requirePermissions={AclPermission.createViewCiPermissions(data.id, data.ciTypeId)} hiddenOnUnSatisfy> */}
              <KanbanIconButton
                variant='transparent'
                size={'sm'}
                onClick={() => {
                  setDataEntry(data);
                  setIsViewEntry(true);
                  openModalIdentifierEntry();
                }}>
                <IconEye />
              </KanbanIconButton>
              {/* </GuardComponent> */}
              {allowEdit && (
                <>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setDataEntry(data);
                      setIsViewEntry(false);
                      openModalIdentifierEntry();
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                  {!data.defaultEntry ? (
                    <KanbanIconButton
                      key={3}
                      title='Delete'
                      color='red'
                      size='sm'
                      variant='transparent'
                      onClick={() => {
                        setDeleteIds([data.tempId]);
                        openModalConfirmDelete();
                      }}>
                      <IconTrash />
                    </KanbanIconButton>
                  ) : (
                    <KanbanIconButton key={3} color='transparent' size='sm' variant='transparent'></KanbanIconButton>
                  )}
                </>
              )}
            </>
          );
        },
      },
    };
  }, [allowEdit, columns, handleUpdateOrCreate, listEntry, onDelete, onSearched, openModalConfirmDelete, openModalIdentifierEntry, setListEntry]);

  const customTableProps = useMemo(() => {
    const tablePropsUpdate = { ...tableProps };
    if (!allowEdit) {
      const { actions, selectableRows, ...rest } = tablePropsUpdate;
      if (selectableRows) {
        selectableRows.enable = false;
      }
      if (actions && actions.deletable) {
        delete actions.deletable;
      }

      return { ...rest, selectableRows, actions };
    }

    return tablePropsUpdate;
  }, [allowEdit, tableProps]);

  const ciIdentifierEntryRef = useRef<CiIdentifierEntryPageMethods | null>(null);

  const onSaveEntry = () => {
    const isValid = ciIdentifierEntryRef.current?.validateData(listEntry);
    if (!isValid) {
      return;
    }
    const entryData = ciIdentifierEntryRef.current?.onSaveData();
    handleUpdateOrCreate(entryData);
    closeModalIdentifierEntry();
  };

  const onAddNewEntry = () => {
    setDataEntry({
      id: 0,
      name: '',
      active: RuleStatus.ENABLE,
      ciIdentifierRuleId: 0,
      tempId: uuid(),
      defaultEntry: false,
    });
    setIsViewEntry(false);
    openModalIdentifierEntry();
  };

  return (
    <>
      {/* Modal confirm delete attribute */}
      <KanbanConfirmModal
        opened={modalConfirmDelete}
        onClose={closeModalConfirmDelete}
        title='Confirm delete'
        onConfirm={() => {
          onDelete(deleteIds);
          closeModalConfirmDelete();
        }}>
        Are you sure to delete this item?
      </KanbanConfirmModal>

      <KanbanConfirmModal
        title={'CI Identifier Entry'}
        onConfirm={isViewEntry ? undefined : onSaveEntry}
        textConfirm={'Save'}
        onClose={closeModalIdentifierEntry}
        opened={openedModalIdentifierEntry}
        modalProps={{
          size: '50%',
        }}>
        {dataEntry && <CiIdentifierEntryPage ref={ciIdentifierEntryRef} entryInfo={dataEntry} listAttributes={listAttributes} isView={isViewEntry} />}
      </KanbanConfirmModal>

      <Space h={'xs'} />
      <Divider />
      <Space h={'xs'} />
      <HeaderTitleComponent
        title='List of entries'
        rightSection={
          allowEdit && (
            <GuardComponent requirePermissions={[AclPermission.createIncidentRequest]} hiddenOnUnSatisfy>
              <KanbanButton
                onClick={() => {
                  onAddNewEntry();
                }}
                leftSection={<IconPlus />}>
                Add entry
              </KanbanButton>
            </GuardComponent>
          )
        }
      />
      <KanbanTable {...customTableProps} />
    </>
  );
};

CiIdentifierEntryTableComponent.whyDidYouRender = true;
CiIdentifierEntryTableComponent.displayName = 'CiIdentifierEntryTableComponent';
export default CiIdentifierEntryTableComponent;
