import React, { useState } from 'react';
import { KanbanTabs } from 'kanban-design-system';
import { BusinessFunctionModalTabBusinessModel } from './BusinessFunctionModalTabBusinessModel';
import { BusinessFunctionModalTabInfomation } from './BusinessFunctionModalTabInfomation';

export type BusinessFunctionsModalProps = {
  ciIdBusinessDomain: number;
  ciIdBusinessFunction: number;
};

export enum BusinessFunctionsModalTabEnum {
  INFORMATION = 'INFORMATION',
  BUSINESS_MODEL = 'BUSINESS_MODEL',
}

export const BusinessFunctionModal = (props: BusinessFunctionsModalProps) => {
  const [activeTab, setActiveTab] = useState<string>(BusinessFunctionsModalTabEnum.INFORMATION);
  return (
    <>
      <KanbanTabs
        configs={{
          defaultValue: BusinessFunctionsModalTabEnum.INFORMATION,
          value: activeTab,
          variant: 'outline',
          onChange: (value) => {
            if (value !== null) {
              setActiveTab(value);
            }
          },
        }}
        tabs={{
          INFORMATION: {
            title: 'Infomation',
            content: (
              <BusinessFunctionModalTabInfomation ciIdBusinessDomain={props.ciIdBusinessDomain} ciIdBusinessFunction={props.ciIdBusinessFunction} />
            ),
          },
          BUSINESS_MODEL: {
            title: 'Business model',
            content: (
              <BusinessFunctionModalTabBusinessModel
                ciIdBusinessDomain={props.ciIdBusinessDomain}
                ciIdBusinessFunction={props.ciIdBusinessFunction}
              />
            ),
          },
        }}
      />
    </>
  );
};

export default BusinessFunctionModal;
