import type { GoJs } from '@common/libs';
import { drawHightLightFromNode, hiddenNode, resetHightLight } from '@common/utils/GoJsHelper';
import { Menu } from '@mantine/core';
import { useOnClickOutside } from 'kanban-design-system';
import React, { useRef } from 'react';
import type { ShowActionDiagramConfigProps } from '../../relationship/CiRelationship';
import { buildCiUrl } from '@common/utils/RouterUtils';
import type { HighlightNodeModel } from '../CiRelationshipGraph';
import { PermissionAction } from '@common/constants/PermissionAction';
import { isCiPermissionAction, isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';
export type MenuGraphPosition = {
  x: number;
  y: number;
};

type MenuGraphProps = {
  nodeHovered?: go.Node;
  goDiagram: GoJs.Diagram | undefined;
  contextMenuPosition: MenuGraphPosition;
  setContextMenuPosition: (menuGraphPosition?: MenuGraphPosition) => void;
  nodeHightLight: HighlightNodeModel;
  setNodeHightLight: (node: HighlightNodeModel) => void;
  toolBarConfig: ShowActionDiagramConfigProps;
  handleOpenModalCreateNewRelationship: (isRoot?: boolean) => void;
};

const MenuGraph = (props: MenuGraphProps) => {
  const {
    contextMenuPosition,
    goDiagram,
    handleOpenModalCreateNewRelationship,
    nodeHightLight,
    nodeHovered,
    setContextMenuPosition,
    setNodeHightLight,
    toolBarConfig,
  } = props;

  //logic hiden menu when click outside
  const menuRef = useRef(null);
  const handleOnClickOutside = () => {
    setContextMenuPosition(undefined);
  };
  useOnClickOutside(menuRef, handleOnClickOutside);

  const detailsCI = () => {
    window.open(buildCiUrl(Number(nodeHovered?.data.data.ciTypeId), Number(nodeHovered?.data.data.ciId)), '_blank');
  };
  return (
    <div
      className='context-menu'
      style={{
        position: 'absolute',
        left: contextMenuPosition.x + 30,
        top: contextMenuPosition.y + 150,
      }}>
      <Menu position='right' shadow='md' width={200} opened withinPortal={false}>
        <Menu.Target>
          <div></div>
        </Menu.Target>

        <Menu.Dropdown ref={menuRef}>
          <Menu.Label>Menu</Menu.Label>
          {toolBarConfig?.menu?.isShowStreamImpact && (
            <>
              <Menu.Item
                onClick={() => {
                  drawHightLightFromNode(goDiagram, nodeHovered?.data.key, true);
                  setNodeHightLight({ nodeId: nodeHovered?.data.key, nodeName: nodeHovered?.data?.label, upStream: true });
                  setContextMenuPosition(undefined);
                }}>
                Show Upstream impact
              </Menu.Item>
              <Menu.Item
                onClick={() => {
                  drawHightLightFromNode(goDiagram, nodeHovered?.data.key, false);
                  setNodeHightLight({ nodeId: nodeHovered?.data.key, nodeName: nodeHovered?.data?.label, upStream: false });
                  setContextMenuPosition(undefined);
                }}>
                Show Downstream impact
              </Menu.Item>
            </>
          )}

          {nodeHovered && (!nodeHovered.data?.isRoot || nodeHovered.data?.data?.isImpact) && (
            <>
              {toolBarConfig?.menu?.isShowHideCi && (
                <Menu.Item
                  onClick={() => {
                    resetHightLight(goDiagram);
                    hiddenNode(goDiagram, nodeHovered);
                    drawHightLightFromNode(goDiagram, nodeHightLight.nodeId, nodeHightLight.upStream);
                    setContextMenuPosition(undefined);
                  }}>
                  Hide CI
                </Menu.Item>
              )}

              {toolBarConfig?.menu?.isShowCiDetails &&
                isCiPermissionAction(Number(nodeHovered.data.data.ciTypeId), Number(nodeHovered.data.data.ciId), [
                  PermissionAction.CI__VIEW_BASIC,
                  PermissionAction.CI__VIEW_ADVANCED,
                ]) && (
                  <Menu.Item
                    onClick={() => {
                      detailsCI();
                      setContextMenuPosition(undefined);
                    }}>
                    Go to CI Details
                  </Menu.Item>
                )}
            </>
          )}
          {toolBarConfig?.menu?.isShowAddRelationship && isCurrentUserMatchPermissions([AclPermission.createCiRelationship]) && (
            <Menu.Item
              onClick={() => {
                handleOpenModalCreateNewRelationship();
                setContextMenuPosition(undefined);
              }}>
              Add Relationship
            </Menu.Item>
          )}

          <Menu.Divider />
        </Menu.Dropdown>
      </Menu>
    </div>
  );
};

export default MenuGraph;
