/* eslint-disable no-constant-condition */
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { KanbanModal } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import { IconEye, IconPlus } from '@tabler/icons-react';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ServiceMappingDesign } from './ServiceMappingDesign';
import { KanbanInput } from 'kanban-design-system';
import { KanbanRadio } from 'kanban-design-system';
import { KanbanTextarea } from 'kanban-design-system';
import type { GoJs } from '@common/libs';
import { NotificationError } from '@common/utils/NotificationUtils';
import type { ServiceMapGraphDataMapping, ServiceMapGraphModel, ServiceMapRuleModel, ServiceMapingModel } from '@models/ServiceMapping';
import { ServiceMapingModelResponse, ServiceMappingApi } from '@api/ServiceMappingApi';
import { KanbanText } from 'kanban-design-system';
import { KanbanTable, KanbanTableProps, TableAffactedSafeType, type ColumnType } from 'kanban-design-system';
import { renderDateTime } from 'kanban-design-system';
import equal from 'fast-deep-equal';
import { formatStandardName } from '@common/utils/StringUtils';
import ViewServiceModal from './ViewServiceModal';
import { isConnected } from '@common/utils/GoJsHelper';
import { ServiceMapEnum } from '@common/constants/ServiceMapEnum';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { IconEdit } from '@tabler/icons-react';
import { KanbanIconButton } from 'kanban-design-system';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { BreadcrumbComponent } from '@pages/admins/breadcrumb/BreadcrumbComponent';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import { MAX_TEXT_LENGTH } from '@common/constants/FieldLengthConstants';

/**
 * convert diagram design to model
 * @param goDiagram
 * @returns
 */
const getModelMapping = (goDiagram: GoJs.Diagram | undefined): ServiceMapingModel => {
  if (!goDiagram) {
    return {
      name: '',
      startCiId: 0,
      status: 1,
    };
  }
  const listGrapModel: ServiceMapGraphModel[] = [];
  goDiagram.links.each((link) => {
    const fromNode = goDiagram.findNodeForKey(link.data.from);
    const toNode = goDiagram.findNodeForKey(link.data.to);
    if (fromNode && toNode) {
      listGrapModel.push({
        from: fromNode.data.key,
        to: toNode.data.key,
        relationshipId: link.data.relationshipId,
      });
    }
  });

  const graphDataMapping: ServiceMapGraphDataMapping[] = [];

  const listRules: ServiceMapRuleModel[] = [];
  goDiagram.nodes.each((node) => {
    if (node && node.data?.rules && node.data.rules?.ciTypeAttrId !== 0) {
      listRules.push({ nodeId: node.data.key, dataQuery: JSON.stringify(node.data.rules) });
    }
    graphDataMapping.push({ nodeId: node?.data?.key, dataId: node?.data?.ciTypeId, type: ServiceMapEnum.TYPE_CI_TYPE });
  });
  const nodeRoot = goDiagram.findNodesByExample({ isRoot: true }).first();
  const dataModel: ServiceMapingModel = {
    name: '',
    startCiId: nodeRoot?.data.data.startCiId,
    startNodeId: nodeRoot?.data.key,
    graph: listGrapModel,
    graphData: goDiagram.model.toJson(),
    graphDataMapping: graphDataMapping,
    status: 1,
    rules: listRules,
  };

  return dataModel;
};

/**
 * validate diagram service mapping before save
 * @param goDiagram
 * @returns
 */
const validateDiagram = (goDiagram: GoJs.Diagram | undefined) => {
  if (!goDiagram) {
    return false;
  }
  if (goDiagram.nodes.count <= 1) {
    NotificationError({
      message: 'Total node in the model must be geater than 1.',
    });
    return false;
  }
  const linkIterator = goDiagram.links.iterator;
  while (linkIterator.next()) {
    const link = linkIterator.value;
    if (!link.data.relationshipId) {
      NotificationError({
        message: 'You have miss add relationship.',
      });
      return false;
    }
  }
  const nodeRoot = goDiagram.findNodesByExample({ isRoot: true }).first();
  if (!nodeRoot) {
    NotificationError({
      message: 'Please add start CI',
    });
    return false;
  }

  //remove all node not connected to root
  const nodesIterator = goDiagram.nodes.iterator;
  while (nodesIterator.next()) {
    const node = nodesIterator.value;
    if (!isConnected(goDiagram, node, nodeRoot)) {
      NotificationError({
        message: 'You have miss designed the link to connect the node.',
      });
      return false;
    }
  }

  return true;
};

export const ServiceMappingPage = () => {
  const columns: ColumnType<ServiceMapingModelResponse>[] = useMemo(() => {
    return [
      {
        title: 'Name',
        name: 'name',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data) => {
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {data}
            </KanbanText>
          );
        },
        width: '20%',
      },

      {
        title: 'Description',
        name: 'description',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data) => {
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {data}
            </KanbanText>
          );
        },
        width: '30%',
      },
      {
        title: 'Service template',
        name: 'serviceTemplate',
        advancedFilter: {
          enable: false,
        },
        customRender: () => {
          return (
            //TODO view template
            'Service 1'
          );
        },
      },
      {
        title: 'Business Criticality',
        name: 'businessCriticality',
        advancedFilter: {
          enable: false,
        },
        customRender: () => {
          return (
            //TODO view businessCriticality
            'Medium'
          );
        },
      },
      {
        title: 'Next update',
        name: 'nextUpdate',
        advancedFilter: {
          enable: false,
        },
        customRender: renderDateTime,
      },
    ];
  }, []);

  const designRef = useRef<{ getGoDiagram: () => go.Diagram | undefined }>(null);

  const resizeRef = useRef<{ resizeDiagram: () => void }>(null);

  const [openedModalServiceMappingDesign, { close: closeModalServiceMappingDesign, open: openModalServiceMappingDesign }] = useDisclosure(false);
  const [openedModalServiceMappingSave, { close: closeModalServiceMappingSave, open: openModalServiceMappingSave }] = useDisclosure(false);
  const [openedModalServiceMappingDetail, { close: closeModalServiceMappingDetail, open: openModalServiceMappingDetail }] = useDisclosure(false);
  const [rowSelected, setRowSelected] = useState<ServiceMapingModelResponse | undefined>(undefined);
  const [serviceMappingModel, setServiceMappingModel] = useState<ServiceMapingModel>({ name: '', status: 1 });
  const [listData, setListData] = useState<ServiceMapingModelResponse[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);
  const fetchListServiceMap = useCallback(() => {
    if (!tableAffected) {
      return;
    }
    const dataSend = tableAffectedToMultiColumnFilterPaginationRequestModel<ServiceMapingModelResponse>(
      tableAffected.sortedBy ? tableAffected : { ...tableAffected, sortedBy: 'createdDate', isReverse: true },
    );

    ServiceMappingApi.getAll(dataSend)
      .then((res) => {
        if (res.data) {
          setListData(res.data.content || []);
          setTotalRecords(res.data.totalElements);
        }
      })
      .catch(() => {});
  }, [tableAffected]);

  useEffect(() => {
    fetchListServiceMap();
  }, [fetchListServiceMap]);
  const handleSave = () => {
    if (designRef.current) {
      const currentDiagram = designRef.current.getGoDiagram();
      if (!validateDiagram(currentDiagram)) {
        return;
      }
    }
    openModalServiceMappingSave();
  };

  const handleButtonAddOrUpdate = () => {
    if (designRef.current) {
      const currentDiagram = designRef.current.getGoDiagram();
      const dataModel = getModelMapping(currentDiagram);
      const dataModelRequest: ServiceMapingModel = {
        ...serviceMappingModel,
        graph: dataModel.graph,
        graphData: dataModel.graphData,
        startCiId: dataModel.startCiId,
        rules: dataModel.rules,
        graphDataMapping: dataModel.graphDataMapping,
        startNodeId: dataModel.startNodeId,
      };
      dataModelRequest.id = rowSelected?.id;
      if (!dataModelRequest.name) {
        NotificationError({
          message: 'Name not empty.',
        });
        return;
      }
      ServiceMappingApi.saveMap(dataModelRequest)
        .then((res) => {
          if (res.data) {
            closeModalServiceMappingDesign();
            closeModalServiceMappingSave();
            fetchListServiceMap();
          }
        })
        .catch(() => {});
    }
  };

  const tableViewListServiceMappingProps: KanbanTableProps<ServiceMapingModelResponse> = useMemo(() => {
    return {
      columns: columns,
      data: listData,
      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
      pagination: {
        enable: true,
      },
      searchable: { enable: true, debounceTime: 300 },
      advancedFilterable: {
        enable: true,
        debounceTime: 1000,
        resetOnClose: true,
        compactMode: true,
      },
      showNumericalOrderColumn: true,
      actions: {
        customAction: isCurrentUserMatchPermissions(AclPermission.actionTableServiceMappingPermissions)
          ? (data) => (
              <>
                <GuardComponent requirePermissions={[AclPermission.viewDetailServiceMap]} hiddenOnUnSatisfy>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setRowSelected(data);
                      openModalServiceMappingDetail();
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                </GuardComponent>
                <GuardComponent requirePermissions={[AclPermission.updateServiceMap]} hiddenOnUnSatisfy>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setRowSelected(data);
                      setServiceMappingModel((prev) => ({ ...prev, ...data }));
                      openModalServiceMappingDesign();
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                </GuardComponent>
              </>
            )
          : undefined,
      },
    };
  }, [columns, listData, openModalServiceMappingDesign, openModalServiceMappingDetail, tableAffected, totalRecords]);

  return (
    <>
      {/* 4763 svc map */}
      <BreadcrumbComponent />
      <HeaderTitleComponent
        title='Service Mappings'
        rightSection={
          <GuardComponent requirePermissions={[AclPermission.createServiceMap]} hiddenOnUnSatisfy>
            <KanbanButton
              onClick={() => {
                setRowSelected(undefined);
                setServiceMappingModel({ name: '', status: 1 });
                openModalServiceMappingDesign();
              }}
              leftSection={<IconPlus />}>
              New Service Mapping
            </KanbanButton>
          </GuardComponent>
        }
      />

      <KanbanModal
        size={'100%'}
        opened={openedModalServiceMappingDesign}
        onClose={closeModalServiceMappingDesign}
        title={rowSelected ? `Update Service Mapping Design: ${rowSelected.name}` : 'Create Service Mapping'}
        contentAbandon
        actions={
          <KanbanButton
            onClick={() => {
              handleSave();
            }}>
            Save
          </KanbanButton>
        }>
        <ServiceMappingDesign ref={designRef} id={rowSelected?.id} />
      </KanbanModal>

      <KanbanModal
        size={'50%'}
        opened={openedModalServiceMappingSave}
        onClose={closeModalServiceMappingSave}
        title={'Name Service Mapping'}
        actions={
          <KanbanButton
            onClick={() => {
              handleButtonAddOrUpdate();
            }}>
            {rowSelected ? 'Update' : 'Add'}
          </KanbanButton>
        }>
        <KanbanInput
          label='Service Name'
          required={true}
          disabled={!!rowSelected}
          maxLength={100}
          value={serviceMappingModel?.name}
          onChange={(e) => setServiceMappingModel((prev) => ({ ...prev, name: e.target.value }))}
          onBlur={(e) => {
            const value = e.target.value;
            setServiceMappingModel((prev) => ({
              ...prev,
              name: formatStandardName(value),
            }));
          }}
        />
        <KanbanTextarea
          label='Service Description'
          defaultValue={serviceMappingModel?.description}
          maxLength={2000}
          onChange={(e) => setServiceMappingModel((prev) => ({ ...prev, description: e.target.value }))}
        />
        <KanbanRadio
          group={{
            name: 'status',
            label: '',
            mb: 'lg',
            description: 'Status',
            withAsterisk: false,
            defaultValue: serviceMappingModel?.status?.toString() || '1',
            onChange: (value) => setServiceMappingModel((prev) => ({ ...prev, status: Number(value) })),
          }}
          radios={[
            {
              value: '1',
              label: 'Enable',
              checked: true,
            },
            {
              value: '0',
              label: 'Disable',
            },
          ]}
        />
        <KanbanInput
          defaultValue={serviceMappingModel?.cronExpression}
          label='Run update every (minute)'
          onChange={(e) => setServiceMappingModel((prev) => ({ ...prev, cronExpression: e.target.value }))}
        />
      </KanbanModal>

      <div style={{ flex: 2 }}>
        <KanbanTable {...tableViewListServiceMappingProps} />
      </div>
      <KanbanModal
        size={'100%'}
        onToggleFullScreen={() => {
          resizeRef.current?.resizeDiagram();
        }}
        opened={openedModalServiceMappingDetail}
        onClose={closeModalServiceMappingDetail}
        title={`View service: ${rowSelected?.name}`}>
        {rowSelected && <ViewServiceModal ref={resizeRef} serviceMapId={rowSelected.id} />}
      </KanbanModal>
    </>
  );
};

ServiceMappingPage.displayName = 'ServiceMappingPage';
export default ServiceMappingPage;
