import { CiRelationshipTypeApi } from '@api/CiRelationshipTypeApi';
import type { ApiResponse } from '@core/api/ApiResponse';
import type { CiRelationshipTypeModel } from '@models/CiRelationshipType';
import { CiRelationshipTypesState, ciRelationshipTypesSlice, getCiRelationshipTypes } from '@slices/CiRelationshipTypesSlice';
import { call, put, select, takeLatest } from 'redux-saga/effects';

function* fetchDataSaga() {
  yield put(
    ciRelationshipTypesSlice.actions.setValue({
      data: [],
    }),
  );
  try {
    const response: ApiResponse<CiRelationshipTypeModel[]> = yield call(CiRelationshipTypeApi.getAll.bind(CiRelationshipTypeApi));
    yield put(
      ciRelationshipTypesSlice.actions.setValue({
        data: response.data || [],
      }),
    );
  } catch (ex) {
    yield put(
      ciRelationshipTypesSlice.actions.setValue({
        data: [],
      }),
    );
  }
}
function* fetchForEmptySaga() {
  const currentData: CiRelationshipTypesState = yield select(getCiRelationshipTypes);
  if (!currentData || !currentData.data || !currentData.data.length) {
    yield call(fetchDataSaga);
  }
}

export function* ciRelationshipTypesSaga() {
  // yield takeEvery(ciRelationshipTypesSlice.actions.fetchData, fetchDataSaga);
  yield takeLatest(ciRelationshipTypesSlice.actions.fetchForEmpty, fetchForEmptySaga);
}
