/* eslint-disable no-console */
import { GoJs } from '@common/libs';
import { makeDiagram } from '@common/utils/GoJsHelper';
import { fetchDiagramServiceMapping } from '@common/utils/ServiceMapHelper';
import type { ServiceMapingDetailsModel } from '@models/ServiceMapping';
import { useGetRelationshipTypes } from '@slices/CiRelationshipTypesSlice';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import styled from 'styled-components';

export interface ServiceMappingGraphHandle {
  getGoDiagram: () => GoJs.Diagram | undefined;
}

type ViewServiceMappingProps = {
  serviceMapDetails?: ServiceMapingDetailsModel;
};

const go = GoJs;
const Diagram = styled.div`
height: 100%;
min-height: calc(var(--kanban-modal-content-max-height, 80vh) - 90px);  
width: 100%;
background: var(--mantine-color-white);
  }
`;

export const ServiceMappingGraphTab = forwardRef<ServiceMappingGraphHandle, ViewServiceMappingProps>((props, ref) => {
  const { serviceMapDetails } = props;
  const [goDiagram, setGoDiagram] = useState<GoJs.Diagram | undefined>(undefined);
  const designRef = useRef<HTMLDivElement | null>(null);
  const ciTypes = useGetCiTypes();
  const relationships = useGetRelationshipTypes();

  useImperativeHandle(
    ref,
    () => ({
      getGoDiagram: () => {
        return goDiagram;
      },
    }),
    [goDiagram],
  );

  useEffect(() => {
    if (designRef.current && !goDiagram && serviceMapDetails?.graphData) {
      const myDiagram = makeDiagram({
        dom: designRef.current,
        showLayout: true,
        isEdit: false,
        isShowSelectionAdornment: false,
      });
      myDiagram.layout.isInitial = false;
      myDiagram.model = go.Model.fromJson(serviceMapDetails?.graphData);
      fetchDiagramServiceMapping(myDiagram, ciTypes.data || [], relationships.data || [], serviceMapDetails);
      setGoDiagram(myDiagram);
    }
  }, [designRef, goDiagram, serviceMapDetails?.graphData, ciTypes.data, relationships.data, serviceMapDetails]);

  return <>{designRef && <Diagram ref={designRef} />}</>;
});

ServiceMappingGraphTab.displayName = 'ServiceMappingGraph';
