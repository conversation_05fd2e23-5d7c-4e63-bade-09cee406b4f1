import { ColumnType, KanbanTable, TableAffactedSafeType } from 'kanban-design-system';
import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { renderDateTime } from 'kanban-design-system';
import equal from 'fast-deep-equal';
import { DateFilterOptionEnum, RangeDateModel, selectDateFilterOptions, CiSdpRequestModel } from '@models/ChangeSdp';
import { ChangeSdpApi } from '@api/ChangeSdpApi';
import { CustomLink } from '../CiRequest';
import { KanbanSelect } from 'kanban-design-system';
import { DD_MM_YYYY_FORMAT, endOfToday, getDateRange, startDate } from '@common/utils/DateUtils';
import { KanbanDatePicker } from 'kanban-design-system';
import { KanbanTooltip } from 'kanban-design-system';
import type { DateValue, DatesRangeValue } from '@mantine/dates';
import { CiRequestTypeEnum } from '@common/constants/CiDetail';
import { ConfigItemApi } from '@api/ConfigItemApi';
import { KanbanIconButton } from 'kanban-design-system';
import { IconEye } from '@tabler/icons-react';
import { buildChangeAssessmentDetailUrl } from '@common/utils/RouterUtils';
import { ChangeAssessmentAction } from '@models/ChangeAssessment';
import { tableAffectedAndRangeDateToPaginationRequestModel } from '@common/utils/KanbanTableUtils';
export type ChangeRequestSDPData = {
  status: string;
  stage: string;
};
export type CiChangeRequestProps = {
  ciId: number;
};

export const ChangeRequestTable = ({ ciId }: CiChangeRequestProps) => {
  const [totalRecords, setTotalRecords] = useState(0);

  const [isLoadingTable, setIsLoadingTable] = useState(false);
  const [changeRequests, setChangeRequests] = useState<CiSdpRequestModel[]>([]);
  const [changeIdMappingSDPData, setChangeIdMappingSDPData] = useState<Record<string, ChangeRequestSDPData>>({});
  const [tableAffectedChange, setTableAffectedChange] = useState<TableAffactedSafeType | undefined>(undefined);
  const [rangeDateToFilter, setRangeDateToFilter] = useState<RangeDateModel>({ fromDate: startDate, toDate: endOfToday });
  const [showCustomRangeDatePicker, setShowCustomRangeDatePicker] = useState<boolean>(false);
  const columns: ColumnType<CiSdpRequestModel>[] = useMemo(
    () => [
      {
        title: 'ID',
        name: 'requestId',
        width: 100,
      },
      {
        title: 'Stage',
        name: 'stage',
        width: 150,
        sortable: false,
        customRender: (_, data) => {
          const statusStateChange = changeIdMappingSDPData[data.requestId];
          return <>{statusStateChange ? statusStateChange.stage : ''}</>;
        },
      },
      {
        title: 'Status',
        name: 'status',
        width: 150,
        sortable: false,
        customRender: (_, data) => {
          const statusStateChange = changeIdMappingSDPData[data.requestId];
          return <>{statusStateChange ? statusStateChange.status : ''}</>;
        },
      },
      {
        title: 'SDP url',
        name: 'requestUrl',
        sortable: false,
        customRender: (data) => {
          return (
            <CustomLink target={'_blank'} to={data}>
              {data}
            </CustomLink>
          );
        },
      },
      {
        title: 'Created date',
        name: 'createdDate',
        width: 200,
        customRender: renderDateTime,
      },
    ],
    [changeIdMappingSDPData],
  );

  const fetchListChangeStatus = useCallback((arrayChangeId: number[]) => {
    if (arrayChangeId.length > 0) {
      ChangeSdpApi.getStatusByChangeIdIn(arrayChangeId, false)
        .then((res) => {
          if (res.data) {
            const statusMap = res.data.reduce((acc: Record<string, ChangeRequestSDPData>, e: CiSdpRequestModel) => {
              acc[e.requestId] = { status: e.status, stage: e.stage };
              return acc;
            }, {});
            setChangeIdMappingSDPData(statusMap);
          }
        })
        .catch(() => {});
    }
  }, []);

  const fetchListChangeAssessment = useCallback(() => {
    if (!tableAffectedChange) {
      return;
    }

    setIsLoadingTable(true);

    ConfigItemApi.getAllRequestByCiId(
      ciId,
      tableAffectedAndRangeDateToPaginationRequestModel(tableAffectedChange, rangeDateToFilter),
      CiRequestTypeEnum.CHANGE,
      false,
    )
      .then((res) => {
        setChangeRequests(res.data.content || []);
        setTotalRecords(res.data.totalElements);
        if (res.data.totalElements) {
          fetchListChangeStatus((res.data.content || []).map((e) => e.requestId));
        }
      })
      .catch(() => {})
      .finally(() => {
        setIsLoadingTable(false);
      });
  }, [tableAffectedChange, ciId, rangeDateToFilter, fetchListChangeStatus]);

  const handleSelectChange = (e: string | null) => {
    setShowCustomRangeDatePicker(e === DateFilterOptionEnum.CUSTOM);
    const rangeDate = getDateRange(e as DateFilterOptionEnum);
    setRangeDateToFilter(rangeDate);
  };

  const handleFromDateChange = (e: DateValue | DatesRangeValue | Date[]) => {
    setRangeDateToFilter((prev) => ({ ...prev, fromDate: e as Date }));
  };

  const handleToDateChange = (e: DateValue | DatesRangeValue | Date[]) => {
    const endOfDay = new Date((e as Date).setHours(23, 59, 59, 999));
    setRangeDateToFilter((prev) => ({ ...prev, toDate: endOfDay }));
  };

  useEffect(() => {
    fetchListChangeAssessment();
  }, [fetchListChangeAssessment]);

  return (
    <>
      <div style={{ flex: 2 }}>
        <KanbanTable
          title='Change request'
          columns={columns}
          key={1}
          data={changeRequests}
          isLoading={isLoadingTable}
          showNumericalOrderColumn={true}
          searchable={{
            enable: true,
            debounceTime: 300,
          }}
          pagination={{
            enable: true,
          }}
          sortable={{
            enable: true,
          }}
          serverside={{
            totalRows: totalRecords,
            onTableAffected(dataSet) {
              if (!equal(tableAffectedChange, dataSet)) {
                setTableAffectedChange(dataSet);
              }
            },
          }}
          actions={{
            customAction: (data) => (
              <>
                <KanbanTooltip label='Go to detail'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      window.open(buildChangeAssessmentDetailUrl(data.id, ChangeAssessmentAction.VIEW), '_blank');
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                </KanbanTooltip>
              </>
            ),
          }}
          customAction={() => (
            <>
              <KanbanSelect
                size='xs'
                m={'xs'}
                miw={'120'}
                data={selectDateFilterOptions}
                defaultValue={DateFilterOptionEnum.ALL}
                onChange={handleSelectChange}
              />
              {showCustomRangeDatePicker && (
                <>
                  <KanbanTooltip label='From date'>
                    <KanbanDatePicker
                      size='xs'
                      my={'xs'}
                      mr={'xs'}
                      defaultValue={rangeDateToFilter.fromDate}
                      valueFormat={DD_MM_YYYY_FORMAT}
                      onChange={handleFromDateChange}
                    />
                  </KanbanTooltip>
                  <KanbanTooltip label='To date'>
                    <KanbanDatePicker
                      size='xs'
                      my={'xs'}
                      defaultValue={rangeDateToFilter.toDate}
                      valueFormat={DD_MM_YYYY_FORMAT}
                      onChange={handleToDateChange}
                    />
                  </KanbanTooltip>
                </>
              )}
            </>
          )}
        />
      </div>
    </>
  );
};
export default ChangeRequestTable;
