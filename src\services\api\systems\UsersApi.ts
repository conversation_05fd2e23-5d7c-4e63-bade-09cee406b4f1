import type { ApiResponseDataBase } from '@core/api/ApiResponse';
import type { AclPermission } from '@models/AclPermission';
import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
import { BaseApi } from 'core/api/BaseApi';
import type { GroupResponse } from './GroupsApi';
import { BaseUrl } from '@core/api/BaseUrl';

export type SysGroupResponse = {
  id: number;
  name: string;
};

//users
export type UserModel = {
  id: number;
  userName: string;
  email: string;
  isActive: boolean;
  isSuperAdmin: boolean;
};

export type EntityUserInfoResponse = {
  id: number;
  userName: string;
  email: string;
  isActive: boolean;
  isSuperAdmin: boolean;
};

export type UserInfoResponse = {
  id: number;
  username: string;
  email: string;
  isActive: boolean;
  isSuperAdmin: boolean;
  groups: SysGroupResponse[];
  aclPermissions: AclPermission[];
  aclOtherPermissions: AclPermission[];
};

export type UserResponse = ApiResponseDataBase & UserModel;
export type UserResponsePagingResponse = PaginationResponseModel<UserResponse>;
export type UserResponseDto = UserResponse & {
  isSetting?: boolean;
  userOfGroup?: boolean;
};
export type EntityUserInfoResponsePagingResponse = PaginationResponseModel<EntityUserInfoResponse>;

export class UsersApi extends BaseApi {
  static baseUrl = BaseUrl.users;
  static baseUrlAdmin = BaseUrl.usersAdmin;

  static meUrl = `${this.baseUrl}/me`;

  static getMe() {
    return BaseApi.getData<UserInfoResponse>(UsersApi.meUrl);
  }

  static getAllUserWithPaging(pagination: PaginationRequestModel<EntityUserInfoResponse>) {
    return BaseApi.getData<EntityUserInfoResponsePagingResponse>(UsersApi.baseUrl, pagination, {}, { useLoading: false, useErrorNotification: true });
  }

  static getAllUserToView(pagination: PaginationRequestModel<EntityUserInfoResponse>, groupId: number) {
    return BaseApi.getData<EntityUserInfoResponsePagingResponse>(
      `${UsersApi.baseUrl}/view?groupId=${groupId}`,
      pagination,
      {},
      { useLoading: false, useErrorNotification: true },
    );
  }

  //users
  static getAllUserWithAdmin(pagination: PaginationRequestModel<UserResponse>) {
    return BaseApi.postData<UserResponsePagingResponse>(`${UsersApi.baseUrlAdmin}/filter`, pagination);
  }

  //users
  static getAllUser(pagination: PaginationRequestModel<UserResponse>) {
    return BaseApi.getData<UserResponsePagingResponse>(UsersApi.baseUrl, pagination);
  }

  static deleteUser(id: number) {
    return BaseApi.deleteData<boolean>(`${UsersApi.baseUrl}/${id}`);
  }

  static deleteUsers(ids: number[]) {
    return BaseApi.delete<boolean>(`${UsersApi.baseUrl}/batch`, {
      ids,
    });
  }
  static activeUser(data: UserModel) {
    const { isActive, isSuperAdmin, ...rest } = data;
    const payload = {
      ...rest,
      status: isActive,
      allGrant: isSuperAdmin,
    };
    return BaseApi.postData<UserModel>(`${UsersApi.baseUrl}/active`, payload);
  }

  static activeUsers(userIds: number[]) {
    return BaseApi.postData<number>(`${UsersApi.baseUrl}/actives`, userIds);
  }

  static inActiveUsers(userIds: number[]) {
    return BaseApi.postData<number>(`${UsersApi.baseUrl}/inactives`, userIds);
  }

  static getAllGroupByUserName(userName: string) {
    return BaseApi.getData<GroupResponse[]>(`${UsersApi.baseUrl}/${userName}/groups`);
  }

  static inSettingUsers(usernames: string[], isSuperAdmin: boolean) {
    return BaseApi.putData<boolean>(`${UsersApi.baseUrl}/admin?isSuperAdmin=${isSuperAdmin}`, usernames);
  }

  static createUser(userNames: string[]) {
    return BaseApi.postData<UserModel>(`${UsersApi.baseUrl}`, { userNames });
  }
}
