import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import type { CiAdvancedSearchDTO, CiAdvancedSearchModel } from '@models/CiAdvancedSearch';

export type CiAdvancedSearchResponse = CiAdvancedSearchModel;

export class CiAdvancedSearchApi extends BaseApi {
  static baseUrl = BaseUrl.ciAdvancedSearches;

  static getAdvancedSearchOfCurrentUser(ciTypeId?: number) {
    return BaseApi.getData<CiAdvancedSearchResponse[]>(`${this.baseUrl}`, { ciTypeId });
  }
  static saveOrUpdateAdvancedSearch(data: CiAdvancedSearchDTO) {
    return BaseApi.postData<CiAdvancedSearchResponse>(`${this.baseUrl}`, data);
  }

  static updateOrOverrideAdvancedSearch(data: CiAdvancedSearchDTO, id: number) {
    return BaseApi.postData<CiAdvancedSearchResponse>(`${this.baseUrl}/${id}`, data);
  }

  static deleteById(id: number) {
    return BaseApi.deleteData<boolean>(`${this.baseUrl}/${id}`);
  }
  static deleteByIds(ids: number[]) {
    return BaseApi.deleteData<boolean>(`${this.baseUrl}`, {
      ids,
    });
  }

  static findByConditions(id: number, name: string, ciTypeId?: number) {
    return BaseApi.getData<CiAdvancedSearchResponse>(`${this.baseUrl}/${id}`, {
      name,
      ciTypeId,
    });
  }
}
