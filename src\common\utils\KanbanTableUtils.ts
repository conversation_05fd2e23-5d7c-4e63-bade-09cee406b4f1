import type { RangeDateModel } from '@models/ChangeSdp';
import type { PaginationRequestModel, QueryRequestModel } from '@models/EntityModelBase';
import { dateToString } from './DateUtils';
import type { TableAffactedSafeType } from 'kanban-design-system';

export function tableAffectedToPaginationRequestModel<T>(tableAffected: TableAffactedSafeType<T>): PaginationRequestModel<T> {
  return {
    page: tableAffected.page - 1,
    size: tableAffected.rowsPerPage || 10,
    search: tableAffected.search,
    sortBy: tableAffected.sortedBy || ('createdDate' as keyof T),
    isReverse: !tableAffected.sortedBy ? true : tableAffected.isReverse,
  };
}

export function tableAffectedToPaginationRequestModelWithSortDefault<T, K extends keyof T>(
  tableAffected: TableAffactedSafeType<T>,
  sortedByDefault: K,
): PaginationRequestModel<T> {
  return {
    page: tableAffected.page - 1,
    size: tableAffected.rowsPerPage || 10,
    search: tableAffected.search,
    sortBy: tableAffected.sortedBy || sortedByDefault,
    isReverse: !tableAffected.sortedBy ? true : tableAffected.isReverse,
  };
}

export function tableAffectedToQueryRequestModel<T>(tableAffected: TableAffactedSafeType<T>): QueryRequestModel<T> {
  return {
    search: tableAffected.search,
    sortBy: tableAffected.sortedBy || ('createdDate' as keyof T),
    isReverse: !tableAffected.sortedBy ? true : tableAffected.isReverse,
  };
}

export function tableAffectedAndRangeDateToPaginationRequestModel<T>(
  tableAffected: TableAffactedSafeType<T>,
  rangeDateRequest: RangeDateModel,
): PaginationRequestModel<T> {
  const paginationRequest = tableAffectedToPaginationRequestModel(tableAffected);
  return {
    ...paginationRequest,
    fromDate: dateToString(rangeDateRequest.fromDate),
    toDate: dateToString(rangeDateRequest.toDate),
  };
}

export function tableAffectedToMultiColumnFilterPaginationRequestModel<T>(tableAffected: TableAffactedSafeType<T>): PaginationRequestModel<T> {
  return {
    page: tableAffected.page - 1,
    size: tableAffected.rowsPerPage || 10,
    search: tableAffected.search,
    //remove default sortedBy
    sortBy: tableAffected.sortedBy,
    isReverse: !tableAffected.sortedBy ? true : tableAffected.isReverse,
    advancedFilterMapping: tableAffected.advancedFilterMapping,
  };
}
