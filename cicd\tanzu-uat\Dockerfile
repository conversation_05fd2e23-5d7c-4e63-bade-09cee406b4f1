FROM k8sdev.mbbank.com.vn/support/nginx:1.21.4
COPY cicd/tanzu-uat/L01/configmap-k8s/nginx.conf /etc/nginx/conf.d/default.conf
COPY build /usr/share/nginx/html/itcmdb-frontend

# RUN touch /var/run/nginx.pid && chown nginx:nginx /var/run/nginx.pid
# RUN chown -R nginx:nginx /var/cache/nginx
# RUN chown -R nginx:nginx /etc/nginx
# USER nginx
# Add permissions for nginx user 
RUN chown -R nginx:nginx /var/cache/nginx && \ 
    chown -R nginx:nginx /var/log/nginx && \ 
    chown -R nginx:nginx /etc/nginx/conf.d 
RUN touch /var/run/nginx.pid && \ 
        chown -R nginx:nginx /var/run/nginx.pid 

# Set the default user. 
USER nginx

EXPOSE 9989
