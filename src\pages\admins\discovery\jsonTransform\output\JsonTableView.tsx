import React, { useEffect, useMemo, useState } from 'react';
import 'rc-tree/assets/index.css';
import { getColumnTypeFromNodes, JsonTransformProps } from '../helper/JsonTransformHelper';
import { ColumnType, KanbanTable, KanbanTableProps } from 'kanban-design-system';
import { useDataTransform } from '@context/DataTransformContext';

const convertValuesToString = (obj: unknown): unknown => {
  if (typeof obj === 'object' && obj !== null) {
    if (Array.isArray(obj)) {
      return obj.map((item) => convertValuesToString(item));
    } else {
      return Object.fromEntries(Object.entries(obj as Record<string, unknown>).map(([key, value]) => [key, convertValuesToString(value)]));
    }
  }
  return String(obj);
};

const JsonTableView: React.FC<JsonTransformProps> = ({ jsonTransform, tableMaxHeight }) => {
  const [keyTable, setKeyTable] = useState<number>(0);
  const [columnDatas, setColumDatas] = useState<ColumnType<unknown>[]>([]);
  const { state: dataTransform } = useDataTransform();
  const columnNames = dataTransform?.columnDatasForOutput;
  useEffect(() => {
    setColumDatas(getColumnTypeFromNodes(columnNames));
    setKeyTable((pre) => {
      return pre + 1;
    });
  }, [columnNames]);

  const columns: ColumnType<unknown>[] = useMemo(() => {
    return columnDatas;
  }, [columnDatas]);

  const tableViewProps: KanbanTableProps<any> = useMemo(() => {
    return {
      columns: columns,
      key: keyTable,
      data: jsonTransform.map((item) => convertValuesToString(item)),
    };
  }, [columns, keyTable, jsonTransform]);

  return <KanbanTable maxHeight={tableMaxHeight} {...tableViewProps} />;
};

export default JsonTableView;
