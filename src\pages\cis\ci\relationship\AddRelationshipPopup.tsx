import React, { useState, useImperativeHandle, forwardRef, useRef, useCallback } from 'react';

import { useDisclosure } from '@mantine/hooks';
import { KanbanModal } from 'kanban-design-system';
import { KanbanButton } from 'kanban-design-system';

import type { CiTypeRelationAttrModel, CiTypeRelationModel } from '@models/CiTypeRelation';
import AddRelationship, { AddRelationshipMethods } from './AddRelationship';
import type { CiRelationshipInfoModel } from '@models/CiRelationship';
import type { RelationShipOfBusinessView } from '@models/CIBusinessViews';
import { ConfigItemApi } from '@api/ConfigItemApi';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';

type AddRelationshipPopupProps = {
  ciId?: number;
  ciTypeId?: number;
  fetchRelationship?: () => void;
  updateDataGraphView?: (relationShips: RelationShipOfBusinessView[]) => void;
  //show model when open fullscreen
  withinPortal?: boolean;
};

export type AddRelationshipPopupMethods = {
  openModalCreateNew: () => void;
  openModalCreateNewWithData: (ciIdData: number, ciTypeIdData: number) => void;
};

export const AddRelationshipPopup = forwardRef<AddRelationshipPopupMethods, AddRelationshipPopupProps>((props, ref) => {
  const { ciId, ciTypeId, fetchRelationship, updateDataGraphView, withinPortal = true } = props;
  const [isValidAdd, setIsValidAdd] = useState(true);
  const [openedModalCreateNew, { close: closeModalCreateNew, open: openModalCreateNew }] = useDisclosure(false);
  const createRelationshipRef = useRef<AddRelationshipMethods | null>(null);
  const [ciRelationships, setCiRelationships] = useState<CiRelationshipInfoModel[]>([]);
  const [ciTypeRelationships, setCiTypeRelationships] = useState<CiTypeRelationModel[]>([]);
  const [allCiTypeRelationAttribute, setAllCiTypeRelationAttribute] = useState<CiTypeRelationAttrModel[]>([]);
  const [ciIdWithDataInput, setCiIdWithDataInput] = useState<number | undefined>(props.ciId);
  const fetchCiRelationships = useCallback(
    async (ciIdOpen?: number) => {
      // when use openModalCreateNewWithData then priority ciIdOpen
      const ciIdData = ciIdOpen ? ciIdOpen : ciId || 0;
      if (ciIdData !== 0) {
        const res = await ConfigItemApi.getAllDirectRelationship(ciIdData);
        setCiRelationships(res.data);
      }
    },
    [ciId],
  );

  // CI TYPE RELATION
  const fetchCiTypeRelationship = useCallback(
    async (ciTypeOpen?: number) => {
      // when use openModalCreateNewWithData then priority ciIdOpen
      const ciTypeIdData = ciTypeOpen ? ciTypeOpen : ciTypeId || 0;
      if (ciTypeIdData !== 0) {
        const res = await ConfigItemTypeApi.getAllRelationships(ciTypeIdData);
        setCiTypeRelationships(res.data || []);

        const resAttributes = await ConfigItemTypeApi.getAllRelationshipAttributes(ciTypeIdData);
        setAllCiTypeRelationAttribute(resAttributes.data);
      }
    },
    [ciTypeId],
  );

  const onConfirmCreateNew = () => {
    createRelationshipRef.current?.onCreateNew();
  };

  const onOpenModalCreateNew = useCallback(async () => {
    await fetchCiTypeRelationship();
    await fetchCiRelationships();
    openModalCreateNew();
  }, [fetchCiRelationships, fetchCiTypeRelationship, openModalCreateNew]);

  const onOpenModalCreateNewWithData = useCallback(
    async (ciIdData: number, ciTypeIdData: number) => {
      setCiIdWithDataInput(ciIdData);
      await fetchCiTypeRelationship(ciTypeIdData);
      await fetchCiRelationships(ciIdData);
      openModalCreateNew();
    },
    [fetchCiRelationships, fetchCiTypeRelationship, openModalCreateNew],
  );

  const onCloseModalCreateNew = () => {
    closeModalCreateNew();
    createRelationshipRef.current?.onResetWhenCloseModalCreateNew();
  };

  const resetCheckbox = (isValid: boolean) => {
    setIsValidAdd(isValid);
  };

  useImperativeHandle<any, AddRelationshipPopupMethods>(
    ref,
    () => ({
      openModalCreateNew: onOpenModalCreateNew,
      openModalCreateNewWithData: onOpenModalCreateNewWithData,
    }),
    [onOpenModalCreateNew, onOpenModalCreateNewWithData],
  );

  return (
    <>
      <KanbanModal
        size={'70%'}
        opened={openedModalCreateNew}
        fullScreen={!withinPortal}
        onClose={onCloseModalCreateNew}
        title={'Create Relationship'}
        withinPortal={withinPortal}
        actions={
          <KanbanButton onClick={onConfirmCreateNew} disabled={isValidAdd}>
            OK
          </KanbanButton>
        }>
        <AddRelationship
          ref={createRelationshipRef}
          ciId={Number(ciId || ciIdWithDataInput)}
          ciTypeRelationships={ciTypeRelationships}
          allCiTypeRelationAttribute={allCiTypeRelationAttribute}
          ciRelationships={ciRelationships}
          fetchRelationship={fetchRelationship}
          setIsValidAdd={resetCheckbox}
          onCloseModalCreateNew={closeModalCreateNew}
          updateDataGraphView={updateDataGraphView}
          withinPortal={withinPortal}
        />
      </KanbanModal>
    </>
  );
});
AddRelationshipPopup.displayName = 'AddRelationshipPopup';
export default AddRelationshipPopup;
