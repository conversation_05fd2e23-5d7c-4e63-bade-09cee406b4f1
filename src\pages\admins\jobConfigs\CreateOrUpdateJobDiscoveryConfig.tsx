import {
  KanbanButton,
  KanbanCheckbox,
  KanbanDateTimePicker,
  KanbanInput,
  KanbanNumberInput,
  KanbanSelect,
  KanbanSwitch,
  KanbanTabs,
  KanbanTextarea,
} from 'kanban-design-system';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Box, ComboboxItem, Flex, Group } from '@mantine/core';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { buildJobDiscoveryConfigUrl, buildListJobDiscoveryConfigUrl, jobDiscoveryConfigPath } from '@common/utils/RouterUtils';
import { IconArrowBack } from '@tabler/icons-react';
import { JobDiscoveryModel } from '@models/JobDiscoveryConfig';
import { JobStatusEnum } from '@common/constants/JobStatusEnum';
import { getComboboxTimeUnitEnum, getTextInterval, TimeUnitEnum } from '@common/constants/TimeUnitEnum';
import { DatesRangeValue, DateValue } from '@mantine/dates';
import { calculateNextRuns, DD_MM_YYYY_HH_MM_SS_FORMAT } from '@common/utils/DateUtils';
import { getComboboxJobTypeEnum, JobTypeEnum } from '@common/constants/JobTypeEnum';
import { DiscoverySourceDataApi } from '@api/discovery/DiscoverySourceDataApi';
import { JobGroupEnum } from '@common/constants/JobGroupEnum';
import { JobDiscoveryConfigApi } from '@api/discovery/JobDiscoveryConfigApi';
import { BreadcrumbComponent, UrlBaseCrumbData } from '../breadcrumb/BreadcrumbComponent';
import { JobDiscoveryAction } from '@common/constants/JobDiscoveryActionEnum';
import { formatStandardName } from '@common/utils/StringUtils';
import { ViewLogJobDiscoveryConfig } from './ViewLogJobDiscoveryConfig';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';

type JobDiscoveryConfigError = {
  startDate?: string;
  endDate?: string;
  retentionDay?: string;
  intervalTime?: string;
};

const isValidData = (data: JobDiscoveryModel): boolean => {
  if (!(data.jobName && data.frequency && data.intervalTime && data.startDate && data.jobType && data.configId)) {
    return false;
  }

  if (data.jobType === JobTypeEnum.CLEAR_DATA_STAGING_TABLE && !data.retentionDay) {
    return false;
  }

  return true;
};

export const validateEndDate = (dataMap: any): string | undefined => {
  if (
    !dataMap.frequency ||
    !dataMap.intervalTime ||
    TimeUnitEnum.ONCE_TIME === dataMap.frequency ||
    !dataMap.startDate ||
    !dataMap.endDate ||
    (dataMap.id && dataMap.status === JobStatusEnum.INACTIVE)
  ) {
    return undefined;
  }

  const currentDate = new Date();
  const startDate = new Date(dataMap.startDate);
  const endDate = new Date(dataMap.endDate);

  // Check if the end date is earlier than the start date
  if (endDate.getTime() < startDate.getTime()) {
    return 'The end date cannot be earlier than the start date';
  }

  // Check if the end date is earlier than the current time
  if (endDate.getTime() < currentDate.getTime()) {
    return 'The end date cannot be earlier than the current time';
  }

  const validEndDate = calculateNextRuns(dataMap.startDate, dataMap.endDate, dataMap.frequency, dataMap.intervalTime, 1);

  // Ensure that the end date is at least intervalTime units away based on frequency
  if (validEndDate.length === 0) {
    return `The end date must be at least ${dataMap.intervalTime} ${dataMap.frequency.toLowerCase()}(s) after the start date`;
  }

  return undefined;
};

export const CreateOrUpdateJobDiscoveryConfig = () => {
  const { id } = useParams();
  const jobConfigId = Number(id);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const actionParam = searchParams.get('action');
  const action = Object.values(JobDiscoveryAction).includes(actionParam as JobDiscoveryAction)
    ? (actionParam as JobDiscoveryAction)
    : JobDiscoveryAction.UPDATE;

  const [dataMap, setDataMap] = useState<JobDiscoveryModel>({
    jobGroup: JobGroupEnum.DISCOVERY,
    jobName: '',
    startDate: new Date(),
    configId: 0,
    intervalTime: 1,
    status: JobStatusEnum.ACTIVE,
    retentionDay: 1,
    frequency: TimeUnitEnum.DAY,
  });
  const [frequencyComboxItem, setFrequencyComboxItem] = useState(getComboboxTimeUnitEnum([TimeUnitEnum.DAY, TimeUnitEnum.WEEK]));
  const [jobTypeComboxItem] = useState(getComboboxJobTypeEnum([JobTypeEnum.GET_DISCOVERY_DATA, JobTypeEnum.CLEAR_DATA_STAGING_TABLE]));
  const [mapName, setMapName] = useState<string>('');
  const [dataSourceComboxItem, setDataSourceComboxItem] = useState<ComboboxItem[]>([]);
  const [errorMessage, setErrorMessage] = useState<JobDiscoveryConfigError>();
  const [isEndDateChecked, setIsEndDateChecked] = useState<boolean>(false);

  const getMapDetails = useCallback(() => {
    if (jobConfigId) {
      JobDiscoveryConfigApi.getById(jobConfigId)
        .then((res) => {
          if (res.data) {
            setDataMap(res.data);
            setMapName(res.data.jobName);
          }
        })
        .catch(() => {});
    }
  }, [jobConfigId]);

  const validateData = useCallback((dataMap: JobDiscoveryModel) => {
    const error = validateEndDate({ ...dataMap });
    if (error) {
      setErrorMessage((prev) => ({ ...prev, endDate: error }));
    } else {
      setErrorMessage((prev) => ({ ...prev, endDate: undefined }));
    }
    return error;
  }, []);

  const saveMap = useCallback(
    (data: JobDiscoveryModel) => {
      if (validateData(data)) {
        return;
      }

      JobDiscoveryConfigApi.save(data)
        .then((res) => {
          if (res.data) {
            NotificationSuccess({ title: 'Success', message: 'Save schedule job successfully' });
            navigate(buildListJobDiscoveryConfigUrl());
          }
        })
        .catch(() => {});
    },
    [navigate, validateData],
  );

  const handleStartDateChange = (dateValue: DateValue | DatesRangeValue | Date[]) => {
    const startDate = new Date(dateValue as Date);
    if (dataMap.endDate && startDate.getTime() > new Date(dataMap.endDate).getTime()) {
      setErrorMessage((prev) => ({ ...prev, startDate: 'The start date must be before the end date' }));
    } else {
      setErrorMessage((prev) => ({ ...prev, startDate: undefined }));
    }

    setDataMap((prev) => ({ ...prev, startDate: startDate }));
  };

  const handleEndDateChange = (dateValue: DateValue | DatesRangeValue | Date[]) => {
    if (dateValue) {
      const endDate = new Date(dateValue as Date);
      const error = validateEndDate({ ...dataMap, endDate });
      if (error) {
        setErrorMessage((prev) => ({ ...prev, endDate: error }));
      } else {
        setErrorMessage((prev) => ({ ...prev, endDate: undefined }));
      }

      setDataMap((prev) => ({ ...prev, endDate: endDate }));
    } else {
      // If no date value is provided, clear the endDate in dataMap
      setDataMap((prev) => ({ ...prev, endDate: undefined }));
      setErrorMessage({ endDate: undefined });
    }
  };

  const renderStartDateLabel = () => (
    <Box p={4}>
      Start date
      {errorMessage?.startDate && <span style={{ color: 'red' }}> - {errorMessage.startDate}</span>}
      <span style={{ color: 'red' }}> *</span>
    </Box>
  );

  const getAllSourceDatas = useCallback(() => {
    DiscoverySourceDataApi.getAllDataSource()
      .then((res) => {
        if (res.data) {
          const datas = res.data || [];
          const comboboxs = datas.map((obj) => {
            return {
              value: `${obj.id}`,
              label: obj.name,
            };
          });
          setDataSourceComboxItem(comboboxs);
        }
      })
      .catch(() => {});
  }, []);

  useEffect(() => {
    const error = validateEndDate(dataMap);

    if (error) {
      setErrorMessage((prev) => ({ ...prev, endDate: error }));
    } else {
      setErrorMessage((prev) => ({ ...prev, endDate: undefined }));
    }
  }, [dataMap]);

  useEffect(() => {
    if (JobTypeEnum.GET_DISCOVERY_DATA === dataMap.jobType) {
      setFrequencyComboxItem(
        getComboboxTimeUnitEnum([TimeUnitEnum.ONCE_TIME, TimeUnitEnum.MINUTE, TimeUnitEnum.HOUR, TimeUnitEnum.DAY, TimeUnitEnum.WEEK]),
      );
    } else {
      setFrequencyComboxItem(getComboboxTimeUnitEnum([TimeUnitEnum.DAY, TimeUnitEnum.WEEK]));
    }
  }, [dataMap.jobType]);

  useEffect(() => {
    getMapDetails();
    getAllSourceDatas();
  }, [getAllSourceDatas, getMapDetails]);

  useEffect(() => {
    if (dataMap.endDate) {
      setIsEndDateChecked(true);
    }
  }, [dataMap.endDate]);

  const locationCustomPaths = useMemo((): UrlBaseCrumbData => {
    const originPath = buildJobDiscoveryConfigUrl(Number(id), JobDiscoveryAction.UPDATE);
    return {
      [`/${id}`]: {
        title: JobDiscoveryAction.CREATE === action ? `${formatStandardName(action)}` : `${formatStandardName(action)} ${dataMap.jobName}`,
        href: originPath,
      },
    };
  }, [action, dataMap.jobName, id]);

  return (
    <>
      <BreadcrumbComponent locationCustomPaths={locationCustomPaths} />

      <KanbanTabs
        configs={{
          variant: 'outline',
          defaultValue: 'INFO',
        }}
        tabs={{
          INFO: {
            title: 'Schedule Jobs',
            content: (
              <>
                <HeaderTitleComponent
                  title={dataMap.id ? `Update ${mapName}` : 'Create schedule job'}
                  rightSection={
                    <Flex gap={10}>
                      <KanbanButton
                        leftSection={<IconArrowBack />}
                        variant='outline'
                        onClick={() => {
                          navigate(jobDiscoveryConfigPath);
                        }}>
                        Cancel
                      </KanbanButton>

                      <KanbanButton
                        disabled={!isValidData(dataMap) || errorMessage?.startDate || errorMessage?.endDate}
                        onClick={() => {
                          saveMap(dataMap);
                        }}>
                        Save
                      </KanbanButton>
                    </Flex>
                  }
                />
                <Group grow>
                  <KanbanInput
                    required
                    label='Name'
                    maxLength={1000}
                    value={dataMap.jobName}
                    onChange={(value) => {
                      setDataMap((prev) => ({ ...prev, jobName: value.target.value }));
                    }}
                    onBlur={() => {
                      setDataMap((prev) => ({ ...prev, jobName: prev.jobName.trim() }));
                    }}
                  />
                  <Group gap={1}>
                    <KanbanSwitch
                      checked={JobStatusEnum.ACTIVE === dataMap.status}
                      onChange={(e) => {
                        setDataMap((prev) => ({ ...prev, status: e.target.checked ? JobStatusEnum.ACTIVE : JobStatusEnum.INACTIVE }));
                      }}
                      label={JobStatusEnum.ACTIVE === dataMap.status ? 'Active' : 'Inactive'}
                    />
                  </Group>
                </Group>
                <Group grow>
                  <KanbanSelect
                    required
                    disabled={!!dataMap.id}
                    searchable
                    value={dataMap.jobType}
                    data={jobTypeComboxItem}
                    label='Action'
                    onChange={(value) => {
                      if (!value) {
                        return;
                      }
                      setDataMap((prev) => ({ ...prev, jobType: value as JobTypeEnum }));
                    }}
                  />
                  <KanbanSelect
                    required
                    searchable
                    value={`${dataMap.configId}`}
                    data={dataSourceComboxItem}
                    label='Data Source'
                    onChange={(value, data) => {
                      if (!value) {
                        return;
                      }
                      setDataMap((prev) => ({ ...prev, configId: Number(value || 0), dataSourceName: data.label }));
                    }}
                  />
                </Group>
                {JobTypeEnum.CLEAR_DATA_STAGING_TABLE === dataMap.jobType && (
                  <Group grow>
                    <KanbanNumberInput
                      label={`Remove records older than ${dataMap.retentionDay ? dataMap.retentionDay : ''} day`}
                      placeholder='Input number value'
                      value={dataMap.retentionDay}
                      allowNegative={false}
                      allowDecimal={false}
                      clampBehavior='strict'
                      min={1}
                      error={errorMessage?.retentionDay}
                      withAsterisk
                      onChange={(value) => {
                        const numberValue = value === '' ? undefined : Number(value);
                        setDataMap((prev) => ({
                          ...prev,
                          retentionDay: JobTypeEnum.CLEAR_DATA_STAGING_TABLE === dataMap.jobType ? numberValue : undefined,
                        }));

                        if (numberValue) {
                          setErrorMessage((prev) => ({ ...prev, retentionDay: undefined }));
                        } else {
                          setErrorMessage((prev) => ({ ...prev, retentionDay: 'Remove records older than day cannot be empty' }));
                        }
                      }}
                    />
                    <span></span>
                  </Group>
                )}
                <div style={{ marginTop: '20px' }}>
                  <HeaderTitleComponent title={''} />
                </div>
                <Group grow>
                  <KanbanSelect
                    required
                    value={dataMap.frequency}
                    data={frequencyComboxItem}
                    label='Repeat Frequency'
                    onChange={(value) => {
                      if (!value) {
                        return;
                      }
                      const selectedFrequency = value as TimeUnitEnum;
                      const isOnceTime = selectedFrequency === TimeUnitEnum.ONCE_TIME;

                      setDataMap((prev) => ({
                        ...prev,
                        frequency: selectedFrequency,
                        endDate: isOnceTime ? undefined : prev.endDate,
                      }));

                      if (isOnceTime) {
                        setIsEndDateChecked(false);
                      }
                    }}
                  />
                  <KanbanNumberInput
                    label={
                      <>
                        {errorMessage?.intervalTime ? (
                          <span style={{ color: 'red' }}>{errorMessage.intervalTime}</span>
                        ) : (
                          getTextInterval(dataMap.intervalTime, dataMap.frequency)
                        )}
                      </>
                    }
                    placeholder='Input number value'
                    disabled={TimeUnitEnum.ONCE_TIME === dataMap.frequency}
                    value={TimeUnitEnum.ONCE_TIME === dataMap.frequency ? '' : dataMap.intervalTime ?? 1}
                    allowNegative={false}
                    allowDecimal={false}
                    clampBehavior='strict'
                    min={1}
                    max={99}
                    withAsterisk
                    onChange={(value) => {
                      const numberValue = value === '' ? undefined : Number(value);
                      if (numberValue) {
                        setErrorMessage((prev) => ({ ...prev, intervalTime: undefined }));
                      } else {
                        setErrorMessage((prev) => ({ ...prev, intervalTime: 'Run every day(s) cannot be empty' }));
                      }

                      setDataMap((prev) => ({
                        ...prev,
                        intervalTime: TimeUnitEnum.ONCE_TIME === dataMap.frequency ? 0 : numberValue ?? prev.intervalTime,
                      }));
                    }}
                  />
                </Group>
                <Group grow>
                  <KanbanDateTimePicker
                    withSeconds
                    label={renderStartDateLabel()}
                    valueFormat={DD_MM_YYYY_HH_MM_SS_FORMAT}
                    value={dataMap.startDate ? new Date(dataMap.startDate) : new Date()}
                    onChange={handleStartDateChange}
                  />
                  <KanbanDateTimePicker
                    clearable
                    withSeconds
                    disabled={TimeUnitEnum.ONCE_TIME === dataMap.frequency || !isEndDateChecked}
                    label={
                      <>
                        <KanbanCheckbox
                          disabled={TimeUnitEnum.ONCE_TIME === dataMap.frequency}
                          label={<>End date {errorMessage?.endDate && <span style={{ color: 'red' }}> - {errorMessage.endDate}</span>}</>}
                          checked={(isEndDateChecked || !!dataMap.endDate) && TimeUnitEnum.ONCE_TIME !== dataMap.frequency}
                          onChange={(e) => {
                            const checked = e.target.checked;
                            setIsEndDateChecked(checked);
                            if (!checked) {
                              setDataMap((prev) => ({ ...prev, endDate: undefined }));
                              setErrorMessage((prev) => ({ ...prev, startDate: undefined, endDate: undefined }));
                            }
                          }}
                        />
                      </>
                    }
                    valueFormat={DD_MM_YYYY_HH_MM_SS_FORMAT}
                    value={TimeUnitEnum.ONCE_TIME === dataMap.frequency ? null : dataMap.endDate ? new Date(dataMap.endDate) : null}
                    minDate={new Date()}
                    onChange={handleEndDateChange}
                  />
                </Group>
                {TimeUnitEnum.ONCE_TIME !== dataMap.frequency && (
                  <Group grow>
                    <KanbanTextarea
                      label={
                        <>
                          Schedule Simulation: <br /> (Limited to first 8)
                        </>
                      }
                      autosize
                      value={calculateNextRuns(dataMap.startDate, dataMap.endDate, dataMap.frequency, dataMap.intervalTime, 8).join('\n')}
                    />
                    <span></span>
                  </Group>
                )}
              </>
            ),
          },
          ...(isCurrentUserMatchPermissions([AclPermission.viewLogJobConfig]) &&
            jobConfigId !== 0 && {
              HISTORY: {
                title: 'Job History',
                content: <ViewLogJobDiscoveryConfig jobId={jobConfigId} jobName={dataMap.jobName} />,
              },
            }),
        }}
      />
    </>
  );
};
