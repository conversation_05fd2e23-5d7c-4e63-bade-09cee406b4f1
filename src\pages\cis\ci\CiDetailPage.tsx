import { ConfigItemApi } from '@api/ConfigItemApi';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanModal, KanbanTabs } from 'kanban-design-system';
import type { ConfigItemInfoModel, ConfigItemModel } from '@models/ConfigItem';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import CiInfo from './CiInfo';
import { Flex, Space } from '@mantine/core';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import type { ConfigItemTypeAttrResponse } from '@api/ConfigItemTypeAttrApi';
import type { ConfigItemAttrModel } from '@models/ConfigItemAttr';
import { useDisclosure } from '@mantine/hooks';
import { KanbanButton } from 'kanban-design-system';
import CiUpdate from './CiUpdate';
import GuardComponent from '@components/GuardComponent';
import { IconCopy, IconEdit, IconTrash } from '@tabler/icons-react';
import { NotificationError, NotificationSuccess, NotificationWarning } from '@common/utils/NotificationUtils';
import { KanbanConfirmModal } from 'kanban-design-system';
import { buildCiManageDetailUrl, buildCiTypeUrl, buildCiUrl, ciPath, ciTypePath } from '@common/utils/RouterUtils';
import type { ConfigItemAttrCustomModel } from '@models/ConfigItemAttrCustom';
import CiHistory from './CiHistory';
import type { HistoryDescription } from '@models/CiHistory';
import { configItemTypeAttrSorted, historyDescriptionToString } from '@common/utils/CiUtils';
import CiRelationship from './relationship/CiRelationship';
import { CiManagementApi } from '@api/CiManagementApi';
import { useSelector } from 'react-redux';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import CiRequest from './CiRequest';
import { CiDetailScreenType, CiDetailSubTabType, ActionTypeEnum } from '@common/constants/CiDetail';
import { AclPermission } from '@models/AclPermission';
import { PermissionAction } from '@common/constants/PermissionAction';
import ConfirmDeleteCiModal, { ConfirmDeleteCiModalMethods } from './ConfirmDeleteCiModal';
import CiDiscovery from './CiDiscovery';
import { isUserAuthorizedForPermissionsGroups } from '@common/utils/AclPermissionUtils';
import { BreadcrumbComponent, UrlBaseCrumbData } from '@pages/admins/breadcrumb/BreadcrumbComponent';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import { formatStandardName } from '@common/utils/StringUtils';

export type CiDetailPageProps = {
  ciId: number;
  ciTypeId: number;
  isFromBusinessView?: boolean;
  allowUpdate?: boolean;
};

export const CiDetailPage = (props: CiDetailPageProps) => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState<string>(CiDetailScreenType.INFO);
  const [openedModalEdit, { close: closeModalEdit, open: openModalEdit }] = useDisclosure(false);
  const [openedModalNoticeChange, { close: closeModalNoticeChange, open: openModalNoticeChange }] = useDisclosure(false);

  const { allowUpdate = true, ciId, ciTypeId, isFromBusinessView } = props;

  const [listCiTypeAttribute, setListCiTypeAttribute] = useState<ConfigItemTypeAttrResponse[]>([]);
  const [ciAttributes, setCiAttributes] = useState<ConfigItemAttrModel[]>([]);
  const [ciAttributesCustom, setCiAttributesCustom] = useState<ConfigItemAttrCustomModel[]>([]);
  const [openedModalAfterUpdate, { close: closeModalAfterUpdate, open: openModalAfterUpdate }] = useDisclosure(false);

  const [ciInfo, setCiInfo] = useState<ConfigItemModel>({
    id: 0,
    ciTypeId: 0,
    name: '',
  });

  const [ciInfoUpdate, setCiInfoUpdate] = useState(ciInfo);
  const [ciAttributeMappingAttributeUpdate, setCiAttributeMappingAttributeUpdate] = useState<Record<number, ConfigItemAttrModel>>({});
  const [ciAttributeCustomUpdate, setCiAttributeCustomUpdate] = useState<ConfigItemAttrCustomModel[]>([]);

  const [historyDescription, setHistoryDescription] = useState<HistoryDescription | undefined>();

  const [oldUserEdit, setOldUserEdit] = useState('');
  const [oldCiTempId, setOldCiTempId] = useState<number>();

  const currentUser = useSelector(getCurrentUser);

  const [actionType, setActionType] = useState<ActionTypeEnum | null>(null);

  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab) {
      setActiveTab(tab);
    }
  }, [searchParams]);
  useEffect(() => {
    ConfigItemApi.getInfoById(ciId)
      .then((res) => {
        if (res.data.ci) {
          setCiInfo(res.data.ci);
          setCiAttributes(res.data.attributes);
          setCiAttributesCustom(res.data.attributeCustoms);
        } else {
          NotificationError({
            message: 'CI Not Found',
          });
          navigate(buildCiTypeUrl(ciTypeId));
        }
      })
      .catch(() => {});
  }, [ciId, ciTypeId, navigate]);

  const fetchCiTypesAttribute = useCallback(() => {
    ConfigItemTypeApi.getAllAttributes(ciTypeId)
      .then((res) => {
        setListCiTypeAttribute(configItemTypeAttrSorted(res.data));
      })
      .catch(() => {});
  }, [ciTypeId]);

  useEffect(() => {
    fetchCiTypesAttribute();
  }, [fetchCiTypesAttribute]);

  const ciAttributeMapping = useMemo(() => {
    const result: Record<number, ConfigItemAttrModel> = {};

    for (const ciTypeAttribute of listCiTypeAttribute) {
      result[ciTypeAttribute.id] = ciAttributes.find((x) => x.ciTypeAttributeId === ciTypeAttribute.id) || {
        ciTypeAttributeId: ciTypeAttribute.id,
        id: 0,
        ciId: 0,
        value: '',
      };
    }

    return result;
  }, [ciAttributes, listCiTypeAttribute]);

  const actionOpenModalUpdateInfo = (actionType: ActionTypeEnum) => {
    if (actionType === ActionTypeEnum.CLONE) {
      setCiInfoUpdate({ ...ciInfo, id: 0, name: `${ciInfo.name} copy` });
    } else {
      setCiInfoUpdate({ ...ciInfo });
    }

    setCiAttributeMappingAttributeUpdate({ ...ciAttributeMapping });
    setCiAttributeCustomUpdate([...ciAttributesCustom]);
    openModalEdit();
  };

  const openModalUpdateInfo = (actionType: ActionTypeEnum) => {
    setActionType(actionType);
    if (actionType === ActionTypeEnum.CLONE) {
      actionOpenModalUpdateInfo(actionType);
      return;
    }

    CiManagementApi.getCiTempByCiIdIn([ciId])
      .then((res) => {
        if (res.data && res.data.length > 0) {
          const oldData = res.data[0];
          setOldCiTempId(oldData.id);
          setOldUserEdit(oldData.owner);
          openModalNoticeChange();
          return;
        } else {
          actionOpenModalUpdateInfo(actionType);
        }
      })
      .catch(() => {});
  };

  const onUpdateCi = () => {
    const data: ConfigItemInfoModel = {
      ci: ciInfoUpdate,
      attributes: Object.values(ciAttributeMappingAttributeUpdate),
      attributeCustoms: ciAttributeCustomUpdate,
    };
    if (historyDescription) {
      data.historyDescription = historyDescriptionToString(historyDescription);
    }
    ConfigItemApi.saveInfo(data)
      .then((res) => {
        // setCiInfo(
        //   res.data.ci || {
        //     id: 0,
        //     ciTypeId: 0,
        //     name: '',
        //   },
        // );
        // setCiAttributes(res.data.attributes);
        // setCiAttributesCustom(res.data.attributeCustoms);
        setOldCiTempId(res.data.tempId);
        if (actionType === ActionTypeEnum.CLONE) {
          NotificationSuccess({
            message: 'The CI has been created but not approved yet. Please submit for approval at Tab CIs Management -> CIs Draft',
          });
        } else {
          NotificationSuccess({
            message: 'Updated successfully',
          });
        }
        if (res && res.status === 200 && res.errorDescription) {
          NotificationWarning({
            message: res.errorDescription,
          });
        }
        closeModalEdit();
        openModalAfterUpdate();

        setActionType(null);
      })
      .catch(() => {})
      .finally(() => {
        setHistoryDescription(undefined);
      });
  };

  const onDeleteCI = () => {
    ConfigItemApi.deleteByIds([ciInfo.id])
      .then(() => {
        NotificationSuccess({
          message: 'Deleted successfully',
        });

        navigate(buildCiTypeUrl(ciInfo.ciTypeId));
      })
      .catch(() => {});
  };

  const onRemoveCiAttributeCustoms = (item: ConfigItemAttrCustomModel) => {
    setCiAttributeCustomUpdate((prev) => {
      return prev.filter((x) => x !== item);
    });
  };

  const onAddCiAttributeCustoms = (item: ConfigItemAttrCustomModel) => {
    setCiAttributeCustomUpdate((prev) => {
      return [...prev, item];
    });
  };

  const isValid = useMemo(() => {
    if (!ciInfoUpdate.name) {
      return false;
    }
    for (const attr of listCiTypeAttribute) {
      if (attr.mandatory) {
        const currentAttr = ciAttributeMappingAttributeUpdate[attr.id];
        if (!currentAttr || !currentAttr.value) {
          return false;
        }
      }
    }

    return true;
  }, [listCiTypeAttribute, ciAttributeMappingAttributeUpdate, ciInfoUpdate]);

  const onCloseModalAfterUpdate = () => {
    NotificationSuccess({
      message: 'The CI has been modified but not approved yet. Please submit for approval at Tab CIs Management -> CIs Draft',
    });
    closeModalAfterUpdate();
  };
  const handleChangeTab = useCallback(
    (tabVal: string) => {
      const params = new URLSearchParams();
      //param tab
      params.set('tab', tabVal);
      // default param subtab of tab
      if (CiDetailScreenType.RELATIONSHIPS === tabVal) {
        params.set('subtab', CiDetailSubTabType.LISTVIEW);
      } else if (CiDetailScreenType.REQUEST === tabVal) {
        params.set('subtab', CiDetailSubTabType.CHANGEREQUEST);
      }
      setSearchParams(params);

      //tab state
      setActiveTab(tabVal);
    },
    [setSearchParams],
  );
  const childRefConfirmDelete = useRef<ConfirmDeleteCiModalMethods | null>(null);
  const ciType = useGetCiTypes().data.find((it) => it.id === ciInfo.ciTypeId);
  const fullCustomPaths = useMemo((): UrlBaseCrumbData => {
    return {
      [`${ciTypePath}`]: { title: `${ciType?.name}`, href: buildCiTypeUrl(ciInfo.ciTypeId) },
      [`${ciPath}`]: { title: `${formatStandardName(ActionTypeEnum.VIEW)} ${ciInfo.name}`, href: buildCiUrl(ciInfo.ciTypeId, ciInfo.id) },
    };
  }, [ciInfo.ciTypeId, ciInfo.id, ciInfo.name, ciType?.name]);
  return (
    <>
      {/* 4736 ci detail   */}
      <BreadcrumbComponent orderFullCustomPaths={fullCustomPaths} />

      {/* Modal confirm delete CI */}
      <ConfirmDeleteCiModal ref={childRefConfirmDelete} onConfirmDelete={onDeleteCI} />

      {/* Modal update CI */}
      <KanbanConfirmModal
        title={actionType === ActionTypeEnum.CLONE ? 'Create new CI' : 'Update CI'}
        onConfirm={onUpdateCi}
        textConfirm={actionType === ActionTypeEnum.CLONE ? 'Save' : 'Update'}
        onClose={closeModalEdit}
        opened={openedModalEdit}
        disabledConfirmButton={!isValid}
        modalProps={{
          size: '60%',
        }}>
        <CiUpdate
          ci={ciInfoUpdate}
          ciAttributes={ciAttributeMappingAttributeUpdate}
          ciTypeAttributes={listCiTypeAttribute}
          onChangeCi={setCiInfoUpdate}
          onChangeCiAttributes={setCiAttributeMappingAttributeUpdate}
          ciAttributesCustom={ciAttributeCustomUpdate}
          onChangeCiAttributeCustoms={setCiAttributeCustomUpdate}
          onRemoveCiAttributeCustoms={onRemoveCiAttributeCustoms}
          onAddCiAttributeCustoms={onAddCiAttributeCustoms}
          onChangeHistoryDescription={setHistoryDescription}
        />
      </KanbanConfirmModal>

      <KanbanModal
        title='Notification'
        onClose={closeModalNoticeChange}
        opened={openedModalNoticeChange}
        size={'xl'}
        actions={
          currentUser.data?.username === oldUserEdit && (
            <KanbanButton
              onClick={() => {
                navigate(buildCiManageDetailUrl(oldCiTempId || 0));
              }}>
              View detail draft version
            </KanbanButton>
          )
        }>
        {currentUser.data?.username === oldUserEdit ? (
          <>CI currently exists another version that has not been approved. Please continue to update the previous record</>
        ) : (
          <>
            <p>
              CI currently exists another version that has not been approved. To update current CI information, please contact user:{' '}
              <b>{oldUserEdit}</b>
            </p>
          </>
        )}
      </KanbanModal>

      {/* modal notice after update CI live */}
      <KanbanConfirmModal
        title={'Create/Update successfully'}
        onConfirm={() => {
          navigate(buildCiManageDetailUrl(oldCiTempId || 0));
        }}
        textConfirm='OK'
        onClose={onCloseModalAfterUpdate}
        opened={openedModalAfterUpdate}
        modalProps={{
          size: 'lg',
        }}>
        {`The CI record has been modified but hasn't been approved yet. Do you want to view detail?`}
      </KanbanConfirmModal>

      <HeaderTitleComponent
        title={`CI: ${ciInfo?.name || ''}`}
        rightSection={
          allowUpdate && (
            <Flex>
              <GuardComponent requirePermissions={AclPermission.createCiPermissions(PermissionAction.CI__UPDATE, ciId, ciTypeId)} hiddenOnUnSatisfy>
                <KanbanButton
                  leftSection={<IconEdit />}
                  onClick={() => {
                    openModalUpdateInfo(ActionTypeEnum.EDIT);
                  }}>
                  Edit
                </KanbanButton>
              </GuardComponent>

              <Space w={'sm'} />
              <GuardComponent requirePermissions={AclPermission.createCiPermissions(PermissionAction.CI__DELETE, ciId, ciTypeId)} hiddenOnUnSatisfy>
                <KanbanButton
                  onClick={() => {
                    childRefConfirmDelete.current?.openConfirmDelete(ciInfo.id);
                  }}
                  leftSection={<IconTrash />}
                  color='red'>
                  Delete
                </KanbanButton>
              </GuardComponent>

              <Space w={'sm'} />
              {isUserAuthorizedForPermissionsGroups(
                [AclPermission.cloneCiPermissionsForCiType(ciTypeId), AclPermission.cloneCiPermissionsForCi(ciId, ciTypeId)],
                true,
              ) && (
                <GuardComponent requirePermissions={[]} hiddenOnUnSatisfy>
                  <KanbanButton
                    leftSection={<IconCopy />}
                    onClick={() => {
                      openModalUpdateInfo(ActionTypeEnum.CLONE);
                    }}>
                    Clone
                  </KanbanButton>
                </GuardComponent>
              )}
            </Flex>
          )
        }
      />

      <KanbanTabs
        configs={{
          defaultValue: isFromBusinessView ? CiDetailScreenType.RELATIONSHIPS : CiDetailScreenType.INFO,
          value: activeTab,
          variant: 'outline',
          onChange: (value) => {
            // if (value === CiDetailScreenType.RELATIONSHIPS) {
            //    navigate(buildCiUrl(ciInfo.ciTypeId, ciInfo.id, value, CiDetailSubTabType.LISTVIEW));
            // } else if (value === CiDetailScreenType.REQUEST) {
            //   navigate(buildCiUrl(ciInfo.ciTypeId, ciInfo.id, value, CiDetailSubTabType.CHANGEREQUEST));
            // } else if (value === CiDetailScreenType.INFO) {
            //   navigate(buildCiUrl(ciInfo.ciTypeId, ciInfo.id, value || CiDetailScreenType.INFO));
            // } else {
            //   navigate(buildCiUrl(ciInfo.ciTypeId, ciInfo.id, value || CiDetailScreenType.DISCOVERY));
            // }
            //preview-discovery :make ci detail screen -> pop up: when change tab =>  change param instead of redirect url
            handleChangeTab(value || CiDetailScreenType.INFO);
          },
        }}
        tabs={{
          INFO: {
            title: 'Info',
            content: (
              <div>
                <CiInfo
                  ci={ciInfo}
                  ciTypeAttributes={listCiTypeAttribute}
                  ciAttributes={ciAttributeMapping}
                  ciAttributesCustom={ciAttributesCustom}
                />
              </div>
            ),
          },
          RELATIONSHIPS: {
            title: 'Relationships',
            content: <CiRelationship ciId={ciId} ciTypeId={ciTypeId} isFromBusinessView={isFromBusinessView} />,
          },
          HISTORY: {
            title: 'History',
            content: <CiHistory ciId={ciId} ciTypeId={ciTypeId} />,
          },
          REQUEST: {
            title: 'Request',
            content: <CiRequest ciId={ciId} ciTypeId={ciTypeId} isViewPage={true} />,
          },
          DISCOVERY: {
            title: 'Discovery',
            content: <CiDiscovery ciId={ciId} ciTypeId={ciTypeId} isViewPage={true} />,
          },
        }}
      />
    </>
  );
};

export default CiDetailPage;
