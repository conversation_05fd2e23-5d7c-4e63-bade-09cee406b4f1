import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import type { NotificationTemplateDto } from '@models/NotificationTemplate';

export class NotificationTemplateApi extends BaseApi {
  static baseUrl = BaseUrl.notificationTemplates;
  static getAll() {
    return BaseApi.getData<NotificationTemplateDto[]>(`${this.baseUrl}`);
  }
  static activateTemplates(listData: NotificationTemplateDto[]) {
    return BaseApi.putData<NotificationTemplateDto[]>(`${this.baseUrl}/activation`, listData);
  }
  static getById(id: number) {
    return BaseApi.getData<NotificationTemplateDto>(`${this.baseUrl}/${id}`);
  }
  static updateTemplate(id: number, data: NotificationTemplateDto) {
    return BaseApi.putData<NotificationTemplateDto>(`${this.baseUrl}/${id}`, data);
  }
}
