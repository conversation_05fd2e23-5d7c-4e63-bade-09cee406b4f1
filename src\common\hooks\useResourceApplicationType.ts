import { ResourceApplicationType } from '@common/constants/ResourceTypeEnum';
import { allCiRuleDestinations, allDiscoveryDestinations, isPrefixMatchPath } from '@common/utils/RouterUtils';
import { useMemo } from 'react';
import { useLocation } from 'react-router-dom';

const pathMatchers: { paths: string[]; type: ResourceApplicationType }[] = [
  { paths: allDiscoveryDestinations, type: ResourceApplicationType.DISCOVERY_RESOURCE },
  { paths: allCiRuleDestinations, type: ResourceApplicationType.CI_RULE_RESOURCE },
];

const useResourceApplicationType = (): ResourceApplicationType => {
  const { pathname } = useLocation();

  const appType = useMemo(() => {
    for (const matcher of pathMatchers) {
      if (matcher.paths.some((path) => path && isPrefixMatchPath(path, pathname))) {
        return matcher.type;
      }
    }
    return ResourceApplicationType.DEFAULT_RESOURCE;
  }, [pathname]);

  return appType;
};

export default useResourceApplicationType;
