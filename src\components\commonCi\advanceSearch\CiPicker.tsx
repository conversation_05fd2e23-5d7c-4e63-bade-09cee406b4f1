import { useCallback, useEffect, useState } from 'react';
import { Combobox, Loader, ScrollArea, TextInput, useCombobox } from '@mantine/core';
import React from 'react';
import type { PaginationRequestModel } from '@models/EntityModelBase';
import { useDebouncedValue } from '@mantine/hooks';
import { ConfigItemApi, type ConfigItemResponse } from '@api/ConfigItemApi';

export type CiPickerProps = {
  onChange?: (value: string) => void;
  value?: string;
  disabled?: boolean;
  ciTypeId?: number;
};

export const CiPicker = (props: CiPickerProps) => {
  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
  });

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<ConfigItemResponse[] | null>(null);
  const [value, setValue] = useState(props.value ?? '');
  const [valueInput, setValueInput] = useState('');
  const [debouncedValueInput] = useDebouncedValue(valueInput, 500);
  const { ciTypeId, disabled } = props;

  const fetchOptions = useCallback(
    (query: string) => {
      setLoading(true);

      const pageInfo: PaginationRequestModel<ConfigItemResponse> = {
        page: 0,
        size: 50,
        search: query || '',
        sortBy: 'name' as keyof ConfigItemResponse,
        isReverse: true,
      };

      ConfigItemApi.getInfoByName(pageInfo, ciTypeId)
        .then((res) => {
          const data = res?.data;
          setData(data?.content || []);
        })
        .catch(() => {
          setData([]);
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [ciTypeId],
  );

  useEffect(() => {
    if (debouncedValueInput !== undefined && debouncedValueInput !== null) {
      fetchOptions(debouncedValueInput);
    }
  }, [debouncedValueInput, fetchOptions]);

  const options = (data || []).map((item) => (
    <Combobox.Option value={item.name} key={item.id}>
      {item.name}
    </Combobox.Option>
  ));

  return (
    <Combobox
      onOptionSubmit={(optionValue) => {
        setValue(optionValue);
        if (props.onChange) {
          props.onChange(optionValue);
        }
        combobox.closeDropdown();
      }}
      withinPortal={true}
      store={combobox}>
      <Combobox.Target>
        <TextInput
          disabled={disabled}
          placeholder='Enter Ci'
          value={value}
          onChange={(event) => {
            const val = event.currentTarget.value;
            if (props.onChange) {
              props.onChange(val);
            }

            setValue(val);
            setValueInput(val);
            // fetchOptions(event.currentTarget.value);
            combobox.resetSelectedOption();
            combobox.openDropdown();
          }}
          onClick={() => combobox.openDropdown()}
          onFocus={() => {
            combobox.openDropdown();
            if (data === null) {
              fetchOptions(value);
            }
          }}
          onBlur={() => combobox.closeDropdown()}
          rightSection={loading ? <Loader size={18} /> : <></>}
        />
      </Combobox.Target>

      <Combobox.Dropdown hidden={data === null}>
        <Combobox.Options>
          <ScrollArea.Autosize type='scroll' mah={250}>
            {options}
          </ScrollArea.Autosize>
          {data?.length === 0 && <Combobox.Empty>No results found</Combobox.Empty>}
        </Combobox.Options>
      </Combobox.Dropdown>
    </Combobox>
  );
};

export default CiPicker;
