import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';

export type ConfigParamModel = {
  id?: number;
  key: string;
  value: string;
};

export type ConfigParamResponse = ConfigParamModel;

export class ConfigParamApi extends BaseApi {
  static baseUrl = BaseUrl.configParams;

  static getAll() {
    return BaseApi.getData<ConfigParamResponse[]>(ConfigParamApi.baseUrl);
  }

  static saveParams(params: ConfigParamModel[]) {
    const updatedParams = params.map((item) => ({
      ...item,
      sysConfigParamKeyEnum: item.key,
    }));
    return BaseApi.putData<ConfigParamResponse[]>(ConfigParamApi.baseUrl, updatedParams);
  }
}
