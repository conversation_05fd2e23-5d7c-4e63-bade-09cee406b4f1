import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
  KanbanButton,
  KanbanNumberInput,
  KanbanSwitch,
  KanbanTable,
  KanbanTooltip,
  type ColumnType,
  type KanbanTableProps,
} from 'kanban-design-system';
import { KanbanText } from 'kanban-design-system';
import { IconCheck, IconEdit, IconEye, IconInfoCircle, IconPencil, IconPlus, IconSquareLetterX, IconTrash } from '@tabler/icons-react';
import { KanbanConfirmModal } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import { KanbanIconButton } from 'kanban-design-system';
import { Badge, Box, Group, rem } from '@mantine/core';
import { v4 as uuid } from 'uuid';
import CiReconciliationEntryPage, { CiReconciliationEntryPageMethods } from './CiReconciliationEntryPage';
import { CiReconciliationEntryAttributeDto, DATA_SOURCE_MANUAL, RuleStatus, type CiReconciliationEntryDto } from '@models/CiReconciliationRule';
import { CiTypeAttributeDto } from '../DnDAttributesComponent';
import { DiscoverySourceDataModel } from '@models/discovery/DiscoverySourceData';
import {
  getDiscoverySourceDataName,
  renderActive,
  renderDiscoverySourceDataName,
  renderEntryName,
  renderValueOrNone,
} from './CiReconciliationRuleComponent';
import GuardComponent from '@components/GuardComponent';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { AclPermission } from '@models/AclPermission';
import { getTextTimeUnit } from '@common/constants/TimeUnitEnum';

interface ImpactedChangeTableComponentProps {
  listEntry: CiReconciliationEntryDto[];
  setListEntry: (val: CiReconciliationEntryDto[]) => void;
  listEntryUpdate: CiReconciliationEntryDto[];
  setListEntryUpdate: (val: CiReconciliationEntryDto[]) => void;
  listEntryDelete: CiReconciliationEntryDto[];
  setListEntryDelete: (val: CiReconciliationEntryDto[]) => void;
  listAttributes: CiTypeAttributeDto[];
  allowEdit: boolean;
  discoverySourceData: DiscoverySourceDataModel[];
}

type ReconciliationEntryError = {
  priority?: string;
};
type ReconciliationErrorMap = Record<string, ReconciliationEntryError>;

export const CiReconciliationEntryTableComponent = (props: ImpactedChangeTableComponentProps) => {
  const {
    allowEdit,
    discoverySourceData,
    listAttributes,
    listEntry,
    listEntryDelete,
    listEntryUpdate,
    setListEntry,
    setListEntryDelete,
    setListEntryUpdate,
  } = props;

  const [openedModalReconciliationEntry, { close: closeModalReconciliationEntry, open: openModalReconciliationEntry }] = useDisclosure(false);
  const [modalConfirmDelete, { close: closeModalConfirmDelete, open: openModalConfirmDelete }] = useDisclosure(false);
  const [dataEntry, setDataEntry] = useState<CiReconciliationEntryDto>();
  const [isViewEntry, setIsViewEntry] = useState(false);
  const [deleteIds, setDeleteIds] = useState<string[]>([]);
  const [priorityChange, setPriorityChange] = useState<number>();
  const [errorMessageMap, setErrorMessageMap] = useState<ReconciliationErrorMap>({});

  const onSearched = useCallback(
    (datas: CiReconciliationEntryDto[], search: string): CiReconciliationEntryDto[] => {
      const lowerCaseSearch = search.toLowerCase();

      return datas.filter((item) => {
        const listId = (item.attributes || []).map((x) => x.ciTypeAttributeId);

        const listKeyAttribute = listAttributes.filter((attr) => listId.includes(attr.attributeId));

        const dataAttributes = (listKeyAttribute || [])
          .map((x) => (x.deleted ? `${x.nameAttribute} - (Deleted)` : x.nameAttribute))
          .join(', ')
          .toLowerCase();
        return (
          item.name?.toLowerCase().includes(lowerCaseSearch) ||
          renderActive(item.active)?.toLowerCase().includes(lowerCaseSearch) ||
          dataAttributes?.includes(lowerCaseSearch) ||
          item.id?.toString().toLowerCase().includes(lowerCaseSearch) ||
          item.priority?.toString().includes(lowerCaseSearch) ||
          item.timeoutData?.toString().includes(lowerCaseSearch) ||
          getDiscoverySourceDataName(item.discoverySourceDataId || 0, discoverySourceData)
            ?.toLowerCase()
            .includes(lowerCaseSearch)
        );
      });
    },
    [discoverySourceData, listAttributes],
  );

  const onDelete = useCallback(
    (ids: string[]) => {
      if (!ids) {
        return;
      }
      // filter list data old (has Id)
      const listDeleted = listEntry.filter((item) => item.id && item.tempId && ids.includes(item.tempId));
      const deletedObject = [...listEntryDelete, ...listDeleted];
      setListEntryDelete(deletedObject);

      // remove in list all data
      const updatedEntry = listEntry.filter((item) => item.tempId && !ids.includes(item.tempId));
      setListEntry(updatedEntry);

      // remove in list update data
      const newListUpdate = listEntryUpdate.filter((item) => item.tempId && !ids.includes(item.tempId));
      setListEntryUpdate(newListUpdate);

      setDeleteIds([]);
    },
    [listEntry, listEntryDelete, listEntryUpdate, setListEntry, setListEntryDelete, setListEntryUpdate],
  );

  const handleUpdateOrCreate = useCallback(
    (newItem: CiReconciliationEntryDto | undefined) => {
      if (!newItem) {
        return;
      }

      const index = listEntry.findIndex((item) => item.tempId === newItem.tempId);
      if (index !== -1) {
        // case create
        const updatedList = listEntry
          .map((item) => (item.tempId === newItem.tempId ? { ...item, ...newItem } : item))
          .sort((a, b) => (a.priority || 0) - (b.priority || 0));
        setListEntry(updatedList);
      } else {
        // case update
        setListEntry([...listEntry, newItem].sort((a, b) => (a.priority || 0) - (b.priority || 0)));
      }

      if (newItem.id > 0) {
        const indexUpdate = listEntryUpdate.findIndex((item) => item.tempId === newItem.tempId);
        if (indexUpdate !== -1) {
          const updatedList = listEntryUpdate.map((item) => (item.tempId === newItem.tempId ? { ...item, ...newItem } : item));
          setListEntryUpdate(updatedList);
        } else {
          setListEntryUpdate([...listEntryUpdate, newItem]);
        }
      }
    },
    [listEntry, listEntryUpdate, setListEntry, setListEntryUpdate],
  );

  const handleUpdatePriority = useCallback(
    (rowData: CiReconciliationEntryDto) => {
      const updatedList = listEntry.map((item) =>
        item.tempId === rowData.tempId ? { ...item, onEditPriority: false, priority: priorityChange } : item,
      );
      setListEntry(updatedList);

      const updatedObj = updatedList.find((x) => x.tempId === rowData.tempId);
      handleUpdateOrCreate(updatedObj);
    },
    [handleUpdateOrCreate, listEntry, priorityChange, setListEntry],
  );

  const renderListKeyAttributes = useCallback(
    (data: CiReconciliationEntryAttributeDto[], applyAllAttribute?: boolean) => {
      if (applyAllAttribute) {
        return (
          <>
            <Group gap={6} style={{ width: '100%' }}>
              <Badge variant='light' color={'blue'} radius='sm' mr='5'>
                <KanbanText fw={500} size='sm' tt='initial'>
                  all
                </KanbanText>
              </Badge>
              <KanbanTooltip
                maw={'40%'}
                bd={'1px solid rgba(0, 119, 255, 0.8)'}
                bg={'white'}
                fs={'italic'}
                c={'var(--mantine-color-blue-4)'}
                label={'Apply to all attributes in this CI Type'}
                style={{ flexShrink: 0 }}
                multiline>
                <Box c={'blue'}>
                  <IconInfoCircle size={'20px'} />
                </Box>
              </KanbanTooltip>
            </Group>
          </>
        );
      }

      const listId = data.map((item) => item.ciTypeAttributeId);
      const listKeyAttribute = listAttributes.filter((attr) => listId.includes(attr.attributeId));
      return listKeyAttribute.map((item, index) => {
        const isDeleted = item.deleted;
        const badgeColor = isDeleted ? 'red' : 'blue';
        const badgeText = isDeleted ? `${item.nameAttribute} - (Deleted)` : item.nameAttribute;

        return (
          <Badge key={index} variant='light' color={badgeColor} radius='sm' mr='5'>
            <KanbanText fw={500} size='sm' tt='initial'>
              {badgeText}
            </KanbanText>
          </Badge>
        );
      });
    },
    [listAttributes],
  );

  const columns: ColumnType<CiReconciliationEntryDto>[] = useMemo(() => {
    return [
      {
        title: 'Id',
        name: 'id',
        hidden: true,
        width: '10%',
      },
      {
        title: 'Name',
        name: 'name',
        width: '10%',
        customRender: (_, rowData) => {
          return renderEntryName(rowData.defaultEntry, rowData.name);
        },
      },
      {
        title: 'Attributes',
        name: 'attributes',
        customRender: (data: CiReconciliationEntryAttributeDto[], rowData) => {
          return renderListKeyAttributes(data, rowData.applyAllAttribute);
        },
      },
      {
        title: 'Active',
        name: 'active',
        width: '10%',
        customRender: (data) => {
          const isCheck = RuleStatus.ENABLE === data;
          return <KanbanText c={isCheck ? 'green' : 'red'}>{isCheck ? 'Active' : 'Inactive'}</KanbanText>;
        },
      },
      {
        title: 'Data Source',
        name: 'discoverySourceDataId',
        customRender: (data) => {
          return renderDiscoverySourceDataName(data, discoverySourceData);
        },
      },
      {
        title: 'Priority',
        name: 'priority',
        width: '10%',
        customRender: (data, rowData) => {
          return (
            <>
              {allowEdit && rowData?.onEditPriority ? (
                <KanbanNumberInput
                  error={errorMessageMap[rowData.tempId]?.priority}
                  value={data}
                  disabled={!allowEdit}
                  allowNegative={false}
                  allowDecimal={false}
                  clampBehavior='strict'
                  autoFocus
                  min={1}
                  max={999}
                  rightSectionPointerEvents='auto'
                  rightSection={
                    <IconCheck
                      style={{ width: rem(20), height: rem(20) }}
                      color='var(--mantine-color-green-5)'
                      onClick={(e) => {
                        e.stopPropagation();
                        handleUpdatePriority(rowData);
                      }}
                    />
                  }
                  onChange={(value) => {
                    const numberValue = value === '' ? undefined : Number(value);
                    setPriorityChange(numberValue);

                    setErrorMessageMap((prev) => ({
                      ...prev,
                      [rowData.tempId]: {
                        ...prev[rowData.tempId],
                        priority: numberValue ? undefined : 'Priority must be a number between 1 and 999',
                      },
                    }));
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  onBlur={() => {
                    handleUpdatePriority(rowData);
                  }}
                />
              ) : (
                <Group grow>
                  {errorMessageMap[rowData.tempId]?.priority ? (
                    <KanbanTooltip
                      bd='1px solid rgba(255, 0, 0, 0.8)'
                      bg='white'
                      fs='italic'
                      c='var(--mantine-color-red-4)'
                      label={errorMessageMap[rowData.tempId]?.priority}
                      multiline>
                      <Box c='red'>
                        <IconSquareLetterX size={20} />
                      </Box>
                    </KanbanTooltip>
                  ) : (
                    <KanbanText>{data}</KanbanText>
                  )}

                  {allowEdit && !rowData.defaultEntry && (
                    <IconPencil
                      style={{ width: rem(20), height: rem(20) }}
                      color='var(--mantine-color-gray-5)'
                      onClick={(e) => {
                        e.stopPropagation();
                        const updatedList = listEntry.map((item) => (item.tempId === rowData.tempId ? { ...item, onEditPriority: true } : item));
                        setListEntry(updatedList);
                        setPriorityChange(data);
                      }}
                    />
                  )}
                </Group>
              )}
            </>
          );
        },
      },
      {
        title: 'Timeout Data',
        name: 'timeoutData',
        width: '10%',
        customRender: (data, rowData) => {
          return `${renderValueOrNone(data)} ${getTextTimeUnit(rowData.timeUnit) ?? ''}`;
        },
      },
    ];
  }, [renderListKeyAttributes, discoverySourceData, allowEdit, errorMessageMap, handleUpdatePriority, listEntry, setListEntry]);

  const tableProps: KanbanTableProps<CiReconciliationEntryDto> = useMemo(() => {
    return {
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 300,
        onSearched: onSearched,
      },
      sortable: {
        enable: true,
      },

      columns: columns,
      data: listEntry,

      pagination: {
        enable: true,
      },
      selectableRows: {
        enable: false,
        onDeleted(rows) {
          const ids = rows.map((x) => x.tempId);
          onDelete(ids);
        },
      },
      onRowClicked: (data) => {
        setDataEntry(data);
        setIsViewEntry(true);
        openModalReconciliationEntry();
      },
      actions: {
        customAction: (data) => {
          return (
            <>
              {allowEdit && (
                <KanbanSwitch
                  disabled={data.defaultEntry}
                  size='xs'
                  mr='xs'
                  color={'green'}
                  checked={RuleStatus.ENABLE === data.active}
                  onChange={(e) => {
                    const value = e.currentTarget.checked;
                    const updatedList = listEntry.map((item) =>
                      item.tempId === data.tempId ? { ...item, active: value ? RuleStatus.ENABLE : RuleStatus.DISABLE } : item,
                    );
                    setListEntry(updatedList);
                    const updatedObj = updatedList.find((x) => x.tempId === data.tempId);
                    handleUpdateOrCreate(updatedObj);
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                />
              )}
              {/* <GuardComponent requirePermissions={AclPermission.createViewCiPermissions(data.id, data.ciTypeId)} hiddenOnUnSatisfy> */}
              <KanbanIconButton
                variant='transparent'
                size={'sm'}
                onClick={() => {
                  setDataEntry(data);
                  setIsViewEntry(true);
                  openModalReconciliationEntry();
                }}>
                <IconEye />
              </KanbanIconButton>
              {/* </GuardComponent> */}
              {allowEdit && (
                <>
                  <KanbanIconButton
                    disabled={data.defaultEntry}
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      setDataEntry(data);
                      setIsViewEntry(false);
                      openModalReconciliationEntry();
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                  <KanbanIconButton
                    disabled={data.defaultEntry}
                    key={3}
                    title='Delete'
                    color='red'
                    size='sm'
                    variant='transparent'
                    onClick={() => {
                      setDeleteIds([data.tempId]);
                      openModalConfirmDelete();
                    }}>
                    <IconTrash />
                  </KanbanIconButton>
                </>
              )}
            </>
          );
        },
      },
    };
  }, [allowEdit, columns, handleUpdateOrCreate, listEntry, onDelete, onSearched, openModalConfirmDelete, openModalReconciliationEntry, setListEntry]);

  const customTableProps = useMemo(() => {
    const tablePropsUpdate = { ...tableProps };
    if (!allowEdit) {
      const { actions, selectableRows, ...rest } = tablePropsUpdate;
      if (selectableRows) {
        selectableRows.enable = false;
      }
      if (actions && actions.deletable) {
        delete actions.deletable;
      }

      return { ...rest, selectableRows, actions };
    }

    return tablePropsUpdate;
  }, [allowEdit, tableProps]);

  const ciReconciliationEntryRef = useRef<CiReconciliationEntryPageMethods | null>(null);

  const onSaveEntry = () => {
    const isValid = ciReconciliationEntryRef.current?.validateData(listEntry);
    if (!isValid) {
      return;
    }
    const entryData = ciReconciliationEntryRef.current?.onSaveData();
    handleUpdateOrCreate(entryData);
    closeModalReconciliationEntry();
  };

  const onAddNewEntry = () => {
    setDataEntry({
      id: 0,
      name: '',
      active: RuleStatus.ENABLE,
      ciReconciliationRuleId: 0,
      tempId: uuid(),
      defaultEntry: false,
      discoverySourceDataId: DATA_SOURCE_MANUAL.id,
    });
    setIsViewEntry(false);
    openModalReconciliationEntry();
  };

  return (
    <>
      {/* Modal confirm delete attribute */}
      <KanbanConfirmModal
        opened={modalConfirmDelete}
        onClose={closeModalConfirmDelete}
        title='Confirm delete'
        onConfirm={() => {
          onDelete(deleteIds);
          closeModalConfirmDelete();
        }}>
        Are you sure to delete this item?
      </KanbanConfirmModal>

      <KanbanConfirmModal
        title={'CI Reconcile Entry'}
        onConfirm={isViewEntry ? undefined : onSaveEntry}
        textConfirm={'Save'}
        onClose={closeModalReconciliationEntry}
        opened={openedModalReconciliationEntry}
        modalProps={{
          size: '50%',
        }}>
        {dataEntry && (
          <CiReconciliationEntryPage
            ref={ciReconciliationEntryRef}
            entryInfo={dataEntry}
            listAttributes={listAttributes}
            isView={isViewEntry}
            listDiscoverySourceData={discoverySourceData}
          />
        )}
      </KanbanConfirmModal>

      <HeaderTitleComponent
        title='List of entries'
        rightSection={
          <Group align='center'>
            {allowEdit && (
              <GuardComponent requirePermissions={[AclPermission.createIncidentRequest]} hiddenOnUnSatisfy>
                <KanbanButton onClick={onAddNewEntry} leftSection={<IconPlus />}>
                  Add entry
                </KanbanButton>
              </GuardComponent>
            )}
            <KanbanTooltip
              bd='1px solid rgba(0, 119, 255, 0.8)'
              bg='white'
              fs='italic'
              c='var(--mantine-color-blue-4)'
              label='If an attribute does not in any Entry, its value may be overwritten by any data source.'
              multiline>
              <Box c='blue'>
                <IconInfoCircle size={20} />
              </Box>
            </KanbanTooltip>
          </Group>
        }
      />
      <KanbanTable {...customTableProps} />
    </>
  );
};

CiReconciliationEntryTableComponent.whyDidYouRender = true;
CiReconciliationEntryTableComponent.displayName = 'CiReconciliationEntryTableComponent';
export default CiReconciliationEntryTableComponent;
