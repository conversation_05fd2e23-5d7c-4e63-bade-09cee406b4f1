import { FlaggedImpactedCiAttachedType } from '@common/constants/CiManagement';
import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import type {
  CiRelationshipDetailsModel,
  CiRelationshipInfoWithImpact,
  CiRelationshipInfoWithImpactMap,
  CiRelationshipModel,
  LinkDataModel,
} from '@models/CiRelationship';
import type { ImportFileResponse, LoadFileResponse } from '@models/ImportFileResponse';
import type { ImportRelationshipRequest } from '@pages/admins/ciRelationship/import';

export type CiRelationshipResponse = CiRelationshipModel;

export class CiRelationshipApi extends BaseApi {
  static baseUrl = BaseUrl.ciRelationships;

  static saveNewRelationshipInfo(items: CiRelationshipResponse[]) {
    return BaseApi.postData<CiRelationshipResponse[]>(`${this.baseUrl}/info`, items);
  }

  static deleteById(id: number) {
    return BaseApi.deleteData<boolean>(`${this.baseUrl}/${id}`);
  }

  static deleteByIds(ids: number[]) {
    return BaseApi.deleteData<boolean[]>(`${this.baseUrl}/batch`, {
      ids,
    });
  }

  static downloadTemplateImportFile() {
    return BaseApi.getData<string>(`${this.baseUrl}/template`);
  }

  static loadFileImportCiRelationship(data: File) {
    const formData = new FormData();
    formData.append('file', data);
    return BaseApi.postData<LoadFileResponse>(`${this.baseUrl}/load-file`, formData);
  }

  static importFileCI(request: ImportRelationshipRequest) {
    const formData = new FormData();
    formData.append('file', request.file);
    formData.append('mappingFields', request.mappingFields);
    return BaseApi.postData<ImportFileResponse>(`${this.baseUrl}/import`, formData);
  }

  static getDetails(linkDatas: LinkDataModel[]) {
    return BaseApi.postData<CiRelationshipDetailsModel>(`${this.baseUrl}/details`, linkDatas);
  }

  static flagCisForGraphView(
    ciInChangePlan: number[] | undefined,
    impactedCi: number | undefined,
    attachedType: FlaggedImpactedCiAttachedType | undefined,
    level = 1,
  ) {
    return BaseApi.postData<CiRelationshipInfoWithImpactMap>(
      `${this.baseUrl}/impacts?impactedCi=${impactedCi}&attachedType=${attachedType}&level=${level}`,
      ciInChangePlan,
    );
  }

  static getImpactMap(id: number, isImpactTo: boolean | true, level = 1) {
    return BaseApi.getData<CiRelationshipInfoWithImpact[]>(`${this.baseUrl}/impact-maps?ciId=${id}&isImpactTo=${isImpactTo}&level=${level}`);
  }
}
