import React, { useState, useImperativeHandle, forwardRef, useEffect, useCallback } from 'react';
import type { AttributeInfoDTO, AttributeInfoEleDTO, ExportFileRequest } from '@models/ExportInfo';
import { Box, Center, Divider, Flex, Paper, ScrollArea, Text } from '@mantine/core';
import { IconPlayerTrackNext, IconPlayerTrackPrev } from '@tabler/icons-react';
import { KanbanButton } from 'kanban-design-system';
import { KanbanSelect } from 'kanban-design-system';
import { getAllTypeFile } from '@service/FileServiceExecute';
import { EXCEL_TYPE } from '@common/constants/FileConstants';
import { DragDropContext, Draggable, Droppable, DropResult } from '@hello-pangea/dnd';
import classes from './DndList.module.scss';
import { useDraggableInPortal } from '@common/hooks/useDraggableInPortal';

type ExportFileProps = {
  attributeOfCis: AttributeInfoDTO[];
};

export type ExportFileMethods = {
  getExportFileData: () => ExportFileData;
};

export type ExportFileData = Omit<ExportFileRequest, 'searchData'>;

export const ExportFileComponent = forwardRef<ExportFileMethods, ExportFileProps>((props, ref) => {
  const renderDraggable = useDraggableInPortal();
  const { attributeOfCis } = props;
  const [typeFile, setTypeFile] = useState(EXCEL_TYPE);
  const [availableColumns, setAvailableColumns] = useState<AttributeInfoEleDTO[]>([]);
  const [selectedColumns, setSelectedColumns] = useState<AttributeInfoEleDTO[]>([]);

  const getExportFileData = useCallback(() => {
    const attributeInfoDTOArray: AttributeInfoDTO[] = selectedColumns.map(({ selected: _, ...dtoWithoutSelected }) => dtoWithoutSelected);
    const fileExportState: ExportFileData = {
      attributeInfoList: attributeInfoDTOArray,
      typeFile: typeFile,
    };
    return fileExportState;
  }, [selectedColumns, typeFile]);

  useImperativeHandle<any, ExportFileMethods>(
    ref,
    () => ({
      getExportFileData: getExportFileData,
    }),
    [getExportFileData],
  );

  useEffect(() => {
    if (attributeOfCis.length > 0) {
      attributeOfCis.sort((a, b) => b.attributeId - a.attributeId);
      const availableColumns: AttributeInfoDTO[] = attributeOfCis.filter((info) => !info.defaultAttribute);
      const selectedColumns: AttributeInfoDTO[] = attributeOfCis.filter((info) => info.defaultAttribute);
      setAvailableColumns(availableColumns);
      setSelectedColumns(selectedColumns);
    }
  }, [attributeOfCis]);

  const typeFiles = getAllTypeFile();

  const onChangeTypeFile = (val: string | null) => {
    setTypeFile(val || EXCEL_TYPE);
  };

  const handleClickAvailableColumns = (attributeId: number) => {
    // Tách biệt logic xử lý
    setAvailableColumns((prevState) => {
      const updatedColumns = [...prevState];
      const index = updatedColumns.findIndex((item) => item.attributeId === attributeId);
      if (index !== -1) {
        updatedColumns[index].selected = !updatedColumns[index].selected;
      }
      return updatedColumns;
    });
  };

  const handleClickSelectedColumns = (attributeId: number) => {
    setSelectedColumns((prevState) => {
      const updatedColumns = [...prevState];
      const index = updatedColumns.findIndex((item) => item.attributeId === attributeId);
      if (index !== -1) {
        updatedColumns[index].selected = !updatedColumns[index].selected;
      }
      return updatedColumns;
    });
  };

  const availableColumnDatas = availableColumns.map((item) => (
    <Paper
      className={item.selected ? classes.active : undefined}
      p='xs'
      mb='sm'
      radius='sm'
      shadow='md'
      withBorder
      key={item.attributeId}
      onClick={() => handleClickAvailableColumns(item.attributeId)}>
      <Text>{item.nameAttribute}</Text>
    </Paper>
  ));

  const selectedColumnDatas = selectedColumns.map((item, index) => (
    <Draggable key={item.attributeId} index={index} draggableId={String(item.attributeId)}>
      {renderDraggable((provided) => (
        <Paper
          className={item.selected ? classes.active : undefined}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          ref={provided.innerRef}
          p='xs'
          mb='sm'
          radius='sm'
          shadow='md'
          withBorder
          onClick={() => handleClickSelectedColumns(item.attributeId)}
          // style={{ ...provided.draggableProps.style,
          //     // left: 'auto !important',
          //     // top: 'auto !important',
          // }}
        >
          <Text>{item.nameAttribute}</Text>
        </Paper>
      ))}
    </Draggable>
  ));

  const handleDragEnd = (result: DropResult) => {
    const { destination, source } = result;
    if (!destination) {
      return;
    }
    const newData = [...selectedColumns];
    const [removed] = newData.splice(source.index, 1);
    newData.splice(destination.index, 0, removed);
    setSelectedColumns(newData);
  };

  const handleClickBtn = (isAvailableColumns: boolean) => {
    if (isAvailableColumns) {
      const newlySelectedColumns = availableColumns.filter((column) => {
        if (!column.defaultAttribute && column.selected) {
          column.selected = false; // Set selected to false
          column.defaultAttribute = true; // Set default to true
          return true;
        }
        return false;
      });
      setSelectedColumns((prevSelectedColumns) => [...prevSelectedColumns, ...newlySelectedColumns]);
      const remainingAvailableColumns = availableColumns.filter(
        (column) => !newlySelectedColumns.some((newColumn) => newColumn.attributeId === column.attributeId),
      );
      setAvailableColumns(remainingAvailableColumns);
    } else {
      const newlyAvailableColumns = selectedColumns.filter((column) => {
        if (column.defaultAttribute && column.selected) {
          column.selected = false; // Set selected to false
          column.defaultAttribute = false; // Set default to false
          return true;
        }
        return false;
      });
      setAvailableColumns((prevAvailableColumns) => [...prevAvailableColumns, ...newlyAvailableColumns]);
      const remainingSelectedColumns = selectedColumns.filter(
        (column) => !newlyAvailableColumns.some((newColumn) => newColumn.attributeId === column.attributeId),
      );
      setSelectedColumns(remainingSelectedColumns);
    }
  };

  return (
    <>
      <Divider />
      <Flex gap='md' mt='sm' mb='sm'>
        <Text fw={500} mb='sm'>
          File Type:{' '}
        </Text>
        <KanbanSelect
          placeholder='File Type'
          data={typeFiles}
          value={typeFile}
          // defaultValue={EXCEL_TYPE}
          onChange={(e) => {
            onChangeTypeFile(e);
          }}
        />
      </Flex>
      <Flex>
        <Paper w={'50%'}>
          <Text fw={500} mb='sm'>
            Available Columns
          </Text>
          <Paper withBorder p='xs'>
            <ScrollArea h={500}>{availableColumnDatas}</ScrollArea>
          </Paper>
          <Box mt='sm' mb='sm'>
            <KanbanButton
              variant={'outline'}
              size={'xs'}
              onClick={() => {
                setAvailableColumns((prevState) => {
                  const updatedColumns = prevState.map((column) => ({
                    ...column,
                    selected: true,
                  }));
                  return updatedColumns;
                });
              }}>
              Selected All
            </KanbanButton>
          </Box>
        </Paper>
        <Center w={'10%'}>
          <Flex direction='column'>
            <div>
              <IconPlayerTrackNext onClick={() => handleClickBtn(true)}></IconPlayerTrackNext>
            </div>
            <div>
              <IconPlayerTrackPrev onClick={() => handleClickBtn(false)}></IconPlayerTrackPrev>
            </div>
          </Flex>
        </Center>
        <Paper w={'50%'}>
          <Text fw={500} mb='sm'>
            Selected Columns
          </Text>
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId='selected-attribute-list'>
              {(provided) => (
                <Paper withBorder p='xs' {...provided.droppableProps} ref={provided.innerRef}>
                  <ScrollArea h={500}>
                    {selectedColumnDatas}
                    {provided.placeholder}
                  </ScrollArea>
                </Paper>
              )}
            </Droppable>
          </DragDropContext>
          <Box mt='sm' mb='sm'>
            <KanbanButton
              variant={'outline'}
              size={'xs'}
              onClick={() => {
                setSelectedColumns((prevState) => {
                  const updatedColumns = prevState.map((column) => ({
                    ...column,
                    selected: true,
                  }));
                  return updatedColumns;
                });
              }}>
              Selected All
            </KanbanButton>
          </Box>
        </Paper>
      </Flex>
    </>
  );
});
ExportFileComponent.displayName = 'ExportFileComponent';
export default ExportFileComponent;
