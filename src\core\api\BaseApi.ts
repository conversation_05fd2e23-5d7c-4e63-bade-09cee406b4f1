import axios, { Axios, AxiosError, AxiosInstance, AxiosPromise, AxiosRequestConfig, AxiosResponse } from 'axios';
import { keycloakAuthenticationInterceptor, requestIdInterceptor } from './RequestInterceptors';
import type { ApiResponse, AxiosResponseApi } from './ApiResponse';
import { directDispath } from '@store';
import { pageLoadingSlice } from '@slices/PageLoadingSlice';
import { handleResponseInterceptor } from './ResponseInterceptors';
import { NotificationError } from '@common/utils/NotificationUtils';
import BaseErrorMessage from './WrapperErrorMessage';
//const config = getConfigs();
const axiosInstance: AxiosInstance = axios.create({
  baseURL: '',
});
axiosInstance.interceptors.request.use(requestIdInterceptor);
axiosInstance.interceptors.request.use(keycloakAuthenticationInterceptor);
handleResponseInterceptor(axiosInstance.interceptors.response);

const extraConfigsDefault = {
  useLoading: true,
  useErrorNotification: true,
};

export enum ErrorCodes {
  ERR_CANCELED = 'ERR_CANCELED',
  ERR_NETWORK = 'ERR_NETWORK',
  ERR_TIMEOUT = 'ERR_TIMEOUT',
  ERR_UNKNOWN = 'ERR_UNKNOWN',
}

export type ExtraConfigApiType = Partial<typeof extraConfigsDefault>;

export class BaseApi {
  static request<T = unknown>(request: (axios: Axios) => AxiosPromise<T>, extraConfigs: ExtraConfigApiType = extraConfigsDefault) {
    const currentConfig = { ...extraConfigsDefault, ...extraConfigs };
    if (currentConfig.useLoading) {
      directDispath(pageLoadingSlice.actions.increment());
    }

    return new Promise<AxiosResponse<T>>((resolve, reject) => {
      return request(axiosInstance)
        .then((response) => {
          resolve(response);
        })
        .catch((error: AxiosError) => {
          reject(error);
          if (currentConfig.useErrorNotification && ErrorCodes.ERR_CANCELED !== error.code) {
            const response = error.response?.data as any;
            NotificationError({
              title: `Request error ${error.code ?? ''}`,
              message: error.code === '000' ? BaseErrorMessage(error.message, response?.clientMessageId) : error.message,
            });
          }
        });
    }).finally(() => {
      if (currentConfig.useLoading) {
        directDispath(pageLoadingSlice.actions.decrement());
      }
    });
  }
  static async handlerResponseData<T = unknown>(response: Promise<AxiosResponse<any, any>>): Promise<ApiResponse<T>> {
    const result = await response;
    return result.data;
  }
  static get<T = unknown>(
    url: string,
    queryParams?: Record<string, any>,
    config: AxiosRequestConfig = {},
    extraConfigs: ExtraConfigApiType = extraConfigsDefault,
    controller?: AbortController,
  ) {
    return this.request((axiosInstance) => {
      return axiosInstance.get<any, AxiosResponseApi<T>>(url, {
        params: queryParams,
        ...config,
        signal: controller?.signal,
      });
    }, extraConfigs);
  }
  static getData<T = unknown>(
    url: string,
    queryParams?: Record<string, any>,
    config: AxiosRequestConfig = {},
    extraConfigs: ExtraConfigApiType = extraConfigsDefault,
    controller?: AbortController,
  ) {
    return this.handlerResponseData<T>(this.get<T>(url, queryParams, config, extraConfigs, controller));
  }

  static post<T = unknown>(
    url: string,
    body?: unknown,
    queryParams?: Record<string, any>,
    config: AxiosRequestConfig = {},
    extraConfigs: ExtraConfigApiType = extraConfigsDefault,
    controller?: AbortController,
  ) {
    return this.request((axiosInstance) => {
      return axiosInstance.post<any, AxiosResponseApi<T>>(url, body, {
        params: queryParams,
        ...config,
        signal: controller?.signal,
      });
    }, extraConfigs);
  }
  static postData<T = unknown>(
    url: string,
    body?: unknown,
    queryParams?: Record<string, any>,
    config: AxiosRequestConfig = {},
    extraConfigs: ExtraConfigApiType = extraConfigsDefault,
    controller?: AbortController,
  ) {
    return this.handlerResponseData<T>(this.post<T>(url, body, queryParams, config, extraConfigs, controller));
  }

  static put<T = unknown>(
    url: string,
    body?: unknown,
    queryParams?: Record<string, any>,
    config: AxiosRequestConfig = {},
    extraConfigs: ExtraConfigApiType = extraConfigsDefault,
    controller?: AbortController,
  ) {
    return this.request((axiosInstance) => {
      return axiosInstance.put<any, AxiosResponseApi<T>>(url, body, {
        params: queryParams,
        ...config,
        signal: controller?.signal,
      });
    }, extraConfigs);
  }
  static putData<T = unknown>(
    url: string,
    body?: unknown,
    queryParams?: Record<string, any>,
    config: AxiosRequestConfig = {},
    extraConfigs: ExtraConfigApiType = extraConfigsDefault,
    controller?: AbortController,
  ) {
    return this.handlerResponseData<T>(this.put<T>(url, body, queryParams, config, extraConfigs, controller));
  }

  static patch<T = unknown>(
    url: string,
    body?: unknown,
    queryParams?: Record<string, any>,
    config: AxiosRequestConfig = {},
    extraConfigs: ExtraConfigApiType = extraConfigsDefault,
    controller?: AbortController,
  ) {
    return this.request((axiosInstance) => {
      return axiosInstance.patch<any, AxiosResponseApi<T>>(url, body, {
        params: queryParams,
        ...config,
        signal: controller?.signal,
      });
    }, extraConfigs);
  }
  static patchData<T = unknown>(
    url: string,
    body?: unknown,
    queryParams?: Record<string, any>,
    config: AxiosRequestConfig = {},
    extraConfigs: ExtraConfigApiType = extraConfigsDefault,
    controller?: AbortController,
  ) {
    return this.handlerResponseData<T>(this.patch<T>(url, body, queryParams, config, extraConfigs, controller));
  }

  static delete<T = unknown>(
    url: string,
    queryParams?: Record<string, any>,
    config: AxiosRequestConfig = {},
    extraConfigs: ExtraConfigApiType = extraConfigsDefault,
    controller?: AbortController,
  ) {
    return this.request((axiosInstance) => {
      return axiosInstance.delete<any, AxiosResponseApi<T>>(url, {
        params: queryParams,
        ...config,
        signal: controller?.signal,
      });
    }, extraConfigs);
  }
  static deleteData<T = unknown>(
    url: string,
    queryParams?: Record<string, any>,
    config: AxiosRequestConfig = {},
    extraConfigs: ExtraConfigApiType = extraConfigsDefault,
    controller?: AbortController,
  ) {
    return this.handlerResponseData<T>(this.delete<T>(url, queryParams, config, extraConfigs, controller));
  }
}
