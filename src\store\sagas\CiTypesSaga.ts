import { ConfigItemType<PERSON>pi, ConfigItemTypeResponse } from '@api/ConfigItemTypeApi';
import type { ApiResponse } from '@core/api/ApiResponse';
import { CreateOrUpdatePayload, DeleteAllPayload, DeletePayload, ciTypesSlice, getCiTypes, CiTypesState } from 'store/slices/CiTypesSlice';
import { put, takeEvery, call, select } from 'redux-saga/effects';

function* fetchDataSaga() {
  yield put(
    ciTypesSlice.actions.setValue({
      isFetching: true,
      isFetched: false,
      data: [],
    }),
  );
  try {
    const response: ApiResponse<ConfigItemTypeResponse[]> = yield call(ConfigItemTypeApi.getAllConfigItemType.bind(ConfigItemTypeApi));
    yield put(
      ciTypesSlice.actions.setValue({
        isFetching: false,
        isFetched: true,
        data: response.data,
      }),
    );
  } catch (ex) {
    yield put(
      ciTypesSlice.actions.setValue({
        isFetching: false,
        isFetched: true,
        data: [],
      }),
    );
  }
}

function* fetchForEmptySaga() {
  const currentData: CiTypesState = yield select(getCiTypes);
  if (!currentData || (!currentData.isFetching && !currentData.isFetched)) {
    yield call(fetchDataSaga);
  }
}

function* deleteDataSaga(data: DeletePayload) {
  try {
    const response: ApiResponse<ConfigItemTypeResponse> = yield call(
      ConfigItemTypeApi.deleteConfigItemType.bind(ConfigItemTypeApi),
      data.payload.data,
    );
    yield put(ciTypesSlice.actions.removeValue(data.payload.data));
    if (response.status === 200 && data.payload.onSuccessCallback) {
      data.payload.onSuccessCallback(response);
    }
  } catch (ex) {
    console.error('deleteDataSage error: ', ex);
  }
}

function* deleteAllDataSaga(data: DeleteAllPayload) {
  try {
    const response: ApiResponse<ConfigItemTypeResponse> = yield call(
      ConfigItemTypeApi.deleteAllConfigItemType.bind(ConfigItemTypeApi),
      data.payload.data,
    );

    if (response.status === 200 && data.payload.onSuccessCallback) {
      data.payload.onSuccessCallback(response);
      yield call(fetchDataSaga);
    }
  } catch (ex) {
    console.error('deleteAllDataSage error: ', ex);
  }
}

function* createOrUpdateDataSaga(data: CreateOrUpdatePayload) {
  try {
    const response: ApiResponse<ConfigItemTypeResponse> = yield call(
      ConfigItemTypeApi.createOrUpdateConfigItemType.bind(ConfigItemTypeApi),
      data.payload.data,
    );
    yield put(ciTypesSlice.actions.updateValue(response.data));
    if (response.status === 200 && data.payload.onSuccessCallback) {
      data.payload.onSuccessCallback(response);
    }
  } catch (ex) {
    console.error('createOrUpdateDataSage error: ', ex);
  }
}

export function* ciTypesSaga() {
  yield takeEvery(ciTypesSlice.actions.fetchData, fetchDataSaga);
  yield takeEvery(ciTypesSlice.actions.fetchForEmpty, fetchForEmptySaga);
  yield takeEvery(ciTypesSlice.actions.deleteData, deleteDataSaga);
  yield takeEvery(ciTypesSlice.actions.deleteAllData, deleteAllDataSaga);
  yield takeEvery(ciTypesSlice.actions.createOrUpdate, createOrUpdateDataSaga);
}
