import type { PermissionAction } from '@common/constants/PermissionAction';
import { PermissionActionType } from '@common/constants/PermissionActionType';
import type { ConfigItemTypeModel } from '@models/ConfigItemType';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { getDirectState } from '@store';
import type { AclPermission } from 'models/AclPermission';
import { findAllChildrenIds } from './CiTypeUtils';

/**
 * check permission CI
 * @param ciTypeId
 * @param ciId
 * @param action
 */
export const isPermission = (ciTypeId: number, ciId: number, action: PermissionAction): boolean => {
  const currentUser = getCurrentUser(getDirectState());
  if (!currentUser) {
    return false;
  }
  const aclPermissions = currentUser.data?.aclPermissions || [];
  const isExitsCIPermision = aclPermissions.find((permission) => permission.typeId === ciId && permission.type === PermissionActionType.CI);
  if (isExitsCIPermision) {
    const existPerrmisson = aclPermissions.find(
      (permission) => permission.action === action && permission.typeId === ciId && permission.type === PermissionActionType.CI,
    );
    return !!existPerrmisson;
  }

  const isExitsCITypePermision = aclPermissions.find(
    (permission) => permission.typeId === ciTypeId && permission.type === PermissionActionType.CI_TYPE,
  );
  if (isExitsCITypePermision) {
    const existPerrmisson = aclPermissions.find(
      (permission) => permission.action === action && permission.typeId === ciTypeId && permission.type === PermissionActionType.CI_TYPE,
    );
    return !!existPerrmisson;
  }
  return false;
};

/**
 * get all action by CI
 * @param permissions
 */
export const getAllActionsByCI = (ciTypeId: number, ciId: number): AclPermission[] => {
  const currentUser = getCurrentUser(getDirectState());
  if (!currentUser) {
    return [];
  }
  const aclPermissions = currentUser.data?.aclPermissions || [];
  const isExitsCIPermision = aclPermissions.find((permission) => permission.typeId === ciId && permission.type === PermissionActionType.CI);
  if (isExitsCIPermision) {
    const existPerrmisson = aclPermissions.filter((permission) => permission.typeId === ciId && permission.type === PermissionActionType.CI);
    return existPerrmisson;
  }

  const isExitsCITypePermision = aclPermissions.filter(
    (permission) => permission.typeId === ciTypeId && permission.type === PermissionActionType.CI_TYPE,
  );
  if (isExitsCITypePermision) {
    const existPerrmisson = aclPermissions.filter((permission) => permission.typeId === ciTypeId && permission.type === PermissionActionType.CI_TYPE);
    return existPerrmisson;
  }
  return [];
};

/**
 * check user have action of ci.
 * @param ciTypeId ciTypeId
 * @param ciId ciId
 * @param actions actions
 * @returns true/ false
 */
export const isCiPermissionAction = (ciTypeId: number, ciId: number, actions: PermissionAction[], isMatchAll = false): boolean => {
  const currentUser = getCurrentUser(getDirectState());
  if (!currentUser) {
    return false;
  }
  const isSupperAdmin = currentUser.data?.isSuperAdmin === true;
  if (isSupperAdmin) {
    return true;
  }

  const ciActions = getAllActionsByCI(ciTypeId, ciId);
  if (isMatchAll) {
    return ciActions.every((obj) => actions.includes(obj.action));
  }
  return ciActions.some((obj) => actions.includes(obj.action));
};

export const getAllActionsByCIType = (ciTypeId: number): AclPermission[] => {
  const currentUser = getCurrentUser(getDirectState());
  if (!currentUser) {
    return [];
  }
  const aclPermissions = currentUser.data?.aclPermissions || [];
  return aclPermissions.filter((permission) => permission.typeId === ciTypeId && permission.type === PermissionActionType.CI_TYPE);
};

/**
 * check permission with AclPermission
 * @param permissions
 */
export const isCurrentUserHasAnyPermissions = (permissions: AclPermission[]): boolean => {
  const currentUser = getCurrentUser(getDirectState());
  if (!currentUser) {
    return false;
  }
  const userAclPermissions = currentUser.data?.aclPermissions || [];

  const existsInPermissionAll = permissions.some((p) => userAclPermissions.some((pa) => isPermissionEqual(p, pa)));
  return !!existsInPermissionAll;
};

export const isCiTypePermission = (ciTypeId: number, action: PermissionAction): boolean => {
  const currentUser = getCurrentUser(getDirectState());
  if (!currentUser) {
    return false;
  }
  const aclPermissions = currentUser.data?.aclPermissions || [];
  const isExitsCITypePermision = aclPermissions.find(
    (permission) => permission.typeId === ciTypeId && permission.type === PermissionActionType.CI_TYPE,
  );
  if (isExitsCITypePermision) {
    const existPerrmisson = aclPermissions.find(
      (permission) => permission.action === action && permission.typeId === ciTypeId && permission.type === PermissionActionType.CI_TYPE,
    );
    return !!existPerrmisson;
  }
  return false;
};

export const aclPermissionGetIdentical = (aclPermission: AclPermission) => {
  return `${aclPermission.action}-${aclPermission.type}${aclPermission.typeId ? `-${aclPermission.typeId}` : ''}`;
};

export const isPermissionEqual = (alcPermissionA: AclPermission, aclPermissionB: AclPermission) => {
  return aclPermissionGetIdentical(alcPermissionA) === aclPermissionGetIdentical(aclPermissionB);
};

export const isCurrentUserMatchPermissions = (requirePermissions: AclPermission[], allMatchPermissions = false): boolean => {
  if (requirePermissions.length === 0) {
    return true;
  }

  const currentUser = getCurrentUser(getDirectState());
  if (!currentUser) {
    return false;
  }
  const isSupperAdmin = currentUser.data?.isSuperAdmin === true;
  if (isSupperAdmin) {
    return true;
  }
  const userPermissions = currentUser.data?.aclPermissions || [];
  const requirePermissionNames = requirePermissions.map((x) => aclPermissionGetIdentical(x));
  const userPermissionNames = userPermissions.map((x) => aclPermissionGetIdentical(x));

  if (allMatchPermissions) {
    return requirePermissionNames.every((permission) => userPermissionNames.includes(permission));
  } else {
    return requirePermissionNames.some((permission) => userPermissionNames.includes(permission));
  }
};

export const checkAllCiPermissions = (ciPermissions: AclPermission[][], allMatchPermissions = false): boolean => {
  return ciPermissions.every((permissions) => isCurrentUserMatchPermissions(permissions, allMatchPermissions));
};

export const getAllCiTypeIdHasPermission = (): number[] => {
  const currentUser = getCurrentUser(getDirectState());

  if (!currentUser) {
    return [];
  }
  const userPermissions = currentUser.data?.aclPermissions || [];
  const ciTypeIdHasPermissions = userPermissions
    .filter((item) => item.type === PermissionActionType.CI_TYPE && item.typeId !== undefined)
    .map((item) => item.typeId as number);

  return Array.from(new Set(ciTypeIdHasPermissions));
};
export const checkCiTypeHasPermission = (citypeId: number, ciTypes: ConfigItemTypeModel[], ciTypeIdHasPermissions: number[]): boolean => {
  const currentUser = getCurrentUser(getDirectState());

  if (!currentUser) {
    return false;
  }
  const isSupperAdmin = currentUser.data?.isSuperAdmin === true;
  if (isSupperAdmin) {
    return true;
  }
  const allChildrenIds = findAllChildrenIds(citypeId, ciTypes);
  return ciTypeIdHasPermissions.includes(citypeId) || allChildrenIds.some((id) => ciTypeIdHasPermissions.includes(id));
};
export const isUserAuthorizedForPermissionsGroups = (permissionGroups: AclPermission[][], allMatchPermissions = false): boolean => {
  const currentUser = getCurrentUser(getDirectState());

  if (!currentUser) {
    return false;
  }

  const isSuperAdmin = currentUser.data?.isSuperAdmin === true;
  if (isSuperAdmin) {
    return true;
  }

  const userPermissions = currentUser.data?.aclPermissions || [];
  const userPermissionNames = userPermissions.map((permission) => aclPermissionGetIdentical(permission));

  // Iterate over each permission group and check the user's permissions
  return permissionGroups.some((group) => {
    const groupPermissionNames = group.map((permission) => aclPermissionGetIdentical(permission));

    if (allMatchPermissions) {
      // Check if all the permissions in the group are included in the user's permissions
      return groupPermissionNames.every((permission) => userPermissionNames.includes(permission));
    } else {
      // Check if at least one permission in the group is included in the user's permissions
      return groupPermissionNames.some((permission) => userPermissionNames.includes(permission));
    }
  });
};
