import type { Attribute, Attributes } from '@models/CiDetailCompare';
import type { ConfigItemAttrModel } from '@models/ConfigItemAttr';
import type { ConfigItemAttrCustomModel } from '@models/ConfigItemAttrCustom';

export class CiManagementExecute {
  // list attribute by pass not check change data
  static listSkipAttribute = [
    'modifiedDate',
    'modifiedBy',
    'createdDate',
    'createdBy',
    'approvedDate',
    'approvedBy',
    'author',
    'ciId',
    'id',
    'type',
    'sysId',
  ];

  static normalizeValue(value: any): any {
    if (value === null || value === undefined || (typeof value === 'string' && value.trim() === '')) {
      return null;
    }
    return value;
  }

  static compareObjects(obj1: Record<string, any>, obj2: Record<string, any>): Attribute {
    const diff: Attribute = {};
    for (const key in obj2) {
      const value1 = this.normalizeValue(obj1[key]);
      const value2 = this.normalizeValue(obj2[key]);
      if (value1 !== value2 && !CiManagementExecute.listSkipAttribute.includes(key)) {
        diff[key] = {
          oldValue: value1,
          newValue: value2,
        };
      }
    }
    return diff;
  }

  static compareAttributes(attr1: ConfigItemAttrModel[], attr2: ConfigItemAttrModel[]): Attributes {
    const diff: Attributes = {};
    for (const attr2Item of attr2) {
      const attr1Item = attr1.find((item) => item.ciTypeAttributeId === attr2Item.ciTypeAttributeId);

      const value2 = this.normalizeValue(attr2Item.value);
      if (attr1Item) {
        const value1 = this.normalizeValue(attr1Item.value);
        if (value1 !== value2) {
          diff[attr2Item.ciTypeAttributeId] = {
            oldValue: value1,
            newValue: value2,
          };
        }
      } else {
        if (attr2Item.value && value2) {
          diff[attr2Item.ciTypeAttributeId] = {
            oldValue: null,
            newValue: value2,
          };
        }
      }
    }

    // Check for removals in attr1
    for (const attr1Item of attr1) {
      const attr2Item = attr2.find((item) => item.ciTypeAttributeId === attr1Item.ciTypeAttributeId);

      if (!attr2Item && attr1Item.value) {
        diff[attr1Item.ciTypeAttributeId] = {
          oldValue: attr1Item.value,
          newValue: null,
        };
      }
    }

    return diff;
  }

  static compareAttributesCustom(attr1: ConfigItemAttrCustomModel[], attr2: ConfigItemAttrCustomModel[]): Attributes {
    const diff: Attributes = {};
    for (const attr2Item of attr2) {
      const attr1Item = attr1.find((item) => item.name === attr2Item.name);

      const value2 = this.normalizeValue(attr2Item.value);
      if (attr1Item) {
        const value1 = this.normalizeValue(attr1Item.value);
        if (value1 !== value2) {
          diff[attr2Item.name] = {
            oldValue: value1,
            newValue: value2,
          };
        }
      } else {
        if (attr2Item.value && value2) {
          diff[attr2Item.name] = {
            oldValue: null,
            newValue: value2,
          };
        }
      }
    }

    // Check for removals in attr1
    for (const attr1Item of attr1) {
      const attr2Item = attr2.find((item) => item.name === attr1Item.name);

      if (!attr2Item && attr1Item.value) {
        diff[attr1Item.name] = {
          oldValue: attr1Item.value,
          newValue: null,
        };
      }
    }

    return diff;
  }
}

export default 1;
