import { Flex } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { ConfigItemTypeAttrModel } from '@models/ConfigItemTypeAttr';
import ListCiTypeAttributeComponent from '@pages/cis/ciType/component/ListCiTypeAttributeComponent';
import { IconEdit, IconTableOptions } from '@tabler/icons-react';
import { KanbanButton, KanbanIconButton, KanbanModal, KanbanText } from 'kanban-design-system';
import React, { useEffect, useState } from 'react';

interface AddColumnListCiProp {
  ciTypeId?: number;
  dataAttributeSelected: ConfigItemTypeAttrModel[];
  updateDataAttributeSelected: (datas: ConfigItemTypeAttrModel[]) => void;
  localStorageKey: string;
}

const AddColumnListCiComponent = ({ ciTypeId, dataAttributeSelected, localStorageKey, updateDataAttributeSelected }: AddColumnListCiProp) => {
  const [temp, setTemp] = useState<ConfigItemTypeAttrModel[]>(dataAttributeSelected);
  const [listDataAttribute, setListDataAttribute] = useState<ConfigItemTypeAttrModel[]>();

  useEffect(() => {
    setTemp(dataAttributeSelected);
  }, [dataAttributeSelected]);
  const [openedModalListAttribute, { close: closeModalListAttribute, open: openModalListAttribute }] = useDisclosure(false);

  const updateListDataAttribute = (datas: ConfigItemTypeAttrModel[]) => {
    setListDataAttribute(datas);
  };

  return (
    <>
      <KanbanIconButton
        ml={'sm'}
        variant={'outline'}
        title='Edit columns'
        onClick={() => {
          openModalListAttribute();
        }}>
        <IconTableOptions />
      </KanbanIconButton>
      <KanbanModal
        size={'55%'}
        opened={openedModalListAttribute}
        onClose={() => {
          const dataStorage = localStorage.getItem(localStorageKey);
          if (dataStorage) {
            const dataAttributeStorage: ConfigItemTypeAttrModel[] = JSON.parse(dataStorage);
            const list =
              listDataAttribute && listDataAttribute.length > 0
                ? dataAttributeStorage.filter((row) => listDataAttribute.some((item) => item.hashId === row.hashId))
                : [];
            localStorage.setItem(localStorageKey, JSON.stringify(list));
            updateDataAttributeSelected(list);
          }
          setTemp(dataAttributeSelected);
          closeModalListAttribute();
        }}
        title={'Edit columns'}
        actions={
          <>
            <Flex justify='space-between' align='center' style={{ width: '90%' }}>
              <KanbanText style={{ fontSize: '12px', color: '#888', fontStyle: 'italic' }}>Up to 20 attributes can be selected</KanbanText>

              <KanbanButton
                leftSection={<IconEdit />}
                onClick={() => {
                  const list =
                    listDataAttribute && listDataAttribute.length > 0
                      ? temp.filter((row) => listDataAttribute.some((item) => item.hashId === row.hashId))
                      : [];
                  updateDataAttributeSelected(list);
                  localStorage.setItem(localStorageKey, JSON.stringify(list));
                  closeModalListAttribute();
                }}>
                Edit
              </KanbanButton>
            </Flex>
          </>
        }>
        <ListCiTypeAttributeComponent
          ciTypeId={ciTypeId}
          dataAttributeSelected={temp}
          updateDataAttributeSelected={setTemp}
          updateListDataAttribute={updateListDataAttribute}
        />
      </KanbanModal>
    </>
  );
};

export default AddColumnListCiComponent;
