import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanTable, KanbanTableProps, TableAffactedSafeType, KanbanIconButton, KanbanButton, ColumnType } from 'kanban-design-system';
import { IconPlus, IconEdit, IconEye, IconCopy, IconUser } from '@tabler/icons-react';
import React, { useEffect, useState, useCallback, useMemo } from 'react';
// import { useDispatch } from 'react-redux';
import { NotificationError } from '@common/utils/NotificationUtils';
import { useNavigate } from 'react-router-dom';
import { buildChangeAssessmentDetailUrl } from '@common/utils/RouterUtils';
import { renderDateTime } from 'kanban-design-system';
import equal from 'fast-deep-equal';
import { KanbanText } from 'kanban-design-system';
import { ChangeAssessmentAction, ChangeAssessmentDraftStatus, type ChangeAssessmentDTO } from '@models/ChangeAssessment';
import ChangeAssessmentExecute from '@service/ChangeAssessmentExecute';
import { ChangeAssessmentApi } from '@api/ChangeAssessmentApi';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { useSelector } from 'react-redux';
import AssignOwnerComponent from './components/AssignOwnerComponent';
import { ChangeSdpApi } from '@api/ChangeSdpApi';
import { checkChangeStagesWhenDelete, checkIsChangeCorThenAuthor } from '@common/utils/ChangeAssessmentUtils';
import { Badge } from '@mantine/core';
import { BreadcrumbComponent } from '../breadcrumb/BreadcrumbComponent';
import { getConfigs } from '@core/configs/Configs';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import { MAX_NUMBER_LENGTH, MAX_TEXT_LENGTH } from '@common/constants/FieldLengthConstants';

export const ChangeAssessmentPage = () => {
  const navigate = useNavigate();

  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);

  const [listData, setListData] = useState<ChangeAssessmentDTO[]>([]);
  const [isLoadingTable, setIsLoadingTable] = useState(false);
  const currentUser = useSelector(getCurrentUser);
  const currentUsername = currentUser.data?.username;
  const isSuperAdmin = currentUser.data?.isSuperAdmin === true;
  const [changeOwner, setChangeOwner] = useState({ author: '', id: 0, draftId: '' });
  const [isOpenAssignOwnerModal, setIsOpenAssignOwnerModal] = useState(false);
  const showImpactedServiceFeature = getConfigs().features.impactedService;

  const columns: ColumnType<ChangeAssessmentDTO>[] = useMemo(
    () => [
      {
        title: 'Change ID',
        name: 'changeId',
        width: '10%',
        advancedFilter: {
          variant: 'number',
          filterModes: ['equals', 'notEquals', 'greaterThan', 'greaterThanOrEqualTo', 'lessThan', 'lessThanOrEqualTo'],
          customProps: { maxLength: MAX_NUMBER_LENGTH },
        },
      },
      {
        title: 'CIs in Change Plan',
        name: 'ciName',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (_, rowData) => {
          const affectedCisString = ChangeAssessmentExecute.getCiNamesString(rowData.affectedCis);
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {affectedCisString}
            </KanbanText>
          );
        },
      },
      ...(!showImpactedServiceFeature
        ? []
        : [
            {
              title: 'Impacted Services',
              name: 'impactedServices',
              advancedFilter: {
                enable: false,
              },
              customRender: (data: any) => {
                const impactedServicesString = ChangeAssessmentExecute.getCiNamesString(data);
                return (
                  <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
                    {impactedServicesString}
                  </KanbanText>
                );
              },
            },
          ]),
      {
        title: 'Draft ID',
        name: 'changeDraftId',
        width: '10%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Draft Status',
        name: 'changeDraftStatus',
        width: '10%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data: string) => {
          const isLocked = ChangeAssessmentDraftStatus.LOCK === data;
          return (
            <Badge mt='5' color={!isLocked ? 'var(--mantine-color-green-6)' : 'var(--mantine-color-red-5)'}>
              {data || ChangeAssessmentDraftStatus.LOCK}
            </Badge>
          );
        },
      },
      {
        title: 'Owner',
        name: 'author',
        width: '10%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Last modified by',
        name: 'modifiedBy',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Last modified date',
        name: 'modifiedDate',
        advancedFilter: {
          variant: 'date',
          customProps: {
            popoverProps: {
              withinPortal: false,
            },
          },
        },
        customRender: renderDateTime,
      },
    ],
    [showImpactedServiceFeature],
  );

  const fetchListChangeAssessment = useCallback(() => {
    if (!tableAffected) {
      return;
    }
    const dataSend = tableAffectedToMultiColumnFilterPaginationRequestModel<ChangeAssessmentDTO>(
      tableAffected.sortedBy ? tableAffected : { ...tableAffected, sortedBy: 'createdDate', isReverse: true },
    );

    setIsLoadingTable(true);
    ChangeAssessmentApi.getAll(dataSend)
      .then((res) => {
        if (res.data) {
          setListData(res.data?.content || []);
          setTotalRecords(res.data.totalElements);
        }
      })
      .catch(() => {})
      .finally(() => {
        setIsLoadingTable(false);
      });
  }, [tableAffected]);

  useEffect(() => {
    fetchListChangeAssessment();
  }, [fetchListChangeAssessment]);

  const deleteRow = useCallback(
    (row: ChangeAssessmentDTO) => {
      ChangeAssessmentApi.deleteByIds([row.id])
        .then((res) => {
          if (res.data) {
            checkChangeStagesWhenDelete(res.data);
            // const newData = listData.filter((x) => x.id !== row.id);
            // setListData(newData);
            fetchListChangeAssessment();
          }
        })
        .catch(() => {});
    },
    [fetchListChangeAssessment],
  );

  const deleteRows = useCallback(
    (rows: ChangeAssessmentDTO[]) => {
      const ids = rows.map((item) => item.id);
      ChangeAssessmentApi.deleteByIds(ids)
        .then((res) => {
          if (res.data) {
            checkChangeStagesWhenDelete(res.data);
            // const newData = listData.filter((item) => !ids.includes(item.id));
            // setListData(newData);
            fetchListChangeAssessment();
          }
        })
        .catch(() => {});
    },
    [fetchListChangeAssessment],
  );

  const openAssignOwnerComponent = useCallback(
    (author: string, id: number, changeDraftId: string) => {
      //Allow super admin / changeCor to open assign modal
      if (isSuperAdmin || checkIsChangeCorThenAuthor('', currentUsername, author)) {
        setIsOpenAssignOwnerModal(true);
        setChangeOwner({ author: author, id: id, draftId: changeDraftId });
      } else {
        NotificationError({
          title: `Error`,
          message: 'Current user do not have permission.',
        });
      }
    },
    [currentUsername, isSuperAdmin],
  );

  const onCloseAssignOwnerModal = () => {
    setIsOpenAssignOwnerModal(false);
  };

  //1632: add async when get change data
  const onActionUpdateChangeAssessment = useCallback(
    async (rowData: ChangeAssessmentDTO) => {
      let changeCor: string | undefined;
      if (rowData.changeId) {
        try {
          //input ChangeAssessmentAction.VIEW instead of UPDATE to get change without validate change data
          changeCor = (await ChangeSdpApi.getById(rowData.changeId, ChangeAssessmentAction.VIEW, rowData.id, false)).data?.changeCor;
        } catch (e) {
          /* empty */
        }
      }
      //Allow super admin or changeCor edit CA  or nonChangeCor edit UNLOCK-CA
      if (
        isSuperAdmin ||
        checkIsChangeCorThenAuthor(changeCor, currentUsername, rowData.author) ||
        ChangeAssessmentDraftStatus.UNLOCK === rowData.changeDraftStatus
      ) {
        navigate(buildChangeAssessmentDetailUrl(rowData.id, ChangeAssessmentAction.UPDATE));
      } else {
        NotificationError({
          title: `Error`,
          message: 'Change assessment has been locked.',
        });
      }
    },
    [currentUsername, isSuperAdmin, navigate],
  );

  const tableViewListChangeAssessmentProps: KanbanTableProps<ChangeAssessmentDTO> = useMemo(() => {
    return {
      columns: columns,
      data: listData,
      key: 1,
      serverside: {
        totalRows: totalRecords,
        onTableAffected(dataSet) {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
      pagination: {
        enable: true,
      },
      advancedFilterable: {
        enable: true,
        debounceTime: 1000,
        resetOnClose: true,
        compactMode: true,
      },
      isLoading: isLoadingTable,
      searchable: { enable: true, debounceTime: 300 },
      showNumericalOrderColumn: true,
      selectableRows: {
        enable: !!isCurrentUserMatchPermissions([AclPermission.deleteChangeAssessment]),
        onDeleted: isCurrentUserMatchPermissions([AclPermission.deleteChangeAssessment])
          ? (rows) => {
              deleteRows(rows);
            }
          : undefined,
      },
      onRowClicked: (data) => {
        isCurrentUserMatchPermissions([AclPermission.viewDetailChangeAssessment]) &&
          navigate(buildChangeAssessmentDetailUrl(data.id, ChangeAssessmentAction.VIEW));
      },
      actions: {
        customAction: isCurrentUserMatchPermissions(AclPermission.actionTableChangeAssessmentPermissions)
          ? (data) => (
              <>
                <GuardComponent requirePermissions={[AclPermission.viewDetailChangeAssessment]} hiddenOnUnSatisfy>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      navigate(buildChangeAssessmentDetailUrl(data.id, ChangeAssessmentAction.VIEW));
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                </GuardComponent>
                <GuardComponent requirePermissions={[AclPermission.createChangeAssessment]} hiddenOnUnSatisfy>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      navigate(buildChangeAssessmentDetailUrl(data.id, ChangeAssessmentAction.COPY));
                    }}>
                    <IconCopy />
                  </KanbanIconButton>
                </GuardComponent>
                <GuardComponent requirePermissions={[AclPermission.updateChangeAssessment]} hiddenOnUnSatisfy>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      onActionUpdateChangeAssessment(data);
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                </GuardComponent>
                <GuardComponent requirePermissions={[]} hiddenOnUnSatisfy>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      openAssignOwnerComponent(data.author, data.id, data.changeDraftId);
                    }}>
                    <IconUser />
                  </KanbanIconButton>
                </GuardComponent>
              </>
            )
          : undefined,
        deletable: isCurrentUserMatchPermissions([AclPermission.deleteChangeAssessment])
          ? {
              onDeleted: (data) => {
                deleteRow(data);
              },
            }
          : undefined,
      },
    };
  }, [
    columns,
    deleteRow,
    deleteRows,
    isLoadingTable,
    listData,
    navigate,
    tableAffected,
    totalRecords,
    openAssignOwnerComponent,
    onActionUpdateChangeAssessment,
  ]);

  return (
    <>
      {/* 4736 change list*/}
      <BreadcrumbComponent />
      {isOpenAssignOwnerModal && <AssignOwnerComponent draftId={changeOwner.draftId} id={changeOwner.id} closeModal={onCloseAssignOwnerModal} />}
      <HeaderTitleComponent
        title='Change Assessments'
        rightSection={
          <GuardComponent requirePermissions={[AclPermission.createChangeAssessment]} hiddenOnUnSatisfy>
            <KanbanButton
              onClick={() => {
                navigate(buildChangeAssessmentDetailUrl(0, ChangeAssessmentAction.CREATE));
              }}
              leftSection={<IconPlus />}>
              Add change
            </KanbanButton>
          </GuardComponent>
        }
      />
      <div style={{ flex: 2 }}>
        <KanbanTable {...tableViewListChangeAssessmentProps} />
      </div>
    </>
  );
};
export default ChangeAssessmentPage;
