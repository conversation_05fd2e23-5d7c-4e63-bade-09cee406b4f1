import type { ConfigItemTypeModel } from '@models/ConfigItemType';

export const findAllChildrenIds = (nodeId: number, ciTypes: ConfigItemTypeModel[]): number[] => {
  const children = ciTypes.filter((item) => item.parentId === nodeId);
  const childrenIds = children.map((item) => item.id);
  const allChildrenIds = childrenIds.flatMap((childId) => findAllChildrenIds(childId, ciTypes));
  return [...childrenIds, ...allChildrenIds];
};

export default 1;
