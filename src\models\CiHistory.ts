import type { EntityModelBase } from './EntityModelBase';

export enum CiHistoryStatus {
  NONE = 'NONE',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}
export enum CiHistoryAction {
  NONE = 'NONE',
  ADDED = 'ADDED',
  EDITED = 'EDITED',
  IMPORT = 'IMPORT',
  REMOVED = 'REMOVED',
}

export type CiHistoryModel = EntityModelBase & {
  id: number;
  ciId: number;
  action?: CiHistoryAction;
  //4243:cihistory : Show data change - compare with ciData (ciInfo+attribute+customAttribute)
  dataChange?: string;
  dataChangeFlatten?: string;
  requester?: string;
  //
  description?: string;
  status: CiHistoryStatus;
};

export type HistoryDescription = {
  info: {
    name: string;
    toValue?: string;
  }[];

  attributes: {
    id: number;
    name: string;
    toValue?: string;
  }[];

  attributeCustoms: {
    id: number;
    name: string;
    toValue?: string;
  }[];

  // toDescription() : string {

  //     let result = '';

  //     for(const item of this.info){
  //         result += `\n-${item.name} changed to ${item.toValue || '-'}`;
  //     }

  //     for(const item of this.attributes){
  //         result += `\n-${item.name} changed to ${item.toValue || '-'}`;
  //     }
  //     for(const item of this.attributeCustoms){
  //         result += `\n-${item.name} changed to ${item.toValue || '-'}`;
  //     }

  //     return result;

  // }
};
