import React, { useMemo, useState } from 'react';
import { CIBussinessViewsApi, type CIBussinessViewsResponse } from '@api/CIBussinessViewsApi';
import { KanbanInput } from 'kanban-design-system';
import { KanbanTextarea } from 'kanban-design-system';
import { KanbanConfirmModal } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import { formatStandardName } from '@common/utils/StringUtils';
import { Divider, NavLink, Popover, ThemeIcon } from '@mantine/core';
import { KanbanTooltip } from 'kanban-design-system';
import { IconDeviceFloppy } from '@tabler/icons-react';
import { KanbanText } from 'kanban-design-system';
import type { CIBusinessViews, DataActionDTO } from '@models/CIBusinessViews';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import type { ToolbarActionProps } from '../CiRelationshipGraph';
import type { GoJs } from '@common/libs';
import { businessViewsPath, navigateTo } from '@common/utils/RouterUtils';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';

type SaveGraphViewProps = {
  cIBusinessViews?: CIBusinessViews;
  ciId: number;
  goDiagram: GoJs.Diagram | undefined;
  toolbarAction: ToolbarActionProps;
  currentLevel?: string;
};

export type CreateGraphViewMethods = {
  openPopupCreate: () => void;
  closePopupCreate: () => void;
  saveOrUpdate: () => void;
  resetForm: () => void;
};

export const SaveGraphView = (props: SaveGraphViewProps) => {
  const { cIBusinessViews, ciId, goDiagram, toolbarAction } = props;
  const viewGraphData: CIBussinessViewsResponse = {
    id: 0,
    viewName: '',
    visibility: '',
    description: '',
    share: true,
    graphView: '',
    ciId: 0,
    ciName: '',
  };
  const [dataView, setDataView] = useState<CIBussinessViewsResponse>({ ...viewGraphData });
  const [openedPopupBussiness, { close: closePopupBussiness, open: openPopupBussiness }] = useDisclosure(false);

  const resetForm = () => {
    setDataView((prev) => ({ ...prev, viewName: '', description: '', id: 0 }));
  };

  const saveOrUpdateViewGraph = (isSaveAs: boolean) => {
    if (!goDiagram) {
      return;
    }

    const ciID = Number(ciId);
    const modelAsJson = goDiagram.model.toJson();
    dataView.graphView = modelAsJson;
    const dataSend: CIBusinessViews = {
      ...dataView,
      ...cIBusinessViews,
    };
    dataSend.graphView = modelAsJson;
    const dataAction: DataActionDTO = {
      filter: toolbarAction.filter,
      graphViewStyle: toolbarAction.graphViewStyle,
      level: props.currentLevel,
      graphViewLinkStyle: toolbarAction.graphViewLinkStyle,
    };
    dataSend.dataAction = JSON.stringify(dataAction);
    if (isSaveAs) {
      dataSend.id = 0;
      dataSend.viewName = dataView.viewName;
      dataSend.description = dataView.description;
    }
    CIBussinessViewsApi.saveOrUpdate(dataSend, ciID)
      .then((res) => {
        if (res.status === 200) {
          const message = !isSaveAs ? 'Update successfully' : 'Create successfully';
          NotificationSuccess({
            message: message,
          });
          if (isSaveAs && isCurrentUserMatchPermissions([AclPermission.viewListBusinessView])) {
            navigateTo(businessViewsPath);
          }
        }
      })
      .catch(() => {
        //nothing
      });
  };

  const save = () => {
    if (cIBusinessViews) {
      saveOrUpdateViewGraph(false);
    } else {
      openPopupBussiness();
    }
  };
  const isViewIconSave: boolean = useMemo(() => {
    // when open from bussiness view  icon save when user have updateBusinessView or createBusinessView
    if (cIBusinessViews && isCurrentUserMatchPermissions([AclPermission.updateBusinessView, AclPermission.createBusinessView])) {
      return true;
    }
    // when open graph view icon save when user have  createBusinessView
    if (!cIBusinessViews && isCurrentUserMatchPermissions([AclPermission.createBusinessView])) {
      return true;
    }
    return false;
  }, [cIBusinessViews]);

  return (
    <>
      {isViewIconSave && (
        <Popover
          withinPortal={false}
          closeOnClickOutside={true}
          clickOutsideEvents={['mouseup', 'touchend']}
          position='right'
          shadow='md'
          width={200}>
          <Popover.Target>
            <KanbanTooltip withinPortal={false} label='Save'>
              <ThemeIcon variant='outline' color={'primary'} size={30}>
                <IconDeviceFloppy size='3rem' stroke={2} />
              </ThemeIcon>
            </KanbanTooltip>
          </Popover.Target>

          <Popover.Dropdown>
            <KanbanText>Save Diagram</KanbanText>
            {cIBusinessViews && isCurrentUserMatchPermissions([AclPermission.updateBusinessView]) && (
              <>
                <Divider />
                <NavLink onClick={save} label='Save' />
              </>
            )}

            {isCurrentUserMatchPermissions([AclPermission.createBusinessView]) && (
              <>
                <Divider />
                <NavLink
                  onClick={() => {
                    resetForm();
                    openPopupBussiness();
                  }}
                  label={cIBusinessViews ? 'Save as' : 'Save'}
                />
              </>
            )}
          </Popover.Dropdown>
        </Popover>
      )}

      <KanbanConfirmModal
        title={'Create Bussiness View'}
        onConfirm={() => saveOrUpdateViewGraph(true)}
        onClose={closePopupBussiness}
        opened={openedPopupBussiness}
        disabledConfirmButton={!dataView.viewName || dataView.viewName.trim() === ''}>
        <KanbanInput
          label='View Name'
          withAsterisk
          value={dataView.viewName || ''}
          onChange={(e) => {
            const value = e.target.value;
            setDataView((prev) => ({ ...prev, viewName: value }));
          }}
          onBlur={(e) => {
            const value = e.target.value;
            setDataView((prev) => ({
              ...prev,
              viewName: formatStandardName(value),
            }));
          }}
        />

        <KanbanTextarea
          label='Description'
          value={dataView.description || ''}
          onChange={(e) => {
            const value = e.target.value;
            setDataView((prev) => ({ ...prev, description: value }));
          }}
        />
      </KanbanConfirmModal>
    </>
  );
};
SaveGraphView.displayName = 'CreateGraphView';
export default SaveGraphView;
