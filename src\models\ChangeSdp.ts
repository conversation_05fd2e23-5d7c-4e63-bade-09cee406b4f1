import type { EntityModelBase } from './EntityModelBase';

export type ChangeResponseDto = {
  changeId: number;
  changeStatus: string;
  changeStage: string;
  messageResponse: string;
  statusResponse: string;
  changeTitle: string;
  changeCor: string;
  changeSdpLink: string;
  cmdbErrorCode: string;
  cmdbErrorMessage: string;
};
export type CiSdpRequestModel = EntityModelBase & {
  requestId: number;
  status: string;
  stage: string;
  requestUrl: string;
};
export enum DateFilterOptionEnum {
  ALL = 'All',
  TODAY = 'Today',
  THIS_WEEK = 'This week',
  THIS_MONTH = 'This month',
  THIS_YEAR = 'This year',
  CUSTOM = 'Custom',
}
export type RangeDateModel = {
  fromDate: Date | null;
  toDate: Date | null;
};
export const selectDateFilterOptions = Object.values(DateFilterOptionEnum).map((enumValue) => ({
  label: enumValue,
  value: enumValue,
}));
export default 1;
