import { ConfigItemApi, ConfigItemResponse } from '@api/ConfigItemApi';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import type { ConfigItemTypeAttrResponse } from '@api/ConfigItemTypeAttrApi';
import { TablerIconKeys, getTablerIconByName } from '@common/utils/IconsUtils';
import GuardComponent from '@components/GuardComponent';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton, KanbanTitle } from 'kanban-design-system';
import { Flex, Space } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import type { ConfigItemInfoModel } from '@models/ConfigItem';
import type { ConfigItemAttrModel } from '@models/ConfigItemAttr';
import { getCiTypeById } from '@slices/CiTypesSlice';
import { IconAffiliate, IconCopy, IconEdit, IconEye, IconFileImport, IconHome, IconPlus, IconTrash } from '@tabler/icons-react';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import CiUpdate from '../ci/CiUpdate';
import { NotificationSuccess, NotificationWarning } from '@common/utils/NotificationUtils';
import { KanbanConfirmModal } from 'kanban-design-system';
import { KanbanIconButton } from 'kanban-design-system';
import { KanbanModal } from 'kanban-design-system';
import CiListComponent, { CiListComponentMethod, CiListCustomTablePropsType } from '@components/commonCi/CiListComponent';
import {
  buildCiUrl,
  buildCiImportUrl,
  buildCiManageDetailUrl,
  ciRelationShipImportPath,
  ciTypePath,
  buildCiTypeUrl,
} from '@common/utils/RouterUtils';
import { CiUpdateSole, type CiUpdateSoleMethods } from '../ci/CiUpdateSole';
import CiRelationship from '../ci/relationship/CiRelationship';
import { CiManagementApi } from '@api/CiManagementApi';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { AclPermission } from '@models/AclPermission';
import { PermissionAction } from '@common/constants/PermissionAction';
import { PermissionActionType } from '@common/constants/PermissionActionType';
import { isCurrentUserMatchPermissions, isUserAuthorizedForPermissionsGroups } from '@common/utils/AclPermissionUtils';
import ConfirmDeleteCiModal, { ConfirmDeleteCiModalMethods } from '../ci/ConfirmDeleteCiModal';
import { getCiHasPermissions } from '@slices/CiHasPermissionSlice';
import type { ConfigItemAttrCustomModel } from '@models/ConfigItemAttrCustom';
import { ActionTypeEnum } from '@common/constants/CiDetail';
import { BreadcrumbComponent, UrlBaseCrumbData } from '@pages/admins/breadcrumb/BreadcrumbComponent';

// const defaultDeleteCiPermissions = AclPermission.defaultDeleteCiPermissions;
export const CiTypePage = () => {
  const navigate = useNavigate();
  // const [allDeleteCiPermissions, setAllDeleteCiPermissions] = useState<AclPermission[]>(defaultDeleteCiPermissions);
  const refCiListComponent = useRef<CiListComponentMethod | null>(null);
  const [listCiTypeAttribute, setListCiTypeAttribute] = useState<ConfigItemTypeAttrResponse[]>([]);
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);
  const [openedModalEdit, { close: closeModalEdit, open: openModalEdit }] = useDisclosure(false);
  const [openedModalRelationship, { close: closeModalRelationship, open: openModalRelationship }] = useDisclosure(false);
  const [openedModalAfterUpdate, { close: closeModalAfterUpdate, open: openModalAfterUpdate }] = useDisclosure(false);
  const [openedModalNoticeChange, { close: closeModalNoticeChange, open: openModalNoticeChange }] = useDisclosure(false);
  const [currentCI, setCurrentCI] = useState<ConfigItemResponse>({
    name: '',
    ciTypeId: 0,
    id: 0,
  });
  const [currentCIAttributes, setCurrentCIAttributes] = useState<Record<number, ConfigItemAttrModel>>({});
  const [ciAttributesCustom, setCiAttributesCustom] = useState<ConfigItemAttrCustomModel[]>([]);

  const [oldUserEdit, setOldUserEdit] = useState('');
  const [oldCiTempId, setOldCiTempId] = useState<number>();
  const [ciIdDel, setCiIdDel] = useState<number>(0);
  const currentUser = useSelector(getCurrentUser);
  const ciHasPermissions = useSelector(getCiHasPermissions)?.ciHasPermissions;
  const { id } = useParams();
  const ciTypeId = Number(id);
  const ciType = useSelector(getCiTypeById(ciTypeId));
  const Icon = getTablerIconByName(ciType?.icon as TablerIconKeys) || IconHome;

  const [isValidUpdate, setIsValidUpdate] = useState(false);
  const [hasImportPermission, setHasImportPermission] = useState(false);

  const [actionType, setActionType] = useState<ActionTypeEnum | null>(null);

  useEffect(() => {
    const userPermissions = currentUser.data?.aclPermissions;
    const isSuperAdmin = currentUser.data?.isSuperAdmin === true;
    if (userPermissions) {
      if (isSuperAdmin) {
        setHasImportPermission(true);
      } else {
        const hasMatchingPermission = userPermissions.some(
          (permission) =>
            permission.type === PermissionActionType.CI_TYPE && permission.action === PermissionAction.CI__IMPORT && permission.typeId !== 0,
        );
        if (hasMatchingPermission) {
          setHasImportPermission(true);
        }
      }
    }
  }, [currentUser]);

  const fetchCiTypesAttribute = useCallback(() => {
    ConfigItemTypeApi.getAllAttributes(ciTypeId, {
      useLoading: false,
    })
      .then((res) => {
        setListCiTypeAttribute(res.data);
      })
      .catch(() => {});
  }, [ciTypeId]);

  useEffect(() => {
    fetchCiTypesAttribute();
  }, [fetchCiTypesAttribute]);

  useEffect(() => {
    const updateCurrentCI = (newCiTypeId: number) => {
      setCurrentCI((prevState) => ({
        ...prevState,
        ciTypeId: newCiTypeId,
      }));
    };

    updateCurrentCI(ciTypeId);
  }, [ciTypeId]);

  const fetchCis = useCallback(() => {
    refCiListComponent.current?.fetchCis();
  }, []);

  const createNewCi = (isContinue: boolean = false) => {
    const current = { ...currentCI };
    current.ciTypeId = ciTypeId;
    const dataRequest: ConfigItemInfoModel = {
      ci: current,
      attributes: Object.values(currentCIAttributes),
      attributeCustoms: ciAttributesCustom,
    };

    ConfigItemApi.saveInfo(dataRequest)
      .then((res) => {
        NotificationSuccess({
          message: 'The CI has been created but not approved yet. Please submit for approval at Tab CIs Management -> CIs Draft',
        });
        if (res && res.status === 200 && res.errorDescription) {
          NotificationWarning({
            message: res.errorDescription,
          });
        }
        fetchCis();
        if (isContinue) {
          resetModalCreateNew();
        } else {
          setOldCiTempId(res.data.tempId);
          doCloseModalCreateNew();
          openModalAfterUpdate();
        }
      })
      .catch(() => {});
  };

  const isValid = useMemo(() => {
    if (!currentCI.name) {
      return false;
    }
    for (const attr of listCiTypeAttribute) {
      if (attr.mandatory) {
        const currentAttr = currentCIAttributes[attr.id];
        if (!currentAttr || !currentAttr.value) {
          return false;
        }
      }
    }

    return true;
  }, [listCiTypeAttribute, currentCIAttributes, currentCI]);

  const [updateCiInfo, setUpdateCiInfo] = useState<
    | {
        ciTypeId: number;
        ciId: number;
        readOnly: boolean;
      }
    | undefined
  >();
  const editCiClick = useCallback(
    (ciTypeId: number, ciId: number, readOnly: boolean, actionType: ActionTypeEnum) => {
      if (readOnly) {
        openModalEdit();
        setUpdateCiInfo({
          ciId: ciId,
          ciTypeId: ciTypeId,
          readOnly,
        });
        return;
      }

      setActionType(actionType);

      if (actionType === ActionTypeEnum.CLONE) {
        openModalEdit();
        setUpdateCiInfo({
          ciId: ciId,
          ciTypeId: ciTypeId,
          readOnly,
        });
      } else {
        CiManagementApi.getCiTempByCiIdIn([ciId])
          .then((res) => {
            if (res.data && res.data.length > 0) {
              const oldData = res.data[0];
              setOldCiTempId(oldData.id);
              setOldUserEdit(oldData.owner);
              openModalNoticeChange();
              return;
            } else {
              openModalEdit();
              setUpdateCiInfo({
                ciId: ciId,
                ciTypeId: ciTypeId,
                readOnly,
              });
            }
          })
          .catch(() => {});
      }
    },
    [openModalEdit, openModalNoticeChange],
  );

  const childRef = useRef<CiUpdateSoleMethods | null>(null);

  const onUpdateCI = () => {
    childRef.current?.update().then((res) => {
      if (res) {
        fetchCis();
        closeModalEdit();
        openModalAfterUpdate();
      }
    });
  };
  const resetModalCreateNew = () => {
    setCurrentCIAttributes({});
    setCurrentCI({
      name: '',
      ciTypeId: ciTypeId,
      id: 0,
    });
    setCiAttributesCustom([]);
  };
  const doCloseModalCreateNew = () => {
    resetModalCreateNew();
    closeModal();
  };

  const customTableProps: CiListCustomTablePropsType = useCallback(
    (currentProps, methods) => {
      currentProps.selectableRows = {
        enable: !!ciHasPermissions,
        onDeleted(rows) {
          methods.deleteCis(rows.map((x) => x.id));
        },
        // onSelectedRowsChanged(rows) {
        //   setCiIds(rows.map((item) => item.id));
        // },
      };
      currentProps.onRowClicked = (data) => {
        if (isCurrentUserMatchPermissions(AclPermission.createViewCiPermissions(data.id, data.ciTypeId))) {
          navigate(buildCiUrl(data.ciTypeId, data.id));
        }
      };
      currentProps.actions = {
        // deletable: hasAnyPermission([AclPermission.deleteCi])
        //   ? {
        //       onDeleted(data) {
        //         methods.deleteCi(data.id);
        //       },
        //     }
        //   : undefined,
        customAction: (data) => {
          return (
            <>
              <GuardComponent requirePermissions={AclPermission.createViewCiPermissions(data.id, data.ciTypeId)} hiddenOnUnSatisfy>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    editCiClick(data.ciTypeId, data.id, true, ActionTypeEnum.VIEW);
                  }}>
                  <IconEye />
                </KanbanIconButton>
              </GuardComponent>
              {/* <GuardComponent requirePermissions={[AclPermission.createCiRelationship]} hiddenOnUnSatisfy> */}
              <KanbanIconButton
                variant='transparent'
                size={'sm'}
                onClick={() => {
                  setUpdateCiInfo({
                    ciId: data.id,
                    ciTypeId: ciTypeId,
                    readOnly: false,
                  });
                  openModalRelationship();
                }}>
                <IconAffiliate />
              </KanbanIconButton>
              {/* </GuardComponent> */}
              <GuardComponent
                requirePermissions={AclPermission.createCiPermissions(PermissionAction.CI__UPDATE, data.id, data.ciTypeId)}
                hiddenOnUnSatisfy>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    editCiClick(data.ciTypeId, data.id, false, ActionTypeEnum.EDIT);
                  }}>
                  <IconEdit />
                </KanbanIconButton>
              </GuardComponent>
              {isUserAuthorizedForPermissionsGroups(
                [AclPermission.cloneCiPermissionsForCiType(data.ciTypeId), AclPermission.cloneCiPermissionsForCi(data.id, data.ciTypeId)],
                true,
              ) && (
                <GuardComponent requirePermissions={[]} hiddenOnUnSatisfy>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      editCiClick(data.ciTypeId, data.id, false, ActionTypeEnum.CLONE);
                    }}>
                    <IconCopy />
                  </KanbanIconButton>
                </GuardComponent>
              )}
              <GuardComponent
                requirePermissions={AclPermission.createCiPermissions(PermissionAction.CI__DELETE, data.id, data.ciTypeId)}
                hiddenOnUnSatisfy>
                <KanbanIconButton
                  color='red'
                  size={'sm'}
                  onClick={() => {
                    setCiIdDel(data.id);
                    childRefConfirmDelete.current?.openConfirmDelete(data.id);
                  }}>
                  <IconTrash />
                </KanbanIconButton>
              </GuardComponent>
            </>
          );
        },
      };

      return currentProps;
    },
    [ciHasPermissions, navigate, editCiClick, ciTypeId, openModalRelationship],
  );
  const childRefConfirmDelete = useRef<ConfirmDeleteCiModalMethods | null>(null);

  const onRemoveCiAttributeCustoms = (item: ConfigItemAttrCustomModel) => {
    setCiAttributesCustom((prev) => {
      return prev.filter((x) => x !== item);
    });
  };

  const onAddCiAttributeCustoms = (item: ConfigItemAttrCustomModel) => {
    setCiAttributesCustom((prev) => {
      return [...prev, item];
    });
  };

  const fullCustomPaths = useMemo((): UrlBaseCrumbData => {
    if (ciType?.name && ciType?.id) {
      // case cis of one ci type
      return {
        [`${ciTypePath}`]: { title: ` ${ciType.name}`, href: buildCiTypeUrl(ciType.id || 0) },
      };
    } else {
      // case all cis
      return {
        [`${ciTypePath}`]: { title: `All CIs`, href: buildCiTypeUrl(0) },
      };
    }
  }, [ciType?.name, ciType?.id]);
  return (
    <>
      {/* 4736 ci type's list ci page   */}
      <BreadcrumbComponent orderFullCustomPaths={fullCustomPaths} />
      {/* Modal confirm delete CI */}
      <ConfirmDeleteCiModal
        ref={childRefConfirmDelete}
        onConfirmDelete={() => {
          refCiListComponent.current?.deleteCi(ciIdDel);
        }}
      />

      <KanbanModal
        title='Create new CI'
        onClose={doCloseModalCreateNew}
        opened={openedModal}
        size={'xl'}
        actions={
          <>
            <KanbanButton disabled={!isValid} onClick={() => createNewCi()}>
              Save
            </KanbanButton>
            <KanbanButton disabled={!isValid} onClick={() => createNewCi(true)}>
              Save and Add New
            </KanbanButton>
          </>
        }>
        <CiUpdate
          ci={currentCI}
          ciAttributes={currentCIAttributes}
          ciTypeAttributes={listCiTypeAttribute}
          ciAttributesCustom={ciAttributesCustom}
          onChangeCi={setCurrentCI}
          onChangeCiAttributes={setCurrentCIAttributes}
          onChangeCiAttributeCustoms={setCiAttributesCustom}
          onRemoveCiAttributeCustoms={onRemoveCiAttributeCustoms}
          onAddCiAttributeCustoms={onAddCiAttributeCustoms}
          isCreateCi={true}
        />
      </KanbanModal>
      <KanbanConfirmModal
        title={actionType === ActionTypeEnum.CLONE ? 'Create new CI' : !updateCiInfo?.readOnly ? 'Update CI' : 'CI Detail'}
        onConfirm={!updateCiInfo?.readOnly ? onUpdateCI : undefined}
        textConfirm={actionType === ActionTypeEnum.CLONE ? 'Save' : 'Update'}
        onClose={closeModalEdit}
        opened={openedModalEdit}
        disabledConfirmButton={!isValidUpdate}
        modalProps={{
          size: '60%',
        }}>
        {updateCiInfo && (
          <CiUpdateSole
            setIsValidUpdate={setIsValidUpdate}
            readOnly={updateCiInfo.readOnly}
            ciId={updateCiInfo.ciId}
            ciTypeId={updateCiInfo.ciTypeId}
            forwardedRef={childRef}
            onAfterUpdate={setOldCiTempId}
            actionType={actionType}
          />
        )}
      </KanbanConfirmModal>
      <KanbanModal size={'100%'} title={'Relationship'} onClose={closeModalRelationship} opened={openedModalRelationship}>
        {updateCiInfo && <CiRelationship isView={true} ciId={updateCiInfo.ciId} ciTypeId={ciTypeId} isFromBusinessView={true} />}
      </KanbanModal>

      <HeaderTitleComponent
        title={''}
        // icon={{
        //   icon: Icon,
        // }}
        leftSection={
          <Flex direction={'row'} align={'center'} gap={'0'}>
            <Icon />
            <KanbanTitle fz={'h4'}>{ciTypeId ? ciType?.name || '' : 'All CIs'}</KanbanTitle>
          </Flex>
        }
        rightSection={
          //TODO Permission
          <Flex direction={'row'} align={'center'}>
            {!!ciTypeId && (
              <GuardComponent requirePermissions={[AclPermission.createCiTypePermission(PermissionAction.CI__CREATE, ciTypeId)]} hiddenOnUnSatisfy>
                <KanbanButton leftSection={<IconPlus />} onClick={openModal}>
                  Create new
                </KanbanButton>
              </GuardComponent>
            )}
            <Space w={'xs'} />
            {ciTypeId === 0 && (
              <GuardComponent requirePermissions={[AclPermission.importCiRelationship]} hiddenOnUnSatisfy>
                <KanbanButton
                  color={'cyan'}
                  leftSection={<IconFileImport />}
                  onClick={() => {
                    navigate(ciRelationShipImportPath);
                  }}>
                  Import Ci Relationship
                </KanbanButton>
              </GuardComponent>
            )}

            <Space w={'xs'} />
            {ciTypeId === 0 && hasImportPermission ? (
              <KanbanButton
                leftSection={<IconFileImport />}
                onClick={() => {
                  navigate(buildCiImportUrl(ciTypeId));
                }}>
                Import Ci
              </KanbanButton>
            ) : (
              <GuardComponent requirePermissions={[AclPermission.createCiTypePermission(PermissionAction.CI__IMPORT, ciTypeId)]} hiddenOnUnSatisfy>
                <KanbanButton
                  leftSection={<IconFileImport />}
                  onClick={() => {
                    navigate(buildCiImportUrl(ciTypeId));
                  }}>
                  Import Ci
                </KanbanButton>
              </GuardComponent>
            )}
          </Flex>
        }
      />

      <KanbanConfirmModal
        title={'Create/Update successfully'}
        onConfirm={() => {
          navigate(buildCiManageDetailUrl(oldCiTempId || 0));
        }}
        textConfirm='OK'
        onClose={closeModalAfterUpdate}
        opened={openedModalAfterUpdate}
        modalProps={{
          size: 'lg',
        }}>
        The CI has been created/updated but not approved yet. Do you want to view detail?
      </KanbanConfirmModal>

      <KanbanConfirmModal
        title={'Notification'}
        onConfirm={() => {
          navigate(buildCiManageDetailUrl(oldCiTempId || 0));
        }}
        textConfirm={currentUser.data?.username === oldUserEdit ? 'View detail draft version' : ''}
        onClose={closeModalNoticeChange}
        opened={openedModalNoticeChange}
        modalProps={{
          size: 'xl',
        }}></KanbanConfirmModal>

      <KanbanModal
        title='Notification'
        onClose={closeModalNoticeChange}
        opened={openedModalNoticeChange}
        size={'xl'}
        actions={
          currentUser.data?.username === oldUserEdit && (
            <KanbanButton
              onClick={() => {
                navigate(buildCiManageDetailUrl(oldCiTempId || 0));
              }}>
              View detail draft version
            </KanbanButton>
          )
        }>
        {currentUser.data?.username === oldUserEdit ? (
          <>CI currently exists another version that has not been approved. Please continue to update the previous record</>
        ) : (
          <>
            <p>
              CI currently exists another version that has not been approved. To update current CI information, please contact user:{' '}
              <b>{oldUserEdit}</b>
            </p>
          </>
        )}
      </KanbanModal>

      <CiListComponent ref={refCiListComponent} ciTypeId={ciTypeId} customTableProps={customTableProps} />
    </>
  );
};
export default CiTypePage;
