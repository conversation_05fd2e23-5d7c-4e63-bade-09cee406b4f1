import { ColumnType, KanbanTable, TableAffactedSafeType } from 'kanban-design-system';
import React, { useState, useMemo } from 'react';
import { renderDateTime } from 'kanban-design-system';
import equal from 'fast-deep-equal';
import type { CiSdpRequestModel } from '@models/ChangeSdp';
import { CustomLink } from '../CiRequest';

export const ProblemRequestTableTab = () => {
  const [totalRecords] = useState(0);

  const [isLoadingTable] = useState(false);
  const [problemRequest] = useState<CiSdpRequestModel[]>([]);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);
  const columns: ColumnType<CiSdpRequestModel>[] = useMemo(
    () => [
      {
        title: 'ID',
        name: 'id',
      },
      {
        title: 'Status',
        name: 'status',
      },
      {
        title: 'URL',
        name: 'url',
        customRender: (data) => {
          return (
            <CustomLink target={'_blank'} to={data}>
              {data}
            </CustomLink>
          );
        },
      },
      {
        title: 'Created date',
        name: 'createdDate',
        customRender: renderDateTime,
      },
    ],
    [],
  );

  return (
    <>
      <div style={{ flex: 2 }}>
        <KanbanTable
          title='Problem request'
          columns={columns}
          key={1}
          data={problemRequest}
          isLoading={isLoadingTable}
          showNumericalOrderColumn={true}
          searchable={{
            enable: true,
            debounceTime: 300,
          }}
          pagination={{
            enable: true,
          }}
          serverside={{
            totalRows: totalRecords,
            onTableAffected(dataSet) {
              if (!equal(tableAffected, dataSet)) {
                setTableAffected(dataSet);
              }
            },
          }}
        />
      </div>
    </>
  );
};
export default ProblemRequestTableTab;
