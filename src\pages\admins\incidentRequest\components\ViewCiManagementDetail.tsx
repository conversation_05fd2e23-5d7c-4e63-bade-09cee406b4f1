import { CiManagementApi, type CiManagementResponse } from '@api/CiManagementApi';
import { NotificationError } from '@common/utils/NotificationUtils';
import { CiManagementDetailViewPopup, type CiManagementDetailViewPopupMethods } from '@pages/cis/ciManagement/modal/CiManagementDetailViewPopup';
import React, { useRef, useState } from 'react';
import { forwardRef, useImperativeHandle } from 'react';

export type ViewCiManagementDetailMethods = {
  viewCiManagementDetail: (ciId: number) => void;
};

export const ViewCiManagementDetail = forwardRef<ViewCiManagementDetailMethods>((_, ref) => {
  const [currentCiTemp, setCurrentCiTemp] = useState<CiManagementResponse>();
  const childRefViewDetail = useRef<CiManagementDetailViewPopupMethods | null>(null);

  useImperativeHandle<any, ViewCiManagementDetailMethods>(
    ref,
    () => ({
      viewCiManagementDetail: (ciId: number) => {
        fetchCiManagementDetail(ciId);
      },
    }),
    [],
  );

  const fetchCiManagementDetail = (ciId: number) => {
    if (ciId > 0) {
      CiManagementApi.getCiTempByCiIdIn([ciId])
        .then((res) => {
          if (res.data && res.data.length > 0) {
            setCurrentCiTemp(res.data[0]);
            childRefViewDetail.current?.openPopupView();
            return;
          } else {
            NotificationError({
              message: 'Current record no draft version exists',
            });
          }
        })
        .catch(() => {});
    }
  };

  return (
    <>
      {/* modal view detail info of CI management draft */}
      <CiManagementDetailViewPopup ref={childRefViewDetail} initData={currentCiTemp} />
    </>
  );
});
ViewCiManagementDetail.displayName = 'ViewCiManagementDetail';
export default ViewCiManagementDetail;
