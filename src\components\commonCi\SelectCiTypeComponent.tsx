import { TablerIconKeys, getTablerIconByName } from '@common/utils/IconsUtils';
import { KanbanButton, KanbanComponentWithLabel, KanbanComponentWithLabelProps } from 'kanban-design-system';
import { KanbanIconButton } from 'kanban-design-system';
import { KanbanConfirmModal } from 'kanban-design-system';
import { KanbanText } from 'kanban-design-system';
import { Box, Flex, Group, NavLink, Pill, ScrollArea, Space, ThemeIcon } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import type { ConfigItemTypeModel } from '@models/ConfigItemType';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import { IconChevronRight, IconHome, IconSearch } from '@tabler/icons-react';
import React, { memo, useEffect, useMemo, useState } from 'react';
import classes from './SelectCiType.module.scss';
import { KanbanInput } from 'kanban-design-system';
import { KanbanCheckbox } from 'kanban-design-system';
import { AclPermission } from '@models/AclPermission';
import { checkCiTypeHasPermission, getAllCiTypeIdHasPermission, isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';

const RenderCiTypesCustomLink = (props: {
  ciTypeIdHasPermissions: number[];
  isRoot: boolean;
  isForceOpenAll?: boolean;
  node: ConfigItemTypeModel;
  ciTypes: ConfigItemTypeModel[];
  controlOpenedCiTypes: number[];
  selectedId?: number;
  setSelectedId: (value?: number, item?: ConfigItemTypeModel) => void;
  onMouseDown?: (value?: number, item?: ConfigItemTypeModel) => void;
  onCheckBox?: (value?: boolean, item?: ConfigItemTypeModel) => void;
  setControlOpenedCiTypes: React.Dispatch<React.SetStateAction<number[]>>;
  customActive?: (item: ConfigItemTypeModel) => boolean;
  customHidden?: (item: ConfigItemTypeModel) => boolean;
  leftSection?: (item: ConfigItemTypeModel) => React.ReactNode;
  rightSection?: (item: ConfigItemTypeModel) => React.ReactNode;
  customExpand?: (item: ConfigItemTypeModel) => boolean;
  onChangeExpand?: (item: ConfigItemTypeModel, state: boolean) => void;
  showInfo?: boolean;
  isCheckbox?: boolean;
  dataCheckboxs?: ConfigItemTypeModel[];
  dataDisables?: number[];
  disabled?: (item: ConfigItemTypeModel) => boolean;
}) => {
  const { ciTypes, controlOpenedCiTypes, customActive, node, setControlOpenedCiTypes } = props;
  const children = ciTypes.filter((item) => item.parentId === node.id);
  const hasPermission = children.some((item) => checkCiTypeHasPermission(item.id, ciTypes, props.ciTypeIdHasPermissions));
  const Icons = getTablerIconByName(node.icon as TablerIconKeys) || IconHome;
  const isDisabled = props.disabled && props.disabled(node);
  const className = useMemo(() => {
    let final = !props.isRoot ? classes.link : '';
    if (isDisabled) {
      final += ` ${classes.disabled}`;
    }
    return final;
  }, [props.isRoot, isDisabled]);
  if (props.customHidden && props.customHidden(node)) {
    return <></>;
  }
  const isActive = customActive ? customActive(node) : node.id === props.selectedId;
  const isOpened = (props.customExpand && props.customExpand(node)) || !!props.isForceOpenAll || controlOpenedCiTypes.includes(node.id);

  return (
    <>
      <NavLink
        key={node.id}
        className={className}
        leftSection={
          <Group gap={5} align='center'>
            {props.isCheckbox && (
              <KanbanCheckbox
                checked={props.dataCheckboxs ? props.dataCheckboxs.some((item) => item.id === node.id) : false}
                onClick={(e) => {
                  e.stopPropagation();
                }}
                disabled={props.dataDisables ? props.dataDisables.some((ciTypeId) => ciTypeId === node.id) : false}
                onChange={(e) => {
                  if (props.onCheckBox) {
                    e.stopPropagation();
                    props.onCheckBox(e.target.checked, node);
                  }
                }}></KanbanCheckbox>
            )}
            {props.leftSection && props.leftSection(node)}
            {Icons && (
              <ThemeIcon variant='outline' color={'primary'} size={25}>
                {' '}
                <Icons size='1.3rem' stroke={2} />
              </ThemeIcon>
            )}
          </Group>
        }
        label={node.name}
        description={
          <>
            {props.showInfo ? (
              <Flex gap='md' justify='flex-start' align='center' direction='row'>
                <KanbanText c='dimmed' size='xs'>
                  {node.id}
                </KanbanText>
                <KanbanText c='dimmed' fs='italic' size='xs' lineClamp={1}>
                  {node.description}
                </KanbanText>
              </Flex>
            ) : (
              ''
            )}
          </>
        }
        disableRightSectionRotation
        rightSection={
          <>
            {props.rightSection && <>{props.rightSection(node)}</>}
            {children.length > 0 && hasPermission ? (
              <Box className={!isOpened ? classes.iconSection : classes.iconSectionActive}>
                <KanbanIconButton
                  size={'sm'}
                  variant='default'
                  onClick={(e) => {
                    e.stopPropagation();
                    if (props.onChangeExpand) {
                      props.onChangeExpand(node, !isOpened);
                    }
                    setControlOpenedCiTypes((prev) => {
                      const opened = prev.includes(node.id);
                      if (opened) {
                        return prev.filter((x) => x !== node.id);
                      }

                      return [...prev, node.id];
                    });
                  }}>
                  <IconChevronRight size='0.8rem' stroke={1.5} />
                </KanbanIconButton>
              </Box>
            ) : undefined}
          </>
        }
        active={isActive}
        variant='light'
        opened={isOpened}
        onClick={() => {
          if (isDisabled) {
            return;
          }
          props.setSelectedId(node.id, node);
          return;
        }}
        onMouseDown={() => {
          if (props.onMouseDown) {
            props.onMouseDown(node.id, node);
          }
          return;
        }}>
        {children.length
          ? children.map(
              (item, key) =>
                checkCiTypeHasPermission(item.id, ciTypes, props.ciTypeIdHasPermissions) && (
                  <RenderCiTypesCustomLink
                    key={key}
                    isRoot={false}
                    node={item}
                    isForceOpenAll={props.isForceOpenAll}
                    ciTypeIdHasPermissions={props.ciTypeIdHasPermissions}
                    controlOpenedCiTypes={controlOpenedCiTypes}
                    setControlOpenedCiTypes={setControlOpenedCiTypes}
                    selectedId={props.selectedId}
                    setSelectedId={props.setSelectedId}
                    customActive={customActive}
                    customHidden={props.customHidden}
                    customExpand={props.customExpand}
                    onChangeExpand={props.onChangeExpand}
                    ciTypes={ciTypes}
                    leftSection={props.leftSection}
                    rightSection={props.rightSection}
                    showInfo={props.showInfo}
                    disabled={props.disabled}
                    onMouseDown={props.onMouseDown}
                    onCheckBox={props.onCheckBox}
                    isCheckbox={props.isCheckbox}
                    dataCheckboxs={props.dataCheckboxs}
                    dataDisables={props.dataDisables}
                  />
                ),
            )
          : null}
      </NavLink>
    </>
  );
};

export type SelectCiTypeComponentProps = KanbanComponentWithLabelProps & {
  value?: number;
  disabled?: boolean;
  allowDeselect?: boolean;
  onChange?: (newValue?: number) => void;
} & {
  hasPermission?: boolean;
};

export type CiTypePickerComponentProps = {
  value?: number;
  onChange?: (newValue?: number, item?: ConfigItemTypeModel) => boolean | void; //Return boolean for change value
  onMouseDown?: (value?: number, item?: ConfigItemTypeModel) => void;
  customActive?: (item: ConfigItemTypeModel) => boolean;
  leftSection?: (item: ConfigItemTypeModel) => React.ReactNode;
  rightSection?: (item: ConfigItemTypeModel) => React.ReactNode;
  rightSectionHover?: (item: ConfigItemTypeModel) => boolean;
  customHidden?: (item: ConfigItemTypeModel) => boolean;
  customExpand?: (item: ConfigItemTypeModel) => boolean;
  onChangeExpand?: (item: ConfigItemTypeModel, state: boolean) => void;
  customListCiTypes?: ConfigItemTypeModel[];
  showAllCIs?: boolean;
  showInfo?: boolean;
  disabled?: (item: ConfigItemTypeModel) => boolean;
  isCheckbox?: boolean;
  onCheckBox?: (value?: boolean, item?: ConfigItemTypeModel, data?: ConfigItemTypeModel[]) => void;
  dataCheckboxs?: ConfigItemTypeModel[];
  scrollHeight?: number;
  dataDisables?: number[];
};

export const CiTypePickerComponent = memo((props: CiTypePickerComponentProps) => {
  const [controlOpenedCiTypes, setControlOpenedCiTypes] = useState<number[]>([]);
  const [value, setValue] = useState<number | undefined>(props.value);
  useEffect(() => {
    setValue(props.value);
  }, [props.value]);

  const [search, setSearch] = useState('');
  const onChangeValue = (value?: number, item?: ConfigItemTypeModel) => {
    if (props.onChange) {
      const result = props.onChange(value, item);
      if (result !== false) {
        setValue(value);
      }
    } else {
      setValue(value);
    }
  };

  const { data: ciTypesData = [] } = useGetCiTypes();

  const { customListCiTypes: listCiTypes = ciTypesData } = props;

  const listCiTypesByFillter = useMemo(() => {
    if (!search) {
      return listCiTypes;
    }
    function findParentTree(data: ConfigItemTypeModel[], parentId?: number, tree: ConfigItemTypeModel[] = []) {
      if (!parentId) {
        return tree;
      }
      const parent = data.find((item) => item.id === parentId);
      if (parent) {
        tree.unshift(parent);
        if (parent.parentId !== null) {
          findParentTree(data, parent.parentId, tree);
        }
      }
      return tree;
    }

    const filteredData = listCiTypes.filter(
      (item) =>
        item.name.toLowerCase().includes(search.toLowerCase()) ||
        (props.showInfo &&
          ((item.description && item.description.toLowerCase().includes(search.toLowerCase())) || item.id.toString().includes(search))),
    );

    const filteredDataWithTree = filteredData.flatMap((item) => [item, ...findParentTree(listCiTypes, item.parentId)]);

    const uniqueFilteredDataWithTree = Array.from(new Set(filteredDataWithTree));
    return uniqueFilteredDataWithTree;
  }, [listCiTypes, search, props.showInfo]);

  const roots = useMemo(() => {
    return listCiTypesByFillter.filter((item) => item.parentId === null);
  }, [listCiTypesByFillter]);

  const ciTypeIdHasPermissions = getAllCiTypeIdHasPermission();

  return (
    <>
      <KanbanInput
        placeholder='Search here'
        value={search}
        onChange={(e) => {
          const value = e.target.value;
          setSearch(value);
        }}
        rightSection={<IconSearch />}
      />
      <ScrollArea flex={1} h={props.scrollHeight}>
        <Box>
          {props.showAllCIs && isCurrentUserMatchPermissions([AclPermission.viewListCiType]) && (
            <RenderCiTypesCustomLink
              isRoot={true}
              node={{
                id: 0,
                name: 'All CIs',
              }}
              isForceOpenAll={!!search}
              controlOpenedCiTypes={controlOpenedCiTypes}
              setControlOpenedCiTypes={setControlOpenedCiTypes}
              ciTypes={listCiTypesByFillter}
              selectedId={value}
              ciTypeIdHasPermissions={ciTypeIdHasPermissions}
              setSelectedId={onChangeValue}
              customActive={props.customActive}
              customHidden={props.customHidden}
              leftSection={props.leftSection}
              rightSection={props.rightSection}
              customExpand={props.customExpand}
              onChangeExpand={props.onChangeExpand}
              disabled={props.disabled}
              onMouseDown={props.onMouseDown}
              dataDisables={props.dataDisables}
            />
          )}
          {roots.map(
            (item, index) =>
              checkCiTypeHasPermission(item.id, listCiTypesByFillter, ciTypeIdHasPermissions) && (
                <RenderCiTypesCustomLink
                  key={index}
                  isRoot={true}
                  isCheckbox={props.isCheckbox}
                  onCheckBox={props.onCheckBox}
                  dataCheckboxs={props.dataCheckboxs}
                  ciTypeIdHasPermissions={ciTypeIdHasPermissions}
                  node={item}
                  isForceOpenAll={!!search}
                  controlOpenedCiTypes={controlOpenedCiTypes}
                  setControlOpenedCiTypes={setControlOpenedCiTypes}
                  ciTypes={listCiTypesByFillter}
                  selectedId={value}
                  setSelectedId={onChangeValue}
                  customActive={props.customActive}
                  customHidden={props.customHidden}
                  leftSection={props.leftSection}
                  rightSection={props.rightSection}
                  customExpand={props.customExpand}
                  onChangeExpand={props.onChangeExpand}
                  showInfo={props.showInfo}
                  disabled={props.disabled}
                  onMouseDown={props.onMouseDown}
                  dataDisables={props.dataDisables}
                />
              ),
          )}
        </Box>
      </ScrollArea>
    </>
  );
});
CiTypePickerComponent.displayName = 'CiTypePickerComponent';
CiTypePickerComponent.whyDidYouRender = true;

export const SelectCiTypeComponent = ({ allowDeselect, hasPermission = true, ...props }: SelectCiTypeComponentProps) => {
  const [openedSelect, { close: closeSelect, open: openSelect }] = useDisclosure(false);

  const [value, setValue] = useState<number | undefined>(props.value);
  const [newValue, setNewValue] = useState<number | undefined>();
  const [previousNameView, setPreviousNameView] = useState<string>('');
  useEffect(() => {
    setValue(props.value);
  }, [props.value]);

  const onChangeValue = (value: number | undefined) => {
    setNewValue(value);
  };

  const listCiTypes = useGetCiTypes().data;

  const onConfirmSelect = () => {
    setValue(newValue);
    if (props.onChange) {
      props.onChange(newValue);
    }

    closeSelect();
  };

  const onOpenSelect = () => {
    setNewValue(value);
    openSelect();
  };

  const onRemoveSelect = () => {
    const newValue = undefined;
    setValue(newValue);
    setNewValue(newValue);
    if (props.onChange) {
      props.onChange(newValue);
    }
  };
  const nameView = useMemo(() => {
    if (hasPermission) {
      const newName = listCiTypes.find((x) => x.id === value)?.name || '';
      setPreviousNameView(newName);
      return newName;
    } else {
      return previousNameView;
    }
  }, [listCiTypes, hasPermission, value, previousNameView]);
  return (
    <>
      <KanbanConfirmModal
        title='Select CI Type'
        onConfirm={onConfirmSelect}
        onClose={closeSelect}
        opened={openedSelect}
        disabledConfirmButton={value === newValue}>
        <CiTypePickerComponent value={newValue} onChange={onChangeValue} />
      </KanbanConfirmModal>
      <KanbanComponentWithLabel {...props}>
        <Flex align={'center'}>
          <KanbanButton size='xs' variant='outline' onClick={onOpenSelect} disabled={props.disabled}>
            Select Value
          </KanbanButton>
          <Space w={'md'} />
          {nameView && (
            <Pill
              size='sm'
              fw={'bold'}
              withRemoveButton={allowDeselect}
              onRemove={() => {
                onRemoveSelect();
              }}>
              {nameView}
            </Pill>
          )}
        </Flex>
      </KanbanComponentWithLabel>
    </>
  );
};
export default SelectCiTypeComponent;
