export type SetFieldValueFn<K> = <Field extends keyof K, Value extends K[Field]>(field: Field, value: Value) => void;

export function transformEnum<T extends Record<string, string | number>>(enumObj: T) {
  return Object.fromEntries(Object.entries(enumObj).map(([key, value]) => [key, { key, value }])) as { [K in keyof T]: { key: K; value: T[K] } };
}

export function groupByToMap<T, Q>(array: T[], predicate: (value: T, index: number, array: T[]) => Q): Map<Q, T[]> {
  return array.reduce((map, value, index, array) => {
    const key = predicate(value, index, array);
    map.get(key)?.push(value) ?? map.set(key, [value]);
    return map;
  }, new Map<Q, T[]>());
}

export function escapeRegex(str: string): string {
  return str.replace(/[-\\/\\^$*+?.()|[\]{}]/g, '\\$&');
}

export function formatSmartNumber(value: number | null | undefined): string {
  if (!value || !Number.isFinite(value)) {
    return '';
  }

  const rounded = Math.round((value + Number.EPSILON) * 10) / 10;
  return Number.isInteger(rounded) ? `${rounded}` : rounded.toFixed(1);
}
