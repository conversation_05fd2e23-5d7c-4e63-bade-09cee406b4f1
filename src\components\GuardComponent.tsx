import type { AclPermission } from 'models/AclPermission';
import { ProtectedComponent, ProtectedRouteProps } from '@core/auth/hocs/ProtectedComponent';
import React from 'react';
import { useSelector } from 'react-redux';
import FobiddenComponent from './ForbiddenComponent';
import { aclPermissionGetIdentical } from '@common/utils/AclPermissionUtils';
import { getCurrentUser } from '@slices/CurrentUserSlice';

export type GuardComponentType = Omit<ProtectedRouteProps, 'errorElement' | 'requirePermissions' | 'userPermissions' | 'isSuperAdmin'> & {
  requirePermissions: AclPermission[];
};

export const GuardComponent = (props: GuardComponentType) => {
  const currentUser = useSelector(getCurrentUser);
  const isSuperAdmin = currentUser.data?.isSuperAdmin === true;
  return (
    <>
      <ProtectedComponent
        errorElement={<FobiddenComponent />}
        requirePermissions={props.requirePermissions.map((x) => aclPermissionGetIdentical(x))}
        userPermissions={(currentUser.data?.aclPermissions || []).map((x) => aclPermissionGetIdentical(x))}
        hiddenOnUnSatisfy={props.hiddenOnUnSatisfy}
        isSuperAdmin={isSuperAdmin}
        allMatchPermissions={props.allMatchPermissions}>
        {props.children}
      </ProtectedComponent>
    </>
  );
};

export default GuardComponent;
