import React, { useEffect, useState } from 'react';
import { CloseButton, Combobox, Group, Input, InputBase, ScrollArea, useCombobox, ThemeIcon, CheckIcon } from '@mantine/core';
import { IconCaretRightFilled, IconCaretDownFilled } from '@tabler/icons-react';
import { KanbanComponentWithLabel, type KanbanComponentWithLabelProps } from 'kanban-design-system';
import stylesCss from './ConfigItemType.module.scss';

const findParentObjects = (parentId: string | undefined, items: TreeSelectItemDTO[], result: TreeSelectItemDTO[] = []) => {
  if (!parentId) {
    return result;
  }

  const parentObject = items.find((item) => item.id === parentId);
  if (parentObject) {
    result.push(parentObject);
    findParentObjects(parentObject.parentId, items, result);
  }

  return result;
};

const filterByValue = (items: TreeSelectItemDTO[], searchText: string): TreeSelectItemDTO[] => {
  if (searchText) {
    const filterItems = items.filter(
      (item) => item.name.toLowerCase().includes(searchText.toLowerCase()) || item.value.toLowerCase().includes(searchText.toLowerCase()),
    );

    // Lấy danh sách đối tượng cha của các phần tử được lọc
    const result: TreeSelectItemDTO[] = filterItems;
    filterItems.forEach((item) => {
      const parent = findParentObjects(item.parentId, items);
      result.push(...parent);
    });

    const uniqueResult = result.reduce((obj: TreeSelectItemDTO[], cur) => {
      if (!obj.find((item) => item.id === cur.id)) {
        obj.push(cur);
      }
      return obj;
    }, []);

    return uniqueResult;
  }
  return items;
};

export type TreeSelectProps = {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  depth: number;
  showChild: boolean;
  expandAll: boolean;
  disabled?: boolean;
} & KanbanComponentWithLabelProps;

export type TreeSelectItemDTO = {
  name: string;
  value: string;
  id: string;
  parentId?: string;
};

export type TreeSelectViewProps = TreeSelectProps & {
  items: TreeSelectItemDTO[];
};

export type TreeSelectItemProps = TreeSelectViewProps & {
  item: TreeSelectItemDTO;
};

const TreeSelectItem = (props: TreeSelectItemProps) => {
  const { depth, expandAll, item, items, showChild, value } = props;
  const [showChildren, setShowChildren] = useState(true);
  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
  });

  const childItems = items.filter((ob) => ob.parentId === item.id);

  useEffect(() => {
    setShowChildren(expandAll);
  }, [expandAll]);

  const displayValue = showChild ? 'block' : 'none';
  return (
    <>
      <div>
        <div
          style={{
            marginLeft: `${depth + 15}px`,
            display: depth > 0 ? displayValue : 'block',
            marginTop: '10px',
            //width: `calc(100% - ${depth + 15}px)`,
          }}>
          <div className={stylesCss['select-item']}>
            <span
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: 10,
                justifyContent: 'space-between',
              }}>
              <span
                style={{
                  padding: '.3rem',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 10,
                  justifyContent: 'space-between',
                  width: '100%',
                }}>
                {childItems && childItems.length > 0 ? (
                  <div
                    onClick={() => {
                      setShowChildren(!showChildren);
                    }}>
                    {!showChildren ? (
                      <>
                        <IconCaretRightFilled
                          size={'1rem'}
                          onClick={() => {
                            setShowChildren(!showChildren);
                          }}
                        />
                      </>
                    ) : (
                      <>
                        <IconCaretDownFilled
                          size={'1rem'}
                          onClick={() => {
                            setShowChildren(!showChildren);
                          }}
                        />
                      </>
                    )}
                  </div>
                ) : (
                  <></>
                )}

                <Combobox.Option value={item.value} key={item.value} style={{ width: '100%' }}>
                  <Group gap='xs'>
                    {item.name}
                    {item.value === value && (
                      <ThemeIcon color='gray' variant='transparent'>
                        {' '}
                        <CheckIcon size={12} />
                      </ThemeIcon>
                    )}
                  </Group>
                </Combobox.Option>
              </span>
            </span>
          </div>

          {childItems.length > 0 ? (
            <>
              {childItems.map((obj: TreeSelectItemDTO, idx: number) => (
                <div key={idx}>
                  <TreeSelectItem item={obj} items={items} depth={depth + 10} showChild={showChildren} expandAll={expandAll} value={value} />
                </div>
              ))}
            </>
          ) : (
            <></>
          )}
        </div>
      </div>
    </>
  );
};

export const TreeSelectItemView = (props: TreeSelectViewProps) => {
  const { depth, expandAll, items, showChild, value } = props;
  const childItems = items.filter((ob) => ob.parentId === undefined);
  return (
    <>
      {childItems.length > 0 ? (
        <>
          {childItems.map((obj: TreeSelectItemDTO, idx: number) => (
            <div key={idx}>
              <TreeSelectItem item={obj} items={items} depth={depth} showChild={showChild} expandAll={expandAll} value={value} />
            </div>
          ))}
        </>
      ) : (
        <></>
      )}
    </>
  );
};

export const TreeSelectCustom = (props: TreeSelectViewProps) => {
  const { depth, disabled, expandAll, items, onChange, showChild, value } = props;
  const [search, setSearch] = useState('');
  const [valueView, setValueView] = useState('');
  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
  });

  useEffect(() => {
    const objectSelected = items.find((ob) => ob.value === value);
    if (objectSelected) {
      setValueView(objectSelected.name);
    }
  }, [value, items]);

  const filteredOptions = filterByValue(items, search);

  return (
    <KanbanComponentWithLabel {...props}>
      <Combobox
        store={combobox}
        withinPortal={true}
        zIndex={999}
        disabled={disabled}
        onOptionSubmit={(val) => {
          if (onChange) {
            onChange(val);
          }
          setValueView(val);
          const objectSelected = items.find((ob) => ob.value === value);
          if (objectSelected) {
            setValueView(objectSelected.name);
          }
          combobox.closeDropdown();
        }}>
        <Combobox.Target>
          <InputBase
            component='button'
            type='button'
            disabled={disabled}
            pointer
            rightSection={
              !value || disabled ? (
                <Combobox.Chevron />
              ) : (
                <CloseButton
                  size='sm'
                  onMouseDown={(event) => event.preventDefault()}
                  onClick={() => {
                    if (onChange) {
                      onChange('');
                    }
                    setValueView('');
                  }}
                  aria-label='Clear value'
                />
              )
            }
            onClick={() => combobox.toggleDropdown()}>
            {valueView || value || <Input.Placeholder>{props.placeholder || ''}</Input.Placeholder>}
          </InputBase>
        </Combobox.Target>

        <Combobox.Dropdown>
          <Combobox.Search value={search} onChange={(event) => setSearch(event.currentTarget.value)} placeholder='Search data' />
          <Combobox.Options>
            <ScrollArea.Autosize mah={200} type='scroll'>
              {filteredOptions.length > 0 ? (
                <>
                  <TreeSelectItemView items={filteredOptions} depth={depth} showChild={showChild} expandAll={expandAll} value={value} />
                </>
              ) : (
                <Combobox.Empty>Nothing found</Combobox.Empty>
              )}
            </ScrollArea.Autosize>
          </Combobox.Options>
        </Combobox.Dropdown>
      </Combobox>
    </KanbanComponentWithLabel>
  );
};
export default TreeSelectCustom;
