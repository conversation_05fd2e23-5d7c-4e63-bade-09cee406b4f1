.scrollable-container {
	& > div {
		&:first-child {
      display: list-item !important;
			// max-width: 200px;
			max-height: 800px;
			overflow-y: scroll;
			overflow-x: scroll;
			border: 1px solid #ccc;
			padding: 5px;
			&::-webkit-scrollbar {
				width: 5px;
			}
			&::-webkit-scrollbar-thumb {
				background-color: #888;
				border-radius: 10px;
				&:hover {
					background-color: #555;
				}
			}
			&::-webkit-scrollbar-track {
				background-color: #f1f1f1;
			}
      & > button {
        width: 100% !important;
      }
		}
	}
}
.content {
	p {
		margin: 10px 0;
	}
}
