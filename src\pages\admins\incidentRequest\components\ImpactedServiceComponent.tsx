import { Box, Flex } from '@mantine/core';
import React from 'react';
import { ImpactedChangeProps, ImpactedTypeTable } from '@models/ChangeAssessment';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { IconPlus, IconSettingsAutomation } from '@tabler/icons-react';
import { ImpactedDataTableComponent } from './ImpactedDataTableComponent';

export type ImpactedServiceComponentProps = ImpactedChangeProps & {
  onProcessAdd: boolean;
  onAddCi: (val: boolean) => void;
  onDeleteCi: (val: number[]) => void;
  calculateImpactedServices: () => void;
  allowEdit: boolean;
};

export const ImpactedServiceComponent: React.FC<ImpactedServiceComponentProps> = ({
  allowEdit,
  calculateImpactedServices,
  cis,
  onAddCi,
  onDeleteCi,
  onProcessAdd,
}) => {
  return (
    <Box>
      <Box mt='sm'>
        <HeaderTitleComponent
          title={'List Impacted Service/Cis'}
          rightSection={
            <>
              {allowEdit && (
                <Flex gap='xs'>
                  <KanbanButton onClick={calculateImpactedServices} variant={'light'} leftSection={<IconSettingsAutomation />}>
                    Calculate Impacted Services
                  </KanbanButton>
                  <KanbanButton
                    leftSection={<IconPlus />}
                    disabled={onProcessAdd}
                    onClick={() => {
                      onAddCi(true);
                    }}>
                    Add Impacted Cis/Service
                  </KanbanButton>
                </Flex>
              )}
            </>
          }
        />
      </Box>
      <ImpactedDataTableComponent typeTable={ImpactedTypeTable.SERVICE} cis={cis} onDelete={onDeleteCi} allowEdit={allowEdit} />
    </Box>
  );
};
