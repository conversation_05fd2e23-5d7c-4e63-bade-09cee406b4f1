import { EntityAction } from './AuditLog';
import { Attribute } from './CiDetailCompare';
import type { EntityModelBase } from './EntityModelBase';

export enum CiIdentifierRuleAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  VIEW = 'VIEW',
}

export enum RuleStatus {
  ENABLE = 'ENABLE',
  DISABLE = 'DISABLE',
}

export enum RULE_TAB {
  INFO = 'INFO',
  HISTORY = 'HISTORY',
}

export type CiIdentifierRule = EntityModelBase & {
  name: string;
  active: RuleStatus;
  ciTypeId: number;
  description?: string;
  skipDuplicate: boolean;
  applyToChild: boolean;
  ciTypeName?: string;
};

export type CiIdentifierEntry = EntityModelBase & {
  name: string;
  active: RuleStatus;
  ciIdentifierRuleId: number;
  priority?: number;
  filterAttributeId?: number;
  filterOperator?: string;
  filterValue?: string;
  defaultEntry: boolean;
};

export type CiIdentifierEntryAttribute = EntityModelBase & {
  ciIdentifierEntryId: number;
  ciTypeAttributeId: number;
};

export type CiIdentifierEntryAttributeDto = CiIdentifierEntryAttribute & {
  name?: string;
};

export type CiIdentifierEntryDto = CiIdentifierEntry & {
  attributes?: CiIdentifierEntryAttributeDto[];
  tempId: string;
  onEdit?: boolean;
};

export type CiIdentifierRuleDto = CiIdentifierRule & {
  listEntries?: CiIdentifierEntryDto[];
  listEntriesCreate?: CiIdentifierEntryDto[];
  listEntriesUpdate?: CiIdentifierEntryDto[];
  listEntriesDelete?: CiIdentifierEntryDto[];
};

export type CiIdentifierLogDto = {
  id: number;
  logType: string;
  logLevel: string;
  ciName: string;
  ciId: number;
  createdDate: string;
  message: string;
};

export type CustomAttributeCompareModel = {
  oldInfo: { [key: string]: string };
  newInfo: { [key: string]: string };
  mapChildren: { [key: string]: CustomAttributeCompareModel };
  mapSelfChange: Attribute;
  action: EntityAction;
  titleAction: string;
};

export type DataCiRuleCompare = {
  ciIdentifierRuleEntry?: CustomAttributeCompareModel;
  ciIdentifierRule?: CustomAttributeCompareModel;
};

export type DataChangeWithAction = {
  key: string;
  oldValue?: string;
  newValue?: string | number;
  action?: EntityAction;
};
export type DataChangeLstWithActionObj = {
  dataChange?: DataChangeWithAction[];
  action?: EntityAction;
  titleAction?: string;
  oldInfo?: { [key: string]: string };
  newInfo?: { [key: string]: string };
};
