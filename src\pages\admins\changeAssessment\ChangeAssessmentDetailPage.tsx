import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { ChangeComponent } from './components/ChangeComponent';
import { ViewAdvancedSearchDataChange } from './components/ViewAdvancedSearchDataChange';
import { KanbanAccordion, KanbanAccordionData, KanbanModal, KanbanTabs, KanbanTabsType, KanbanText, KanbanTooltip } from 'kanban-design-system';
import { AffectedCiComponent } from './components/ChangeAffectedCiComponent';
import { ImpactedServiceComponent } from './components/ImpactedServiceComponent';
import { Box, Flex } from '@mantine/core';
import type { ConfigItemResponse } from '@api/ConfigItemApi';
import {
  ChangeAssessmentAction,
  ChangeAssessmentDraftStatus,
  ChangeAssessmentRequestDto,
  CiImpactedByRelationshipInformationResponseDto,
  ImpactedCiParameters,
  ImpactedTypeTable,
} from '@models/ChangeAssessment';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton, KanbanIconButton, KanbanTitle, KanbanConfirmModal } from 'kanban-design-system';
import { buildChangeAssessmentDetailUrl, changeAssessmentPath, navigateTo } from '@common/utils/RouterUtils';
import { ChangeAssessmentApi } from '@api/ChangeAssessmentApi';
import { NotificationError, NotificationSuccess } from '@common/utils/NotificationUtils';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { IconCopy, IconEdit, IconTrash, IconLockOpen, IconLock, IconUser } from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import styled from 'styled-components';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';
import AssignOwnerComponent from './components/AssignOwnerComponent';
import { ChangeSdpApi } from '@api/ChangeSdpApi';
import {
  checkChangeStagesWhenDelete,
  checkIsChangeCorThenAuthor,
  createChangeAssessmentRequest,
  keyImpactedCiAndMergedCisInChangeAndAttType,
  keyPairImpactedCiAndAttType,
} from '@common/utils/ChangeAssessmentUtils';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { useSelector } from 'react-redux';
import { ImpactedServiceActEnum, ServiceAttachedTypeEnum } from '@common/constants/CiDetail';
import { ImpactedCiComponent } from './components/impactedCi/ImpactedCiComponent';
import { ImpactedCiHistoryApi, type CiImpactedHistoryDto } from '@api/ImpactedCiHistoryApi';
import { FlaggedImpactedCiAttachedType } from '@common/constants/CiManagement';
import { ISO_DATE_FORMAT, dateToString } from '@common/utils/DateUtils';
import { StringMapEntry } from './components/impactedCi/ImpactedCiFlagTableComponent';
import { getConfigs } from '@core/configs/Configs';
import { BreadcrumbComponent, UrlBaseCrumbData } from '../breadcrumb/BreadcrumbComponent';

import { formatStandardName } from '@common/utils/StringUtils';

const ScrollableDiv = styled.div`
  overflow-y: auto;
  flex: 1;
`;
const WrapperContentDiv = styled.div`
  display: flex;
  flex-direction: column;
  max-height: var(--kanban-appshell-maxheight-content);
`;
const getActiveTab = (value: string | null): ImpactedTypeTable => {
  // Use Object.entries to get key-value pairs for (
  for (const [key, enumValue] of Object.entries(ImpactedTypeTable)) {
    if (enumValue === value) {
      return ImpactedTypeTable[key as keyof typeof ImpactedTypeTable];
    }
  }
  return ImpactedTypeTable.CI;
};
const titleView = (idNumber: number, action: string | null) => {
  if (idNumber > 0) {
    switch (action) {
      case ChangeAssessmentAction.UPDATE:
        return 'Update Change Assessment Detail';
      case ChangeAssessmentAction.COPY:
        return 'Copy Change Assessment Detail';
      case ChangeAssessmentAction.VIEW:
        return 'View Change Assessment Detail';
      default:
        return 'Change Assessment Detail';
    }
  } else {
    return 'Create Change Assessment';
  }
};
//1615: key for change assessment impacted service( id and attached type: MANUAL/CALCULATED)
const getImpactedServiceKey = (id: number, attachedType?: string) => {
  return `${id}-${attachedType}`;
};
//1632: handle check when {press SAVE(in update CA screen)/ Press EDIT in view screen}
const checkIfAllowUpdateChangeAssessment = (isAllowChangeCis: boolean): boolean => {
  if (!isAllowChangeCis) {
    NotificationError({
      title: `Error`,
      message: 'Change assessment has been locked.',
    });
    return false;
  }
  return true;
};

const convertToFlagCiHistoryRequest = (
  item: CiImpactedByRelationshipInformationResponseDto,
  changeAssessmentId: number,
  isDelete: boolean,
): CiImpactedHistoryDto => {
  return {
    id: item.ciImpactedHistoryId,
    ciRelationshipId: item.relationshipId,
    requestMappingId: changeAssessmentId,
    ciImpactedId: item.ciImpactedId,
    ciChangeId: item.rootCiId,
    relationshipLevel: item.relationshipLevel,
    deleted: isDelete,
    flowNodes: item.flowNodes,
    attachedType: item.attachedType || FlaggedImpactedCiAttachedType.CALCULATED,
    //3130 case calculated ,still send ciChangeIds =  [item.ciChangeId]
    ciChangeIds: item.ciChangePlans ? item.ciChangePlans.map((it) => it.id) : [],
    userComment: item.userComment,
    impactedAssessmentLevel: item.impactedAssessmentLevel,
  };
};
export const ChangeAssessmentDetailPage = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const actionParam = searchParams.get('action');
  const action = Object.values(ChangeAssessmentAction).includes(actionParam as ChangeAssessmentAction)
    ? (actionParam as ChangeAssessmentAction)
    : ChangeAssessmentAction.VIEW;
  const idNumber = Number(id);
  const isUpdateData = ChangeAssessmentAction.UPDATE === action;

  //1615: Initial info of impacted services, for listCiImpactNew, listCiImpactRemove
  const [listServiceImpactInit, setListServiceImpactInit] = useState<ConfigItemResponse[]>([]);

  const [listFlagCi, setListFlagCi] = useState<CiImpactedByRelationshipInformationResponseDto[]>([]);
  const [listFlagCiInit, setListFlagCiInit] = useState<CiImpactedByRelationshipInformationResponseDto[]>([]);

  const [listCiImpact, setListCiImpact] = useState<ConfigItemResponse[]>([]);
  const [listServiceImpact, setListServiceImpact] = useState<ConfigItemResponse[]>([]);

  const [listCiImpactNew, setListCiImpactNew] = useState<number[]>([]);
  const [listCiImpactRemove, setListCiImpactRemove] = useState<number[]>([]);
  //1615: Maintain: 2 state of impacted service: save plus attachedType info
  const [listServiceImpactNew, setListServiceImpactNew] = useState<ConfigItemResponse[]>([]);
  const [listServiceImpactRemove, setListServiceImpactRemove] = useState<ConfigItemResponse[]>([]);
  //2494: listNonRelactedImpactedCi for tab Impacted cis
  const [listManualImpactedCiNew, setListManualImpactedCiNew] = useState<number[]>([]);
  const [onProcessAddDataImpact, setOnProcessAddDataImpact] = useState(false);

  const [activeTab, setActiveTab] = useState<ImpactedTypeTable>(ImpactedTypeTable.CI);
  const [changeId, setChangeId] = useState<number | undefined>();
  const [allowEdit, setAllowEdit] = useState(true);
  const [openedModalDelete, { close: closeModalDelete, open: openModalDelete }] = useDisclosure(false);
  const [allowViewImpactData, setAllowViewImpactData] = useState(true);
  const [draftStatus, setDraftStatus] = useState<ChangeAssessmentDraftStatus>(ChangeAssessmentDraftStatus.LOCK);
  const [draftId, setDraftId] = useState('');
  const [cloneDraftId, setCloneDraftId] = useState<string>();
  const [author, setAuthor] = useState('');
  const [isChangeCorThenAuthor, setIsChangeCorThenAuthor] = useState(false);
  const [isOpenAssignOwnerModal, setIsOpenAssignOwnerModal] = useState(false);
  const [initLinkedChangeId, setInitLinkedChangeId] = useState<number | undefined>();
  const currentUser = useSelector(getCurrentUser);
  const isSuperAdmin = currentUser.data?.isSuperAdmin === true;
  const isLinkedChangeIdAndLocked = initLinkedChangeId && ChangeAssessmentDraftStatus.LOCK === draftStatus;

  const [isChangeCorForImpactedCi, setIsChangeCorForImpactedCi] = useState(false);

  const showImpactedServiceFeature = getConfigs().features.impactedService;

  //1632: flag that indicated user able to update CA, fix bug COPY not show add impacted CI
  const isAllowChangeCis =
    ChangeAssessmentDraftStatus.UNLOCK === draftStatus ||
    isChangeCorThenAuthor ||
    (isSuperAdmin && !isLinkedChangeIdAndLocked) ||
    ChangeAssessmentAction.COPY === action ||
    ChangeAssessmentAction.CREATE === action;
  const currentUsername = useSelector(getCurrentUser).data?.username;

  useEffect(() => {
    setAllowEdit(ChangeAssessmentAction.VIEW !== action);
  }, [action]);
  //051224 logic check if currentuser is change cor to allow update impactlevel / comment
  const checkIfChangeCorForImpactedCi = useCallback(() => {
    if (ChangeAssessmentAction.COPY === action || ChangeAssessmentAction.CREATE === action) {
      setIsChangeCorForImpactedCi(true);
    } else if (ChangeAssessmentAction.UPDATE === action) {
      if (initLinkedChangeId) {
        ChangeSdpApi.getById(Number(initLinkedChangeId), ChangeAssessmentAction.VIEW, idNumber, false)
          .then((res) => {
            const resData = res.data;
            setIsChangeCorForImpactedCi(checkIsChangeCorThenAuthor(resData.changeCor, currentUsername, author || ''));
          })
          .catch(() => {});
      } else {
        setIsChangeCorForImpactedCi(checkIsChangeCorThenAuthor('', currentUsername, author || ''));
      }
    }
  }, [action, author, currentUsername, idNumber, initLinkedChangeId]);
  useEffect(() => {
    checkIfChangeCorForImpactedCi();
  }, [checkIfChangeCorForImpactedCi]);
  //use when COPY from a change assessment/UPDATE
  const fetchChangeDetail = useCallback(() => {
    if (idNumber > 0) {
      ChangeAssessmentApi.getById(idNumber)
        .then((res) => {
          const resData = res.data;
          if (resData) {
            if (ChangeAssessmentAction.COPY !== action) {
              setChangeId(resData.changeId);
              //1632: state for indicate current IsChangeCor or Author
              if (!resData.changeId) {
                setIsChangeCorThenAuthor(checkIsChangeCorThenAuthor('', currentUsername, resData.author));
              }
              setInitLinkedChangeId(resData.changeId);
              setDraftStatus(resData.changeDraftStatus);
              setDraftId(resData.changeDraftId);
              setAuthor(resData.author);
              //1615: save info of init ServiceImpacts
              setListServiceImpactInit(resData.impactedServices);
            } else {
              //for COPY screen
              //1632 : fix bug when COPY : since start use new flow (ListCiImpactNew ,ListServiceImpactNew indicate that list to save new ci /service) : bug dont save new ci/service
              setListCiImpactNew(resData.affectedCis.map((item) => item.id));
              setListServiceImpactNew(resData.impactedServices);
              //2494: copy manual added impacted ci
              setListManualImpactedCiNew(resData.ciImpactedFlags.map((item) => item.id));
              //1632: fix when COPY: init = []
              setListServiceImpactInit([]);
            }
            setCloneDraftId(resData.changeDraftId);

            ImpactedCiHistoryApi.findCiImpactedHistoryLatestByChangeId(idNumber)
              .then((res) => {
                if (res.data) {
                  const resData = res.data.filter((item) => !item.deleted);
                  if (ChangeAssessmentAction.COPY !== action) {
                    setListFlagCi(resData);
                    setListFlagCiInit(resData);
                  } else {
                    setListFlagCi(
                      resData.map((item) => {
                        return { ...item, id: 0 };
                      }),
                    );
                    setListFlagCiInit([]);
                  }
                }
              })
              .catch(() => {});

            setListCiImpact(resData.affectedCis);
            setListServiceImpact(resData.impactedServices);
          }
        })
        .catch(() => {});
    }
  }, [idNumber, action, currentUsername]);

  useEffect(() => {
    fetchChangeDetail();
  }, [fetchChangeDetail]);

  useEffect(() => {
    if (activeTab) {
      setOnProcessAddDataImpact(false);
    }
  }, [activeTab]);

  //1632: effect when press COPY from VIEW screen:  reset state.
  useEffect(() => {
    if (ChangeAssessmentAction.COPY === action) {
      setAuthor('');
      setDraftId((prev) => {
        setCloneDraftId(prev);
        return '';
      });
    }
  }, [action]);

  /**
   * @listInputData : list service from ( Add services button OR Calculated services button)
   * @action: ADD : user press Add services button, CALCULATION: user press Calculated services button
   */
  const addDataImpact = useCallback(
    (listInputDataRaw: ConfigItemResponse[], action: ImpactedServiceActEnum | null) => {
      let listInput = [...listInputDataRaw];
      if (ImpactedTypeTable.CI === activeTab) {
        const listOldData = [...listCiImpact];
        const listOldIds = listOldData.map((item) => item.id);

        const newItems = listInput.filter((item) => !listOldIds.includes(item.id));

        // add list new CI
        const newIds = new Set(newItems.map((obj) => obj.id));
        setListCiImpactNew((prev) => [...prev, ...newIds]);
        // Update list CI Impact
        setListCiImpact([...listOldData, ...newItems]);
      }

      if (ImpactedTypeTable.SERVICE === activeTab) {
        //listCurrent: hold data for current screen service impacted
        let listCurrent = [...listServiceImpact];
        const listCurrentIds = listCurrent.map((item) => item.id);

        //1615: when ADD input data = MANUAL, when CALCULATION input data = CALCULATED
        listInput = listInput.map((item) => ({
          ...item,
          attachedType: ImpactedServiceActEnum.ADD === action ? ServiceAttachedTypeEnum.MANUAL : ServiceAttachedTypeEnum.CALCULATED,
        }));

        const listAdd = listInput.filter((item) => !listCurrentIds.includes(item.id));
        if (ImpactedServiceActEnum.CALCULATION === action) {
          //ListServiceImpact : state for current list that show in screen: Add items has key diff with ListServiceImpact, Remove items do not exist in listInput
          const listCurrentCalculate = listServiceImpact.filter((item) => ServiceAttachedTypeEnum.CALCULATED === item.attachedType);
          const listInputIds = listInput.map((item) => item.id);
          const listRemoveOnScreenIds = listCurrentCalculate.filter((item) => !listInputIds.includes(item.id)).map((item) => item.id);
          listCurrent = listCurrent.filter((item) => !listRemoveOnScreenIds.includes(item.id));
          listCurrent = [...listCurrent, ...listAdd];
          setListServiceImpact([...listCurrent]);

          const listCurrentIdAttachTypeKey = listCurrent.map((item) => getImpactedServiceKey(item.id, item.attachedType));

          //ListServiceImpactRemove: All items exist in listServiceImpactInit but not exist in listCurrent
          const listRemoveOnDb = listServiceImpactInit.filter(
            (item) => !listCurrentIdAttachTypeKey.includes(getImpactedServiceKey(item.id, item.attachedType)),
          );
          setListServiceImpactRemove([...listRemoveOnDb]);

          //ListServiceImpactNew:    All items exist in listCurrent but not exist in listServiceImpactInit
          const listIdAttachTypeInitKey = listServiceImpactInit.map((item) => getImpactedServiceKey(item.id, item.attachedType));
          const listAddIntoDb = listCurrent.filter((item) => !listIdAttachTypeInitKey.includes(getImpactedServiceKey(item.id, item.attachedType)));
          setListServiceImpactNew([...listAddIntoDb]);
        } else {
          setListServiceImpactNew((prev) => [...prev, ...listAdd]);
          setListServiceImpact([...listCurrent, ...listAdd]);
        }
      }
      //2494: logic add ci from advanced affected ci to table Not related
      //3130: add to list flagged cis
      if (ImpactedTypeTable.CI_IMPACTED_FLAG === activeTab) {
        const listOldData: CiImpactedByRelationshipInformationResponseDto[] = [...listFlagCi];
        const listOldIds = listOldData.map((item) => item.id);
        const newItems = listInput
          .filter((item) => !listOldIds.includes(item.id))
          .map((it) => {
            return {
              ciImpactedId: it.id,
              ciTypeImpactedId: it.ciTypeId,
              ciImpactedName: it.name,
              createdByCiImpacted: it.createdBy,
              createdDateCiImpacted: dateToString(it.createdDate, ISO_DATE_FORMAT),
              attachedType: FlaggedImpactedCiAttachedType.MANUAL,
              relationshipId: 0,
              ciImpactedHistoryId: 0,
              ciChangeId: 0,
              ciTypeChangeId: 0,
              relationshipLevel: 0,
              rootCiId: 0,
              markRelated: false,
              rootCiTypeId: 0,
              deleted: false,
            } as CiImpactedByRelationshipInformationResponseDto;
          });
        setListFlagCi([...newItems, ...listOldData]);
      }

      //close modal after press add to change
      setOnProcessAddDataImpact(false);
    },
    [activeTab, listCiImpact, listServiceImpact, listServiceImpactInit, listFlagCi],
  );

  //1615: handle remove affected cis
  const removeCis = useCallback(
    (listData: ConfigItemResponse[]) => {
      const listIds = listData.map((item) => item.id);
      const listOldData = [...listCiImpact];
      const updatedList = listOldData.filter((item) => !listIds.includes(item.id));

      //1615:Case remove item that just add (not yet save) , remove that item from ListCiImpactNew
      const setRemoveIds = new Set(listData.map((item) => item.id));
      setListCiImpactNew((prev) => prev.filter((item) => !setRemoveIds.has(item)));
      // add list remove CI
      setListCiImpactRemove((prev) => [...prev, ...listIds]);
      // Update list CI Impact
      setListCiImpact(updatedList);
    },
    [listCiImpact],
  );
  //2494: logic  remove ci from non related cis
  // const removeManualImpactedCi = useCallback(
  //   (listData: ConfigItemResponse[]) => {
  //     const listIds = listData.map((item) => item.id);
  //     const listOldData = [...listManualImpactedCi];
  //     const updatedList = listOldData.filter((item) => !listIds.includes(item.id));

  //     //1615:Case remove item that just add (not yet save) , remove that item from ListCiImpactNew
  //     const setRemoveIds = new Set(listData.map((item) => item.id));
  //     setListManualImpactedCiNew((prev) => prev.filter((item) => !setRemoveIds.has(item)));
  //     // add list remove CI
  //     setListManualImpactedCiRemove((prev) => [...prev, ...listIds]);
  //     // Update list CI Impact
  //     setListManualImpactedCi(updatedList);
  //   },
  //   [listManualImpactedCi],
  // );

  const removeServices = useCallback(
    (listData: ConfigItemResponse[]) => {
      const listOldData = [...listServiceImpact];
      //1615: Case remove item that just add (not yet save) , remove that item from ListServiceImpactNew , check with key = id+attachedType in case 2 item same id but diff by attached type
      const idAttachedTypeSet = new Set(listData.map((item) => getImpactedServiceKey(item.id, item.attachedType)));
      setListServiceImpactNew((prev) => prev.filter((item) => !idAttachedTypeSet.has(getImpactedServiceKey(item.id, item.attachedType))));

      //1615: ListServiceImpactRemove
      const initServiceImpactSet = new Set(listServiceImpactInit.map((item) => getImpactedServiceKey(item.id, item.attachedType)));
      const listToRemove = listData.filter((item) => initServiceImpactSet.has(getImpactedServiceKey(item.id, item.attachedType)));
      setListServiceImpactRemove((prev) => [...prev, ...listToRemove]);

      // Update list Service Impact
      const listIds = listData.map((item) => item.id);
      const listChange = listOldData.filter((item) => listIds.includes(item.id));
      setListServiceImpact(listServiceImpact.filter((item) => !listChange.includes(item)));
    },
    [listServiceImpact, listServiceImpactInit],
  );
  //3849: flagged table comment required
  const validateListFlaggedCi = (listAddImpactedCiHistory: CiImpactedHistoryDto[]) => {
    const isInValid = listAddImpactedCiHistory.some((it) => {
      const notSelectManualCiInChangePlan =
        FlaggedImpactedCiAttachedType.MANUAL === it.attachedType && (!it.ciChangeIds || it.ciChangeIds.length === 0);
      const notComment = FlaggedImpactedCiAttachedType.MANUAL === it.attachedType && !it.userComment;
      return notSelectManualCiInChangePlan || notComment;
    });

    if (isInValid) {
      NotificationError({
        message: 'Manually CIs in change plan and comment is required in flagged impacted CIs',
      });
    }
    return !isInValid;
  };
  const updateChangeRequest = () => {
    //1632: check allow update when press SAVE
    if (!checkIfAllowUpdateChangeAssessment(isAllowChangeCis)) {
      return;
    }

    if (idNumber > 0) {
      //1615: create request for save CA
      const listServiceImpactNewRes = listServiceImpactNew.map((ci) => ({
        ciId: ci.id,
        type: ImpactedTypeTable.SERVICE,
        attachedType: ServiceAttachedTypeEnum.CALCULATED === ci.attachedType ? ServiceAttachedTypeEnum.CALCULATED : ServiceAttachedTypeEnum.MANUAL,
      }));
      const listServiceImpactRemoveRes = listServiceImpactRemove.map((ci) => ({
        ciId: ci.id,
        type: ImpactedTypeTable.SERVICE,
        attachedType: ServiceAttachedTypeEnum.CALCULATED === ci.attachedType ? ServiceAttachedTypeEnum.CALCULATED : ServiceAttachedTypeEnum.MANUAL,
      }));

      //keyOfOneFlagCiRow: check update or not
      const flagCiInDbMap = listFlagCiInit.reduce((acc: StringMapEntry<CiImpactedByRelationshipInformationResponseDto>, item) => {
        acc[keyImpactedCiAndMergedCisInChangeAndAttType(item)] = item; // Use the id as the key and the item as the value
        return acc;
      }, {});
      const setImpactedCiAndAttTypeKey = new Set(listFlagCi.map((item) => keyPairImpactedCiAndAttType(item)));

      const listAddOrUpdateImpactedCiHistory: CiImpactedHistoryDto[] = listFlagCi
        .filter((item) => {
          const toUpdateCiFlag = flagCiInDbMap[keyImpactedCiAndMergedCisInChangeAndAttType(item)];

          if (!toUpdateCiFlag) {
            //case update manual , check if update cis in change plan
            return true;
          }

          const isUpdateManualChangePlans =
            FlaggedImpactedCiAttachedType.MANUAL === item.attachedType &&
            item.ciChangePlans
              ?.map((it) => it.id)
              .toSorted()
              .join(',') !==
              toUpdateCiFlag.ciChangePlans
                ?.map((it) => it.id)
                .toSorted()
                .join(',');
          if (isUpdateManualChangePlans) {
            return true;
          }
          if (toUpdateCiFlag.userComment !== item.userComment || toUpdateCiFlag.impactedAssessmentLevel !== item.impactedAssessmentLevel) {
            return true;
          }
        })
        .map((item) => {
          return convertToFlagCiHistoryRequest(item, idNumber, false);
        });
      const isValid = validateListFlaggedCi(listAddOrUpdateImpactedCiHistory);
      if (!isValid) {
        return;
      }
      const listDeleteImpactedCiHistory: CiImpactedHistoryDto[] = listFlagCiInit
        .filter((item) => !setImpactedCiAndAttTypeKey.has(keyPairImpactedCiAndAttType(item)))
        .map((item) => {
          return convertToFlagCiHistoryRequest(item, idNumber, true);
        });
      const listImpactedCiHistory: CiImpactedHistoryDto[] = [...listDeleteImpactedCiHistory, ...listAddOrUpdateImpactedCiHistory];

      const dataRequest: ChangeAssessmentRequestDto = createChangeAssessmentRequest(
        changeId,
        listCiImpactNew,
        listServiceImpactNewRes,
        listManualImpactedCiNew,
        listCiImpactRemove,
        listServiceImpactRemoveRes,
        listImpactedCiHistory,
      );

      ChangeAssessmentApi.updateChange(dataRequest, idNumber)
        .then((res) => {
          if (res.data) {
            NotificationSuccess({
              message: 'Update successfully.',
            });
            navigateTo(buildChangeAssessmentDetailUrl(idNumber, ChangeAssessmentAction.UPDATE));
          } else {
            NotificationError({
              message: 'Error when update change request.',
            });
          }
        })
        .catch(() => {});
    }
  };
  const onChangeDraftStatus = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.stopPropagation();
      const updateStatus = ChangeAssessmentDraftStatus.LOCK === draftStatus ? ChangeAssessmentDraftStatus.UNLOCK : ChangeAssessmentDraftStatus.LOCK;
      ChangeAssessmentApi.updateDraftStatus(updateStatus, idNumber)
        .then((res) => {
          if (res.data) {
            setDraftStatus(updateStatus);
            NotificationSuccess({
              message: ChangeAssessmentDraftStatus.LOCK === draftStatus ? 'Unlock draft successfully!' : 'Lock draft successfully!',
            });
          }
        })
        .catch(() => {
          NotificationError({
            message: 'Error when update change draft status.',
          });
        });
    },
    [draftStatus, idNumber],
  );

  const addNewChangeRequest = () => {
    //1615:convert ConfigItemResponse to ChangeAffectCiDTO to send request
    const listServiceImpactNewRes = listServiceImpactNew.map((ci) => ({
      ciId: ci.id,
      type: ImpactedTypeTable.SERVICE,
      attachedType: ServiceAttachedTypeEnum.CALCULATED === ci.attachedType ? ServiceAttachedTypeEnum.CALCULATED : ServiceAttachedTypeEnum.MANUAL,
    }));
    const listServiceImpactRemoveRes = listServiceImpactRemove.map((ci) => ({
      ciId: ci.id,
      type: ImpactedTypeTable.SERVICE,
      attachedType: ServiceAttachedTypeEnum.CALCULATED === ci.attachedType ? ServiceAttachedTypeEnum.CALCULATED : ServiceAttachedTypeEnum.MANUAL,
    }));
    const flagCiInDbKey = new Set(listFlagCiInit.map((item) => keyImpactedCiAndMergedCisInChangeAndAttType(item)));
    const listAddImpactedCiHistory = listFlagCi
      .filter((item) => !flagCiInDbKey.has(keyImpactedCiAndMergedCisInChangeAndAttType(item)))
      .map((item) => {
        return convertToFlagCiHistoryRequest(item, 0, false);
      });
    const isValid = validateListFlaggedCi(listAddImpactedCiHistory);
    if (!isValid) {
      return;
    }
    const dataRequest: ChangeAssessmentRequestDto = createChangeAssessmentRequest(
      changeId,
      listCiImpactNew,
      listServiceImpactNewRes,
      listManualImpactedCiNew,
      listCiImpactRemove,
      listServiceImpactRemoveRes,
      listAddImpactedCiHistory,
    );
    ChangeAssessmentApi.createNewChange(dataRequest)
      .then((res) => {
        if (res.data) {
          NotificationSuccess({
            message: 'Created successfully.',
          });
          navigateTo(changeAssessmentPath);
        } else {
          NotificationError({
            message: 'Error when create new change request.',
          });
        }
      })
      .catch(() => {});
  };

  const calculateImpactedServices = useCallback(() => {
    const affectedCiIds: number[] = listCiImpact.map((entity) => entity.id);
    const impactedCiParameters: ImpactedCiParameters = {
      affectedCiIds: affectedCiIds,
    };
    if (affectedCiIds.length > 0) {
      ChangeAssessmentApi.calculateImpactedServices(impactedCiParameters)
        .then((res) => {
          if (res.data.length > 0) {
            addDataImpact(res.data, ImpactedServiceActEnum.CALCULATION);
          } else {
            addDataImpact([], ImpactedServiceActEnum.CALCULATION);
          }
        })
        .catch(() => {});
    } else {
      NotificationError({
        message: 'Please choose ci(s) in change plan.',
      });
    }
  }, [listCiImpact, addDataImpact]);

  const listSelectedCisAdvancedSearch: ConfigItemResponse[] = useMemo(() => {
    switch (activeTab) {
      case ImpactedTypeTable.CI:
        return listCiImpact;
      case ImpactedTypeTable.SERVICE:
        return listServiceImpact;
      case ImpactedTypeTable.CI_IMPACTED_FLAG: {
        return listFlagCi
          .filter((it) => FlaggedImpactedCiAttachedType.MANUAL === it.attachedType)
          .map((it) => ({ ciTypeId: it.ciTypeImpactedId, id: it.ciImpactedId, name: it.ciImpactedName }));
      }
      default:
        return [];
    }
  }, [activeTab, listCiImpact, listFlagCi, listServiceImpact]);

  const tabViews = useMemo(() => {
    const ciTab = {
      content: (
        <AffectedCiComponent
          cis={listCiImpact}
          onProcessAdd={onProcessAddDataImpact}
          onAddCi={setOnProcessAddDataImpact}
          onDeleteCi={removeCis}
          allowEdit={allowEdit && isAllowChangeCis}
        />
      ),
      title: 'CIS IN CHANGE PLAN',
    };

    const serviceTab = {
      content: (
        <ImpactedServiceComponent
          cis={listServiceImpact}
          onProcessAdd={onProcessAddDataImpact}
          onAddCi={setOnProcessAddDataImpact}
          onDeleteCi={removeServices}
          calculateImpactedServices={calculateImpactedServices}
          allowEdit={allowEdit && isAllowChangeCis}
        />
      ),
      title: 'IMPACTED SERVICES',
    };
    const impactedCiTab = {
      content: (
        <ImpactedCiComponent
          listCiChange={listCiImpact}
          allowEdit={allowEdit && isAllowChangeCis}
          listFlagCi={listFlagCi}
          setListFlagCi={setListFlagCi}
          onAddCi={setOnProcessAddDataImpact}
          onProcessAdd={onProcessAddDataImpact}
          isChangeCorForImpactedCi={isChangeCorForImpactedCi}
        />
      ),
      title: 'IMPACTED CIS',
    };
    if (showImpactedServiceFeature) {
      return {
        CI: ciTab,
        SERVICE: serviceTab,
        CI_IMPACTED_FLAG: impactedCiTab,
      } as KanbanTabsType;
    } else {
      return {
        CI: ciTab,
        CI_IMPACTED_FLAG: impactedCiTab,
      } as KanbanTabsType;
    }
  }, [
    allowEdit,
    calculateImpactedServices,
    isAllowChangeCis,
    isChangeCorForImpactedCi,
    listCiImpact,
    listFlagCi,
    listServiceImpact,
    onProcessAddDataImpact,
    removeCis,
    removeServices,
    showImpactedServiceFeature,
  ]);
  const accordionItems: KanbanAccordionData[] = useMemo(
    () => [
      {
        key: 'linkSdp',
        content: (
          <ChangeComponent
            changeId={changeId}
            initLinkedChangeId={initLinkedChangeId}
            setChangeId={setChangeId}
            allowEdit={allowEdit}
            setAllowEdit={setAllowEdit}
            action={action as ChangeAssessmentAction}
            setAllowViewImpactData={setAllowViewImpactData}
            author={author}
            sdpRequestChangeId={idNumber}
            setIsChangeCorThenAuthor={setIsChangeCorThenAuthor}
          />
        ),
        title: (
          <KanbanTitle order={4} c={'var(--mantine-color-blue-8)'}>
            Link to a change in SDP
          </KanbanTitle>
        ),
      },
      {
        key: 'impactData',
        content: (
          <Box mt='sm'>
            <KanbanTabs
              configs={{
                defaultValue: activeTab,
                onChange: (val) => {
                  setActiveTab(getActiveTab(val));
                  if (ImpactedTypeTable.CI_IMPACTED_FLAG === val) {
                    setOnProcessAddDataImpact(false);
                  }
                },
              }}
              tabs={tabViews}
            />
          </Box>
        ),
        title: (
          <Flex gap={10}>
            <KanbanTitle order={4} c={'var(--mantine-color-blue-8)'}>
              List CIs in change plan, impacted services and impacted CIs
            </KanbanTitle>
            {isUpdateData && (isChangeCorThenAuthor || (isSuperAdmin && !isLinkedChangeIdAndLocked)) && (
              <KanbanTooltip label={draftStatus === ChangeAssessmentDraftStatus.LOCK ? 'Unlock change assessment' : 'Lock change assessment'}>
                <KanbanIconButton
                  onClick={onChangeDraftStatus}
                  size='sm'
                  bg={ChangeAssessmentDraftStatus.LOCK === draftStatus ? 'var(--mantine-color-red-5)' : 'var(--mantine-color-green-8)'}>
                  {ChangeAssessmentDraftStatus.LOCK === draftStatus ? <IconLock /> : <IconLockOpen />}
                </KanbanIconButton>
              </KanbanTooltip>
            )}
          </Flex>
        ),
      },
      {
        key: 'addImpact',
        content: (
          // activeTab === ImpactedTypeTable.CI_IMPACTED_FLAG ? (
          <KanbanModal onClose={() => setOnProcessAddDataImpact(false)} opened={onProcessAddDataImpact} size={'70%'}>
            <ViewAdvancedSearchDataChange
              title={'LIST CIS'}
              //list cis already added
              cis={listSelectedCisAdvancedSearch}
              activeTab={activeTab}
              //1615: data from ADD services must have attachedType =MANUAL
              onAddChange={(items: ConfigItemResponse[]) => {
                let manualItems = items;
                if (ImpactedTypeTable.SERVICE === activeTab) {
                  manualItems = items.map((item) => ({ ...item, attachedType: ServiceAttachedTypeEnum.MANUAL }));
                }
                return addDataImpact(manualItems, ImpactedTypeTable.SERVICE === activeTab ? ImpactedServiceActEnum.ADD : null);
              }}
              onClose={undefined}
            />
          </KanbanModal>
        ),
        title: (
          // <KanbanTitle order={4} c={'var(--mantine-color-blue-8)'}>
          //   AFFECTED SERVICE/CIS
          // </KanbanTitle>
          <></>
        ),
      },
    ],
    [
      changeId,
      initLinkedChangeId,
      allowEdit,
      action,
      author,
      idNumber,
      activeTab,
      tabViews,
      isUpdateData,
      isChangeCorThenAuthor,
      isSuperAdmin,
      isLinkedChangeIdAndLocked,
      draftStatus,
      onChangeDraftStatus,
      onProcessAddDataImpact,
      listSelectedCisAdvancedSearch,
      addDataImpact,
    ],
  );

  const customAccordionItems = useMemo(() => {
    const accordionItemsUpdate = [...accordionItems];
    let updateItems = accordionItemsUpdate;
    if (!allowViewImpactData) {
      updateItems = updateItems.filter((x) => x.key !== 'impactData');
    }

    if (!onProcessAddDataImpact) {
      updateItems = updateItems.filter((x) => x.key !== 'addImpact');
    }

    return updateItems;
  }, [accordionItems, allowViewImpactData, onProcessAddDataImpact]);

  const onDeleteChange = () => {
    if (idNumber) {
      ChangeAssessmentApi.deleteByIds([idNumber])
        .then((res) => {
          if (res.data) {
            checkChangeStagesWhenDelete(res.data);
            navigateTo(changeAssessmentPath);
          }
        })
        .catch(() => {})
        .finally(() => {});
    }
  };
  const onCloseAssignOwnerModal = () => {
    setIsOpenAssignOwnerModal(false);
  };
  const onOpenAssignOwnerModal = () => {
    if (checkIsChangeCorThenAuthor('', currentUsername, author) || isSuperAdmin) {
      setIsOpenAssignOwnerModal(true);
    } else {
      NotificationError({
        title: `Error`,
        message: 'Current user do not have permission on this function',
      });
    }
  };

  function handleEditChangeAssessment(id: number, changeId: number | undefined) {
    if (changeId) {
      //input ChangeAssessmentAction.VIEW instead of UPDATE to get change without validate change data
      ChangeSdpApi.getById(changeId, ChangeAssessmentAction.VIEW, id, false)
        .then(() => {
          navigate(buildChangeAssessmentDetailUrl(idNumber, ChangeAssessmentAction.UPDATE));
        })
        .catch(() => {});
    } else if (checkIfAllowUpdateChangeAssessment(isAllowChangeCis)) {
      navigate(buildChangeAssessmentDetailUrl(idNumber, ChangeAssessmentAction.UPDATE));
    }
  }

  useEffect(() => {
    if (!draftStatus) {
      setDraftStatus(ChangeAssessmentDraftStatus.LOCK);
    }
  }, [draftStatus]);

  const locationCustomPaths = useMemo((): UrlBaseCrumbData => {
    const originPath = buildChangeAssessmentDetailUrl(idNumber, action);

    let detailBread = '';
    if (draftId) {
      detailBread = `${formatStandardName(action)} ${draftId}`;
    } else if (ChangeAssessmentAction.CREATE === action) {
      detailBread = `${formatStandardName(action)}`;
    } else if (ChangeAssessmentAction.COPY === action && cloneDraftId) {
      detailBread = `${formatStandardName(action)} ${cloneDraftId}`;
    }

    return {
      [`/${idNumber}`]: {
        title: detailBread,
        href: originPath,
      },
    };
  }, [action, cloneDraftId, draftId, idNumber]);
  return (
    <>
      {/* 4736 change assessment detail*/}
      <BreadcrumbComponent locationCustomPaths={locationCustomPaths} />
      <KanbanConfirmModal title='Delete Change' onConfirm={onDeleteChange} textConfirm='Delete' onClose={closeModalDelete} opened={openedModalDelete}>
        {'Are you sure to delete this request?'}
      </KanbanConfirmModal>
      <WrapperContentDiv>
        <div>
          <HeaderTitleComponent
            title={titleView(idNumber, action)}
            leftSection={
              <>
                {isUpdateData && (currentUsername === author || isSuperAdmin) && (
                  <KanbanTooltip label='Assign change assessment owner'>
                    <KanbanIconButton size='sm' onClick={onOpenAssignOwnerModal}>
                      <IconUser />
                    </KanbanIconButton>
                  </KanbanTooltip>
                )}
                {isOpenAssignOwnerModal && <AssignOwnerComponent id={idNumber} draftId={draftId} closeModal={onCloseAssignOwnerModal} />}
                {author && `Owner: ${author}`}
              </>
            }
            rightSection={
              <Flex gap={10}>
                <KanbanButton
                  variant='outline'
                  onClick={() => {
                    navigateTo(changeAssessmentPath);
                  }}>
                  Cancel
                </KanbanButton>
                {!allowEdit && (
                  <>
                    <GuardComponent requirePermissions={[AclPermission.createChangeAssessment]} hiddenOnUnSatisfy>
                      <KanbanButton
                        leftSection={<IconCopy />}
                        color={'cyan'}
                        variant='light'
                        onClick={() => {
                          navigate(buildChangeAssessmentDetailUrl(idNumber, ChangeAssessmentAction.COPY));
                        }}>
                        Copy
                      </KanbanButton>
                    </GuardComponent>
                    <GuardComponent requirePermissions={[AclPermission.deleteChangeAssessment]} hiddenOnUnSatisfy>
                      <KanbanButton leftSection={<IconTrash />} color={'red'} onClick={openModalDelete}>
                        Delete
                      </KanbanButton>
                    </GuardComponent>
                    <GuardComponent requirePermissions={[AclPermission.updateChangeAssessment]} hiddenOnUnSatisfy>
                      <KanbanButton
                        leftSection={<IconEdit />}
                        onClick={() => {
                          handleEditChangeAssessment(idNumber, changeId);
                        }}>
                        Edit
                      </KanbanButton>
                    </GuardComponent>
                  </>
                )}
                {allowEdit && (
                  <KanbanButton
                    onClick={() => {
                      if (isUpdateData) {
                        updateChangeRequest();
                      } else {
                        addNewChangeRequest();
                      }
                    }}>
                    Save
                  </KanbanButton>
                )}
              </Flex>
            }
          />
        </div>
        {draftId && (
          <Flex gap='xl'>
            <KanbanText fw={500} w={'50%'} mb={'sm'}>
              Change Draft ID: {draftId}
            </KanbanText>
          </Flex>
        )}
        <ScrollableDiv>
          <KanbanAccordion
            variant='separated'
            chevronPosition={'left'}
            transitionDuration={200}
            defaultValue={['linkSdp', 'impactData', 'addImpact']}
            multiple
            data={customAccordionItems}
          />
        </ScrollableDiv>
      </WrapperContentDiv>
    </>
  );
};
export default ChangeAssessmentDetailPage;
