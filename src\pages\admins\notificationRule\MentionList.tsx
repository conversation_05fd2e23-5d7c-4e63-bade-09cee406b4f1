import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import styleScss from './MentionList.module.scss';
import type { MentionListHandle, MentionListProps } from '@models/NotificationTemplate';

const MentionList = forwardRef<MentionListHandle, MentionListProps>((props, ref) => {
  const [selectedIndex, setSelectedIndex] = useState(0);

  const selectItem = (index: number) => {
    const item = props.items[index];

    if (item) {
      props.command({ id: item });
    }
  };

  const upHandler = () => {
    setSelectedIndex((selectedIndex + props.items.length - 1) % props.items.length);
  };

  const downHandler = () => {
    setSelectedIndex((selectedIndex + 1) % props.items.length);
  };

  const enterHandler = () => {
    selectItem(selectedIndex);
  };

  useEffect(() => {
    setSelectedIndex(0);
  }, [props.items]);

  useImperativeHandle(ref, () => ({
    onKeyDown: ({ event }) => {
      if (event.key === 'ArrowUp') {
        upHandler();
        return true;
      }

      if (event.key === 'ArrowDown') {
        downHandler();
        return true;
      }

      if (event.key === 'Enter') {
        enterHandler();
        return true;
      }

      return false;
    },
  }));

  return (
    <div className={styleScss['dropdown-menu']}>
      {props.items.length ? (
        props.items.map((item, index) => (
          <button className={index === selectedIndex ? styleScss['is-selected'] : ''} key={index} onClick={() => selectItem(index)}>
            {item}
          </button>
        ))
      ) : (
        <div>No result</div>
      )}
    </div>
  );
});
MentionList.displayName = 'MentionList';
export default MentionList;
