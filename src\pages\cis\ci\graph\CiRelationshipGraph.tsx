import { ConfigItemApi } from '@api/ConfigItemApi';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import type { ConfigItemTypeAttrResponse } from '@api/ConfigItemTypeAttrApi';
import { GoJs } from '@common/libs';
import { configItemTypeAttrSorted } from '@common/utils/CiUtils';
import {
  appendNode,
  GraphData,
  GRAPH_VIEW_TYPE,
  hideNodeRelationships,
  LinkDataTemplate,
  makeDiagram,
  PalleteTemplate,
  settingGraphViewType,
  fetchDiagram,
  hiddenNodeTransaction,
  appendNodeTransaction,
  drawHightLightFromNode,
  scrollAndZoomToItem,
  GRAPH_VIEW_LINK_STYPES,
  changeDiagramLinkStyle,
  generateTemplateAndLinks,
  AppendNodeProps,
  DiagramConfig,
} from '@common/utils/GoJsHelper';
import { NotificationError } from '@common/utils/NotificationUtils';
import { KanbanModal } from 'kanban-design-system';
import { KanbanTabs } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import type { CIBusinessViews, RelationShipOfBusinessView } from '@models/CIBusinessViews';
import type { CiRelationshipCountModel, CiRelationshipInfoModel, CiRelationshipInfoWithImpact } from '@models/CiRelationship';
import type { ConfigItemModel } from '@models/ConfigItem';
import type { ConfigItemAttrModel } from '@models/ConfigItemAttr';
import type { ConfigItemAttrCustomModel } from '@models/ConfigItemAttrCustom';
import { ciRelationshipTypesSlice, getCiRelationshipTypes } from '@slices/CiRelationshipTypesSlice';
import { getCiTypes } from '@slices/CiTypesSlice';

import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import stylesCss from './CiRelationshipGraph.module.scss';

import { AddRelationshipPopup, type AddRelationshipPopupMethods } from '../relationship/AddRelationshipPopup';
import CiHistory from '../CiHistory';
import CiInfo from '../CiInfo';
import type { ObjectData } from '@common/libs/gojs/go';
import CiRequest from '../CiRequest';
import type { ShowActionDiagramConfigProps } from '../relationship/CiRelationship';
import { FullScreen, useFullScreenHandle } from 'react-full-screen';
import styled from 'styled-components';
import GraphToolbarActionView from './graphActions/GraphToolbarActionView';
import { getNodeLevels } from './graphActions/FilterCiRelationship';
import MenuGraph, { MenuGraphPosition } from './graphActions/MenuGraph';
import RightGraphButton from './graphActions/RightGraphButton';
import { FlaggedImpactedCiAttachedType } from '@common/constants/CiManagement';
import { getSystemParamByKey } from '@slices/SystemParameterSlice';
import { Quadtree } from '@common/libs/gojs/Quadtree';
import {
  computeDocumentBounds,
  onModelChanged,
  onViewportChanged,
  virtualUniqueKey,
} from '@common/utils/GoJsVirtualizationDiagramWithNoLayoutHelper';
import { SystemConfigParamKey } from '@common/constants/SystemConfigParam';
const go = GoJs;
export type CiRelationshipGraphProps = {
  ciId: number;
  ciTypeId: number;
  ciRelationships?: CiRelationshipInfoModel[];
  toolBarConfig?: ShowActionDiagramConfigProps;
  dataViewGraph?: CIBusinessViews;
  currentLevel: string;
  showLayout: boolean;
  activeSubTab?: string;
  isView?: boolean;
  onToggleFullScreenModel?: (newValue: boolean) => boolean | void;
  onSetDiagramVirtualized?: (useDiagramVirtualized: boolean) => void;
};

export type CiRelationshipGraphAdvanceProps = CiRelationshipGraphProps & {
  // ciRelationshipsState: CiRelationshipInfoModel[];
  // setCiRelationshipsState: React.Dispatch<React.SetStateAction<CiRelationshipInfoModel[]>>;
  toolbarActionState: ToolbarActionProps;
  setToolbarActionState: React.Dispatch<React.SetStateAction<ToolbarActionProps>>;
  useDiagramVirtualized?: boolean;
};

export type CiRelationshipGraphMethods = {
  getGoDiagram: () => GoJs.Diagram | undefined;
  updateGoDiagram: (template: PalleteTemplate[], links: LinkDataTemplate[]) => void;
  handleOpenModalCreateNewRelationship: (isRoot?: boolean) => void;
};

export type FilterModel = {
  oldDatasCiNameRef: ObjectData[];
  datasCiNameRef: ObjectData[];
  oldLinksCiNameRef: LinkDataTemplate[];
};

export const GrapViewWrapper = styled.div`
  position: relative;
`;

export type ToolbarActionProps = {
  level: string;
  filter: FilterModel;
  graphViewStyle: GRAPH_VIEW_TYPE;
  graphViewLinkStyle: GRAPH_VIEW_LINK_STYPES;
  isImpactTo?: boolean;
};

export type HighlightNodeModel = {
  nodeId?: number;
  nodeName?: string;
  upStream?: boolean;
};
export const filterInit: FilterModel = {
  datasCiNameRef: [],
  oldDatasCiNameRef: [],
  oldLinksCiNameRef: [],
};

export const CiRelationshipGraph = forwardRef(function CiRelationshipGraph(props: CiRelationshipGraphAdvanceProps, ref) {
  // const [goDiagram, setGoDiagram] = useState<GoJs.Diagram | undefined>(undefined);
  const goDiagramRef = useRef<go.Diagram | undefined>(undefined);
  const rootJumpRef = useRef<boolean>(false);
  const ciRelationshipTypes = useSelector(getCiRelationshipTypes);
  const [ciSelected, setCiSelected] = useState<number>(props.ciId);
  const [nodeHovered, setNodeHovered] = useState<go.Node>();
  const [ciTypeSelected, setCiTypeSelected] = useState<number>(props.ciTypeId);
  const [contextMenuPosition, setContextMenuPosition] = useState<MenuGraphPosition | undefined>(undefined);
  // const [ciRelationships, setCiRelationships] = useState<CiRelationshipInfoModel[]>([]);
  const ciRelationships = useMemo(() => props.ciRelationships ?? [], [props.ciRelationships]);
  const [ciCount, setCiCount] = useState<CiRelationshipCountModel[]>([]);
  const [openedModalCiInfo, { close: closeModalCiInfo, open: openModalCiInfo }] = useDisclosure(false);
  const [listCiTypeAttribute, setListCiTypeAttribute] = useState<ConfigItemTypeAttrResponse[]>([]);
  const [ciAttributes, setCiAttributes] = useState<ConfigItemAttrModel[]>([]);
  const [ciAttributesCustom, setCiAttributesCustom] = useState<ConfigItemAttrCustomModel[]>([]);
  const isVirtualized = props?.useDiagramVirtualized;
  const goOverviewRef = useRef<go.Overview>();
  const maxNode = useSelector(getSystemParamByKey(SystemConfigParamKey.GRAPH_NODE_MAX))?.value;
  const maxLink = useSelector(getSystemParamByKey(SystemConfigParamKey.GRAPH_RELATIONSHIP_MAX))?.value;
  const { dataViewGraph, onSetDiagramVirtualized } = props;

  const [ciInfo, setCiInfo] = useState<ConfigItemModel>({
    id: 0,
    ciTypeId: 0,
    name: '',
  });

  const [ciInfoSelect, setCiInfoSelect] = useState<ConfigItemModel>({
    id: 0,
    ciTypeId: 0,
    name: '',
  });

  const toolbarAction = props.toolbarActionState;
  const setToolbarAction = props.setToolbarActionState;

  const ciTypes = useSelector(getCiTypes);
  const createRelationshipPopupRef = useRef<AddRelationshipPopupMethods | null>(null);
  const loadBusinessViewInitRef = useRef(false);
  const previewRef = useRef(null);
  const [isShowPreview, setIsShowPreview] = useState(false);
  const [attachedType, setAttachedType] = useState<FlaggedImpactedCiAttachedType | undefined>(undefined);

  const [levelFilter, setLevelFilter] = useState(0);

  const [nodeHightLight, setNodeHightLight] = useState<HighlightNodeModel>({});
  const dispatch = useDispatch();

  // all variable for view diagram virtualization with no layout
  const myWholeModelRef = useRef<go.GraphLinksModel>(new go.GraphLinksModel());
  const myWholeQuadtreeRef = useRef<Quadtree<any>>(new Quadtree<any>(1, Infinity, new go.Rect()));
  const networkRef = useRef<HTMLDivElement | null>(null);
  const myWholeModel = myWholeModelRef.current;
  const myWholeQuadtree = myWholeQuadtreeRef.current;
  //
  const toolBarConfig: ShowActionDiagramConfigProps = useMemo(() => {
    if (props.toolBarConfig) {
      return props.toolBarConfig;
    }
    if (props.isView) {
      // set default config when only view
      return {
        toolBar: {
          isShowLocate: true,
          isShowUpStreamAndDownStream: true,
          isShowChangeStyle: true,
          isShowChangeLinkStyle: true,
          isShowImpactToAndImpactBy: false,
        },
        menu: {
          isShowStreamImpact: true,
          isShowCiDetails: true,
        },
      };
    }

    return {
      toolBar: {
        isShowLocate: true,
        isShowFilter: true,
        isShowSave: true,
        isShowDownload: true,
        isShowUpStreamAndDownStream: true,
        isShowChangeStyle: true,
        isShowChangeLinkStyle: true,
        isShowImpactToAndImpactBy: false,
      },
      menu: {
        isShowStreamImpact: true,
        isShowHideCi: true,
        isShowCiDetails: true,
        isShowAddRelationship: true,
      },
    };
  }, [props.toolBarConfig, props.isView]);

  const isMenuSetting = useMemo(() => {
    if (!toolBarConfig.menu) {
      return false;
    }
    //devsec-978534 toolBarConfig?.menu
    return !Object.values(toolBarConfig.menu).every((value) => value === false);
  }, [toolBarConfig.menu]);

  const isToolBarSetting = useMemo(() => {
    if (!toolBarConfig.toolBar) {
      return false;
    }
    return !Object.values(toolBarConfig.toolBar).every((value) => value === false);
  }, [toolBarConfig.toolBar]);

  const handleFullScreen = useFullScreenHandle();

  // useEffect(() => {
  //   if (CiDetailSubTabType.IMPACTMAP === props.activeSubTab) {
  //     CiRelationshipApi.getImpactMap(props.ciId, toolbarAction.isImpactTo ?? true, Number(props.currentLevel))
  //       .then((res) => {
  //         setCiRelationships(res.data || []);
  //       })
  //       .catch(() => {});
  //   }
  // }, [props.ciId, props.currentLevel, props.activeSubTab, toolbarAction.isImpactTo]);
  /**
   * Get ciChangePlan, impactedCi
   */

  useEffect(() => {
    if (ciRelationships.length) {
      const ciRelationshipDatas: CiRelationshipInfoWithImpact[] = ciRelationships;
      const [firstCiRelationship] = ciRelationshipDatas;

      setAttachedType(firstCiRelationship.attachedType);
    }
  }, [ciRelationships]);

  /**
   * Logic data view model ci info
   */
  const fetchCiTypesAttribute = useCallback(() => {
    if (ciTypeSelected === 0) {
      return;
    }
    ConfigItemTypeApi.getAllAttributes(ciTypeSelected)
      .then((res) => {
        setListCiTypeAttribute(configItemTypeAttrSorted(res.data));
      })
      .catch(() => {});
  }, [ciTypeSelected]);

  /**
   * When click from business view then setting dataAction saved
   */
  useEffect(() => {
    const action = props.dataViewGraph?.dataActionParse;
    if (action) {
      const newAction: ToolbarActionProps = {
        filter: action.filter || filterInit,
        level: action.level || '1',
        graphViewStyle: action.graphViewStyle || GRAPH_VIEW_TYPE.TOP_DOWN,
        graphViewLinkStyle: action.graphViewLinkStyle || GRAPH_VIEW_LINK_STYPES.ORTHOGONAL_ROUTING,
      };

      setToolbarAction(newAction);
    } else {
      setToolbarAction((prev) => ({ ...prev, filter: filterInit }));
    }
  }, [props.dataViewGraph, setToolbarAction]);

  useEffect(() => {
    setToolbarAction((prev) => {
      return {
        ...prev,
        level: props.currentLevel,
      };
    });
  }, [props.currentLevel, setToolbarAction]);

  useEffect(() => {
    fetchCiTypesAttribute();
  }, [fetchCiTypesAttribute]);

  const ciAttributeMapping = useMemo(() => {
    const result: Record<number, ConfigItemAttrModel> = {};

    for (const ciTypeAttribute of listCiTypeAttribute) {
      result[ciTypeAttribute.id] = ciAttributes.find((x) => x.ciTypeAttributeId === ciTypeAttribute.id) || {
        ciTypeAttributeId: ciTypeAttribute.id,
        id: 0,
        ciId: 0,
        value: '',
      };
    }

    return result;
  }, [ciAttributes, listCiTypeAttribute]);

  const getRelationshipTypeById = useCallback(
    (id: number) => {
      return ciRelationshipTypes.data.find((x) => x.id === id);
    },
    [ciRelationshipTypes],
  );

  /**
   * Get all child node of ciId
   * @param ciId
   */
  const getChildrenRelationship = useCallback(
    (ciId: number, node: GoJs.Node) => {
      const goDiagram = goDiagramRef.current;
      ConfigItemApi.getAllRelationshipByCiId(ciId, true)
        .then((res) => {
          const appendNodeProps: AppendNodeProps = {
            goDiagram,
            datas: res.data,
            ciTypes: ciTypes.data,
            getRelationshipTypeById,
            ciAddNewRelationship: undefined,
            isAddNew: false,
            isUseTransaction: true,
            isVirtualized,
          };

          appendNode(appendNodeProps).then(() => {
            //Fix bug about position node
            goDiagram?.centerRect(node.actualBounds);
          });
        })
        .catch(() => {});
    },
    [ciTypes.data, getRelationshipTypeById, isVirtualized],
  );

  /**
   * get all parrent of node with ciId
   * @param ciId
   */
  const getParentRelationship = useCallback(
    (ciId: number, node: GoJs.Node) => {
      const goDiagram = goDiagramRef.current;
      ConfigItemApi.getAllRelationshipByCiId(ciId, false)
        .then((res) => {
          const appendNodeProps: AppendNodeProps = {
            goDiagram,
            datas: res.data,
            ciTypes: ciTypes.data,
            getRelationshipTypeById,
            ciAddNewRelationship: undefined,
            isAddNew: false,
            isUseTransaction: true,
            isVirtualized,
          };
          appendNode(appendNodeProps).then(() => {
            //Fix bug about position node
            goDiagram?.centerRect(node.actualBounds);
          });
        })
        .catch(() => {});
    },
    [ciTypes.data, getRelationshipTypeById, isVirtualized],
  );

  const getGoDiagram = useCallback(() => {
    const goDiagram = goDiagramRef.current;
    return goDiagram;
  }, []);

  const updateGoDiagram = useCallback((template: PalleteTemplate[], links: LinkDataTemplate[]) => {
    const goDiagram = goDiagramRef.current;
    if (goDiagram) {
      goDiagram.model = new go.GraphLinksModel(template, links);
    }
  }, []);

  const handleOpenModalCreateNewRelationship = useCallback(
    (isRoot?: boolean) => {
      if (isRoot) {
        createRelationshipPopupRef.current?.openModalCreateNewWithData(props.ciId, props.ciTypeId);
      } else {
        createRelationshipPopupRef.current?.openModalCreateNewWithData(nodeHovered?.data.data.ciId, nodeHovered?.data.data.ciTypeId);
      }
    },
    [props.ciId, props.ciTypeId, nodeHovered],
  );

  const methods: CiRelationshipGraphMethods = useMemo(
    () => ({
      getGoDiagram,
      updateGoDiagram,
      handleOpenModalCreateNewRelationship,
    }),
    [getGoDiagram, updateGoDiagram, handleOpenModalCreateNewRelationship],
  );

  useImperativeHandle<any, CiRelationshipGraphMethods>(ref, () => methods, [methods]);
  // useEffect(() => {
  //   setCiRelationships([...(props.ciRelationships || [])]);
  // }, [props.ciRelationships]);

  useEffect(() => {
    // get all node ids in graview and count all child + parent nodes from server
    const ciIds = ciRelationships.flatMap((item) => [item.fromCi, item.toCi]);
    if (ciIds.length) {
      ConfigItemApi.ciCountRelationships(ciIds)
        .then((res) => {
          setCiCount(res.data || []);
        })
        .catch(() => {});
    }
  }, [ciRelationships]);

  useEffect(() => {
    dispatch(ciRelationshipTypesSlice.actions.fetchForEmpty());
  }, [dispatch]);

  const handleOpenModalCiInfo = useCallback(
    (ciId: number) => {
      ConfigItemApi.getInfoById(ciId)
        .then((res) => {
          if (res.data && res.data.ci) {
            setCiInfoSelect(res.data.ci);
            setCiAttributes(res.data.attributes);
            setCiAttributesCustom(res.data.attributeCustoms);
            openModalCiInfo();
          } else {
            NotificationError({
              message: 'CI Not Found',
            });
          }
        })
        .catch(() => {});
    },
    [openModalCiInfo],
  );

  /**
   * method handle when node of diagram removed or append
   * @returns
   */
  const updateLevelFilter = useCallback(() => {
    const goDiagram = goDiagramRef.current;
    if (!goDiagram) {
      return;
    }
    const nodeLevels = getNodeLevels(goDiagram);
    if (!nodeLevels.length || nodeLevels.length < 2) {
      setLevelFilter(0);
      return;
    }

    let maxLevel = nodeLevels[0].minLevel;

    for (let i = 0; i < nodeLevels.length; i++) {
      if (nodeLevels[i].minLevel > maxLevel) {
        maxLevel = nodeLevels[i].minLevel;
      }
    }

    setLevelFilter(maxLevel);
    drawHightLightFromNode(goDiagram, nodeHightLight.nodeId, nodeHightLight.upStream);
    // fix center node root
    const nodeRoot = goDiagram.findNodesByExample({ isRoot: true }).first();

    if (nodeRoot && nodeRoot.data.key && !isVirtualized) {
      scrollAndZoomToItem(goDiagram, nodeRoot.data.key);
    }
  }, [isVirtualized, nodeHightLight.nodeId, nodeHightLight.upStream]);

  /**
   * Show/hide relationship
   */
  const showHideRelationship = useCallback(
    (e: go.DiagramEvent) => {
      const goDiagram = goDiagramRef.current;
      if (!goDiagram) {
        return;
      }
      const itemNodes = goDiagram.nodes.iterator;
      while (itemNodes.next()) {
        const itemNode = itemNodes.value;
        if (!itemNode.visible) {
          goDiagram.remove(itemNode);
        }
      }
      const part = e.subject.part;
      if (part instanceof go.Adornment) {
        const buttonShowChild = e.subject.findObject('showChildNode');
        const buttonShowParent = e.subject.findObject('showParentNode');
        const buttonShowCiInfo = e.subject.findObject('showInfoNode');
        goDiagram.model.startTransaction('update adornment visibility');
        const fromCi = part.data.data.ciId;
        setCiSelected(fromCi);
        setCiTypeSelected(part.data.data.ciTypeId);
        const node = goDiagram.findNodeForKey(part.data.key);
        if (!node) {
          return;
        }
        // Xử lý show/hide CHILD
        if (buttonShowChild !== null) {
          const actualOut = node.findLinksOutOf().count;
          const expectedOut = node.data.totalFrom;

          if (actualOut < expectedOut) {
            // Chưa hiện đủ => hiện thêm
            getChildrenRelationship(fromCi, node);
            goDiagram.model.setDataProperty(node.data, 'showChildLine', true); // ẩn nút sau khi show
          } else {
            // Đã hiện đủ => ẩn đi
            hideNodeRelationships(goDiagram, node, true);
            goDiagram.model.setDataProperty(node.data, 'showChildLine', false); // hiện lại nút
          }
        }

        // Xử lý show/hide PARENT
        if (buttonShowParent !== null) {
          const actualIn = node.findLinksInto().count;
          const expectedIn = node.data.totalTo;

          if (actualIn < expectedIn) {
            getParentRelationship(fromCi, node);
            goDiagram.model.setDataProperty(node.data, 'showParentLine', true);
          } else {
            hideNodeRelationships(goDiagram, node, false);
            goDiagram.model.setDataProperty(node.data, 'showParentLine', false);
          }
        }

        if (buttonShowCiInfo !== null) {
          handleOpenModalCiInfo(fromCi);
        }
        goDiagram.model.commitTransaction('update adornment visibility');
        node.updateAdornments();
      }
    },
    [handleOpenModalCiInfo, getChildrenRelationship, getParentRelationship],
  );

  useEffect(() => {
    if (!networkRef.current) {
      return;
    }
    // Gỡ Diagram cũ nếu tồn tại
    if (goDiagramRef.current) {
      goDiagramRef.current.div = null;
      goDiagramRef.current.clear();
      goDiagramRef.current = undefined;
    }

    const diagramConfig: DiagramConfig = {
      dom: networkRef.current,
      showLayout: props.showLayout,
      isEdit: false,
      isShowSelectionAdornment: true,
      isShowTotalChild: true,
      showOnlyInfo: !(props.toolBarConfig?.toolBar?.isShowUpStreamAndDownStream ?? true),
      isVirtualized: isVirtualized,
    };

    const myDiagram: GoJs.Diagram = makeDiagram(diagramConfig);

    if (previewRef.current) {
      if (!goOverviewRef.current) {
        goOverviewRef.current = new go.Overview(previewRef.current);
      }

      goOverviewRef.current.observed = myDiagram;
    }
    myDiagram.isVirtualized = true;
    goDiagramRef.current = myDiagram;
  }, [isVirtualized, props.showLayout, props.toolBarConfig?.toolBar?.isShowUpStreamAndDownStream]);
  const handleBackgroundSingleClicked = useCallback(() => {
    setContextMenuPosition(undefined);
  }, []);

  const handleaddModelChangedListener = useCallback(
    (evt: GoJs.ChangedEvent) => {
      if (evt.isTransactionFinished) {
        const transactionName = evt.object && evt.object.name;
        if ([appendNodeTransaction, hiddenNodeTransaction].includes(transactionName)) {
          updateLevelFilter();
          //reset save fillter
          // setToolbarAction((prev) => ({ ...prev, filter: filterInit })); // error render state
        }
      }
    },
    [updateLevelFilter],
  );

  const handleObjectContextClicked = useCallback((e: go.DiagramEvent) => {
    const part = e.subject.part;
    if (part instanceof go.Node) {
      setNodeHovered(part);
      const mousePt = e.diagram.lastInput.viewPoint;
      setContextMenuPosition({ x: mousePt.x, y: mousePt.y });
      setCiInfoSelect({
        id: part.data.data.ciId,
        ciTypeId: part.data.data.ciTypeId,
        name: part.data.label,
      });
    }
  }, []);

  const handleLayoutComplete = useCallback(
    (e: go.DiagramEvent) => {
      const goDiagram = e.diagram;
      const nodeRoot = goDiagram.findNodesByExample({ isRoot: true }).first();
      const jumpToNoded = rootJumpRef.current;
      if (nodeRoot && nodeRoot.data.key && !jumpToNoded) {
        scrollAndZoomToItem(goDiagram, nodeRoot.data.key);
      }
      updateLevelFilter();
      rootJumpRef.current = true;
    },
    [updateLevelFilter],
  );

  useEffect(() => {
    const goDiagram = goDiagramRef.current;
    if (!goDiagram) {
      return;
    }
    goDiagram.addDiagramListener('BackgroundSingleClicked', handleBackgroundSingleClicked);

    //add show menu
    goDiagram.addDiagramListener('ObjectContextClicked', handleObjectContextClicked);
    goDiagram.addDiagramListener('ObjectSingleClicked', showHideRelationship);

    goDiagram.addModelChangedListener(handleaddModelChangedListener); // ảnh hưởng đến virtualized
    goDiagram.addDiagramListener('LayoutCompleted', handleLayoutComplete); // ảnh hưởng đến virtualized

    if (isVirtualized) {
      // add event when add virtualization
      goDiagram.addDiagramListener('ViewportBoundsChanged', (e: go.DiagramEvent) => {
        onViewportChanged(e, myWholeQuadtree, myWholeModel);
      });
      goDiagram.addModelChangedListener((e: go.ChangedEvent) => {
        onModelChanged(e, myWholeQuadtree, myWholeModel);
      });
      goDiagram.model.makeUniqueKeyFunction = (model: go.Model, data: ObjectData) => {
        return virtualUniqueKey(model, myWholeModel, data);
      };
    }
    return () => {
      goDiagram.removeDiagramListener('BackgroundSingleClicked', handleBackgroundSingleClicked);
      goDiagram.removeDiagramListener('LayoutCompleted', handleLayoutComplete);
      goDiagram.removeDiagramListener('ObjectContextClicked', handleObjectContextClicked);
      goDiagram.removeDiagramListener('ObjectSingleClicked', showHideRelationship);
      goDiagram.removeModelChangedListener(handleaddModelChangedListener);
      // remove event when add virtualization
      goDiagram.removeDiagramListener('ViewportBoundsChanged', (e: go.DiagramEvent) => {
        onViewportChanged(e, myWholeQuadtree, myWholeModel);
      });
      goDiagram.removeModelChangedListener((e: go.ChangedEvent) => {
        onModelChanged(e, myWholeQuadtree, myWholeModel);
      });
    };
  }, [
    showHideRelationship,
    handleBackgroundSingleClicked,
    handleaddModelChangedListener,
    updateLevelFilter,
    handleObjectContextClicked,
    handleLayoutComplete,
    myWholeQuadtree,
    myWholeModel,
    isVirtualized,
  ]);

  useEffect(() => {
    ConfigItemApi.getById(Number(props.ciId))
      .then((res) => {
        if (res.data) {
          setCiInfo(res.data);
        }
      })
      .catch(() => {});
  }, [props.ciId]);

  /**
   * init reder data from business view
   */
  useEffect(() => {
    const goDiagram = goDiagramRef.current;
    if (!goDiagram) {
      return;
    }
    if (props.dataViewGraph?.dataActionParse) {
      const diagramStyle = props.dataViewGraph?.dataActionParse?.graphViewStyle || GRAPH_VIEW_TYPE.TOP_DOWN;
      const diagramLinkStyle = props.dataViewGraph?.dataActionParse?.graphViewLinkStyle || GRAPH_VIEW_LINK_STYPES.ORTHOGONAL_ROUTING;
      settingGraphViewType(goDiagram, props.ciId, diagramStyle);
      changeDiagramLinkStyle(goDiagram, diagramLinkStyle);
    }

    if (props.dataViewGraph?.graphView) {
      goDiagram.layout.isInitial = false;
      if (!loadBusinessViewInitRef.current) {
        goDiagram.model = go.Model.fromJson(props.dataViewGraph.graphView);
        fetchDiagram(goDiagram);
        loadBusinessViewInitRef.current = true;
        goDiagram.layout.isInitial = true;
      }
    }
  }, [props.ciId, props.dataViewGraph?.dataActionParse, props.dataViewGraph?.graphView]);

  /**
   * init load diagram from server
   */
  useEffect(() => {
    const goDiagram = goDiagramRef.current;
    //when exist business view then not render diagram from data link
    if (!goDiagram || dataViewGraph?.graphView) {
      return;
    }
    const graphData: GraphData = generateTemplateAndLinks(ciRelationships, ciInfo, ciTypes.data, getRelationshipTypeById, ciCount);
    const template: PalleteTemplate[] = graphData.template;
    const links: LinkDataTemplate[] = graphData.links;

    rootJumpRef.current = false; // reset flag jump to node root

    const useDiagramVirtualized = template.length > Number(maxNode) && links.length > Number(maxLink);
    if (onSetDiagramVirtualized) {
      if (useDiagramVirtualized) {
        onSetDiagramVirtualized(true);
        myWholeQuadtree.clear(); // Rất quan trọng: Xóa Quadtree cũ trước khi thêm lại các node với bounds mới
        const numNodes = template.length;
        const sqrt = Math.floor(Math.sqrt(numNodes));
        for (let i = 0; i < numNodes; i++) {
          const node = template[i];
          node.bounds = new go.Rect((i % sqrt) * 180, Math.floor(i / sqrt) * 180, 80, 100);
          myWholeQuadtree.add(node, node.bounds); // Thêm node (đã có bounds) vào Quadtree
        }

        myWholeModel.nodeDataArray = template;
        myWholeModel.linkDataArray = links;
        const diagramNodeKeys = new Set<string>();
        goDiagram.nodes.each((node) => {
          if (node.data?.key) {
            diagramNodeKeys.add(node.data.key);
          }
        });

        // ---- Lấy danh sách node từ template theo key hiện có ----
        const visibleNodes = template.filter((node) => diagramNodeKeys.has(node.key));
        const visibleLinks = links.filter((link) => diagramNodeKeys.has(link.from) && diagramNodeKeys.has(link.to));

        const model = goDiagram.model as go.GraphLinksModel;

        model.nodeDataArray = visibleNodes;
        model.linkDataArray = visibleLinks;
        goDiagram.fixedBounds = computeDocumentBounds(myWholeModel);
      } else {
        onSetDiagramVirtualized(false);
        goDiagram.layout.isInitial = true;
        goDiagram.model = new go.GraphLinksModel(template, links);
      }
      const nodeRoot = goDiagram.findNodesByExample({ isRoot: true }).first();
      if (nodeRoot && nodeRoot.data.key && !useDiagramVirtualized) {
        scrollAndZoomToItem(goDiagram, nodeRoot.data.key);
      }
    }

    // goDiagram.layout.isInitial = true;
    // goDiagram.model = new go.GraphLinksModel(template, links);
  }, [
    ciCount,
    ciInfo,
    ciRelationships,
    ciTypes.data,
    getRelationshipTypeById,
    maxLink,
    maxNode,
    myWholeModel,
    myWholeQuadtree,
    onSetDiagramVirtualized,

    dataViewGraph?.graphView,
  ]);

  /**
   *  view new relationship after add relationship
   */
  const updateDataGraphView = (relationShips: RelationShipOfBusinessView[]) => {
    const goDiagram = goDiagramRef.current;
    ConfigItemApi.getDetailRelationships(relationShips)
      .then((res) => {
        if (res.data) {
          const appendNodeProps: AppendNodeProps = {
            goDiagram,
            datas: res.data,
            ciTypes: ciTypes.data,
            getRelationshipTypeById,
            ciAddNewRelationship: ciSelected,
            isAddNew: true,
            isUseTransaction: true,
            isVirtualized,
          };
          appendNode(appendNodeProps).then(() => {
            const isDrawHightLight = nodeHightLight.nodeId && goDiagram?.findNodeForKey(nodeHightLight.nodeId);
            if (isDrawHightLight) {
              drawHightLightFromNode(goDiagram, nodeHightLight.nodeId, nodeHightLight.upStream);
            }
          });
        }
      })
      .catch(() => {});
  };

  return (
    <>
      <GrapViewWrapper>
        <FullScreen handle={handleFullScreen}>
          <>
            <AddRelationshipPopup
              withinPortal={!handleFullScreen.active}
              ref={createRelationshipPopupRef}
              updateDataGraphView={updateDataGraphView}
            />

            <RightGraphButton
              goDiagram={goDiagramRef.current}
              nodeHightLight={nodeHightLight}
              levelFilter={levelFilter}
              setNodeHightLight={setNodeHightLight}
              isShowPreview={isShowPreview}
              setIsShowPreview={setIsShowPreview}
              handleFullScreen={handleFullScreen}
              attachedType={attachedType}
            />
            <div className={`${stylesCss['preview']}`}>
              <div className={`${stylesCss['preview-content']} ${!isShowPreview && stylesCss['preview-hidden']}`} ref={previewRef}></div>
            </div>
            {/* toollbar */}
            {isToolBarSetting && (
              <GraphToolbarActionView
                dataViewGraph={props.dataViewGraph}
                currentLevel={props.currentLevel}
                ciId={props.ciId}
                goDiagram={goDiagramRef.current}
                toolBarConfig={toolBarConfig}
                toolbarAction={toolbarAction}
                setToolbarAction={setToolbarAction}
                isDiagramVirtual={isVirtualized}
              />
            )}
          </>

          <div ref={networkRef} className={stylesCss['diagram']}></div>
          {contextMenuPosition && isMenuSetting && (
            <MenuGraph
              nodeHovered={nodeHovered}
              goDiagram={goDiagramRef.current}
              contextMenuPosition={contextMenuPosition}
              setContextMenuPosition={setContextMenuPosition}
              nodeHightLight={nodeHightLight}
              setNodeHightLight={setNodeHightLight}
              toolBarConfig={toolBarConfig}
              handleOpenModalCreateNewRelationship={handleOpenModalCreateNewRelationship}
            />
          )}

          <KanbanModal withinPortal={!handleFullScreen.active} size={'xl'} opened={openedModalCiInfo} onClose={closeModalCiInfo} title={'Ci Info'}>
            <KanbanTabs
              configs={{
                defaultValue: '1',
              }}
              tabs={{
                '1': {
                  title: 'Info',
                  content: (
                    <div>
                      <CiInfo
                        ci={ciInfoSelect}
                        ciTypeAttributes={listCiTypeAttribute}
                        ciAttributes={ciAttributeMapping}
                        ciAttributesCustom={ciAttributesCustom}
                      />
                    </div>
                  ),
                },
                '2': {
                  title: 'History',
                  content: <CiHistory ciId={ciSelected} ciTypeId={ciInfoSelect.ciTypeId} />,
                },
                '3': {
                  title: 'Request',
                  content: <CiRequest ciId={ciInfoSelect.id} ciTypeId={ciInfoSelect.ciTypeId} isViewPage={false} />,
                },
              }}
            />
          </KanbanModal>
        </FullScreen>
      </GrapViewWrapper>
    </>
  );
});

export default CiRelationshipGraph;
