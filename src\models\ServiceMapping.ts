import type { OperatorEnum } from '@common/constants/OperatorEnum';
import type { CiRelationshipInfoModel } from './CiRelationship';
import type { EntityModelBase } from './EntityModelBase';
import type { ServiceMapEnum, ServiceMapFilterTypeEnum } from '@common/constants/ServiceMapEnum';
import type { CiTypeAttributeDataType } from './CiType';

export type ServiceMapingModel = {
  id?: number;
  name: string;
  description?: string;
  startCiId?: number;
  startCiName?: string;
  startNodeId?: number;
  cronExpression?: string;
  status: number;
  graph?: ServiceMapGraphModel[];
  graphData?: string;
  rules?: ServiceMapRuleModel[];
  graphDataMapping?: ServiceMapGraphDataMapping[];
};

export type ServiceMapGraphDataMapping = {
  id?: number;
  serviceMapId?: number;
  nodeId?: number;
  dataId?: number;
  type?: ServiceMapEnum;
};

export type ServiceMapGraphModel = {
  from: number;
  to: number;
  relationshipId: number;
};
export type ServiceMapRuleModel = {
  ciTypeId?: number;
  ciTypeAttrId?: number | string;
  type?: CiTypeAttributeDataType;
  ciTypeAttrName?: string;
  nodeId?: number;
  label?: string;
  value?: string;
  operator?: OperatorEnum;
  dataQuery?: string;
  attributeType?: ServiceMapFilterTypeEnum;
};
export type ServiceMappings = EntityModelBase & {
  ciId: number;
  name: string;
  description?: string;
  startCi?: number;
  cronExpression?: string;
  status: number;
  graphData?: string;
};
export type ServiceMapCiImpactModel = EntityModelBase & {
  id: number;
  serviceMapId: number;
  ciId: number;
  ciName: string;
  ciTypeId: number;
  ciTypeName: string;
  version: number;
  description: string;
  nodeId: number;
};

export type ServiceMapCiRelationshipInfoModel = CiRelationshipInfoModel & {
  fromNodeId: number;
  toNodeId: number;
};

export type ServiceMapingDetailsModel = ServiceMapingModel & {
  ciImpactRelationships: ServiceMapCiRelationshipInfoModel[];
  ciImpacts: ServiceMapCiImpactModel[];
};
