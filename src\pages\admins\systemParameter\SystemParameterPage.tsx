import { ConfigParamApi, ConfigParamModel } from '@api/systems/ConfigParamApi';
import { SystemConfigParamKey, systemConfigParams, SystemConfigParamType } from '@common/constants/SystemConfigParam';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { Flex, Grid } from '@mantine/core';
import { systemParameterSlice, useGetSystemParams } from '@slices/SystemParameterSlice';
import { KanbanButton, KanbanInput, KanbanNumberInput } from 'kanban-design-system';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { BreadcrumbComponent, UrlBaseCrumbData } from '../breadcrumb/BreadcrumbComponent';
import { systemParameterPagePath } from '@common/utils/RouterUtils';
const isValidateParam = (datas: ConfigParamModel[]): boolean => {
  const params = systemConfigParams();
  for (const param of params) {
    const data = datas.find((item) => item.key === param.key);

    if (!data) {
      return false;
    }

    if (SystemConfigParamType.NUMBER === param.type) {
      const value = Number(data.value);
      if ((param.min !== undefined && value < param.min) || (param.max !== undefined && value > param.max)) {
        return false;
      }
    }

    if (SystemConfigParamType.TEXT === param.type) {
      const length = data.value?.length;

      if ((param.min !== undefined && length < param.min) || (param.max !== undefined && length > param.max)) {
        return false;
      }
    }
  }

  return true;
};

export const SystemParameterPage = () => {
  const allParams = useGetSystemParams();
  const dispatch = useDispatch();
  const [listData, setListData] = useState<ConfigParamModel[]>(allParams.data);
  const params = systemConfigParams();

  useEffect(() => {
    if (allParams) {
      setListData(allParams.data);
    }
  }, [allParams]);

  const getConfigValue = (key: SystemConfigParamKey) => {
    return listData.find((data) => key === data.key)?.value;
  };

  const onSave = () => {
    ConfigParamApi.saveParams(listData)
      .then((res) => {
        if (res.data) {
          dispatch(systemParameterSlice.actions.fetchData());
          NotificationSuccess({
            message: 'Update Config successfully!',
          });
        }
      })
      .catch(() => {});
  };

  const setConfigParam = (key: SystemConfigParamKey, value: string) => {
    const allParamSetting = [...listData];
    const lstParamOther = allParamSetting.filter((data) => key !== data.key);
    const paramEdit = allParamSetting.find((data) => key === data.key);
    lstParamOther.push({
      id: paramEdit ? paramEdit.id : undefined,
      key: key,
      value: value,
    });
    setListData(lstParamOther);
  };

  const locationCustomPaths = useMemo((): UrlBaseCrumbData => {
    const originPath = systemParameterPagePath;

    return {
      [originPath]: {
        title: 'System Parameters',
        href: originPath,
      },
    };
  }, []);

  return (
    <>
      {/* 4763 system param */}
      <BreadcrumbComponent locationCustomPaths={locationCustomPaths} />

      <HeaderTitleComponent title='System Parameters' />

      {params.map((param, index) => (
        <Grid columns={4} key={index}>
          <Grid.Col span={1}>
            <KanbanInput disabled value={param.key} />
          </Grid.Col>

          {SystemConfigParamType.NUMBER === param.type && (
            <Grid.Col span={3}>
              <KanbanNumberInput
                min={param.min}
                max={param.max}
                placeholder={`From ${param.min} to ${param.max}`}
                clampBehavior={'strict'}
                value={getConfigValue(param.key)}
                onChange={(value) => {
                  setConfigParam(param.key, value.toString());
                }}
              />
            </Grid.Col>
          )}
          {SystemConfigParamType.TEXT === param.type && (
            <Grid.Col span={3}>
              <KanbanInput
                minLength={param.min}
                maxLength={param.max}
                placeholder='Value'
                value={getConfigValue(param.key)}
                onChange={(e) => {
                  const value = e.target.value;
                  setConfigParam(param.key, value);
                }}
              />
            </Grid.Col>
          )}
        </Grid>
      ))}

      <Flex justify={'flex-end'}>
        <KanbanButton disabled={!isValidateParam(listData)} onClick={onSave}>
          Save
        </KanbanButton>
      </Flex>
    </>
  );
};

export default SystemParameterPage;
