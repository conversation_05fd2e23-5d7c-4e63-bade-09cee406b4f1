import React, { useState, useRef } from 'react';
import { IconChevronUp } from '@tabler/icons-react';
import { buildBusinessDomainDetailUrl } from '@common/utils/RouterUtils';
import { useNavigate } from 'react-router-dom';
import { HeightScrollItemComponent, MAX_RECORD_IN_COLLAPSE } from '@common/constants/BusinessDomainConstants';
import { Box, Group, Card, ActionIcon, Flex } from '@mantine/core';
import type { BusinessDomainItem, BusinessDomainModel as BusinessDomainModel } from '@models/BusinessDomain';
import styles from './BusinessDomainItem.module.scss';
import { KanbanButton, KanbanText, KanbanTooltip } from 'kanban-design-system';

type BusinessDomainItemProps = {
  data: BusinessDomainModel;
};

export const BusinessDomainItemComponent = (props: BusinessDomainItemProps) => {
  const navigate = useNavigate();
  const businessDomainList = props.data;
  const viewport = useRef<HTMLDivElement>(null);

  const [isExpand, setIsExpand] = useState(false);
  const expand = () => {
    setIsExpand(true);
  };

  const closeExpand = () => {
    setIsExpand(false);
    scrollToTop();
  };

  const scrollToTop = () => viewport.current?.scrollTo({ top: 0 });

  const renderDomainButtons = (domains: BusinessDomainItem[]) => {
    return domains?.map((item, index) => (
      <Box key={index}>
        <KanbanTooltip label={item.name}>
          <KanbanButton
            mt={5}
            mb={5}
            variant='outline'
            size='xs'
            maw={'100%'}
            bg={'var(--mantine-color-violet-1)'}
            className={styles['btn-border-color']}
            color='var(--mantine-color-black)'
            onClick={() => item.id && navigate(buildBusinessDomainDetailUrl(item.id))}>
            <KanbanText truncate='end'>{item.name}</KanbanText>
          </KanbanButton>
        </KanbanTooltip>
      </Box>
    ));
  };
  const isHasMoreItem = (businessDomainList.listSubDomain?.length || 0) > MAX_RECORD_IN_COLLAPSE;
  const numberRecordShowMore = (businessDomainList.listSubDomain?.length || 0) - MAX_RECORD_IN_COLLAPSE;

  return (
    <>
      <Card padding={'0'} pb={'md'} style={isExpand ? { gridRow: 'span 3' } : {}}>
        <Card withBorder radius='md'>
          <Box mb={'md'}>
            <Flex align={'center'}>
              <KanbanTooltip label={businessDomainList.name}>
                <KanbanText flex={1} fw={700} c='var(--mantine-color-black)' truncate='end'>
                  {businessDomainList.name}
                </KanbanText>
              </KanbanTooltip>
              <KanbanButton
                variant={'outline'}
                size={'xs'}
                onClick={() => {
                  if (businessDomainList.id !== undefined) {
                    navigate(buildBusinessDomainDetailUrl(businessDomainList.id));
                  }
                }}>
                View Detail
              </KanbanButton>
            </Flex>
          </Box>
          <Box
            ref={viewport}
            className={styles['scroll-wrapper']}
            style={
              isExpand
                ? {
                    // fix bug CMDB-4771
                    minHeight: HeightScrollItemComponent.HeightExpand,
                    maxHeight: HeightScrollItemComponent.HeightExpand,
                  }
                : {
                    minHeight: HeightScrollItemComponent.HeightCollapse,
                  }
            }>
            <Box>
              {renderDomainButtons(businessDomainList.listSubDomain?.slice(0, MAX_RECORD_IN_COLLAPSE - 1) || [])}
              {!isExpand ? (
                <Flex align={'center'} justify='flex-start'>
                  {numberRecordShowMore >= 0 &&
                    renderDomainButtons(businessDomainList.listSubDomain?.slice(MAX_RECORD_IN_COLLAPSE - 1, MAX_RECORD_IN_COLLAPSE) || [])}
                  {isHasMoreItem && (
                    <KanbanButton flex={'0 0 auto'} ml='xs' size={'xs'} color='teal' radius={'lg'} onClick={expand}>
                      {` + ${numberRecordShowMore} `}
                    </KanbanButton>
                  )}
                </Flex>
              ) : (
                renderDomainButtons(businessDomainList.listSubDomain?.slice(MAX_RECORD_IN_COLLAPSE - 1) || [])
              )}
            </Box>
          </Box>
        </Card>
        <Group justify='center' className={styles['group-translate-y']}>
          {isExpand && (
            <ActionIcon color='var(--mantine-color-primary-outline)' variant='filled' size='xs' radius='xl' onClick={closeExpand}>
              <IconChevronUp color='var(--mantine-color-blue-0)' stroke={1} />
            </ActionIcon>
          )}
        </Group>
      </Card>
    </>
  );
};
export default BusinessDomainItemComponent;
