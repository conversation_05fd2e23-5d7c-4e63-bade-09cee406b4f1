import React, { useState, useImperativeHandle, forwardRef, useEffect, useMemo, useCallback } from 'react';
import { KanbanTextarea } from 'kanban-design-system';
import { KanbanRadio } from 'kanban-design-system';
import SelectCiComponent from '@components/commonCi/SelectCiComponent';
import { ColumnType, KanbanTable } from 'kanban-design-system';
import { KanbanSelect } from 'kanban-design-system';
import { KanbanCheckbox } from 'kanban-design-system';
import { CiRelationshipInfoModel, CiRelationshipModel, RelationshipTypeInput } from '@models/CiRelationship';
import type { CiTypeRelationAttrModel, CiTypeRelationModel } from '@models/CiTypeRelation';
import type { ConfigItemModel } from '@models/ConfigItem';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import { CiRelationshipApi } from '@api/CiRelationshipApi';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { CiTypeAttributeDataType } from '@models/CiType';
import { KanbanDateInput } from 'kanban-design-system';
import { KanbanNumberInput } from 'kanban-design-system';
import type { RelationShipOfBusinessView } from '@models/CIBusinessViews';
import { useGetRelationshipTypes } from '@slices/CiRelationshipTypesSlice';
import { DD_MM_YYYY_FORMAT } from '@common/utils/DateUtils';
import CiTypeRelationshipSelect from '../component/CiTypeRelationshipSelect';

type AddRelationshipPopupProps = {
  ciId: number;
  // ciTypeId: number,
  ciTypeRelationships: CiTypeRelationModel[];
  allCiTypeRelationAttribute: CiTypeRelationAttrModel[];
  ciRelationships?: CiRelationshipInfoModel[];
  fetchRelationship?: () => void;

  setIsValidAdd: (isValid: boolean) => void;
  onCloseModalCreateNew: () => void;
  updateDataGraphView?: (relationShips: RelationShipOfBusinessView[]) => void;
  withinPortal?: boolean;
};

export type AddRelationshipMethods = {
  onCreateNew: () => void;
  onResetWhenCloseModalCreateNew: () => void;
};

type CiRelationshipAttributeType = {
  ciId: number;
  attributeId: number;
  value: string;
};

export const AddRelationship = forwardRef<AddRelationshipMethods, AddRelationshipPopupProps>((props, ref) => {
  const {
    allCiTypeRelationAttribute,
    ciId,
    ciRelationships,
    ciTypeRelationships,
    fetchRelationship,
    onCloseModalCreateNew,
    setIsValidAdd,
    updateDataGraphView,
    withinPortal = true,
  } = props;
  const [typeInput, setTypeInput] = useState(`${RelationshipTypeInput.SUGGEST}`);
  // const [isValidAdd, setIsValidAdd] = useState(true);
  // const [openedModalCreateNew, { close: closeModalCreateNew, open: openModalCreateNew }] = useDisclosure(false);
  const ciRelationshipTypes = useGetRelationshipTypes().data;
  // const [ciTypeRelationships, setCiTypeRelationships] = useState<CiTypeRelationModel[]>([]);
  // const [allCiTypeRelationAttribute, setAllCiTypeRelationAttribute] = useState<CiTypeRelationAttrModel[]>([]);
  const [listDataCi, setListDataCi] = useState<ConfigItemModel[]>([]);
  const [listCiSelected, setListCiSelected] = useState<ConfigItemModel[]>([]);
  const [columnsAttribute, setColumnsAttribute] = useState<ColumnType<ConfigItemModel>[]>([]);
  const [dataCreateNew, setDataCreateNew] = useState<CiRelationshipModel>({
    fromCi: 0,
    toCi: 0,
    relationshipId: 0,
    id: 0,
    listToCi: [],
  });
  const [isInverse, setIsInverse] = useState(false);
  const [ciTypeRelationshipSelected, setCiTypeRelationshipSelected] = useState<CiTypeRelationModel>();
  const [valueAttributes, setValueAttributes] = useState<CiRelationshipAttributeType[]>([]);
  let columns: ColumnType<ConfigItemModel>[] = [
    {
      title: 'CI Type',
      name: 'name',
    },
  ];

  const onResetWhenCloseModalCreateNew = useCallback(() => {
    // closeModalCreateNew();
    setListDataCi([]);
    setListCiSelected([]);
    setValueAttributes([]);
    setDataCreateNew({
      fromCi: 0,
      toCi: 0,
      relationshipId: 0,
      id: 0,
      listToCi: [],
    });
    setIsValidAdd(true);
    setIsInverse(false);
    setTypeInput(`${RelationshipTypeInput.SUGGEST}`);
    setColumnsAttribute([
      {
        title: 'CI Type',
        name: 'name',
      },
    ]);
  }, [setIsValidAdd]);

  const onCreateNew = useCallback(() => {
    const listNewItem: CiRelationshipModel[] = [];
    if (typeInput === RelationshipTypeInput.SUGGEST) {
      if (!(listCiSelected && listCiSelected.length > 0) || !ciTypeRelationshipSelected) {
        return;
      }

      let isInverse = false;
      if (ciTypeRelationshipSelected?.referCiTypeId === ciTypeRelationshipSelected?.toCiType) {
        isInverse = true;
      }
      if (listCiSelected && listCiSelected.length > 0) {
        listCiSelected.forEach((item) => {
          let fromCi = ciId;
          let toCi = item.id;
          if (isInverse) {
            fromCi = toCi;
            toCi = ciId;
          }

          const dataAttributes = valueAttributes
            .filter((x) => x.ciId === item.id)
            .map((k) => ({
              id: 0,
              attributeId: k.attributeId,
              value: k.value,
            }));
          const newRelationship: CiRelationshipModel = {
            id: 0,
            fromCi: fromCi,
            toCi: toCi,
            relationshipId: ciTypeRelationshipSelected?.relationshipId || 0,
            relationshipAttributes: dataAttributes,
          };
          listNewItem.push(newRelationship);
        });
      }
    } else {
      if (!(dataCreateNew.listToCi && dataCreateNew.listToCi.length > 0) || !dataCreateNew.relationshipId) {
        return;
      }

      dataCreateNew.listToCi.forEach((x) => {
        const data = { ...dataCreateNew };
        data.fromCi = ciId;
        data.toCi = x;
        if (isInverse) {
          data.fromCi = x;
          data.toCi = ciId;
        }
        listNewItem.push(data);
      });
    }
    if (listNewItem && listNewItem.length > 0) {
      CiRelationshipApi.saveNewRelationshipInfo(listNewItem)
        .then((res) => {
          if (res.data) {
            const relationships = res.data.map((relationship) => {
              return {
                from: relationship.fromCi,
                to: relationship.toCi,
              };
            });
            if (updateDataGraphView) {
              updateDataGraphView(relationships);
            }
            if (fetchRelationship) {
              fetchRelationship();
            }
          }

          NotificationSuccess({
            message: 'Created successfully',
          });
          onCloseModalCreateNew();
          // onResetWhenCloseModalCreateNew();
        })
        .catch(() => {});
    }
  }, [
    ciId,
    ciTypeRelationshipSelected,
    dataCreateNew,
    fetchRelationship,
    isInverse,
    listCiSelected,
    onCloseModalCreateNew,
    typeInput,
    valueAttributes,
    updateDataGraphView,
  ]);

  const onChangeValueAttribute = (attributeId: number, ciId: number, val: string) => {
    const newValue = {
      ciId: ciId,
      attributeId: attributeId,
      value: val,
    };
    setValueAttributes((prev) => {
      const indexAttribute = prev.findIndex((x) => x.ciId === ciId && x.attributeId === attributeId);

      if (indexAttribute >= 0) {
        const updatedItems = [...prev];
        updatedItems[indexAttribute] = newValue;
        return updatedItems;
      } else {
        return [...prev, newValue];
      }
    });
  };

  const onChangeSuggestSelect = (suggestId: number) => {
    // reset data
    setListCiSelected([]);
    setValueAttributes([]);

    // load data view
    const ciTypeRelationshipObj = ciTypeRelationships.find((x) => x.id === suggestId);

    setCiTypeRelationshipSelected(ciTypeRelationshipObj);

    let ciTypeId = ciTypeRelationshipObj?.toCiType || 0;
    let nameCiTypeSelected = ciTypeRelationshipObj?.toCiTypeName || '';
    if (ciTypeRelationshipObj?.referCiTypeId === ciTypeRelationshipObj?.toCiType) {
      ciTypeId = ciTypeRelationshipObj?.fromCiType || 0;
      nameCiTypeSelected = ciTypeRelationshipObj?.fromCiTypeName || '';
    }

    ConfigItemTypeApi.getAllCisWithChildrenPaging(ciTypeId, {
      page: 0,
      size: 100,
      sortBy: 'createdDate',
    })
      .then((res) => {
        if (res?.data?.content) {
          const dataFilter = res.data.content.filter((x) => !listCiIdRefer.includes(x.id));
          setListDataCi(dataFilter);
        } else {
          setListDataCi([]);
        }
      })
      .catch(() => {
        setListDataCi([]);
      });

    const currentRelationshipAttribute = allCiTypeRelationAttribute.filter((e) => e.ciTypeRelationshipId === suggestId);

    columns = [
      {
        title: nameCiTypeSelected,
        name: 'name',
      },
    ];
    if (currentRelationshipAttribute) {
      currentRelationshipAttribute.forEach((item) => {
        columns.push({
          title: item.name,
          name: `attribute${item.id}`,
          customRender: (_data, rowData) => {
            if (CiTypeAttributeDataType.TEXT === item.type) {
              return (
                <KanbanTextarea
                  maxLength={2000}
                  onChange={(e) => {
                    const value = e.currentTarget.value;
                    onChangeValueAttribute(item.id, rowData.id, value);
                  }}></KanbanTextarea>
              );
            }
            if (CiTypeAttributeDataType.PICK_LIST === item.type) {
              const arrayOption = item.options ? JSON.parse(item.options) : [];
              return (
                <KanbanSelect
                  data={arrayOption}
                  onChange={(e) => {
                    const value = e || '';
                    onChangeValueAttribute(item.id, rowData.id, value);
                  }}></KanbanSelect>
              );
            }
            if (CiTypeAttributeDataType.DATE === item.type) {
              return (
                <KanbanDateInput
                  valueFormat={DD_MM_YYYY_FORMAT}
                  onChange={(e) => {
                    const value = String(e);
                    onChangeValueAttribute(item.id, rowData.id, value);
                  }}></KanbanDateInput>
              );
            }
            if (CiTypeAttributeDataType.NUMBER === item.type) {
              return (
                <KanbanNumberInput
                  maxLength={2000}
                  onChange={(e) => {
                    const value = String(e);
                    onChangeValueAttribute(item.id, rowData.id, value);
                  }}></KanbanNumberInput>
              );
            }
          },
        });
      });
    }
    setColumnsAttribute(columns);
  };

  const listCiTypeRelationshipCombobox = useMemo(() => {
    return ciTypeRelationships.map((item) => {
      // relationship + CI Type name;
      const ciRelationship = ciRelationshipTypes.find((x) => x.id === item.relationshipId);

      let relationshipTypeName = ciRelationship ? ciRelationship.type : '';
      let toCiTypeName = item.toCiTypeName;
      if (item.toCiType === item.referCiTypeId) {
        relationshipTypeName = ciRelationship ? ciRelationship.inverseType : '';
        toCiTypeName = item.fromCiTypeName;
      }

      return {
        relationshipId: item.id, // CI Type RelationshipId
        relationshipTypeName: relationshipTypeName,
        toCiTypeName: toCiTypeName,
      };
    });
  }, [ciTypeRelationships, ciRelationshipTypes]);

  const listCiIdRefer = useMemo(() => {
    return (ciRelationships || []).map((obj) => {
      const idToUse = obj.fromCi === ciId ? obj.toCi : obj.fromCi;
      return idToUse;
    });
  }, [ciRelationships, ciId]);

  const listRelationshipCombobox = useMemo(() => {
    return ciRelationshipTypes.map((item) => {
      return {
        value: `${item.id}`,
        label: `${item.type} - ${item.inverseType}`,
      };
    });
  }, [ciRelationshipTypes]);

  useImperativeHandle<any, AddRelationshipMethods>(
    ref,
    () => ({
      onCreateNew: onCreateNew,
      onResetWhenCloseModalCreateNew,
    }),
    [onCreateNew, onResetWhenCloseModalCreateNew],
  );

  useEffect(() => {
    if (typeInput === RelationshipTypeInput.SUGGEST) {
      if (!(listCiSelected && listCiSelected.length > 0) || !ciTypeRelationshipSelected) {
        setIsValidAdd(true);
      } else {
        setIsValidAdd(false);
      }
    } else {
      if (!(dataCreateNew.listToCi && dataCreateNew.listToCi.length > 0) || !dataCreateNew.relationshipId) {
        setIsValidAdd(true);
      } else {
        setIsValidAdd(false);
      }
    }
  }, [typeInput, listCiSelected, ciTypeRelationshipSelected, dataCreateNew, setIsValidAdd]);

  return (
    <>
      <KanbanRadio
        group={{
          name: 'typeSelect',
          label: '',
          mb: 'lg',
          description: 'Select type input',
          withAsterisk: false,
          value: typeInput,
          onChange: (value) => {
            //reset suggest relationship table
            setListDataCi([]);
            setListCiSelected([]);
            setTypeInput(value);
          },
        }}
        radios={[
          {
            value: RelationshipTypeInput.SUGGEST,
            label: 'Suggest relationship',
            checked: true,
          },
          {
            value: RelationshipTypeInput.SEARCH,
            label: 'Search other CI(s)',
          },
        ]}
      />
      {typeInput === RelationshipTypeInput.SUGGEST && (
        <>
          <CiTypeRelationshipSelect
            withinPortal={withinPortal}
            label='Suggest relationship'
            withAsterisk
            items={listCiTypeRelationshipCombobox}
            itemAttributes={allCiTypeRelationAttribute}
            onChange={(value) => {
              const suggestId = value ? Number(value) : 0;
              onChangeSuggestSelect(suggestId);
            }}
          />

          <KanbanTable
            key={listDataCi[0]?.id || ''}
            columns={columnsAttribute}
            data={listDataCi}
            title='CIs data'
            searchable={{
              enable: true,
            }}
            selectableRows={{
              enable: true,
              onSelectedRowsChanged(rows) {
                setListCiSelected(rows);
              },
            }}
          />
        </>
      )}
      {typeInput === RelationshipTypeInput.SEARCH && (
        <>
          <SelectCiComponent
            withinPortal={withinPortal}
            label='Select CI'
            maxSelection={50}
            withAsterisk
            excludeSelectCiIds={listCiIdRefer}
            onChange={(value) => {
              const listIdCi = (value || []).map((obj) => obj.id);
              setDataCreateNew((prev) => {
                const current = { ...prev };
                current.listToCi = listIdCi;
                return current;
              });
            }}
          />

          <KanbanSelect
            comboboxProps={{ position: 'bottom', middlewares: { flip: false, shift: false }, offset: 0, withinPortal: false }}
            searchable
            label='Relationship'
            withAsterisk
            value={`${dataCreateNew.relationshipId}`}
            data={listRelationshipCombobox}
            onChange={(value) => {
              setDataCreateNew((prev) => {
                const current = { ...prev };
                current.relationshipId = value ? Number(value) : 0;
                return current;
              });
            }}></KanbanSelect>

          <KanbanCheckbox
            label='Inverse relationship'
            checked={isInverse}
            onChange={(e) => {
              const value = e.target.checked;
              setIsInverse(value);
            }}
          />
        </>
      )}
    </>
  );
});
AddRelationship.displayName = 'AddRelationship';
export default AddRelationship;
