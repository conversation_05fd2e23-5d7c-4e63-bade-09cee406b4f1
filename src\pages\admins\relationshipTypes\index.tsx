import { CiRelationshipTypeApi, CiRelationshipTypeResponse } from '@api/CiRelationshipTypeApi';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { KanbanInput } from 'kanban-design-system';
import { KanbanTextarea } from 'kanban-design-system';
import { KanbanConfirmModal } from 'kanban-design-system';
import { KanbanTable, ColumnType, KanbanTableProps } from 'kanban-design-system';
import { renderDateTime } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import type { CiRelationshipTypeModel } from '@models/CiRelationshipType';
import { ciRelationshipTypesSlice, getCiRelationshipTypes } from '@slices/CiRelationshipTypesSlice';
import { IconPlus, IconEdit, IconEye } from '@tabler/icons-react';
import React, { useCallback, useEffect, useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { KanbanIconButton } from 'kanban-design-system';
import { NotificationSuccess, NotificationError } from '@common/utils/NotificationUtils';
import { formatStandardName } from '@common/utils/StringUtils';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { BreadcrumbComponent } from '../breadcrumb/BreadcrumbComponent';

export const RelationshipTypes = () => {
  const dispatch = useDispatch();

  const columns: ColumnType<CiRelationshipTypeResponse>[] = useMemo(() => {
    return [
      {
        title: 'Relationship Type ID',
        name: 'id',
      },
      {
        title: 'Type',
        name: 'type',
      },

      {
        title: 'Inverse type',
        name: 'inverseType',
      },
      {
        title: 'Description',
        name: 'description',
      },
      {
        title: 'Created by',
        name: 'createdBy',
      },
      {
        title: 'Created date',
        name: 'createdDate',
        customRender: renderDateTime,
      },
      {
        title: 'Modified by',
        name: 'modifiedBy',
        hidden: true,
      },
      {
        title: 'Modified date',
        name: 'modifiedDate',
        hidden: true,
        customRender: renderDateTime,
      },
    ];
  }, []);
  const ciRelationshipTypes = useSelector(getCiRelationshipTypes);
  const listData = useMemo(() => {
    return ciRelationshipTypes?.data || [];
  }, [ciRelationshipTypes]);

  const setListData = useCallback(
    (data: CiRelationshipTypeModel[]) => {
      dispatch(
        ciRelationshipTypesSlice.actions.setValue({
          data: data,
        }),
      );
    },
    [dispatch],
  );

  useEffect(() => {
    dispatch(ciRelationshipTypesSlice.actions.fetchForEmpty());
  }, [dispatch]);

  const [openedModalCreateNew, { close: closeModalCreateNew, open: openModalCreateNew }] = useDisclosure(false);
  const defaultData = {
    id: 0,
    inverseType: '',
    type: '',
  };
  const [dataCreateNew, setDataCreateNew] = useState<CiRelationshipTypeModel>(defaultData);
  const [isDetailView, setIsDetailView] = useState<boolean>(false);

  const deleteRow = useCallback(
    (id: number) => {
      CiRelationshipTypeApi.deleteById(id)
        .then((res) => {
          if (res.data) {
            NotificationSuccess({
              message: 'Deleted successfully',
            });
            const newData = listData.filter((x) => x.id !== id);
            setListData(newData);
          }
        })
        .catch(() => {});
    },
    [listData, setListData],
  );

  const deleteRelationshipType = useCallback(
    (ids: number[]) => {
      CiRelationshipTypeApi.deleteByIds(ids)
        .then((res) => {
          if (res.data.length === 0) {
            NotificationSuccess({
              message: 'Deleted successfully',
            });
            const newData = listData.filter((item) => !ids.includes(item.id));
            setListData(newData);
          } else {
            NotificationError({
              message: `Relationship Type has id in : ${res.data.toString()} used.`,
            });
            const idSuccess = ids.filter((id) => !res.data.includes(id));
            const newData = listData.filter((item) => !idSuccess.includes(item.id));
            setListData(newData);
          }
        })
        .catch(() => {});
    },
    [listData, setListData],
  );

  const onConfirmCreateNew = () => {
    if (!dataCreateNew.type || !dataCreateNew.inverseType) {
      return;
    }
    CiRelationshipTypeApi.saveNewRelationship(dataCreateNew)
      .then((res) => {
        if (dataCreateNew.id !== 0) {
          NotificationSuccess({
            message: 'Updated successfully',
          });
        } else {
          NotificationSuccess({
            message: 'Created successfully',
          });
        }

        setDataCreateNew({
          id: 0,
          inverseType: '',
          type: '',
        });

        const index = listData.findIndex((x) => x.id === res.data.id);
        const newData = [...listData];
        if (index >= 0) {
          newData[index] = res.data;
        } else {
          newData.unshift(res.data);
        }
        setListData(newData);

        closeModalCreateNew();
      })
      .catch(() => {});
  };

  const openCreateModal = () => {
    setIsDetailView(false);
    setDataCreateNew(defaultData);
    openModalCreateNew();
  };
  const tableViewListRelationshipTypeProps: KanbanTableProps<CiRelationshipTypeResponse> = useMemo(() => {
    return {
      columns: columns,
      data: listData,
      key: 1,
      pagination: {
        enable: true,
      },
      searchable: { enable: true, debounceTime: 300 },
      showNumericalOrderColumn: true,
      selectableRows: {
        enable: !!isCurrentUserMatchPermissions([AclPermission.deleteRelationshipType]),
        onDeleted: isCurrentUserMatchPermissions([AclPermission.deleteRelationshipType])
          ? (rows) => {
              deleteRelationshipType(rows.map((x) => x.id));
            }
          : undefined,
      },
      onRowClicked: (data) => {
        if (isCurrentUserMatchPermissions([AclPermission.updateRelationshipType])) {
          setIsDetailView(false);
          setDataCreateNew({ ...data });
          openModalCreateNew();
        }
      },
      actions: {
        customAction: (data) => (
          <>
            <KanbanIconButton
              variant='transparent'
              size={'sm'}
              onClick={() => {
                setIsDetailView(true);
                setDataCreateNew({ ...data });
                openModalCreateNew();
              }}>
              <IconEye />
            </KanbanIconButton>
            <GuardComponent hiddenOnUnSatisfy requirePermissions={[AclPermission.updateRelationshipType]}>
              <KanbanIconButton
                variant='transparent'
                size={'sm'}
                onClick={() => {
                  setIsDetailView(false);
                  setDataCreateNew({ ...data });
                  openModalCreateNew();
                }}>
                <IconEdit />
              </KanbanIconButton>
            </GuardComponent>
          </>
        ),
        deletable: isCurrentUserMatchPermissions([AclPermission.deleteRelationshipType])
          ? {
              onDeleted: (data) => {
                deleteRow(data.id);
              },
            }
          : undefined,
      },
    };
  }, [columns, deleteRelationshipType, deleteRow, listData, openModalCreateNew]);

  return (
    <>
      {/* 4746 relationship types*/}
      <BreadcrumbComponent />
      <KanbanConfirmModal
        title={!isDetailView ? 'Create/update relationship' : 'Relationship Type Detail'}
        onConfirm={!isDetailView ? onConfirmCreateNew : undefined}
        onClose={closeModalCreateNew}
        opened={openedModalCreateNew}
        disabledConfirmButton={
          !dataCreateNew.type || !dataCreateNew.inverseType || dataCreateNew.type.trim() === '' || dataCreateNew.inverseType.trim() === ''
        }>
        <KanbanInput
          disabled={isDetailView}
          withAsterisk
          label='Type'
          maxLength={255}
          value={dataCreateNew.type || ''}
          onChange={(e) => {
            const value = e.target.value;
            setDataCreateNew((prev) => {
              const current = { ...prev };
              current.type = value;
              return current;
            });
          }}
          onBlur={(e) => {
            const value = e.target.value;
            setDataCreateNew((prev) => {
              return {
                ...prev,
                type: formatStandardName(value),
              };
            });
          }}
        />
        <KanbanInput
          disabled={isDetailView}
          withAsterisk
          label='Inverse type'
          maxLength={255}
          value={dataCreateNew.inverseType || ''}
          onChange={(e) => {
            const value = e.target.value;
            setDataCreateNew((prev) => {
              const current = { ...prev };
              current.inverseType = value;
              return current;
            });
          }}
          onBlur={(e) => {
            const value = e.target.value;
            setDataCreateNew((prev) => {
              return {
                ...prev,
                inverseType: formatStandardName(value),
              };
            });
          }}
        />
        <KanbanTextarea
          disabled={isDetailView}
          label='Description'
          maxLength={2000}
          value={dataCreateNew.description || ''}
          onChange={(e) => {
            const value = e.target.value;
            setDataCreateNew((prev) => {
              const current = { ...prev };
              current.description = value;
              return current;
            });
          }}
        />
      </KanbanConfirmModal>

      <HeaderTitleComponent
        title='Relationship Types'
        rightSection={
          <GuardComponent hiddenOnUnSatisfy requirePermissions={[AclPermission.createRelationshipType]}>
            <KanbanButton onClick={openCreateModal} leftSection={<IconPlus />}>
              Create new
            </KanbanButton>
          </GuardComponent>
        }
      />

      <KanbanTable {...tableViewListRelationshipTypeProps} />
    </>
  );
};
export default RelationshipTypes;
