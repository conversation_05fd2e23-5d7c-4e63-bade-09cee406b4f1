import type { PermissionAction } from '@common/constants/PermissionAction';
import type { ConfigItemPermissionOtherModel, ConfigItemTypePermissionModel } from './ConfigItemTypePermission';
import type { EntityModelBase } from './EntityModelBase';
import type { PermissionActionType } from '@common/constants/PermissionActionType';
import type { UserSettingGroupModel } from '@api/systems/GroupsApi';

export type RoleModel = EntityModelBase & {
  name: string;
  description?: string;
  groupOfRoles?: UserSettingGroupModel[];
};
export type PermissionOtherModel = EntityModelBase & {
  type: PermissionActionType;
  action: PermissionAction;
};
export type PermissionCiOrCiTypeRoleModel = {
  id: number;
  name: string;
  description: string;
  action: PermissionAction[];
  ciType?: string;
};
export type RoleSettingRequest = {
  basicInfoRole: RoleModel;
  permissionOther: ConfigItemPermissionOtherModel[];
  permissionCi: ConfigItemTypePermissionModel[];
};
