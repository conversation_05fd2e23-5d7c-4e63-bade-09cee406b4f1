import React, { useEffect, useState } from 'react';
import CiRelationshipGraph, { CiRelationshipGraphMethods, CiRelationshipGraphProps, FilterModel, ToolbarActionProps } from './CiRelationshipGraph';
import { CiRelationshipInfoModel } from '@models/CiRelationship';
import { CiDetailSubTabType } from '@common/constants/CiDetail';
import { CiRelationshipApi } from '@api/CiRelationshipApi';
import { GRAPH_VIEW_LINK_STYPES, GRAPH_VIEW_TYPE } from '@common/utils/GoJsHelper';

type Props = CiRelationshipGraphProps & {
  useDiagramVirtualized?: boolean;
};
const filterInit: FilterModel = {
  datasCiNameRef: [],
  oldDatasCiNameRef: [],
  oldLinksCiNameRef: [],
};
const CiRelationshipGraphWrapper = React.forwardRef<CiRelationshipGraphMethods, Props>((props, ref) => {
  const { onSetDiagramVirtualized, useDiagramVirtualized, ...rest } = props;

  const [ciRelationships, setCiRelationships] = useState<CiRelationshipInfoModel[]>([]);

  const [toolbarAction, setToolbarAction] = useState<ToolbarActionProps>({
    level: props.currentLevel,
    filter: filterInit,
    graphViewStyle: GRAPH_VIEW_TYPE.TOP_DOWN,
    graphViewLinkStyle: GRAPH_VIEW_LINK_STYPES.ORTHOGONAL_ROUTING,
  });

  useEffect(() => {
    setCiRelationships([...(props.ciRelationships || [])]);
  }, [props.ciRelationships]);

  useEffect(() => {
    if (CiDetailSubTabType.IMPACTMAP === props.activeSubTab) {
      CiRelationshipApi.getImpactMap(props.ciId, toolbarAction.isImpactTo ?? true, Number(props.currentLevel))
        .then((res) => {
          setCiRelationships(res.data || []);
        })
        .catch(() => {});
    }
  }, [props.ciId, props.currentLevel, props.activeSubTab, toolbarAction.isImpactTo]);

  const Component = CiRelationshipGraph;

  return (
    <Component
      ref={ref}
      {...rest}
      useDiagramVirtualized={useDiagramVirtualized}
      ciRelationships={ciRelationships}
      toolbarActionState={toolbarAction}
      setToolbarActionState={setToolbarAction}
      onSetDiagramVirtualized={onSetDiagramVirtualized}
    />
  );
});
CiRelationshipGraphWrapper.displayName = 'CiRelationshipGraphWrapper';
export default CiRelationshipGraphWrapper;
