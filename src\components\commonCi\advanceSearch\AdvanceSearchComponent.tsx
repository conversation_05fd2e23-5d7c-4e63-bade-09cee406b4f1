import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import QueryBuilder, {
  ActionProps,
  Field,
  RuleGroupType,
  add,
  remove,
  type RuleType,
  transformQuery,
  type ValueEditorProps,
  type FieldSelectorProps,
  type Path,
  OperatorSelectorProps,
  update,
  InputType,
} from 'react-querybuilder';
import { QueryBuilderMantine } from '@react-querybuilder/mantine';
import { KanbanButton } from 'kanban-design-system';
import { Box, Card, ComboboxItem, Container, Flex, Group, ScrollArea } from '@mantine/core';
import { KanbanIconButton } from 'kanban-design-system';
import { IconClearAll, IconFileExport, IconLoader, IconPlus, IconSearch, IconTrash } from '@tabler/icons-react';
import styles from './AdvanceSearchComponent.module.scss';
import { useGetRelationshipTypes } from '@slices/CiRelationshipTypesSlice';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import { OperatorEnum } from '@common/constants/OperatorEnum';
import CustomValueEditor from './CustomValueEditor';
import { KanbanTitle } from 'kanban-design-system';
import { NotificationError, NotificationSuccess } from '@common/utils/NotificationUtils';

import CustomSelectControl from './CustomSelectControl';
import { useDisclosure } from '@mantine/hooks';
import { KanbanModal } from 'kanban-design-system';
import ExportFileComponent, { ExportFileMethods } from '@pages/cis/ci/export';
import type { AttributeInfoDTO, AttributeInfoEleDTO, ExportFileRequest } from '@models/ExportInfo';
import type { QueryRequestModel } from '@models/EntityModelBase';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import { EXCEL_TYPE } from '@common/constants/FileConstants';
import ListAdvancedSearchComponent from '@pages/cis/ci/advanceSearch/ListAdvancedSearch';
import type { CiAdvancedSearchDTO, CiAdvancedSearchModel } from '@models/CiAdvancedSearch';
import { KanbanConfirmModal } from 'kanban-design-system';
import AdvancedSearchForm from '@pages/cis/ci/advanceSearch/AdvancedSearchForm';
import { CiAdvancedSearchApi } from '@api/CiAdvanedSearchApi';
import { KanbanText } from 'kanban-design-system';
import { CIAdvancedSearchService } from '@service/CiAdvanceSearchExecute';
import { StatusSavedAdvanceSearch } from '@common/constants/StatusSavedAdvanceSearch';
import { KanbanSelect } from 'kanban-design-system';
import equal from 'fast-deep-equal';
import { generateFileExportName } from '@common/utils/ExcelUtils';
import fileDownload from 'js-file-download';
import { useSelector } from 'react-redux';
import { getCurrentUser } from '@slices/CurrentUserSlice';

const initialQueryDefault: RuleGroupType = {
  combinator: 'and',
  rules: [
    {
      rules: [
        {
          field: '',
          operator: '=',
          value: '',
        },
      ],
      combinator: 'and',
    },
  ],
};

interface QueryRule {
  field: string;
  operator: string;
  value: string;
  group?: string;
  fieldType?: string;
  disabled?: boolean;
}

interface QueryGroup {
  combinator: string;
  rules: QueryRule[];
  not: boolean;
}

export type AdvanceSearchData = {
  combinator: string;
  rules: QueryGroup[];
};

export type FieldChangeProps = {
  fieldName?: string;
  path?: Path;
};

export type AdvanceSearchProps = {
  fields?: Field[];
  onSearch?: (value: AdvanceSearchData) => void;
  setShowBtnExport?: (value: boolean) => void;
  queryRequestModel?: QueryRequestModel<any>;
  ciTypeId?: number;
  initialQuery?: RuleGroupType;
} & FieldChangeProps;

export type AdvanceSearchMethod = {
  showBtnExport: (value: boolean) => void;
};

export const listOperatorTypeEmpty: string[] = [OperatorEnum.EMPTY, OperatorEnum.NOT_EMPTY];

export const AdvanceSearchComponent = forwardRef<AdvanceSearchMethod, AdvanceSearchProps>((props, ref) => {
  // Add check if admin then export, fix and confirm by Duyhh.ho and test date 18/11/2024
  const currentUser = useSelector(getCurrentUser);
  const isSuperAdmin = currentUser.data?.isSuperAdmin === true;

  const { initialQuery, onSearch } = props;
  const [isShowBtnExport, setShowBtnExport] = useState(false);

  const [query, setQuery] = useState(initialQuery || initialQueryDefault);
  const [fieldChange, setFieldChange] = useState<FieldChangeProps>({});
  const [isInit, setIsInit] = useState(true);
  const { ciTypeId } = props;

  const ciRelationshipTypes = useGetRelationshipTypes()?.data;

  const listRelationshipCombobox = useMemo(() => {
    const relationshipTypes = ciRelationshipTypes || [];

    const sortedRelationshipTypes = [...relationshipTypes];
    sortedRelationshipTypes.sort((a, b) => a.type.localeCompare(b.type));
    return sortedRelationshipTypes.map((item) => {
      return {
        value: `${item.id}`,
        label: `${item.type} - ${item.inverseType}`,
      };
    });
  }, [ciRelationshipTypes]);

  const ciTypes = useGetCiTypes();

  const listCiTypeCombobox = useMemo(() => {
    const ciTypeData = ciTypes.data || [];
    const sortedCiTypeData = [...ciTypeData];

    sortedCiTypeData.sort((a, b) => a.name.localeCompare(b.name));

    return sortedCiTypeData.map((item) => {
      return {
        value: `${item.id}`,
        label: item.name,
      };
    });
  }, [ciTypes]);

  useImperativeHandle<any, AdvanceSearchMethod>(
    ref,
    () => ({
      showBtnExport: setShowBtnExport,
    }),
    [setShowBtnExport],
  );

  // Custom Rule component
  const removeRuleAction: React.FC<ActionProps> = ({ disabled, path }) => {
    return (
      <>
        {disabled ? (
          <KanbanIconButton key={3} color='transparent' size='sm' variant='transparent'></KanbanIconButton>
        ) : (
          <KanbanIconButton
            color='red'
            size='sm'
            onClick={() => {
              setQuery(remove(query, path));
            }}>
            <IconTrash size='1rem' />
          </KanbanIconButton>
        )}
      </>
    );
  };

  const removeGroupAction: React.FC<ActionProps> = ({ path }) => {
    return (
      <>
        <KanbanButton
          color={'red'}
          variant={'outline'}
          size={'xs'}
          onClick={() => {
            setQuery(remove(query, path));
          }}>
          Remove
        </KanbanButton>
      </>
    );
  };

  const addRuleAction: React.FC<ActionProps> = ({ level, path }) => {
    if (level === 1) {
      return (
        <KanbanButton
          size={'xs'}
          onClick={() => {
            setQuery(add(query, { field: '', operator: '=', value: '' }, path));
          }}>
          Add Rule
        </KanbanButton>
      );
    } else {
      return <></>;
    }
  };

  const addGroupAction: React.FC<ActionProps> = ({ level, path }) => {
    if (level > 0) {
      return null;
    }
    return (
      <KanbanButton
        size={'xs'}
        onClick={() => {
          setQuery(
            add(
              query,
              {
                rules: [
                  {
                    field: '',
                    operator: '=',
                    value: '',
                  },
                ],
                combinator: 'and',
                not: false,
              },
              path,
            ),
          );
        }}>
        Add Group
      </KanbanButton>
    );
  };

  const getOperators = useCallback(
    (fieldName: string) => {
      const field = (props.fields || []).find((x) => x.name === fieldName);

      if (field?.name) {
        switch (field.name) {
          case 'ciTypeId':
            return [
              { name: OperatorEnum.IS, label: 'is' },
              { name: OperatorEnum.IS_NOT, label: 'is not' },
            ];
          case 'name':
          case 'createdBy':
          case 'fromCi':
          case 'toCi':
            return [
              { name: OperatorEnum.IS, label: 'is' },
              { name: OperatorEnum.IS_NOT, label: 'is not' },
              { name: OperatorEnum.CONTAINS, label: 'contain' },
              { name: OperatorEnum.NOT_CONTAINS, label: 'not contain' },
              { name: OperatorEnum.BEGIN_WITH, label: 'start with' },
              { name: OperatorEnum.END_WITH, label: 'end with' },
            ];
          case 'relationshipType':
            return [
              { name: OperatorEnum.IS, label: 'is' },
              { name: OperatorEnum.IS_NOT, label: 'is not' },
              { name: OperatorEnum.EMPTY, label: 'is empty' },
              { name: OperatorEnum.NOT_EMPTY, label: 'not empty' },
            ];
        }
        //devsec-833030: field check above -> remove ? of field?
        switch (field.inputType?.toLocaleLowerCase()) {
          case 'text':
            return [
              { name: OperatorEnum.IS, label: 'is' },
              { name: OperatorEnum.IS_NOT, label: 'is not' },
              { name: OperatorEnum.CONTAINS, label: 'contain' },
              { name: OperatorEnum.NOT_CONTAINS, label: 'not contain' },
              { name: OperatorEnum.BEGIN_WITH, label: 'start with' },
              { name: OperatorEnum.END_WITH, label: 'end with' },
              { name: OperatorEnum.EMPTY, label: 'is empty' },
              { name: OperatorEnum.NOT_EMPTY, label: 'not empty' },
            ];
          case 'number':
            return [
              { name: OperatorEnum.IS, label: 'is' },
              { name: OperatorEnum.IS_NOT, label: 'is not' },
              { name: OperatorEnum.GREATER_THAN, label: '>' },
              { name: OperatorEnum.GREATER_THAN_OR_EQUAL, label: '>=' },
              { name: OperatorEnum.LESS_THAN, label: '<' },
              { name: OperatorEnum.LESS_THAN_OR_EQUAL, label: '<=' },
              { name: OperatorEnum.CONTAINS, label: 'contain' },
              { name: OperatorEnum.NOT_CONTAINS, label: 'not contain' },
              { name: OperatorEnum.BEGIN_WITH, label: 'start with' },
              { name: OperatorEnum.END_WITH, label: 'end with' },
              { name: OperatorEnum.EMPTY, label: 'is empty' },
              { name: OperatorEnum.NOT_EMPTY, label: 'not empty' },
            ];

          case 'date':
            return [
              { name: OperatorEnum.GREATER_THAN, label: 'after' },
              { name: OperatorEnum.LESS_THAN, label: 'before' },
              { name: OperatorEnum.BETWEEN, label: 'between' },
            ];
        }
      }
      return [
        { name: OperatorEnum.IS, label: 'is' },
        { name: OperatorEnum.IS_NOT, label: 'is not' },
        { name: OperatorEnum.CONTAINS, label: 'contain' },
        { name: OperatorEnum.NOT_CONTAINS, label: 'not contain' },
        { name: OperatorEnum.BEGIN_WITH, label: 'start with' },
        { name: OperatorEnum.END_WITH, label: 'end with' },
        { name: OperatorEnum.EMPTY, label: 'is empty' },
        { name: OperatorEnum.NOT_EMPTY, label: 'not empty' },
      ];
    },
    [props.fields],
  );

  const ruleProcessor = useCallback(
    (r: RuleType): RuleType & { group?: string; fieldType?: InputType | null } => {
      const field = (props.fields || []).find((f) => f.name === r.field);
      return {
        ...r,
        group: field?.group,
        fieldType: field?.inputType,
      };
    },
    [props.fields],
  );

  const MemoizedValueEditor = useCallback(
    (props: ValueEditorProps) => {
      return <CustomValueEditor {...props} listCiType={listCiTypeCombobox} listRelationship={listRelationshipCombobox} ciTypeId={ciTypeId} />;
    },
    [ciTypeId, listCiTypeCombobox, listRelationshipCombobox],
  );

  const MemoizedControlEditor = useCallback(
    (props: FieldSelectorProps) => {
      return <CustomSelectControl {...props} setFieldChange={setFieldChange} ciTypeId={ciTypeId} />;
    },
    [ciTypeId],
  );

  const updateQueryOperatorRemoveValue = useCallback((value: string | null, path: Path) => {
    setQuery((query) => {
      const model = update(query, 'operator', value, path);
      const modelUpdate = update(model, 'value', '', path);
      return modelUpdate;
    });
  }, []);

  const operatorSelectorCustom = (props: OperatorSelectorProps) => {
    const { disabled, fieldData, rule } = props;
    return (
      <KanbanSelect
        disabled={disabled}
        placeholder='Choose value'
        w={'250px'}
        searchable
        value={props.value}
        mb={0}
        data={props.options as ComboboxItem[]}
        onChange={(val) => {
          if (fieldData.inputType?.toLowerCase() === 'date' && OperatorEnum.BETWEEN === rule.operator && rule.operator !== val) {
            updateQueryOperatorRemoveValue(val, props.path);
          } else {
            setQuery(update(query, 'operator', val, props.path));
          }
          // props.handleOnChange(val);
        }}
      />
    );
  };

  const validateQuery = (obj: AdvanceSearchData) => {
    if (equal(initialQueryDefault, obj)) {
      return;
    }
    if (!obj || Object.keys(obj).length === 0) {
      return 'Error: Object is null or empty';
    }

    // if (!obj.combinator || !obj.rules || obj.rules.length === 0) {
    //   return 'Combinator and Group must be not empty';
    // }

    for (const group of obj.rules) {
      // if (!group || !group.rules || group.rules.length === 0) {
      //   return 'Rule group must be not empty';
      // }

      if (!group.combinator) {
        return 'Error: "combinator" is empty in a rule group';
      }

      for (const rule of group.rules) {
        console.info(`${rule}-${typeof rule}`);
        if (
          !rule.field ||
          !rule.group ||
          !rule.operator ||
          ((!rule.value || (typeof rule.value === 'string' && !rule.value.trim())) && !listOperatorTypeEmpty.includes(rule.operator))
        ) {
          return 'Error: "field", "operator", or "value" is empty or null';
        }
        if (OperatorEnum.BETWEEN === rule.operator) {
          const [dateStartStr = '', dateEndStr = ''] = rule.value ? rule.value.split(',') : [];

          if (!dateStartStr || !dateEndStr) {
            return 'Error: "value" of operator BETWEEN must be enter dateStart and dateEnd';
          }

          const dateStart = new Date(dateStartStr);
          const dateEnd = new Date(dateEndStr);
          if (dateStart >= dateEnd) {
            return 'Error: "value" of operator BETWEEN: dateStart must be less than dateEnd';
          }
        }
      }
    }

    return '';
  };

  const ruleExists = (rules: RuleType[], fieldName: string) => {
    return rules.some((item) => item.field === fieldName);
  };

  useEffect(() => {
    if (query) {
      setShowBtnExport(false);
      // validateQuery(query as AdvanceSearchData);
    }
    if (query && fieldChange.fieldName && fieldChange.path) {
      const groupIdx = fieldChange.path[0];
      const ruleGroup = query.rules[groupIdx] as RuleGroupType;
      const rules = ruleGroup.rules as RuleType[];

      if (fieldChange.fieldName === 'relationshipType') {
        if (!ruleExists(rules, 'toCi')) {
          setQuery(add(query, { field: 'toCi', operator: '=', value: '' }, [groupIdx]));
        }
      }
      if (['fromCi', 'toCi'].includes(fieldChange.fieldName)) {
        if (!ruleExists(rules, 'relationshipType')) {
          setQuery(add(query, { field: 'relationshipType', operator: '=', value: '' }, [groupIdx]));
        }
      }
      setFieldChange({});
    }
  }, [query, fieldChange]);

  const getQueryWithoutId = useCallback(
    (queryRule?: RuleGroupType) => {
      const result = transformQuery(queryRule || query, { ruleProcessor });
      const jsonString = JSON.stringify(result, ['combinator', 'rules', 'field', 'operator', 'value', 'group', 'fieldType', 'disabled', 'not']);
      const queryWithoutId: AdvanceSearchData = JSON.parse(jsonString);
      return queryWithoutId;
    },
    [query, ruleProcessor],
  );

  // execute when init data without component
  useEffect(() => {
    if (isInit && initialQuery && onSearch) {
      const dataSearch = getQueryWithoutId();
      onSearch(dataSearch);
      setIsInit(false);
    }
  }, [getQueryWithoutId, initialQuery, isInit, onSearch]);

  // execute Export CI
  const [openedModalExportFile, { close: closeModalExportFile, open: openModalExportFile }] = useDisclosure(false);

  const exportFileRef = useRef<ExportFileMethods | null>(null);

  const [attributeInfos, setAttributeInfos] = useState<AttributeInfoEleDTO[]>([]);

  const exportFile = () => {
    const exportData = exportFileRef.current?.getExportFileData();
    const attributeInfoList = exportData?.attributeInfoList ?? [];
    const updatedAttributeInfoList: AttributeInfoDTO[] = attributeInfoList.map((item, index) => ({
      ...item,
      position: index,
    }));

    const ciTypeId = props.ciTypeId ? props.ciTypeId : 0;
    if (props.queryRequestModel) {
      const queryWithoutId = getQueryWithoutId();
      const messageValid = validateQuery(queryWithoutId);
      if (messageValid) {
        NotificationError({
          title: 'Invalid data when execute advance search',
          message: messageValid,
        });
        return;
      }

      if (updatedAttributeInfoList.length === 0) {
        NotificationError({
          message: 'You have to select one or more attributes to export',
        });
        return;
      }

      const exportFileRequest: ExportFileRequest = {
        attributeInfoList: updatedAttributeInfoList,
        searchData: { ...props.queryRequestModel, advanceSearch: queryWithoutId },
        typeFile: exportData?.typeFile ?? EXCEL_TYPE,
      };

      // ConfigItemTypeApi.exportAllCiInCiTypes(ciTypeId, exportFileRequest)
      //   .then((res) => {
      //     exportFileFromBase64(res.data, generateFileExportName());
      //   })
      //   .catch(() => {});
      ConfigItemTypeApi.exportAllCiInCiTypes(ciTypeId, exportFileRequest)
        .then((res) => {
          if (res instanceof Blob) {
            fileDownload(res, generateFileExportName());
          }
        })
        .catch(() => {});
    }
  };

  const openModalExportFileFunc = () => {
    const ciTypeId = props.ciTypeId ? props.ciTypeId : 0;
    if (props.queryRequestModel) {
      const queryWithoutId = getQueryWithoutId();
      const messageValid = validateQuery(queryWithoutId);
      if (messageValid) {
        NotificationError({
          title: 'Invalid data when execute advance search',
          message: messageValid,
        });
        return;
      }
      ConfigItemTypeApi.getAllAttributeCisInCiType(ciTypeId, props.queryRequestModel, queryWithoutId)
        .then((res) => {
          setAttributeInfos(res.data);
          openModalExportFile();
        })
        .catch(() => {});
    }
  };

  // const closeModalExportFileFunc = ()=>{
  //     setShowBtnExport(false);
  //     closeModalExportFile();
  // };

  // execute Load Advanced search
  const ciAdvancedSearchDefault: CiAdvancedSearchModel = {
    id: 0,
    name: '',
    description: '',
    dataQuery: '',
    userName: '',
  };
  const [openedModalLoadAdvanceSearch, { close: closeModalLoadAdvanceSearch, open: openModalLoadAdvanceSearch }] = useDisclosure(false);
  const [showLoadButton, setShowLoadButton] = useState<boolean>(false);
  const [dataSelected, setDataSelected] = useState<CiAdvancedSearchModel>(ciAdvancedSearchDefault);
  const [openedModalConfirmOverride, { close: closeModalConfirmOverride, open: openModalConfirmOverride }] = useDisclosure(false);
  const [openedModalCreateAdvancedSearch, { close: closeModalCreateAdvancedSearch, open: openModalCreateAdvancedSearch }] = useDisclosure(false);
  const [statusSavedAvancedSearch, setStatusSavedAvancedSearch] = useState<string>(StatusSavedAdvanceSearch.IS_CREATE);
  const defaultData = {
    id: 0,
    name: '',
    description: '',
    dataQuery: '',
  };
  const [advancedSearchFormData, setAdvancedSearchFormData] = useState<CiAdvancedSearchDTO>(defaultData);

  // Update check if is loaded then show name
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const [totalSelectedLoadSearch, setTotalSelectedLoadSearch] = useState<number>(0);

  const updateTotalSelectedLoadSearch = (total: number) => {
    setTotalSelectedLoadSearch(total);
  };

  const updateAdvancedSearchFormData = (newFormData: CiAdvancedSearchDTO) => {
    setAdvancedSearchFormData(newFormData);
  };
  const loadAdvancedSearch = () => {
    setQuery(JSON.parse(dataSelected.dataQuery) as RuleGroupType);
    setIsLoaded(true);
    closeModalLoadAdvanceSearch();
  };
  const updateShowLoadButton = (isShow: boolean) => {
    setShowLoadButton(isShow);
  };
  const updateDataSelected = (datas: CiAdvancedSearchModel) => {
    setDataSelected(datas);
  };

  const executeEventConfirmCreateAdvancedSearch = () => {
    const queryWithoutId = getQueryWithoutId();
    advancedSearchFormData.dataQuery = JSON.stringify(queryWithoutId);
    CiAdvancedSearchApi.saveOrUpdateAdvancedSearch(advancedSearchFormData)
      .then((res) => {
        NotificationSuccess({
          message: 'Saved successfully',
        });
        // setAdvancedSearchFormData(defaultData);
        if (statusSavedAvancedSearch === StatusSavedAdvanceSearch.IS_UPDATE) {
          setDataSelected((prevState) => ({
            ...prevState,
            description: res.data.description,
            name: res.data.name,
            id: res.data.id,
          }));
        }
        if (statusSavedAvancedSearch === StatusSavedAdvanceSearch.IS_OVERRIDE) {
          setDataSelected((prevState) => ({
            ...prevState,
            id: advancedSearchFormData.id,
            name: res.data.name,
          }));
        }
        closeModalCreateAdvancedSearch();
      })
      .catch(() => {});
  };

  const onConfirmOverride = () => {
    executeEventConfirmCreateAdvancedSearch();
    closeModalConfirmOverride();
  };

  const onConfirmCreateAdvancedSearch = () => {
    CiAdvancedSearchApi.findByConditions(advancedSearchFormData.id, advancedSearchFormData.name, advancedSearchFormData.ciTypeId)
      .then((res) => {
        if (res.data) {
          setStatusSavedAvancedSearch(StatusSavedAdvanceSearch.IS_OVERRIDE);
          openModalConfirmOverride();
        } else {
          // const isUpdate = CIAdvancedSearchService.checkIsUpdate(advancedSearchFormData.id,dataSelected.name, advancedSearchFormData.name );
          executeEventConfirmCreateAdvancedSearch();
        }
      })
      .catch(() => {});
  };

  const executeWhenOpenModalCreateAdvancedSearch = () => {
    if (dataSelected.id > 0) {
      const advancedSearchDTO: CiAdvancedSearchDTO = CIAdvancedSearchService.convertToDTO(dataSelected);
      setAdvancedSearchFormData(advancedSearchDTO);
      setStatusSavedAvancedSearch(StatusSavedAdvanceSearch.IS_UPDATE);
    } else {
      setAdvancedSearchFormData({
        ...defaultData,
        ciTypeId: ciTypeId === 0 ? undefined : ciTypeId,
      });
      setStatusSavedAvancedSearch(StatusSavedAdvanceSearch.IS_CREATE);
    }
    openModalCreateAdvancedSearch();
  };
  const executeWhenOpenLoadAdvancedSearch = () => {
    setShowLoadButton(false);
    openModalLoadAdvanceSearch();
  };

  return (
    <>
      <KanbanConfirmModal
        modalProps={{ size: 'lg', zIndex: 1000 }}
        title={'Confirm Override Data'}
        onConfirm={onConfirmOverride}
        onClose={closeModalConfirmOverride}
        opened={openedModalConfirmOverride}>
        <KanbanText>Name Advanced Search is existed. Would you like override data? </KanbanText>
      </KanbanConfirmModal>

      <KanbanConfirmModal
        modalProps={{ size: 'lg' }}
        title={'Save Advanced Search'}
        onConfirm={onConfirmCreateAdvancedSearch}
        onClose={closeModalCreateAdvancedSearch}
        opened={openedModalCreateAdvancedSearch}
        disabledConfirmButton={!advancedSearchFormData.name || advancedSearchFormData.name.trim() === ''}>
        <AdvancedSearchForm
          ciAdvancedSearchInfo={advancedSearchFormData}
          updateAdvancedSearchFormData={updateAdvancedSearchFormData}></AdvancedSearchForm>
      </KanbanConfirmModal>

      <KanbanModal
        size={'60%'}
        opened={openedModalLoadAdvanceSearch}
        onClose={() => {
          const check = totalSelectedLoadSearch > 0 && isLoaded;
          setIsLoaded(check);
          closeModalLoadAdvanceSearch();
        }}
        title={'Load Advance Search'}
        actions={
          <KanbanButton disabled={!showLoadButton} leftSection={<IconLoader />} onClick={loadAdvancedSearch}>
            Load
          </KanbanButton>
        }>
        <ListAdvancedSearchComponent
          updateShowLoadButton={updateShowLoadButton}
          updateDataSelected={updateDataSelected}
          updateTotalSelectedLoadSearch={updateTotalSelectedLoadSearch}
          ciTypeId={ciTypeId}></ListAdvancedSearchComponent>
      </KanbanModal>

      <KanbanModal
        size={'50%'}
        opened={openedModalExportFile}
        onClose={closeModalExportFile}
        title={'Export File'}
        actions={<KanbanButton onClick={exportFile}>Export</KanbanButton>}>
        <ExportFileComponent ref={exportFileRef} attributeOfCis={attributeInfos} />
      </KanbanModal>
      <Card bg={'gray.0'} mb={'md'}>
        <Container fluid w={'100%'} p={0}>
          <Flex gap='sm' justify='center' align='center' direction='row' wrap='wrap' mb={'xs'}>
            <KanbanTitle order={4}>Advance Search</KanbanTitle>
            {dataSelected.name && isLoaded && (
              <Flex gap='sm' justify='center' align='center' direction='row' wrap='wrap'>
                <KanbanText size='lg'>-</KanbanText>
                <KanbanText c={'red.7'} size='lg'>
                  {dataSelected.name}
                </KanbanText>
              </Flex>
            )}
          </Flex>
          <ScrollArea.Autosize mah={300}>
            <QueryBuilderMantine>
              <QueryBuilder
                fields={props.fields}
                query={query}
                onQueryChange={(x) => {
                  setQuery(x);
                }}
                controlClassnames={{
                  ruleGroup: styles['ruleGroup'],
                  body: styles['ruleGroup-body'],
                  header: styles['ruleGroup-header'],
                  rule: styles['rule'],
                }}
                getOperators={getOperators}
                controlElements={{
                  fieldSelector: MemoizedControlEditor,
                  addGroupAction: addGroupAction,
                  removeRuleAction: removeRuleAction,
                  removeGroupAction: removeGroupAction,
                  addRuleAction: addRuleAction,
                  valueEditor: MemoizedValueEditor,
                  operatorSelector: operatorSelectorCustom,
                }}
              />
            </QueryBuilderMantine>
          </ScrollArea.Autosize>
          <Group justify='space-between' mt={'sm'}>
            <Box>
              <KanbanButton
                mr='xs'
                leftSection={<IconClearAll />}
                variant={'outline'}
                color={'red'}
                size={'xs'}
                onClick={() => {
                  setQuery(initialQuery || initialQueryDefault);
                  setDataSelected(ciAdvancedSearchDefault);
                  setIsLoaded(false);
                }}>
                Reset
              </KanbanButton>
              <KanbanButton
                mr='xs'
                size={'xs'}
                leftSection={<IconPlus />}
                variant='outline'
                color='cyan'
                onClick={executeWhenOpenModalCreateAdvancedSearch}>
                Save
              </KanbanButton>
              <KanbanButton mr='xs' size={'xs'} leftSection={<IconLoader />} variant='outline' onClick={executeWhenOpenLoadAdvancedSearch}>
                Load
              </KanbanButton>

              <KanbanButton
                leftSection={<IconSearch />}
                size={'xs'}
                onClick={() => {
                  if (props.onSearch) {
                    const queryWithoutId = getQueryWithoutId();
                    console.info('queryWithoutId', queryWithoutId);
                    const messageValid = validateQuery(queryWithoutId);
                    if (messageValid) {
                      NotificationError({
                        title: 'Invalid data',
                        message: messageValid,
                      });
                      return;
                    }
                    props.onSearch(queryWithoutId);
                  }
                }}>
                Apply
              </KanbanButton>
            </Box>
            {isSuperAdmin && (
              <Box>
                <KanbanButton
                  size={'xs'}
                  disabled={!isShowBtnExport}
                  leftSection={<IconFileExport />}
                  color={'cyan'}
                  onClick={openModalExportFileFunc}>
                  Export
                </KanbanButton>
              </Box>
            )}
          </Group>
        </Container>
      </Card>
    </>
  );
});
AdvanceSearchComponent.displayName = 'AdvanceSearchComponent';
export default AdvanceSearchComponent;
