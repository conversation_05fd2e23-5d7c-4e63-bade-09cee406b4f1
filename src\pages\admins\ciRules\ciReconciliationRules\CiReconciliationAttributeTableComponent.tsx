import React, { useCallback, useMemo } from 'react';
import { KanbanTable, type ColumnType, type KanbanTableProps } from 'kanban-design-system';
import { KanbanText } from 'kanban-design-system';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { Badge } from '@mantine/core';
import { CiReconciliationAttribute, type CiReconciliationEntryDto } from '@models/CiReconciliationRule';
import { CiTypeAttributeDto } from '../DnDAttributesComponent';
import { DiscoverySourceDataModel } from '@models/discovery/DiscoverySourceData';
import {
  getDiscoverySourceDataName,
  renderActive,
  renderDiscoverySourceDataName,
  renderEntryName,
  renderValueOrNone,
} from './CiReconciliationRuleComponent';
import { getTextTimeUnit } from '@common/constants/TimeUnitEnum';

interface ImpactedChangeTableComponentProps {
  listEntry: CiReconciliationEntryDto[];
  listAttributes: CiTypeAttributeDto[];
  allowEdit: boolean;
  discoverySourceData: DiscoverySourceDataModel[];
}

const useReconciliationAttributes = (listEntry: CiReconciliationEntryDto[], listAttributes: CiTypeAttributeDto[]): CiReconciliationAttribute[] => {
  return useMemo(() => {
    const attributesMap = new Map<number, any>();
    const attributesFromEntries: CiReconciliationAttribute[] = [];

    const createAttributeObj = (attrId: number, attrName: string, entry: CiReconciliationEntryDto): CiReconciliationAttribute => ({
      attributeId: attrId,
      nameAttribute: attrName,
      nameEntry: entry.name,
      active: renderActive(entry.active),
      priority: entry.priority,
      defaultEntry: entry.defaultEntry,
      discoverySourceDataId: entry.discoverySourceDataId,
      timeoutData: entry.timeoutData,
      timeUnit: entry.timeUnit,
    });

    listEntry.forEach((entry) => {
      if (entry.applyAllAttribute) {
        listAttributes.forEach((attr) => {
          if (!attr.deleted) {
            const attrObj = createAttributeObj(attr.attributeId, attr.nameAttribute || '', entry);

            attributesMap.set(attr.attributeId, attr);
            attributesFromEntries.push(attrObj);
          }
        });
      } else {
        (entry.attributes || []).forEach((attr) => {
          const attrName = attr.ciTypeAttributeName || attr.name || '';
          const attrObj = createAttributeObj(attr.ciTypeAttributeId, attrName, entry);

          attributesMap.set(attr.ciTypeAttributeId, attr);
          attributesFromEntries.push(attrObj);
        });
      }
    });

    const attributesFromList = listAttributes
      .filter((attr) => !attributesMap.has(attr.attributeId))
      .filter((attr) => !attr.deleted)
      .map((attr) => ({
        attributeId: attr.attributeId,
        nameAttribute: attr.nameAttribute,
        nameDiscoverySource: '(none)',
      }));

    return [...attributesFromEntries, ...attributesFromList];
  }, [listAttributes, listEntry]);
};

export const CiReconciliationAttributeTableComponent = (props: ImpactedChangeTableComponentProps) => {
  const { allowEdit, discoverySourceData, listAttributes, listEntry } = props;

  const onSearched = useCallback(
    (datas: CiReconciliationAttribute[], search: string): CiReconciliationAttribute[] => {
      const lowerCaseSearch = search.toLowerCase();

      const dataSearch = datas.filter((item) => {
        const listKeyAttribute = listAttributes.filter((attr) => item.attributeId === attr.attributeId);

        const dataAttributes = (listKeyAttribute || [])
          .map((x) => (x.deleted ? `${x.nameAttribute} - (Deleted)` : x.nameAttribute))
          .join(', ')
          .toLowerCase();
        return (
          item.nameAttribute?.toLowerCase().includes(lowerCaseSearch) ||
          item.nameEntry?.toLowerCase().includes(lowerCaseSearch) ||
          getDiscoverySourceDataName(item.discoverySourceDataId || 0, discoverySourceData)
            ?.toLowerCase()
            .includes(lowerCaseSearch) ||
          item.priority?.toString().includes(lowerCaseSearch) ||
          item.timeoutData?.toString().includes(lowerCaseSearch) ||
          item.active?.toLowerCase().includes(lowerCaseSearch) ||
          dataAttributes?.includes(lowerCaseSearch)
        );
      });

      const attributesMap = new Map<number, number>();
      dataSearch.forEach((item) => {
        const currentCount = attributesMap.get(item.attributeId) || 0;
        attributesMap.set(item.attributeId, currentCount + 1);
      });

      dataSearch.sort((a, b) => b.attributeId - a.attributeId);
      dataSearch.forEach((item) => {
        item._rowSpan = attributesMap.get(item.attributeId);
      });

      return dataSearch.sort((a, b) => (a.priority || 0) - (b.priority || 0)).sort((a, b) => a.nameAttribute.localeCompare(b.nameAttribute));
    },
    [discoverySourceData, listAttributes],
  );

  const renderAttribute = useCallback(
    (data: CiReconciliationAttribute) => {
      const listKeyAttribute = listAttributes.filter((attr) => data.attributeId === attr.attributeId);
      return listKeyAttribute.map((item, index) => {
        const isDeleted = item.deleted;
        const badgeColor = isDeleted ? 'red' : 'blue';
        const badgeText = isDeleted ? `${item.nameAttribute} - (Deleted)` : item.nameAttribute;

        return (
          <Badge key={index} variant='light' color={badgeColor} radius='sm' mr='5'>
            <KanbanText fw={500} size='sm' tt='initial'>
              {badgeText}
            </KanbanText>
          </Badge>
        );
      });
    },
    [listAttributes],
  );

  const reconciliationData = useReconciliationAttributes(listEntry, listAttributes);

  const columns: ColumnType<CiReconciliationAttribute>[] = useMemo(() => {
    return [
      {
        title: 'Attributes',
        name: 'nameAttribute',
        customRender: (data, rowData) => {
          return {
            value: renderAttribute(rowData),
            customProps: {
              rowSpan: rowData._rowSpan,
            },
          };
        },
      },
      {
        title: 'Active',
        name: 'active',
        width: '10%',
        customRender: (data) => {
          if (!data) {
            return '(none)';
          }
          const isCheck = 'Active' === data;
          return <KanbanText c={isCheck ? 'green' : 'red'}>{data}</KanbanText>;
        },
      },
      {
        title: 'Data Source',
        name: 'discoverySourceDataId',
        customRender: (data) => {
          return renderDiscoverySourceDataName(data, discoverySourceData);
        },
      },
      {
        title: 'Entry',
        name: 'nameEntry',
        customRender: (_, rowData) => {
          return renderEntryName(rowData.defaultEntry || false, rowData.nameEntry || '');
        },
      },
      {
        title: 'Priority',
        name: 'priority',
        width: '10%',
        customRender: (data) => {
          return renderValueOrNone(data);
        },
      },
      {
        title: 'Timeout data',
        name: 'timeoutData',
        width: '10%',
        customRender: (data, rowData) => {
          return `${renderValueOrNone(data)} ${getTextTimeUnit(rowData.timeUnit) ?? ''}`;
        },
      },
    ];
  }, [discoverySourceData, renderAttribute]);

  const tableProps: KanbanTableProps<CiReconciliationAttribute> = useMemo(() => {
    return {
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 300,
        onSearched: onSearched,
      },
      sortable: {
        enable: false,
      },
      columns: columns,
      data: reconciliationData,
      pagination: {
        enable: false,
      },
      selectableRows: {
        enable: false,
      },
    };
  }, [columns, onSearched, reconciliationData]);

  const customTableProps = useMemo(() => {
    const tablePropsUpdate = { ...tableProps };
    if (!allowEdit) {
      const { actions, selectableRows, ...rest } = tablePropsUpdate;
      if (selectableRows) {
        selectableRows.enable = false;
      }
      if (actions && actions.deletable) {
        delete actions.deletable;
      }

      return { ...rest, selectableRows, actions };
    }

    return tablePropsUpdate;
  }, [allowEdit, tableProps]);

  return (
    <>
      <HeaderTitleComponent title='List of attributes' />
      <KanbanTable {...customTableProps} />
    </>
  );
};

CiReconciliationAttributeTableComponent.whyDidYouRender = true;
CiReconciliationAttributeTableComponent.displayName = 'CiReconciliationAttributeTableComponent';
export default CiReconciliationAttributeTableComponent;
