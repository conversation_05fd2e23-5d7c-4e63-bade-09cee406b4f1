import { KanbanTable, ColumnType, KanbanTooltip, KanbanText, KanbanInput } from 'kanban-design-system';
import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { ConfigItemTypeAttrApi } from '@api/ConfigItemTypeAttrApi';
import { ConfigItemTypeAttrModel } from '@models/ConfigItemTypeAttr';
import { CiTypeAttributeDataType } from '@models/CiType';
import { Badge, Flex } from '@mantine/core';
import classes from '../CIType.module.scss';
type ListCiTypeAttributeProps = {
  ciTypeId?: number;
  dataAttributeSelected: ConfigItemTypeAttrModel[];
  updateDataAttributeSelected: (datas: ConfigItemTypeAttrModel[]) => void;
  updateListDataAttribute: (datas: ConfigItemTypeAttrModel[]) => void;
};

export const ListCiTypeAttributeComponent = (props: ListCiTypeAttributeProps) => {
  const { ciTypeId, dataAttributeSelected, updateDataAttributeSelected, updateListDataAttribute } = props;
  const [listData, setListData] = useState<ConfigItemTypeAttrModel[]>([]);
  const [listDataSearch, setListDataSearch] = useState<ConfigItemTypeAttrModel[]>([]);
  const [currentAttributeSelected, setCurrentAttributeSelected] = useState<ConfigItemTypeAttrModel[]>(dataAttributeSelected);

  const columns: ColumnType<ConfigItemTypeAttrModel>[] = useMemo(() => {
    const cols = [
      {
        title: 'Attribute Name',
        name: 'name',
        customRender: (data, rowData) => {
          if (!data) {
            return rowData.ciTypeId === 0 ? '(Default Attribute)' : '';
          }
          return (
            <KanbanTooltip
              bd={'1px solid rgba(0, 119, 255, 0.8)'}
              bg={'white'}
              fs={'italic'}
              c={'var(--mantine-color-blue-4)'}
              label={data}
              style={{ flexShrink: 0, wordBreak: 'break-all' }}
              maw={'450px'}
              multiline>
              <Badge variant='transparent' color='black' radius='sm'>
                <KanbanText size='sm' tt='initial' lineClamp={1}>
                  {data}
                </KanbanText>
              </Badge>
            </KanbanTooltip>
          );
        },
      },
      {
        title: 'Ci Type',
        name: 'ciTypeName',
        width: '45%',
        customRender: (data, rowData) => {
          if (!data) {
            return rowData.ciTypeId === 0 ? '(Default Attribute)' : '';
          }
          return (
            <KanbanTooltip
              maw={'40%'}
              bd={'1px solid rgba(0, 119, 255, 0.8)'}
              bg={'white'}
              fs={'italic'}
              c={'var(--mantine-color-blue-4)'}
              label={data}
              style={{ flexShrink: 0 }}
              multiline>
              <Badge variant='light' color='blue' radius='sm' mr={5}>
                <KanbanText size='sm' tt='initial' lineClamp={1}>
                  {data}
                </KanbanText>
              </Badge>
            </KanbanTooltip>
          );
        },
      },
    ] as ColumnType<ConfigItemTypeAttrModel>[];

    return cols as ColumnType<ConfigItemTypeAttrModel>[];
  }, []);

  useEffect(() => {
    ConfigItemTypeAttrApi.getSuggestCiTypeAttribute(ciTypeId && ciTypeId !== 0 ? ciTypeId : undefined)
      .then((res) => {
        if (res?.data) {
          const listAttributeDefault = res.data.filter((item) => item.ciTypeId === 0);
          const listAttributeCustom = res.data.filter((item) => CiTypeAttributeDataType.REFERENCE !== item.type && item.ciTypeId !== 0);

          const mergedList = [...listAttributeDefault, ...listAttributeCustom];
          setListData(mergedList);
          setListDataSearch(mergedList);
        }
      })
      .catch(() => {});
  }, [ciTypeId]);

  useEffect(() => {
    updateListDataAttribute(listData);
  }, [listData, updateListDataAttribute]);

  const onSearched = useCallback(
    (search: string) => {
      const lowerSearch = search.trim().toLowerCase();
      if (!lowerSearch) {
        setListDataSearch(listData);
        return;
      }
      const dataSearch = listData.filter(({ ciTypeId, ciTypeName = '', name = '' }) => {
        const targetName = name.toLowerCase();
        const targetType = (ciTypeId === 0 ? '(Default Attribute)' : ciTypeName).toLowerCase();
        return targetName.includes(lowerSearch) || targetType.includes(lowerSearch);
      });
      setListDataSearch(dataSearch);
    },
    [listData],
  );

  return (
    <>
      <Flex justify={'right'}>
        <KanbanInput
          size='xs'
          placeholder='Search by any field'
          maxLength={4000}
          withAsterisk
          onChange={(event) => {
            const value = event.target.value ?? '';
            onSearched(value);
          }}
        />
      </Flex>

      <KanbanTable
        columns={columns}
        key={1}
        data={listDataSearch}
        showNumericalOrderColumn={false}
        searchable={{
          enable: false,
        }}
        actions={{}}
        pagination={{
          enable: true,
        }}
        showTopBar={false}
        selectableRows={{
          enable: true,
          maxSelection: 20,
          onSelectedRowsChanged(rows) {
            updateDataAttributeSelected(rows.filter((row) => listData.some((item) => item.hashId === row.hashId)));
          },
          crossPageSelected: {
            rowKey: 'hashId',
            selectedRows: currentAttributeSelected.filter((row) => listData.some((item) => item.hashId === row.hashId)),
            setSelectedRows: setCurrentAttributeSelected,
          },
        }}
        tableWrapperClasses={`${classes.customTableWrapper} `}
      />
    </>
  );
};
export default ListCiTypeAttributeComponent;
