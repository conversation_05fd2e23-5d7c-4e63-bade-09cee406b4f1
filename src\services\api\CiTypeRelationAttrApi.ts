import type { ApiResponseDataBase } from '@core/api/ApiResponse';
import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import type { CiTypeRelationAttrModel } from '@models/CiTypeRelation';

export type CiTypeRelationAttrResponse = ApiResponseDataBase & CiTypeRelationAttrModel;

export class CiTypeRelationAttrApi extends BaseApi {
  static baseUrl = BaseUrl.ciTypeRelationshipAttributes;
}
