import type { ConfigItemInfoModel } from '@models/ConfigItem';
import { KanbanButton, KanbanTabs, KanbanText, renderDateTime } from 'kanban-design-system';

import React, { Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import type { ConfigItemTypeAttrResponse } from '@api/ConfigItemTypeAttrApi';
import type { ConfigItemAttrModel } from '@models/ConfigItemAttr';
import { configItemTypeAttrSorted } from '@common/utils/CiUtils';
import CiInfo from '../../cis/ci/CiInfo';
import CiNoticeChange from '../../cis/ciManagement/detail/CiNoticeChange';
import type { DataCiCompare } from '@models/CiDetailCompare';
import { DiscoveryPreviewDataCiApi } from '@api/discovery/DiscoveryPreviewDataCiApi';
import { CiManagementExecute } from '@service/CiManagementExecute';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { DiscoveryPreviewDataCiModel } from '@models/DiscoveryPreviewDataCi';
import { DiscoveryPreviewDataCiAction } from '@common/constants/DiscoveryPreviewDataCiConstants';
import { Flex } from '@mantine/core';
import { DEFAULT_ID_FOR_CI_DESCRIPTION, DEFAULT_ID_FOR_CI_NAME } from '@common/constants/CommonConstants';
type DiscoveryCiDataDetailPageProps = {
  idProp?: number;
  onClosedPopupDetail?: () => void;
};
export const DiscoveryCiDataDetailPage: React.FC<DiscoveryCiDataDetailPageProps> = ({ idProp, onClosedPopupDetail }) => {
  const { id } = useParams();

  const [listCiTypeAttribute, setListCiTypeAttribute] = useState<ConfigItemTypeAttrResponse[]>([]);
  const [discoveredCiAttributes, setDiscoveredCiAttributes] = useState<ConfigItemAttrModel[]>([]);

  const [ciLive, setCiLive] = useState<ConfigItemInfoModel | undefined>();
  const [previewDci, setPreviewDci] = useState<DiscoveryPreviewDataCiModel>();

  const fetchDiscoveryDataCiDetail = useCallback(() => {
    if (!id && !idProp) {
      return;
    }
    const idDci = id || idProp;
    DiscoveryPreviewDataCiApi.getById(Number(idDci))
      .then((res) => {
        const resData = res.data;
        if (resData) {
          setPreviewDci(resData);
          if (resData.listDataCiDetail) {
            setDiscoveredCiAttributes(
              resData.listDataCiDetail.map((it) => ({
                id: 0,
                ciTypeAttributeId: it.ciTypeAttributeId || 0,
                value: it.ciAttributeValue || '',
                ciId: resData.ciId || 0,
              })),
            );
          }
          //current live ci : 2 case : 1.create main   +    2.update main /create duplicate
          let resLiveCi: ConfigItemInfoModel = {
            ci: { id: 0, ciTypeId: resData.ciTypeId || 0, name: resData.sourceDiscoveryCiName || '' },
            attributes: [],
            attributeCustoms: [],
          };
          if (resData.ciInfo) {
            resLiveCi = resData.ciInfo;
          }
          setCiLive(resLiveCi);

          if (resData.listCiTypeAttribute) {
            // setCiTypeAttributes(resData.listCiTypeAttribute);
            setListCiTypeAttribute(configItemTypeAttrSorted(resData.listCiTypeAttribute));
          }
        }
      })
      .catch(() => {
        if (onClosedPopupDetail) {
          onClosedPopupDetail();
        }
      });
  }, [id, idProp, onClosedPopupDetail]);
  useEffect(() => {
    fetchDiscoveryDataCiDetail();
  }, [fetchDiscoveryDataCiDetail]);

  const differenceObject: DataCiCompare | null = useMemo(() => {
    // const discoverCiName = discoveredCiAttributes.find((it) => DEFAULT_ID_FOR_CI_NAME === it.ciTypeAttributeId)?.value;
    // const discoverCiDescription = discoveredCiAttributes.find((it) => DEFAULT_ID_FOR_CI_DESCRIPTION === it.ciTypeAttributeId)?.value;
    // const discoveredCi: ConfigItemModel = {
    //   id: previewDci?.ciId || 0,
    //   ciTypeId: previewDci?.ciTypeId || 0,
    //   name: discoverCiName || '',
    //   description: discoverCiDescription || '',
    // };
    if (ciLive?.attributes) {
      return {
        // ci: CiManagementExecute.compareObjects(ciLive, discoveredCi),
        // preview-discovery: push info of ci (Name+Description) into CI Attributes info with id = -99, -98
        attributes: CiManagementExecute.compareAttributes(ciLive.attributes, discoveredCiAttributes),
      };
    }
    return null;
  }, [ciLive?.attributes, discoveredCiAttributes]);

  const ciAttributeMapping = useMemo(() => {
    const result: Record<number, ConfigItemAttrModel> = {};

    for (const ciTypeAttribute of listCiTypeAttribute) {
      const discoverAtt = discoveredCiAttributes.find((x) => x.ciTypeAttributeId === ciTypeAttribute.id);
      const liveAtt = ciLive?.attributes.find((x) => x.ciTypeAttributeId === ciTypeAttribute.id);

      const ciTypeEmptyAtt = {
        ciTypeAttributeId: ciTypeAttribute.id,
        id: 0,
        ciId: 0,
        value: '',
      };
      result[ciTypeAttribute.id] = discoverAtt ? discoverAtt : liveAtt ? liveAtt : ciTypeEmptyAtt;
    }

    return result;
  }, [discoveredCiAttributes, listCiTypeAttribute, ciLive?.attributes]);
  const renderCiRightSection = useMemo(() => {
    // Destructure necessary properties from previewDci
    const { action, ciId } = previewDci || {};

    // Determine the action type and corresponding properties
    const isUpdate = DiscoveryPreviewDataCiAction.UPDATE === action && ciId;
    const isCreateNew = DiscoveryPreviewDataCiAction.CREATE === action && !ciId;
    const isCreateDuplicate = DiscoveryPreviewDataCiAction.DUPLICATE === action && ciId;

    // Define common button structure for both actions
    const buttons = (primaryText: string, primaryBg: string, secondaryText: string, secondaryBg: string) => (
      <Flex direction='row' gap='xs' ml='xs'>
        <KanbanButton size='compact-xs' bg={primaryBg}>
          {primaryText}
        </KanbanButton>
        <KanbanButton size='compact-xs' bg={secondaryBg} c='white'>
          {secondaryText}
        </KanbanButton>
      </Flex>
    );

    // Return the correct set of buttons based on the action
    if (isUpdate) {
      return buttons(DiscoveryPreviewDataCiAction.UPDATE, 'yellow', 'main', 'green');
    }

    if (isCreateNew) {
      return buttons(DiscoveryPreviewDataCiAction.CREATE, '', 'main', 'green');
    }

    if (isCreateDuplicate) {
      return buttons(DiscoveryPreviewDataCiAction.DUPLICATE, 'orange', 'main', 'green');
    }

    // Return empty fragment as fallback
    return <></>;
  }, [previewDci]);
  const tabProps = useMemo(() => {
    const discoverCiName = discoveredCiAttributes.find((it) => DEFAULT_ID_FOR_CI_NAME === it.ciTypeAttributeId)?.value;
    const discoverCiDescription = discoveredCiAttributes.find((it) => DEFAULT_ID_FOR_CI_DESCRIPTION === it.ciTypeAttributeId)?.value;
    let updatedNameAndDescriptCi;
    if (ciLive?.ci) {
      updatedNameAndDescriptCi = { ...ciLive?.ci, name: discoverCiName || '', description: discoverCiDescription };
    }
    return {
      configs: {
        defaultValue: '1',
      },
      tabs: {
        '1': {
          title: 'Info',
          content: (
            <div>
              {updatedNameAndDescriptCi && (
                <CiInfo
                  ci={updatedNameAndDescriptCi}
                  hightLightChange={true}
                  differenceObject={differenceObject}
                  ciTypeAttributes={listCiTypeAttribute}
                  ciAttributes={ciAttributeMapping}
                  ciAttributesCustom={[]}
                  hiddenCustomAttribute={true}
                  includeReferenceData={true}
                />
              )}
            </div>
          ),
        },
      },
    };
  }, [ciAttributeMapping, ciLive?.ci, differenceObject, discoveredCiAttributes, listCiTypeAttribute]);

  //case 1.update CI name (id att  = -99) show attribute value of -99, 2.not update att ci name -99 then show sourceDiscoveryCiName
  const renderCiName = useMemo(() => {
    const discoveredCiName = discoveredCiAttributes.find((it) => DEFAULT_ID_FOR_CI_NAME === it.ciTypeAttributeId)?.value;
    if (discoveredCiName) {
      return discoveredCiName;
    } else {
      return previewDci?.sourceDiscoveryCiName;
    }
  }, [discoveredCiAttributes, previewDci?.sourceDiscoveryCiName]);
  return (
    <>
      <Flex direction={'row'} align={'center'} gap={'xs'}>
        <HeaderTitleComponent title={`CI: ${renderCiName} `} rightSection={renderCiRightSection} />
        <KanbanText c={'gray'} mr='xs'>
          Preview ID : {previewDci?.id}
        </KanbanText>
        <KanbanText fs={'italic'} c={'gray'}>
          Time: {renderDateTime(previewDci?.createdDate)}
        </KanbanText>
      </Flex>

      {differenceObject && <CiNoticeChange differenceObject={differenceObject} listCiTypeAttribute={listCiTypeAttribute} />}
      <KanbanTabs {...tabProps} />
    </>
  );
};
DiscoveryCiDataDetailPage.whyDidYouRender = true;
DiscoveryCiDataDetailPage.displayName = 'DiscoveryCiDataDetailPage';
export default DiscoveryCiDataDetailPage;
