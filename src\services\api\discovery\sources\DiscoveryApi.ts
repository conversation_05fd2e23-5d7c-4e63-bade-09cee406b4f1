import { <PERSON><PERSON><PERSON><PERSON> } from './NetboxApi';
import { <PERSON><PERSON><PERSON><PERSON> } from './RancherApi';
import { ApiResponse } from '@core/api/ApiResponse';
import { DiscoverySourceTypeEnum } from '@common/constants/DiscoverySourceTypeEnum';
import { ResourceConfigTypeEnum } from '@common/constants/ResourceConfigTypeEnum';
import { AixHmcApi } from './AixHmcApi';
import { VropsA<PERSON> } from './VropsApi';
import { SolarwindApi } from './SolarwindApi';
import { SolarWindsSourceEnum } from '@common/constants/SolarWindsSourceEnum';
import { SnmpApi } from './SnmpApi';
import { DeviceTypeEnum } from '@common/constants/DeviceTypeEnum';
import { ActiveIqApi } from './ActiveIqApi';
import { SnmpVersionEnum } from '@common/constants/SnmpVersionEnum';
import { F5Api } from './F5Api';
import { DeviceRoleEnum } from '@common/constants/DeviceRoleEnum';
import { K8sApi } from './K8sApi';

export class DiscoveryApi {
  static discoveryData = (sourceType: DiscoverySourceTypeEnum, sourceData: string): Promise<ApiResponse<any>> => {
    switch (sourceType) {
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_K8S_PROJECT:
        return K8sApi.discovery();
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_TANZU:
        return RancherApi.findAllDeploymentByProjectId(sourceData, ResourceConfigTypeEnum.RANCHER_TANZU);
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_MACHINE:
        return NetboxApi.findVirtualMachines();
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_DISK:
        return NetboxApi.findVirtualDisks();
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_CLUSTER:
        return NetboxApi.findVirtualClusters();
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_AIX_HMC:
        return AixHmcApi.findAllDataAixHmc();
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_AIX_HMC_MANAGED_SYSTEM:
        return AixHmcApi.findAllManagedSystem();
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_AIX_HMC_LOGICAL_PARTITION:
        return AixHmcApi.findAllLogicalPartition();
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_DICM_DEVICE:
        return NetboxApi.findDevices();
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_VROPS_CLUSTER:
        return VropsApi.findAllClusters();
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_VROPS_RESOURCES:
        return VropsApi.findAllResources();
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_SOLARWIND_NETWORK_USE_API:
        return SolarwindApi.findAllByApi(SolarWindsSourceEnum.NETWORK);
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_TANZU_NODE:
        return RancherApi.findAllNodes(ResourceConfigTypeEnum.RANCHER_TANZU);
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_TANZU_CLUSTERS:
        return RancherApi.findAllClusters(ResourceConfigTypeEnum.RANCHER_TANZU);
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_SNMP_NETWORK_CHECKPOINT:
        return SnmpApi.findAllDevice(DeviceTypeEnum.CHECKPOINT);
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_SNMP_NETWORK_PALOALTO:
        return SnmpApi.findAllDevice(DeviceTypeEnum.PALOALTO);
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_ACTIVE_IQ_CLUSTER_NODE:
        return ActiveIqApi.findAllClusterNodes();
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_ACTIVE_IQ_STORAGE_VOLUMES:
        return ActiveIqApi.findAllStorageVolumes();
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_F5_SNMP:
        return SnmpApi.findAllDevice(DeviceTypeEnum.F5, SnmpVersionEnum.SNMP_V2);
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_F5_API:
        return F5Api.findAllDevice();
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_SOLARWIND_NETWORK_USE_DB:
        return SolarwindApi.findAllByDb();
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_DICM_DEVICE_ROLE_SERVER:
        return NetboxApi.findDevices(DeviceRoleEnum.SEVER);
      default:
        throw new Error('Unknown source type');
    }
  };
}
