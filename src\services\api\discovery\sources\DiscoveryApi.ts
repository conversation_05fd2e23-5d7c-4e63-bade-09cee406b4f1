import { <PERSON>box<PERSON><PERSON> } from './NetboxApi';
import { <PERSON><PERSON><PERSON><PERSON> } from './RancherApi';
import { ApiResponse } from '@core/api/ApiResponse';
import { DiscoverySourceTypeEnum } from '@common/constants/DiscoverySourceTypeEnum';
import { ResourceConfigTypeEnum } from '@common/constants/ResourceConfigTypeEnum';
import { AixHmcApi } from './AixHmcApi';
import { VropsApi } from './VropsApi';
import { SolarwindApi } from './SolarwindApi';
import { SolarWindsSourceEnum } from '@common/constants/SolarWindsSourceEnum';
import { SnmpApi } from './SnmpApi';
import { DeviceTypeEnum } from '@common/constants/DeviceTypeEnum';
import { ActiveIqApi } from './ActiveIqApi';
import { SnmpVersionEnum } from '@common/constants/SnmpVersionEnum';
import { F5Api } from './F5Api';
import { DeviceRoleEnum } from '@common/constants/DeviceRoleEnum';
import { K8sApi } from './K8sApi';

export class DiscoveryApi {
  static discoveryData = (sourceType: DiscoverySourceTypeEnum, sourceData: string, isFull: boolean = false): Promise<ApiResponse<any>> => {
    let apiPromise: Promise<ApiResponse<any>>;

    switch (sourceType) {
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_K8S_PROJECT:
        apiPromise = K8sApi.discovery();
        break;
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_TANZU:
        apiPromise = RancherApi.findAllDeploymentByProjectId(sourceData, ResourceConfigTypeEnum.RANCHER_TANZU);
        break;
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_MACHINE:
        apiPromise = NetboxApi.findVirtualMachines();
        break;
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_DISK:
        apiPromise = NetboxApi.findVirtualDisks();
        break;
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_CLUSTER:
        apiPromise = NetboxApi.findVirtualClusters();
        break;
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_AIX_HMC:
        apiPromise = AixHmcApi.findAllDataAixHmc();
        break;
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_AIX_HMC_MANAGED_SYSTEM:
        apiPromise = AixHmcApi.findAllManagedSystem();
        break;
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_AIX_HMC_LOGICAL_PARTITION:
        apiPromise = AixHmcApi.findAllLogicalPartition();
        break;
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_DICM_DEVICE:
        apiPromise = NetboxApi.findDevices();
        break;
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_VROPS_CLUSTER:
        apiPromise = VropsApi.findAllClusters();
        break;
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_VROPS_RESOURCES:
        apiPromise = VropsApi.findAllResources();
        break;
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_SOLARWIND_NETWORK_USE_API:
        apiPromise = SolarwindApi.findAllByApi(SolarWindsSourceEnum.NETWORK);
        break;
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_TANZU_NODE:
        apiPromise = RancherApi.findAllNodes(ResourceConfigTypeEnum.RANCHER_TANZU);
        break;
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_TANZU_CLUSTERS:
        apiPromise = RancherApi.findAllClusters(ResourceConfigTypeEnum.RANCHER_TANZU);
        break;
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_SNMP_NETWORK_CHECKPOINT:
        apiPromise = SnmpApi.findAllDevice(DeviceTypeEnum.CHECKPOINT);
        break;
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_SNMP_NETWORK_PALOALTO:
        apiPromise = SnmpApi.findAllDevice(DeviceTypeEnum.PALOALTO);
        break;
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_ACTIVE_IQ_CLUSTER_NODE:
        apiPromise = ActiveIqApi.findAllClusterNodes();
        break;
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_ACTIVE_IQ_STORAGE_VOLUMES:
        apiPromise = ActiveIqApi.findAllStorageVolumes();
        break;
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_F5_SNMP:
        apiPromise = SnmpApi.findAllDevice(DeviceTypeEnum.F5, SnmpVersionEnum.SNMP_V2);
        break;
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_F5_API:
        apiPromise = F5Api.findAllDevice();
        break;
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_SOLARWIND_NETWORK_USE_DB:
        apiPromise = SolarwindApi.findAllByDb();
        break;
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_DICM_DEVICE_ROLE_SERVER:
        apiPromise = NetboxApi.findDevices(DeviceRoleEnum.SEVER);
        break;
      default:
        throw new Error('Unknown source type');
    }

    // If isFull is false, limit the data to 10 records
    if (!isFull) {
      return apiPromise.then((response) => {
        if (response.status === 200 && Array.isArray(response.data)) {
          return {
            ...response,
            data: response.data.slice(0, 10),
          };
        }
        return response;
      });
    }

    return apiPromise;
  };
}
