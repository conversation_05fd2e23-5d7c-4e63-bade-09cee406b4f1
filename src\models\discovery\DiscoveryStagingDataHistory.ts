import type { EntityModelBase } from '@models/EntityModelBase';

export type DiscoveryStagingDataHistoryModel = EntityModelBase & {
  id: number;
  totalRecordSuccess: number;
  totalRecordFail: number;
  dataSourceId: number;
  staggingTableId: number;
  staggingVersionId: number;
  discoveryStagingVersionId: number;
  jobStartDate: Date;
  jobEndDate: Date;
  statusJob: string;
  totalTimeExcuteJob: number;
};

export type DiscoveryStagingDataHistoryResponse = DiscoveryStagingDataHistoryModel;
