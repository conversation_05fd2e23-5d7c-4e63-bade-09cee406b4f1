export const formatStandardName = (input: string) => {
  const words = input.trim().split(/\s+/);
  // if(words.length < 2 && input.toUpperCase() !== input ){
  //     const newInput = input.replace(/([A-Z])/g, ' $1').trim();
  //     words = newInput.split(/\s+/);
  // }

  const output = words.join(' ');

  return output.charAt(0).toUpperCase() + output.slice(1).toLowerCase();
};

export const isBlank = (input: string | undefined | null): boolean => {
  return !input?.trim();
};

export const arraysEqual = (arr1: number[], arr2: number[]) => {
  if (arr1.length !== arr2.length) {
    return false;
  }
  const sortedArr1 = [...arr1].sort();
  const sortedArr2 = [...arr2].sort();
  return sortedArr1.every((value, index) => value === sortedArr2[index]);
};

export const splitSentence = (sentense?: string): string => {
  const MAX_LENGTH = 30;
  const ELIPSIS = '...';
  if (sentense === undefined) {
    return '';
  }
  if (sentense.length <= MAX_LENGTH) {
    return sentense;
  }
  const newSentense = sentense.substring(0, MAX_LENGTH);
  if (sentense[MAX_LENGTH] === '') {
    return newSentense + ELIPSIS;
  }
  if (newSentense.lastIndexOf(' ') !== -1) {
    return newSentense.substring(0, newSentense.lastIndexOf(' ')) + ELIPSIS;
  }
  return newSentense + ELIPSIS;
};
