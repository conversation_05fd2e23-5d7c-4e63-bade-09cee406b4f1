import { TimeUnitEnum } from '@common/constants/TimeUnitEnum';
import { EntityAction } from './AuditLog';
import { Attribute } from './CiDetailCompare';
import type { EntityModelBase } from './EntityModelBase';

export enum CiReconciliationRuleAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  VIEW = 'VIEW',
}

export enum RuleStatus {
  ENABLE = 'ENABLE',
  DISABLE = 'DISABLE',
}

export enum RULE_TAB {
  INFO = 'INFO',
  HISTORY = 'HISTORY',
}

export enum DataSourceType {
  MANUAL = 'MANUAL',
  ANY = 'ANY',
}

export const DataSourceMeta: Record<DataSourceType, { id: number; name: string }> = {
  [DataSourceType.MANUAL]: { id: -99, name: 'Manual' },
  [DataSourceType.ANY]: { id: 0, name: 'Any' },
};

export const DATA_SOURCE_MANUAL = DataSourceMeta[DataSourceType.MANUAL];
export const DATA_SOURCE_ANY = DataSourceMeta[DataSourceType.ANY];

export type CiReconciliationRule = EntityModelBase & {
  name: string;
  active: RuleStatus;
  ciTypeId: number;
  description?: string;
  skipDuplicate: boolean;
  applyToChild: boolean;
  ciTypeName?: string;
};

export type CiReconciliationEntry = EntityModelBase & {
  name: string;
  active: RuleStatus;
  ciReconciliationRuleId: number;
  priority?: number;
  defaultEntry: boolean;
  discoverySourceDataId?: number;
  timeUnit?: TimeUnitEnum;
  timeoutData?: number;
  applyAllAttribute?: boolean;
};

export type CiReconciliationEntryAttribute = EntityModelBase & {
  ciReconciliationEntryId: number;
  ciTypeAttributeId: number;
  ciTypeAttributeName?: string;
};

export type CiReconciliationEntryAttributeDto = CiReconciliationEntryAttribute & {
  name?: string;
};

export type CiReconciliationEntryDto = CiReconciliationEntry & {
  attributes?: CiReconciliationEntryAttributeDto[];
  tempId: string;
  onEditPriority?: boolean;
  onEditTimeoutData?: boolean;
};

export type CiReconciliationRuleDto = CiReconciliationRule & {
  listEntries?: CiReconciliationEntryDto[];
  listEntriesCreate?: CiReconciliationEntryDto[];
  listEntriesUpdate?: CiReconciliationEntryDto[];
  listEntriesDelete?: CiReconciliationEntryDto[];
};

export type CiReconciliationLogDto = {
  id: number;
  logType: string;
  logLevel: string;
  ciName: string;
  ciId: number;
  createdDate: string;
  message: string;
};

export type CustomAttributeCompareModel = {
  oldInfo: { [key: string]: string };
  newInfo: { [key: string]: string };
  mapChildren: { [key: string]: CustomAttributeCompareModel };
  mapSelfChange: Attribute;
  action: EntityAction;
  titleAction: string;
};

export type DataCiRuleCompare = {
  ciReconciliationRuleEntry?: CustomAttributeCompareModel;
  ciReconciliationRule?: CustomAttributeCompareModel;
};

export type DataChangeWithAction = {
  key: string;
  oldValue?: string;
  newValue?: string | number;
  action?: EntityAction;
};
export type DataChangeLstWithActionObj = {
  dataChange?: DataChangeWithAction[];
  action?: EntityAction;
  titleAction?: string;
  oldInfo?: { [key: string]: string };
  newInfo?: { [key: string]: string };
};

export type CiReconciliationAttribute = {
  attributeId: number;
  nameAttribute: string;
  nameEntry?: string;
  active?: string;
  priority?: number;
  nameDiscoverySource?: string;
  discoverySourceDataId?: number;
  defaultEntry?: boolean;
  timeUnit?: TimeUnitEnum;
  timeoutData?: number;
  _rowSpan?: number;
  _index?: number;
};
