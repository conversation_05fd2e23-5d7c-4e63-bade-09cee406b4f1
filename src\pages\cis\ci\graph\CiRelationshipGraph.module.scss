$transition: opacity 0.3s ease-in-out;

.icon {
    opacity: 0.7;
    transition: $transition;
    background-color: white;

    &:hover {
        opacity: 1;
    }
}

.toolbar {
    opacity: 1;
    transition: $transition;
    position: absolute;
    z-index: calc(var(--mantine-z-index-modal) - 1);
    top: 50%;
    transform: translateY(-50%);
    padding: var(--mantine-spacing-xs);
    border: 0.3px solid var(--mantine-color-primary-6);
    background-color: var(--mantine-color-white);
}

.icon-graph-view-top-right {
    opacity: 1;
    transition: $transition;
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    padding: 10px;
    background-color: var(--mantine-color-white);
}



.diagram {
    height: 100%;
    min-height: 60vh;
    width: 100%;
    background: var(--mantine-color-white);
}

.icon-zoom {
    position: absolute;
    z-index: calc(var(--mantine-z-index-modal) + 1);
    bottom: 10%;
    right: 25px;

    //z-index: 2;
    &:hover {
        opacity: 1;
    }
}

.preview {
    position: absolute;
    bottom: 10%;
    right: 20px;

    z-index: calc(var(--mantine-z-index-modal) - 1);
    opacity: 0.7;

    &:hover {
        opacity: 1;
    }
}

.preview-content {
    width: 225px;
    height: 225px;
    border: 1px solid var(--mantine-color-primary-6);
    background-color: white;
}

.preview-hidden {
    opacity: 0;
    display: none;
}

.preview-button {
    position: absolute;
    bottom: 0;
    left: 0px;
    z-index: 2;
}