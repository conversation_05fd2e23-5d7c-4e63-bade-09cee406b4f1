// #region Basic
$rqb-spacing: 0.5rem !default;
$rqb-background-color: rgba(186, 195, 207, 0.2) !default;
$rqb-border-color: var(--mantine-color-gray-2);
$rqb-border-style: solid !default;
$rqb-border-radius: 0.25rem !default;
$rqb-border-width: 1px !default;
// #endregion

// #region Drag-and-drop
$rqb-dnd-hover-border-bottom-color: rebeccapurple !default;
$rqb-dnd-hover-copy-border-bottom-color: #669933 !default;
$rqb-dnd-hover-border-bottom-style: dashed !default;
$rqb-dnd-hover-border-bottom-width: 2px !default;
// #endregion

// #region Branches
$rqb-branch-indent: $rqb-spacing !default;
$rqb-branch-color: $rqb-border-color !default;
$rqb-branch-width: $rqb-border-width !default;
$rqb-branch-radius: $rqb-border-radius !default;
$rqb-branch-style: $rqb-border-style !default;
// #endregion

// Default styles
.ruleGroup {
  display: flex;
  flex-direction: column;
  gap: $rqb-spacing;
  padding: $rqb-spacing;
  border-color: $rqb-border-color;
  border-style: $rqb-border-style;
  border-radius: $rqb-border-radius;
  border-width: $rqb-border-width;
  background: $rqb-background-color;

  .ruleGroup-body {
    display: flex;
    flex-direction: column;
    gap: $rqb-spacing;

    &:empty {
      display: none;
    }
  }

  .ruleGroup-header,
  .rule {
    display: flex;
    gap: $rqb-spacing;
    align-items: center;
  }

  .rule {
    & > *:not(button) {
      flex: 1
    }
    .rule-value {
      &:has(.rule-value-list-item) {
        display: flex;
        gap: $rqb-spacing;
        align-items: baseline;
      }

      //width: 250px;
      flex: 1
    }
    .rule-operators{
      flex: 1
    }

  }

  .betweenRules {
    display: flex;
  }

  .shiftActions {
    display: flex;
    flex-direction: column;

    & > * {
      background-color: transparent;
      border: none;
      cursor: pointer;
      padding: 0;
    }
  }
}
