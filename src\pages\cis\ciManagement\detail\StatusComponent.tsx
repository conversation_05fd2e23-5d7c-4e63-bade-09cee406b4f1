import React, { useEffect, useMemo, useState } from 'react';
import { Badge } from '@mantine/core';
import { StatusCiManagement } from '@common/constants/CiManagement';

type StatusComponentProps = {
  value: StatusCiManagement;
};

export const StatusComponent = (props: StatusComponentProps) => {
  const { value } = props;
  const [color, setColor] = useState('gray');

  const calculatedColor = useMemo(() => {
    if (!value) {
      return '';
    }

    switch (value) {
      case StatusCiManagement.DRAFT:
        return 'gray';
      case StatusCiManagement.WAITING:
        return 'yellow';
      case StatusCiManagement.APPROVED:
        return 'green';
      case StatusCiManagement.REJECTED:
        return 'red';
      default:
        return '';
    }
  }, [value]);

  useEffect(() => {
    setColor(calculatedColor);
  }, [calculatedColor]);

  return (
    <>
      <Badge mt={'5'} color={color}>
        {value}
      </Badge>
    </>
  );
};

export default StatusComponent;
