import type { CiRelationshipTypeModel } from '@models/CiRelationshipType';
import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import type { RootStoreType } from '@store';
import { useFetchForEmpty } from './CommonSlice';

export interface CiRelationshipTypesState {
  data: CiRelationshipTypeModel[];
}

const initialState: CiRelationshipTypesState = {
  data: [],
};

export const ciRelationshipTypesSlice = createSlice({
  name: 'ciRelationshipTypes',
  initialState,
  reducers: {
    fetchData() {},
    //Fetch only if data is empty
    fetchForEmpty() {},
    setValue(_state, action: PayloadAction<CiRelationshipTypesState>) {
      return action.payload;
    },
  },
});

export const getCiRelationshipTypes = (store: RootStoreType) => store.ciRelationshipTypes;

//This hook will auto fetch if Relationship is Empty
export const useGetRelationshipTypes = () => {
  const ciRelationshipTypes = useFetchForEmpty(getCiRelationshipTypes, ciRelationshipTypesSlice);

  return ciRelationshipTypes;
};
