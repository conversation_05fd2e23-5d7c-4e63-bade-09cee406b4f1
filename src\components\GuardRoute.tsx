import React from 'react';
import { useSelector } from 'react-redux';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { aclPermissionGetIdentical } from '@common/utils/AclPermissionUtils';
import ForbiddenPage from '@pages/base/ForbiddenPage';
import { ProtectedRoute } from '@core/auth/hocs/ProtectedRoute';
import type { AclPermission } from '@models/AclPermission';
import useRequiredPermissions from '@common/hooks/useRequiredPermissions';
import type { PermissionActionType } from '@common/constants/PermissionActionType';

export type GuardRouteType = {
  requirePermissions: AclPermission[];
  children: React.ReactNode;
  permissionType?: PermissionActionType;
};

const GuardRoute: React.FC<GuardRouteType> = (props: GuardRouteType) => {
  const currentUser = useSelector(getCurrentUser);
  const allRequirePermissions = useRequiredPermissions(props.requirePermissions, props.permissionType);

  return (
    <ProtectedRoute
      errorElement={<ForbiddenPage />}
      requirePermissions={allRequirePermissions.map(aclPermissionGetIdentical)}
      userPermissions={(currentUser.data?.aclPermissions || []).map(aclPermissionGetIdentical)}
      isSuperAdmin={currentUser.data?.isSuperAdmin === true}>
      {props.children}
    </ProtectedRoute>
  );
};

export default GuardRoute;
