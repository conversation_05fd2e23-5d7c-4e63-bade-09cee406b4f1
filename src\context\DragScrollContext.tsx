import React, { createContext, useContext, useRef, useState } from 'react';

const DroppedItemsRefsContext = createContext<{
  refs: React.MutableRefObject<Record<string, HTMLElement | null>>;
  scrollToItem: (id: string) => void;
  registerRef: (id: string, element: HTMLElement | null) => void;
  highlightItem: (ids: string[]) => void;
  highlightedItemIds: string[];
  onUpdateItemIdWhenDropCurrent: (id: string) => void;
  itemIdWhenDropCurrent: string;
} | null>(null);

export const DroppedItemsRefsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const refs = useRef<Record<string, HTMLElement | null>>({});
  const [highlightedItemIds, setHighlightedItemIds] = useState<string[]>([]);
  const [itemIdWhenDropCurrent, setItemIdWhenDropCurrent] = useState<string>('');

  const onUpdateItemIdWhenDropCurrent = (id: string) => {
    setItemIdWhenDropCurrent(id);
  };

  const scrollToItem = (id: string) => {
    const element = refs.current[id];
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  const registerRef = (id: string, element: HTMLElement | null) => {
    if (itemIdWhenDropCurrent && itemIdWhenDropCurrent === id && element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      element.style.border = '1px solid #3399FF';
      setTimeout(() => {
        element.style.border = 'none';
      }, 1000);
      setItemIdWhenDropCurrent('');
    }
    refs.current[id] = element;
  };

  const highlightItem = (ids: string[]) => {
    setHighlightedItemIds((prev) => [...prev, ...ids]);

    setTimeout(() => {
      setHighlightedItemIds((prev) => prev.filter((itemId) => !ids.includes(itemId)));
    }, 3000);
  };

  return (
    <DroppedItemsRefsContext.Provider
      value={{ refs, scrollToItem, registerRef, highlightItem, highlightedItemIds, onUpdateItemIdWhenDropCurrent, itemIdWhenDropCurrent }}>
      {children}
    </DroppedItemsRefsContext.Provider>
  );
};

export const useDroppedItemsRefs = () => {
  const context = useContext(DroppedItemsRefsContext);
  if (!context) {
    throw new Error('useDroppedItemsRefs must be used within a DroppedItemsRefsProvider');
  }
  return context;
};
