import { Base<PERSON><PERSON> } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
import type { RoleModel } from '@models/Role';
import type { GroupSettingRolesModel } from './GroupsApi';
import type { ConfigItemPermissionOtherModel, ConfigItemTypePermissionModel } from '@models/ConfigItemTypePermission';

export type RoleResponse = RoleModel;
export type RoleDto = RoleResponse & {
  isSetting: boolean;
  roleOfGroup?: number;
};

export type RoleDataRequest = {
  groupIds: number[];
  basicInfo: RoleModel;
  grantOthers: ConfigItemPermissionOtherModel[];
  grantCis: ConfigItemTypePermissionModel[];
};

export type RolePagingResponse = PaginationResponseModel<RoleDto>;
export class RolesApi extends BaseApi {
  static baseUrl = BaseUrl.roles;

  static getAll(pagination: PaginationRequestModel<RolePagingResponse>) {
    return BaseApi.postData<RolePagingResponse>(`${this.baseUrl}/filter`, pagination);
  }

  static getAllToView(pagination: PaginationRequestModel<RolePagingResponse>, groupId: number) {
    return BaseApi.getData<RolePagingResponse>(`${this.baseUrl}/view?groupId=${groupId}`, pagination);
  }

  static async getById(id: number) {
    return BaseApi.getData<RoleResponse>(`${this.baseUrl}/${id}`);
  }
  static getAllGroupWithRoleId(roleId: number) {
    return BaseApi.getData<number[]>(`${this.baseUrl}/${roleId}/groups`);
  }
  static saveOrUpdate(
    groupIds: number[],
    basicInfoRole: RoleModel,
    permissions: ConfigItemPermissionOtherModel[],
    permissionCi: ConfigItemTypePermissionModel[],
  ) {
    const dataSend: RoleDataRequest = { groupIds, basicInfo: basicInfoRole, grantCis: permissionCi, grantOthers: permissions };
    return BaseApi.postData<RoleResponse>(`${this.baseUrl}`, dataSend);
  }
  static updateGroupIntoRole(roleId: number, groupIds: number[]) {
    return BaseApi.putData<GroupSettingRolesModel[]>(`${this.baseUrl}/${roleId}/groups`, groupIds);
  }
  static removeGroupsFromRole(roleId: number, groupIds: number[]) {
    return BaseApi.deleteData<GroupSettingRolesModel[]>(`${this.baseUrl}/${roleId}/groups`, { groupIds });
  }
  static deleteByIds(ids: number[]) {
    return BaseApi.deleteData<number[]>(`${this.baseUrl}`, {
      ids,
    });
  }
}
