.highlight {
  animation: highlight-animation 3s ease-in-out;
}

@keyframes highlight-animation {
  0% {
    background-color: rgba(255, 255, 0, 0.8);
    border: 1px solid rgba(255, 200, 0, 0.8);
    transform: scale(1.02);
  }

  50% {
    background-color: rgba(255, 255, 0, 0.3);
    border: 1px solid rgba(255, 200, 0, 0.5);
    transform: scale(1.03);
  }

  100% {
    background-color: transparent;
    border: none;
    transform: scale(1);
  }
}