import { KanbanTable, TableAffactedSafeType, KanbanTableSelectHandleMethods, ColumnType } from 'kanban-design-system';
import React, { useEffect, useState, useCallback, useMemo, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import equal from 'fast-deep-equal';
import { KanbanText, KanbanInput, KanbanButton } from 'kanban-design-system';
import { BusinessDomainApi } from '@api/BusinessDomainApi';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import { BusinessDomainAggregateItem } from '@models/BusinessDomain';
import { buildBusinessDomainDetailUrl } from '@common/utils/RouterUtils';
import { BusinessDomainDetailTab, BusinessDomainType } from '@common/constants/BusinessDomainConstants';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { IconSearch } from '@tabler/icons-react';
import { BreadcrumbComponent } from '../../breadcrumb/BreadcrumbComponent';
export const BusinessDomainAggregatePage = () => {
  const [searchParams] = useSearchParams();
  const ciName = searchParams.get('ciName')?.trim() || '';

  const [search, setSearch] = useState(ciName);

  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);
  const [allowFetch, setAllowFetch] = useState<boolean>(false);

  const ref = useRef<KanbanTableSelectHandleMethods<BusinessDomainAggregateItem>>(null);

  const [listData, setListData] = useState<BusinessDomainAggregateItem[]>([]);
  const [isLoadingTable, setIsLoadingTable] = useState(false);

  const columns: ColumnType<BusinessDomainAggregateItem>[] = useMemo(
    () => [
      {
        title: 'Name',
        name: 'name',
        width: '20%',
      },
      {
        title: 'CI ID',
        name: 'id',
        hidden: true,
        advancedFilter: {
          variant: 'number',
          filterModes: ['equals', 'notEquals', 'greaterThan', 'greaterThanOrEqualTo', 'lessThan', 'lessThanOrEqualTo'],
        },
      },
      {
        title: 'Description',
        name: 'description',
        customRender: (data: any) => {
          return <KanbanText lineClamp={2}>{data}</KanbanText>;
        },
        width: '50%',
      },
      {
        title: 'Parent ID',
        name: 'parentId',
        hidden: true,
        advancedFilter: {
          variant: 'number',
          filterModes: ['equals', 'notEquals', 'greaterThan', 'greaterThanOrEqualTo', 'lessThan', 'lessThanOrEqualTo'],
        },
      },
      {
        title: 'Parent Name',
        name: 'parentName',
        width: '20%',
      },
      {
        title: 'CI Type Name',
        name: 'ciTypeName',
        width: '20%',
      },
    ],
    [],
  );

  const fetchBusinessFunction = useCallback(() => {
    if (!allowFetch) {
      return;
    }
    if (!tableAffected) {
      return;
    }
    setIsLoadingTable(true);
    const pagination = tableAffectedToMultiColumnFilterPaginationRequestModel(tableAffected);
    BusinessDomainApi.getBusinessDomainAggregate(pagination)
      .then((res) => {
        if (res.data) {
          setListData(res.data?.content || []);
          setTotalRecords(res.data.totalElements);
        }
      })
      .catch(() => {})
      .finally(() => {
        setIsLoadingTable(false);
      });
  }, [tableAffected, allowFetch]);

  useEffect(() => {
    fetchBusinessFunction();
  }, [fetchBusinessFunction]);

  useEffect(() => {
    if (ref.current) {
      ref.current.setSearch(ciName);
      ref.current.setSort('name' as keyof BusinessDomainAggregateItem, false);
      setAllowFetch(true);
    }
  }, [ref, ciName]);

  return (
    <>
      <BreadcrumbComponent />
      <HeaderTitleComponent
        title={``}
        rightSection={
          <KanbanInput
            placeholder='Enter CI Name'
            // w={200}
            value={search}
            onChange={(e) => {
              const value = e.target.value;
              setSearch(value);
            }}
            onKeyUp={(element) => {
              if (element.key === 'Enter') {
                setSearch(search?.trim() || '');
                ref.current?.setSearch(search?.trim() || '');
              }
            }}
            leftSection={
              <KanbanButton
                size='compact-xs'
                variant='outline'
                bd={0}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setSearch(search?.trim() || '');
                  ref.current?.setSearch(search?.trim() || '');
                }}>
                <IconSearch />
              </KanbanButton>
            }
          />
        }></HeaderTitleComponent>

      <KanbanTable
        columns={columns}
        key={1}
        ref={ref}
        data={listData}
        isLoading={isLoadingTable}
        showNumericalOrderColumn={true}
        sortable={{
          enable: true,
        }}
        searchable={{
          enable: false,
          debounceTime: 800,
        }}
        advancedFilterable={{
          enable: true,
          debounceTime: 1000,
          resetOnClose: true,
          compactMode: true,
        }}
        onRowClicked={(data) => {
          if (data.ciTypeEnum === BusinessDomainType.BUSINESS_DOMAIN) {
            window.open(buildBusinessDomainDetailUrl(data.id), '_blank', 'noopener,noreferrer');
          } else if (data.ciTypeEnum === BusinessDomainType.BUSINESS_FUNCTION) {
            if (data.parentId !== undefined) {
              window.open(
                buildBusinessDomainDetailUrl(data.parentId, BusinessDomainDetailTab.BUSINESS_FUNCTION, data.id, data.name),
                '_blank',
                'noopener,noreferrer',
              );
            }
          }
        }}
        pagination={{
          enable: true,
        }}
        serverside={{
          totalRows: totalRecords,
          onTableAffected(dataSet) {
            if (!equal(tableAffected, dataSet) && allowFetch) {
              setTableAffected(dataSet);
            }
          },
        }}
      />
    </>
  );
};

export default BusinessDomainAggregatePage;
