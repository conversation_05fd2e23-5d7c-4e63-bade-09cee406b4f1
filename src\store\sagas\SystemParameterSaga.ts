import { ConfigParamApi, ConfigParamResponse } from '@api/systems/ConfigParamApi';
import type { ApiResponse } from '@core/api/ApiResponse';
import { getSystemParams, systemParameterSlice, SystemParamState } from '@slices/SystemParameterSlice';
import { call, put, select, takeEvery } from 'redux-saga/effects';

function* fetchDataSaga() {
  yield put(
    systemParameterSlice.actions.setValue({
      isFetching: true,
      isFetched: false,
      data: [],
    }),
  );
  try {
    const response: ApiResponse<ConfigParamResponse[]> = yield call(ConfigParamApi.getAll.bind(ConfigParamApi));
    yield put(
      systemParameterSlice.actions.setValue({
        isFetching: false,
        isFetched: true,
        data: response?.data || [],
      }),
    );
  } catch (ex) {
    yield put(
      systemParameterSlice.actions.setValue({
        isFetching: false,
        isFetched: true,
        data: [],
      }),
    );
  }
}

function* fetchForEmptySaga() {
  const currentData: SystemParamState = yield select(getSystemParams);
  if (!currentData || (!currentData.isFetching && !currentData.isFetched)) {
    yield call(fetchDataSaga);
  }
}
export function* systemParameterSaga() {
  yield takeEvery(systemParameterSlice.actions.fetchData, fetchDataSaga);
  yield takeEvery(systemParameterSlice.actions.fetchForEmpty, fetchForEmptySaga);
}
