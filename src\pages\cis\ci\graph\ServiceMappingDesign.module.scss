.diagram-wrapper {
    flex: 2;
    height: 100%;
    width: 100%;
    //min-height: var(--kanban-modal-content-max-height);
    position: relative;
}

.diagram {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: var(--mantine-color-white);
}

.ci-type {
    flex: 1;
    height: 100%;
    position: relative;
    display: flex;
}

.ci-type-wrapper {
    flex: 1;
    height: 100%;
    //overflow-y: auto;
}

.button-function {
    position: absolute;
    top: 10px;
    right: 0;
    transform: translateX(100%);
    z-index: 5;

}

.diagram-wrapper {
    position: relative;
}

.diagram-placeholder {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0.5;
    padding: 0;
    text-align: center;
}

.preview {
    position: absolute;
    bottom: 25px;
    right: 10px;
    z-index: var(--mantine-z-index-modal);
    opacity: 0.7;

    &:hover {
        opacity: 1;
    }
}


.preview-content {
    width: 225px;
    height: 225px;
    border: 1px solid var(--mantine-color-primary-6);
    background-color: white;
}

.preview-hidden {
    opacity: 0;
    display: none;
}

.preview-button {
    float: right;
    z-index: var(--mantine-z-index-modal);
}