import React from 'react';
import { useMemo } from 'react';
import type { FieldSelectorProps } from 'react-querybuilder';
import { SelectGroups } from './SelectGroups';
import type { FieldChangeProps } from './AdvanceSearchComponent';

export interface Option {
  label: string;
  name: string;
  group: string;
  groupView: string;
  type: string;
  tag: string;
}

export type FieldSelectorPropsCustom = FieldSelectorProps & {
  setFieldChange: (value: FieldChangeProps) => void;
  ciTypeId?: number;
};

const isOption = (obj: any): obj is Option => obj && typeof obj === 'object' && 'name' in obj && 'group' in obj;

const CustomSelectControl = (props: FieldSelectorPropsCustom) => {
  const { ciTypeId, disabled, handleOnChange, options, path, setFieldChange, value } = props;
  const groupedOptions = useMemo(() => {
    const groups = new Set<string>();
    const newOptions: Option[] = [];

    options.forEach((opt) => {
      if ('groupView' in opt) {
        groups.add(opt.groupView);
      } else if ('group' in opt) {
        groups.add(opt.group);
      }

      // case ciTypeId != 0 then remove 'ciTypeId' in list
      const isCiTypeFilterExcluded = ciTypeId && ciTypeId !== 0 && !disabled && isOption(opt) && opt.name === 'ciTypeId' && opt.group === 'Attribute';
      if (!isCiTypeFilterExcluded) {
        newOptions.push(opt as unknown as Option);
      }
    });

    return Array.from(groups).map((group) => ({
      label: group,
      items: newOptions
        .filter((x) => (x.groupView ? x.groupView === group : x.group === group))
        .map((el) => ({ name: el.label, value: el.name, description: el.type, tag: el.tag })),
    }));
  }, [ciTypeId, disabled, options]);

  const onChange = (val: string | null) => {
    setFieldChange({ fieldName: val || '', path: path });
    handleOnChange(val);
  };

  return <SelectGroups datas={groupedOptions} value={value} onChange={onChange} placeholder='Select Field' disabled={disabled}></SelectGroups>;
};

export default CustomSelectControl;
