import React from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { SelectionConfigs, SelectionItem } from './Configs';
import { KanbanTitle } from 'kanban-design-system';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { BreadcrumbComponent } from './breadcrumb/BreadcrumbComponent';
const Wrapper = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  column-gap: var(--mantine-spacing-md);
  row-gap: var(--mantine-spacing-lg);
`;
const Section = styled.div``;
const SectionTitle = styled.div`
  h3 {
    margin: 0;
    margin-left: var(--mantine-spacing-md);
    margin-top: 5px;
    display: inline-block;
    font-size: var(--mantine-font-size-md);
    vertical-align: top;
  }
`;
const SectionSelection = styled.div``;
const SectionSelectionItem = styled(Link)`
  all: unset;

  font-size: var(--mantine-font-size-sm);
  display: inline-block;
  margin-right: var(--mantine-spacing-md);
  position: relative;
  cursor: pointer;
  &:hover {
    color: var(--mantine-primary-color-filled-hover);
  }
  &::after {
    color: black;
    content: '|';
    display: inline-block;
    margin-left: var(--mantine-spacing-md);
  }
  &:nth-last-child(1)::after {
    content: none;
  }
`;

const renderSectionSelectionItems = (selections: SelectionItem[]) => {
  return selections.map((item, key) => {
    if (isCurrentUserMatchPermissions(item.requirePermissions)) {
      return (
        <SectionSelectionItem key={key} to={item.path}>
          {item.title}
        </SectionSelectionItem>
      );
    }
    return null;
  });
};

export const SelectionPage = () => {
  return (
    <>
      {/* 4763 admin setting */}
      <BreadcrumbComponent />
      <Wrapper>
        {SelectionConfigs.map((section, key) => {
          if (isCurrentUserMatchPermissions(section.requirePermissions || [])) {
            return (
              <Section key={key}>
                <SectionTitle>
                  <section.icon.icon {...(section.icon.props || {})} size={'2rem'} />
                  <KanbanTitle order={3}>{section.title}</KanbanTitle>
                </SectionTitle>
                <SectionSelection>{renderSectionSelectionItems(section.selections)}</SectionSelection>
              </Section>
            );
          }
          return null;
        })}
      </Wrapper>
    </>
  );
};

export default SelectionPage;
