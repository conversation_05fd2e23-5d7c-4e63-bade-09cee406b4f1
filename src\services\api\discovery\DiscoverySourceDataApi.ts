import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
import { DiscoverySourceDataModel, DiscoverySourceDataResponse } from '@models/discovery/DiscoverySourceData';

export type DiscoverySourceDataPagingResponse = PaginationResponseModel<DiscoverySourceDataResponse>;
export class DiscoverySourceDataApi extends BaseApi {
  static baseUrl = BaseUrl.discoverySourceDatas;

  static getAll(pagination: PaginationRequestModel<DiscoverySourceDataResponse>) {
    return BaseApi.postData<DiscoverySourceDataPagingResponse>(`${this.baseUrl}/filter`, pagination);
  }

  static async getById(id: number) {
    return BaseApi.getData<DiscoverySourceDataResponse>(`${this.baseUrl}/${id}`);
  }

  static createOrUpdate(data: DiscoverySourceDataModel) {
    return BaseApi.postData<DiscoverySourceDataResponse>(`${this.baseUrl}`, data);
  }

  static deleteByIds(ids: number[]) {
    return BaseApi.deleteData<number[]>(`${this.baseUrl}`, {
      ids,
    });
  }

  static executionTestTransformJson(data: DiscoverySourceDataModel) {
    return BaseApi.postData<string[]>(`${this.baseUrl}/test`, data);
  }

  static getAllDataSource(isView: boolean = false) {
    return BaseApi.getData<DiscoverySourceDataModel[]>(`${this.baseUrl}/`, { isView });
  }
}
