import type { ConfigItemTypeModel } from '@models/ConfigItemType';
import { PayloadAction, createSelector, createSlice } from '@reduxjs/toolkit';
import type { RootStoreType } from '@store';
import type { PayloadActionWithCallback } from '@models/redux/ReduxPayloadAction';
import { useFetchForEmpty } from './CommonSlice';
export type CiTypesState = {
  isFetching: boolean;
  isFetched: boolean;
  data: ConfigItemTypeModel[];
};

const initialState: CiTypesState = {
  isFetching: false,
  isFetched: false,
  data: [],
};

export type CreateOrUpdatePayload = PayloadActionWithCallback<ConfigItemTypeModel>;
export type DeletePayload = PayloadActionWithCallback<number>;
export type DeleteAllPayload = PayloadActionWithCallback<number[]>;
export const ciTypesSlice = createSlice({
  name: 'ciTypes',
  initialState,
  reducers: {
    fetchData() {},
    fetchForEmpty() {},
    deleteData(_state, _action: DeletePayload) {},
    deleteAllData(_state, _action: DeleteAllPayload) {},
    createOrUpdate(_state, _action: CreateOrUpdatePayload) {},
    setValue(_state, action: PayloadAction<CiTypesState>) {
      return action.payload;
    },
    addValue(state, action: PayloadAction<ConfigItemTypeModel>) {
      state.data.push(action.payload);
    },
    removeValue(state, action: PayloadAction<number>) {
      const index = state.data.findIndex((obj) => obj.id === action.payload);
      if (index !== -1) {
        state.data.splice(index, 1);
      }
    },
    updateValue(state, action: PayloadAction<ConfigItemTypeModel>) {
      const objIndex = state.data.findIndex((obj) => obj.id === action.payload.id);
      if (objIndex >= 0) {
        state.data[objIndex] = { ...state.data[objIndex], ...action.payload };
      } else {
        state.data.push(action.payload);
      }
    },
  },
});

export const getCiTypes = (store: RootStoreType) => store.ciTypes;

export const getCiTypeById = (id: number) =>
  createSelector(getCiTypes, (data) => {
    return data.data.find((x) => x.id === id);
  });

//This hook will auto fetch if Ci Types is Empty
export const useGetCiTypes = () => {
  const ciTypes = useFetchForEmpty(getCiTypes, ciTypesSlice);

  return ciTypes;
};
