import type { CiAdvancedSearchDTO, CiAdvancedSearchModel } from '@models/CiAdvancedSearch';

export class CIAdvancedSearchService {
  static convertToDTO(model: CiAdvancedSearchModel): CiAdvancedSearchDTO {
    const { dataQuery, description, id, name } = model;
    return { id, name, description, dataQuery };
  }
  static checkIsUpdate(id: number, nameAfter: string, nameBefore: string): boolean {
    return id === 0 ? false : nameAfter === nameBefore;
  }
}

export default 1;
