import type { GoJs } from '@common/libs';
import { PalleteTemplateFull, makeDiagram } from '@common/utils/GoJsHelper';
import React, { useCallback, useEffect, useRef, useState, DragEvent, forwardRef, useImperativeHandle, useMemo } from 'react';
import stylesCss from './ServiceMappingDesign.module.scss';
import type { TablerIconKeys } from '@common/utils/IconsUtils';
import go from '@common/libs/gojs/go';
import type { ConfigItemTypeModel } from '@models/ConfigItemType';
import { useOnClickOutside } from 'kanban-design-system';
import { useDispatch, useSelector } from 'react-redux';
import { ciRelationshipTypesSlice, getCiRelationshipTypes, useGetRelationshipTypes } from '@slices/CiRelationshipTypesSlice';
import { Divider, Menu, Popover, type ComboboxItem, Flex, Group } from '@mantine/core';
import { CustomDrag, DragAndDropCustomResult } from './CustomDrag';
import { KanbanSelect } from 'kanban-design-system';
import { CiTypePickerComponent } from '@components/commonCi/SelectCiTypeComponent';
import { useDisclosure } from '@mantine/hooks';
import { KanbanModal } from 'kanban-design-system';
import { KanbanButton } from 'kanban-design-system';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import { ServiceMappingApi } from '@api/ServiceMappingApi';
import { KanbanThemeIcon } from 'kanban-design-system';
import { IconChevronUp, IconDragDrop, IconX } from '@tabler/icons-react';
import { KanbanText } from 'kanban-design-system';
import { KanbanCheckbox } from 'kanban-design-system';
import { KanbanIconButton } from 'kanban-design-system';
import { ServiceMappingDesignRules } from './ServiceMappingDesignRules';
import type { ServiceMapingModel, ServiceMapRuleModel } from '@models/ServiceMapping';
import { NotificationError } from '@common/utils/NotificationUtils';
import { RelationshipTypeConstants } from '@common/constants/RelationshipTypeConstants';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import { fetchDiagramServiceMapping } from '@common/utils/ServiceMapHelper';
export interface ServiceMappingDesignHandle {
  getGoDiagram: () => GoJs.Diagram | undefined;
}

type ServiceMappingDesignProps = {
  id?: number;
};

export const ServiceMappingDesign = forwardRef<ServiceMappingDesignHandle, ServiceMappingDesignProps>((props, ref) => {
  const diagramNetworkRef = useRef<HTMLDivElement>(null);
  const [goDiagram, setGoDiagram] = useState<GoJs.Diagram | undefined>(undefined);
  const [serviceMapingModel, setServiceMapingModel] = useState<ServiceMapingModel | undefined>(undefined);
  const [ciTypeInfo, setCiTypeInfo] = useState<ConfigItemTypeModel[] | undefined>(undefined);
  const [contextLinkPosition, setContextLinkPosition] = useState<{ x: number; y: number } | undefined>(undefined);
  const [contextMenuPosition, setContextMenuPosition] = useState<{ x: number; y: number } | undefined>(undefined);
  const [ciTypeComboboxs, setCiTypeComboboxs] = useState<ComboboxItem[]>([]);
  const ciRelationshipTypes = useSelector(getCiRelationshipTypes);
  const [linkSelected, setLinkSelected] = useState<{ from: number; to: number }>({ from: 0, to: 0 });
  const [openedModalAddStartCI, { close: closeModalAddStartCI, open: openModalAddStartCI }] = useDisclosure(false);
  const [openedModalEditRules, { close: closeModalEditRules, open: openModalEditRules }] = useDisclosure(false);
  const [nodeSelected, setNodeSelected] = useState<go.Node | undefined>(undefined);
  const [ciStartComboboxs, setCiStartComboboxs] = useState<ComboboxItem[]>([]);
  const [ciStartSelected, setCiStartSelected] = useState<{ ciId?: number; ciName?: string } | undefined>(undefined);
  const [showPlaceHolder, setShowPlaceHolder] = useState<boolean>(false);
  const [dataChecboxs, setDataCheckboxs] = useState<ConfigItemTypeModel[]>([]);
  const [isShowPreview, setIsShowPreview] = useState(false);
  const [rule, setRule] = useState<ServiceMapRuleModel>({});
  const ciTypesData = useGetCiTypes().data || [];
  const previewRef = useRef(null);
  const dispatch = useDispatch();
  const ciTypes = useGetCiTypes();
  const relationships = useGetRelationshipTypes();

  useImperativeHandle(
    ref,
    () => ({
      getGoDiagram: () => {
        return goDiagram;
      },
    }),
    [goDiagram],
  );

  useEffect(() => {
    dispatch(ciRelationshipTypesSlice.actions.fetchForEmpty());
  }, [dispatch]);
  useEffect(() => {
    const ciTypes: ComboboxItem[] = [];
    if (!ciRelationshipTypes) {
      setCiTypeComboboxs(ciTypes);
      return;
    }
    const comboboxItems = ciRelationshipTypes.data.map((item) => ({
      value: item.id.toString(),
      label: item.type,
    }));

    comboboxItems.unshift({
      value: RelationshipTypeConstants.ANY.key.toString(),
      label: RelationshipTypeConstants.ANY.value,
    });

    ciTypes.push(...comboboxItems);
    setCiTypeComboboxs(ciTypes);
  }, [ciRelationshipTypes]);

  //logic hiden menu when click outside
  const menuAddCiTypeRef = useRef(null);
  const handleOnClickOutside = () => {
    setContextLinkPosition(undefined);
    //setContextSettingStartCIPosition(undefined);
  };
  useOnClickOutside(menuAddCiTypeRef, handleOnClickOutside);

  const handleBackgroundSingleClicked = useCallback(() => {
    setContextLinkPosition(undefined);
    setContextMenuPosition(undefined);
  }, []);

  const handleObjectSingleClicked = useCallback(
    (e: go.DiagramEvent) => {
      if (!goDiagram) {
        return;
      }
      const part = e.subject.part;
      if (part instanceof go.Adornment) {
        const buttonAddCiType = e.subject.findObject('addCiType');
        // logic add relationship
        if (buttonAddCiType) {
          setLinkSelected({ from: part.data.from, to: part.data.to });
          const mousePt = e.diagram.lastInput.viewPoint;
          setContextLinkPosition({ x: mousePt.x, y: mousePt.y });
        }
      }
    },
    [goDiagram],
  );

  const handleObjectContextClicked = useCallback((e: go.DiagramEvent) => {
    const part = e.subject.part;
    if (part instanceof go.Node && diagramNetworkRef.current) {
      const mousePt = e.diagram.lastInput.viewPoint;
      setContextMenuPosition({ x: mousePt.x + 30, y: mousePt.y + 30 });
      setNodeSelected(part);
    }
  }, []);

  useEffect(() => {
    if (diagramNetworkRef.current && !goDiagram) {
      const myDiagram = makeDiagram({
        dom: diagramNetworkRef.current,
        showLayout: true,
        isEdit: true,
        isShowSelectionAdornment: true,
        isShowTotalChild: false,
      });
      myDiagram.layout = new go.Layout();
      if (props?.id) {
        ServiceMappingApi.getById(props.id)
          .then((res) => {
            if (res.data && res.data.graphData) {
              myDiagram.model = go.Model.fromJson(res.data.graphData);
              setCiStartSelected({ ciId: res.data.startCiId, ciName: res.data.startCiName });
              setServiceMapingModel(res.data);
              setGoDiagram(myDiagram);
              // view message error when delete start CI
              if (!res.data.startCiId) {
                NotificationError({
                  message: 'Start CI has been deleted, please set up the start CI again.',
                });
              }
            }
          })
          .catch(() => {});
      } else {
        setShowPlaceHolder(true);
        setIsShowPreview(false);
        setGoDiagram(myDiagram);
      }

      if (previewRef.current) {
        const myOverview = new go.Overview(
          previewRef.current, // the HTML DIV element for the Overview
        );
        myOverview.observed = myDiagram;
      }
    }
  }, [diagramNetworkRef, goDiagram, props?.id, previewRef]);
  // fetch diagram
  useEffect(() => {
    if (!goDiagram) {
      return;
    }

    fetchDiagramServiceMapping(goDiagram, ciTypes.data || [], relationships.data || [], serviceMapingModel);
  }, [goDiagram, ciTypes.data, relationships.data, serviceMapingModel]);

  const handleChangedSelection = useCallback(
    (e: go.ChangedEvent) => {
      if (!goDiagram || !e) {
        return;
      }
      if (goDiagram.nodes.count > 0) {
        setShowPlaceHolder(false);
      } else {
        setShowPlaceHolder(true);
      }
    },
    [goDiagram],
  );

  useEffect(() => {
    if (!goDiagram) {
      return;
    }

    goDiagram.addDiagramListener('ObjectContextClicked', handleObjectContextClicked);
    goDiagram.addDiagramListener('ObjectSingleClicked', handleObjectSingleClicked);
    goDiagram.addDiagramListener('BackgroundSingleClicked', handleBackgroundSingleClicked);
    goDiagram.model.addChangedListener(handleChangedSelection);
    return () => {
      goDiagram.removeDiagramListener('ObjectContextClicked', handleObjectContextClicked);
      goDiagram.removeDiagramListener('ObjectSingleClicked', handleObjectSingleClicked);
      goDiagram.removeDiagramListener('BackgroundSingleClicked', handleBackgroundSingleClicked);
      goDiagram.model.removeChangedListener(handleChangedSelection);
    };
  }, [goDiagram, handleObjectSingleClicked, handleBackgroundSingleClicked, handleObjectContextClicked, handleChangedSelection]);

  const handleOpenAddSrartCI = () => {
    if (!nodeSelected?.data?.ciTypeId) {
      return;
    }
    ConfigItemTypeApi.getAllCis(Number(nodeSelected.data.ciTypeId))
      .then((res) => {
        if (res.data) {
          const comboboxItems = res.data.map((item) => ({
            value: item.id.toString(),
            label: item.name,
          }));
          setCiStartComboboxs(comboboxItems);
          if (nodeSelected?.data?.data?.startCiId !== ciStartSelected?.ciId) {
            setCiStartSelected(undefined);
          }
          openModalAddStartCI();
        }
      })
      .catch(() => {});
  };

  const handleOpenAddRules = () => {
    openModalEditRules();
  };
  const handleDrop = (result: DragAndDropCustomResult) => {
    if (!result || !goDiagram || !ciTypeInfo || !ciTypeInfo.length || !diagramNetworkRef.current) {
      return;
    }
    const bound = diagramNetworkRef.current.getBoundingClientRect();
    const initialMousePt = new go.Point(result.e.clientX - bound.left, result.e.clientY - bound.top);
    const initialDropPt = goDiagram.transformViewToDoc(initialMousePt);
    const offsetY = 100; // Offset for y coordinate
    ciTypeInfo.forEach((ciType, index) => {
      const offsetPt = new go.Point(initialDropPt.x, initialDropPt.y + offsetY * index);
      const newNode: PalleteTemplateFull = {
        key: goDiagram.nodes.count + 1,
        icon: (ciType?.icon || 'IconHome') as TablerIconKeys,
        label: ciType?.name || '',
        loc: go.Point.stringify(offsetPt),
        ciTypeId: ciType?.id,
      };
      goDiagram.model.addNodeData(newNode);
    });
    setDataCheckboxs([]);
    setCiTypeInfo([]);
  };

  const updateLink = (data: ComboboxItem) => {
    if (!goDiagram || !linkSelected || !data) {
      return;
    }

    const linkSearch = goDiagram.findLinksByExample({ from: linkSelected.from, to: linkSelected.to }).first();

    if (linkSearch) {
      goDiagram.startTransaction('updateLinkTransaction');
      goDiagram.model.setDataProperty(linkSearch.data, 'text', data.label);
      goDiagram.model.setDataProperty(linkSearch.data, 'relationshipId', Number(data.value));
      goDiagram.commitTransaction('updateLinkTransaction');
      goDiagram.updateAllTargetBindings();
    }
  };

  const getRelationshipCheckbox: string = useMemo(() => {
    if (!goDiagram) {
      return '0';
    }
    const linkSearch = goDiagram.findLinksByExample({ from: linkSelected.from, to: linkSelected.to }).first();
    if (!linkSearch) {
      return '0';
    }
    return `${linkSearch.data.relationshipId}`;
  }, [goDiagram, linkSelected]);

  /**
   * handle drag start set image start
   */
  const handleDragStart = useCallback(
    (e: DragEvent<HTMLDivElement>) => {
      if (!ciTypeInfo) {
        return;
      }
      if (e.currentTarget.children.length > 0) {
        const dataImageDrag = e.currentTarget.children[0].cloneNode(true) as HTMLElement;
        dataImageDrag.innerHTML = '';
        // Set custom drag image
        e.dataTransfer.setDragImage(dataImageDrag, 16, 16);
      }
    },
    [ciTypeInfo],
  );

  const handleRemoveNode = () => {
    if (!goDiagram || !nodeSelected) {
      return;
    }
    const removeNodeTransaction = 'removeNode';
    goDiagram.startTransaction(removeNodeTransaction);
    goDiagram.remove(nodeSelected);
    goDiagram.commitTransaction(removeNodeTransaction);
  };

  const handleRemoveStartCi = () => {
    if (!goDiagram || !nodeSelected) {
      return;
    }
    const nodeRoot = goDiagram.findNodesByExample({ isRoot: true }).first();
    if (nodeRoot) {
      const removeNodeTransaction = 'RemoveStartCi';
      goDiagram.startTransaction(removeNodeTransaction);
      goDiagram.model.setDataProperty(nodeRoot.data, 'isRoot', false);
      goDiagram.model.setDataProperty(nodeRoot.data, 'data', { startCiId: undefined, startCiName: undefined });
      goDiagram.commitTransaction(removeNodeTransaction);
    }
  };

  const handleRemoveRule = () => {
    if (!goDiagram || !nodeSelected) {
      return;
    }
    const nodeSearch = goDiagram.findNodeForKey(nodeSelected.key);
    if (nodeSearch) {
      const removeNodeTransaction = 'RemoveRule';
      goDiagram.startTransaction(removeNodeTransaction);
      goDiagram.model.setDataProperty(nodeSearch.data, 'rules', undefined);
      goDiagram.commitTransaction(removeNodeTransaction);
    }
  };

  const handleAddCi = () => {
    if (!ciStartSelected || !goDiagram || !nodeSelected) {
      return;
    }
    const addCiStartTransaction = 'addCiStartTransaction';
    goDiagram.startTransaction(addCiStartTransaction);
    goDiagram.nodes.each((node) => {
      if (node.data.key === nodeSelected.key) {
        goDiagram.model.setDataProperty(node.data, 'isRoot', true);
        goDiagram.model.setDataProperty(node.data, 'data', { startCiId: ciStartSelected.ciId, startCiName: ciStartSelected.ciName });
        goDiagram.model.setDataProperty(node.data, 'rules', undefined);
      } else {
        goDiagram.model.setDataProperty(node.data, 'isRoot', false);
        goDiagram.model.setDataProperty(node.data, 'data', { startCiId: undefined, startCiName: undefined });
      }
    });
    goDiagram.commitTransaction(addCiStartTransaction);
    closeModalAddStartCI();
    setContextMenuPosition(undefined);
  };

  const handleAddRule = () => {
    if (!goDiagram || !nodeSelected || !rule.ciTypeId) {
      return;
    }

    if (nodeSelected?.data?.ciTypeId === rule.ciTypeId) {
      if (!rule.ciTypeAttrId || !rule.operator || !rule.value) {
        NotificationError({
          message: 'Please enter complete information.',
        });
        return;
      }
    } else {
      // if exists a atribute setting have value then check all value require
      const fieldArr = [rule.ciTypeAttrId, rule.operator, rule.value];
      const isTotalFieldValid = fieldArr.filter((x) => !x).length;
      if (isTotalFieldValid > 0 && isTotalFieldValid < fieldArr.length) {
        NotificationError({
          message: 'Please enter complete information.',
        });
        return;
      }
      const node = goDiagram.findNodeForKey(nodeSelected.key);
      if (node) {
        const ciType = ciTypesData.find((ciType) => ciType.id === rule.ciTypeId);
        const updateNodeTransaction = 'update node';
        goDiagram.startTransaction(updateNodeTransaction);
        const data = node.data as PalleteTemplateFull;
        goDiagram.model.setDataProperty(data, 'icon', (ciType?.icon || 'IconHome') as TablerIconKeys);
        goDiagram.model.setDataProperty(data, 'label', ciType?.name);
        goDiagram.model.setDataProperty(data, 'ciTypeId', ciType?.id);
        goDiagram.model.setDataProperty(data, 'rules', undefined);
        goDiagram.commitTransaction(updateNodeTransaction);
        const nodeUpdate = goDiagram.findNodeForKey(nodeSelected.key);
        if (nodeUpdate) {
          setNodeSelected(nodeUpdate);
        }
      }
    }

    // setting rule
    if (rule.ciTypeAttrId && rule.operator && rule.value) {
      const addRuleTransaction = 'addRuleTransaction';
      goDiagram.startTransaction(addRuleTransaction);
      goDiagram.nodes.each((node) => {
        if (node.data.key === nodeSelected.key) {
          goDiagram.model.setDataProperty(node.data, 'rules', rule);
        }
      });
      goDiagram.commitTransaction(addRuleTransaction);
    }

    closeModalEditRules();
    setContextMenuPosition(undefined);
  };

  const leftIcon: (item: ConfigItemTypeModel) => React.ReactNode = useCallback(
    (node) => {
      return (
        <KanbanCheckbox
          checked={dataChecboxs.some((x) => x.id === node.id)}
          mb={0}
          onClick={(e) => {
            e.stopPropagation();
            setDataCheckboxs((prev) => {
              const opened = prev.some((x) => x.id === node.id);
              if (opened) {
                return prev.filter((x) => x.id !== node.id);
              }
              return [...prev, node];
            });
          }}
        />
      );
    },
    [dataChecboxs],
  );

  return (
    <>
      <Flex gap='xs' align='flex-start' w={'100%'}>
        <div className={stylesCss['ci-type']}>
          <div className={stylesCss['ci-type-wrapper']}>
            <CustomDrag customDragStart={(e, _) => handleDragStart(e)}>
              <CiTypePickerComponent
                leftSection={leftIcon}
                onMouseDown={(_, node) => {
                  if (node && dataChecboxs.length === 0) {
                    setCiTypeInfo([node]);
                  } else {
                    setCiTypeInfo(dataChecboxs);
                  }
                }}
              />
            </CustomDrag>
          </div>
          <div className={stylesCss['button-function']}>
            {dataChecboxs.length > 0 && (
              <Group>
                <KanbanIconButton onClick={() => setDataCheckboxs([])} variant='transparent' size={'lg'} pl={12} c={'red'}>
                  <IconX />
                </KanbanIconButton>
                {dataChecboxs.length} Item(s) Checked
              </Group>
            )}
          </div>
        </div>
        <div className={stylesCss['diagram-wrapper']}>
          <CustomDrag onDrop={handleDrop}>
            <div style={{ flex: 1, width: '100%', height: '100%' }}>
              <div key={'diagram'} ref={diagramNetworkRef} className={stylesCss['diagram']}></div>

              <div className={`${stylesCss['preview']}`}>
                <KanbanIconButton
                  className={`${stylesCss['preview-button']} ${!isShowPreview && stylesCss['preview-hidden']}`}
                  onClick={() => setIsShowPreview(false)}
                  variant='transparent'
                  size={'md'}
                  pl={5}
                  c={'red'}>
                  <IconX />
                </KanbanIconButton>
                <div className={`${stylesCss['preview-content']}  ${!isShowPreview && stylesCss['preview-hidden']}`} ref={previewRef}></div>
                <KanbanIconButton
                  className={` ${stylesCss['preview-button']} ${isShowPreview && stylesCss['preview-hidden']}`}
                  onClick={() => setIsShowPreview(true)}
                  size={'md'}
                  c={'white'}>
                  <IconChevronUp />
                </KanbanIconButton>
              </div>
              {showPlaceHolder && (
                <div className={stylesCss['diagram-placeholder']}>
                  <KanbanThemeIcon variant='white' p={0} size={'xs'}>
                    <IconDragDrop size={50} />
                  </KanbanThemeIcon>
                  <KanbanText>Drag the CI TYPE and create relationships for CI Types.</KanbanText>
                </div>
              )}

              {contextLinkPosition && (
                <div
                  className='context-menu'
                  style={{
                    position: 'absolute',
                    left: contextLinkPosition.x,
                    top: contextLinkPosition.y,
                  }}>
                  <Popover position='right' shadow='md' width={200} opened withinPortal={false}>
                    <Popover.Target>
                      <div></div>
                    </Popover.Target>
                    <Popover.Dropdown>
                      Select Relationship
                      <KanbanSelect
                        searchable={true}
                        data={ciTypeComboboxs}
                        defaultValue={getRelationshipCheckbox}
                        onChange={(value, data) => {
                          updateLink(data);
                          setContextLinkPosition(undefined);
                        }}
                      />
                      <Divider />
                    </Popover.Dropdown>
                  </Popover>
                </div>
              )}

              {contextMenuPosition && (
                <div
                  className='context-menu'
                  style={{
                    position: 'absolute',
                    left: contextMenuPosition.x,
                    top: contextMenuPosition.y,
                  }}
                  onContextMenu={(e) => e.preventDefault()}>
                  <Menu position='right' shadow='md' width={200} opened withinPortal={false}>
                    <Menu.Target>
                      <div></div>
                    </Menu.Target>
                    <Menu.Dropdown>
                      <Menu.Label>Menu</Menu.Label>
                      <Menu.Item onClick={() => handleOpenAddSrartCI()}>Edit Start CI</Menu.Item>
                      {!nodeSelected?.data?.isRoot && (
                        <Menu.Item
                          onClick={() => {
                            handleOpenAddRules();
                          }}>
                          Edit CI Rules
                        </Menu.Item>
                      )}

                      {ciStartSelected && nodeSelected?.data?.isRoot && (
                        <Menu.Item
                          c={'red'}
                          onClick={() => {
                            handleRemoveStartCi();
                            setContextMenuPosition(undefined);
                          }}>
                          Remove Start CI
                        </Menu.Item>
                      )}

                      {nodeSelected?.data?.rules && (
                        <Menu.Item
                          c={'red'}
                          onClick={() => {
                            handleRemoveRule();
                            setContextMenuPosition(undefined);
                          }}>
                          Remove Rule
                        </Menu.Item>
                      )}

                      <Menu.Item
                        c={'red'}
                        onClick={() => {
                          handleRemoveNode();
                          setContextMenuPosition(undefined);
                        }}>
                        Delete
                      </Menu.Item>

                      <Menu.Divider />
                    </Menu.Dropdown>
                  </Menu>
                  <KanbanModal
                    size={'50%'}
                    opened={openedModalAddStartCI}
                    onClose={closeModalAddStartCI}
                    title={'Select CI'}
                    actions={<KanbanButton onClick={() => handleAddCi()}>Add</KanbanButton>}>
                    <KanbanSelect
                      searchable={true}
                      data={ciStartComboboxs}
                      defaultValue={`${nodeSelected?.data?.data?.startCiId === ciStartSelected?.ciId ? ciStartSelected?.ciId : undefined}`}
                      onChange={(value, item) => setCiStartSelected({ ciId: Number(value), ciName: item.label })}
                    />
                  </KanbanModal>

                  <KanbanModal
                    size={'50%'}
                    opened={openedModalEditRules}
                    onClose={closeModalEditRules}
                    title={'Setting CI Rule'}
                    actions={<KanbanButton onClick={() => handleAddRule()}>Add</KanbanButton>}>
                    <ServiceMappingDesignRules setRule={setRule} node={nodeSelected} />
                  </KanbanModal>
                </div>
              )}
            </div>
          </CustomDrag>
        </div>
      </Flex>
    </>
  );
});

ServiceMappingDesign.displayName = 'ServiceMappingDesign';
