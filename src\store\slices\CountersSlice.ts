import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import type { RootStoreType } from '@store';

interface CounterState {
  value: number;
}

const initialState: CounterState = {
  value: 0,
};

export const countersSlice = createSlice({
  name: 'counters',
  initialState,
  reducers: {
    increment(state) {
      state.value++;
    },
    decrement(state) {
      state.value--;
    },
    changeByAmount(state, action: PayloadAction<number>) {
      state.value += action.payload;
    },
  },
});

export const getCounters = (store: RootStoreType) => store.counters;
