import { Box, Flex } from '@mantine/core';
import React, { useCallback, useEffect, useState } from 'react';
import { KanbanText } from 'kanban-design-system';
import { KanbanButton } from 'kanban-design-system';
import { ChangeDetailComponent } from './ChangeDetailComponent';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { ChangeSdpApi } from '@api/ChangeSdpApi';
import type { ChangeResponseDto } from '@models/ChangeSdp';
import { KanbanNumberInput } from 'kanban-design-system';
import { CHANGE_ACTIONS_DISABLE_LINK_SDP, CHANGE_ASSESSMENT_ACTIONS_TO_CREATE, ChangeAssessmentAction } from '@models/ChangeAssessment';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { useSelector } from 'react-redux';
import { checkIsChangeCorThenAuthor } from '@common/utils/ChangeAssessmentUtils';
import { NotificationError } from '@common/utils/NotificationUtils';

const CustomLink = styled(Link)`
  text-decoration: none;
  color: blue;

  &:hover {
    text-decoration: underline;
  }
`;

export type ChangeComponentProps = {
  changeId: number | undefined;
  setChangeId: (val: number | undefined) => void;
  initLinkedChangeId: number | undefined;
  allowEdit: boolean;
  setAllowEdit: (val: boolean) => void;
  setAllowViewImpactData: (val: boolean) => void;
  action: ChangeAssessmentAction;
  author: string;
  sdpRequestChangeId: number | undefined;
  setIsChangeCorThenAuthor: (val: boolean) => void;
};

export const defaultChangeResponse: ChangeResponseDto = {
  changeId: 0,
  changeStatus: '',
  changeStage: '',
  messageResponse: '',
  statusResponse: '',
  changeTitle: '',
  changeCor: '',
  changeSdpLink: '',
  cmdbErrorCode: '',
  cmdbErrorMessage: '',
};

export const ChangeComponent: React.FC<ChangeComponentProps> = ({
  action,
  allowEdit,
  author,
  changeId,
  initLinkedChangeId,
  sdpRequestChangeId,
  setAllowEdit,
  setAllowViewImpactData,
  setChangeId,
  setIsChangeCorThenAuthor,
}) => {
  const [disableUnlinkBtn, setDisableUnlinkBtn] = useState(true);
  const [disableSdpLinkBtn, setDisableSdpLinkBtn] = useState(false);
  const [changeData, setChangeData] = useState<ChangeResponseDto>(defaultChangeResponse);
  const [showData, setShowData] = useState(false);
  const [valueInput, setValueInput] = useState<string | number>(changeId || '');
  const currentUser = useSelector(getCurrentUser);
  const currentUsername = currentUser.data?.username;
  const isSuperAdmin = currentUser.data?.isSuperAdmin === true;
  const resetDataPage = useCallback(() => {
    setShowData(false);
    setChangeData(defaultChangeResponse);
  }, []);

  const unLinkSdp = useCallback(() => {
    setValueInput('');
    setAllowEdit(true);
    setDisableUnlinkBtn(true);
    setDisableSdpLinkBtn(false);
    resetDataPage();
  }, [resetDataPage, setAllowEdit]);

  useEffect(() => {
    setChangeId(valueInput ? Number(valueInput) : undefined);
  }, [setChangeId, valueInput]);

  useEffect(() => {
    if (ChangeAssessmentAction.CREATE === action) {
      setAllowViewImpactData(true);
    }
  }, [action, setAllowViewImpactData]);

  const handleSdpLinkAndUnlink = useCallback(() => {
    if (CHANGE_ASSESSMENT_ACTIONS_TO_CREATE.includes(action) || (ChangeAssessmentAction.UPDATE === action && !initLinkedChangeId)) {
      setDisableUnlinkBtn(false);
    }
    if (!CHANGE_ACTIONS_DISABLE_LINK_SDP.includes(action)) {
      setDisableSdpLinkBtn(true);
    }
  }, [action, initLinkedChangeId]);
  // Call when mount/ on press sdp link
  const getAndValidateChangeData = useCallback(() => {
    if (valueInput) {
      let isShowInvalid = ChangeAssessmentAction.VIEW !== action;
      //Cause this call when mount/ on press sdp link => when UPDATE :get sdp change by ACTION VIEW to avoid check, set isShowInvalid to avoid error notification
      let virtualAction = action;
      if (ChangeAssessmentAction.UPDATE === action && disableSdpLinkBtn) {
        virtualAction = ChangeAssessmentAction.VIEW;
        isShowInvalid = false;
      }
      ChangeSdpApi.getById(Number(valueInput), virtualAction, sdpRequestChangeId, isShowInvalid)
        .then((res) => {
          const resData = res.data;
          //1615: case change info invalid
          if (res.data.cmdbErrorCode) {
            NotificationError({
              message: res.data.cmdbErrorMessage,
            });
            setChangeData(resData);
            setShowData(true);
            handleSdpLinkAndUnlink();
            return;
          }
          setChangeData(resData);
          setShowData(true);
          setAllowViewImpactData(true);
          //1632 when UPDATE & not linked SDP  then able to unlink after get changeData
          handleSdpLinkAndUnlink();
          setIsChangeCorThenAuthor(checkIsChangeCorThenAuthor(resData.changeCor, currentUsername, author));
        })
        .catch(() => {
          resetDataPage();
        });
    }
  }, [
    valueInput,
    action,
    disableSdpLinkBtn,
    sdpRequestChangeId,
    setAllowViewImpactData,
    handleSdpLinkAndUnlink,
    setIsChangeCorThenAuthor,
    currentUsername,
    author,
    resetDataPage,
  ]);
  useEffect(() => {
    if (ChangeAssessmentAction.UPDATE === action && initLinkedChangeId !== undefined && initLinkedChangeId) {
      //if CA linked by SDP changeId
      // initLinkedChangeId !== undefined avoid first render
      //case 1: edit but linked
      //load data at second render,
      //disableBtnLinkSDP
      setDisableSdpLinkBtn(true);
      getAndValidateChangeData();
    }
  }, [action, getAndValidateChangeData, initLinkedChangeId]);

  useEffect(() => {
    if (ChangeAssessmentAction.UPDATE === action && initLinkedChangeId !== undefined && !initLinkedChangeId) {
      //2542-2540: case update change with new sdp link, only owner has permission
      const isAuthor = checkIsChangeCorThenAuthor('', currentUsername, author);
      //2880 Not allow admin to link SDP
      setDisableSdpLinkBtn(!isAuthor);
      setIsChangeCorThenAuthor(isAuthor);
    }
  }, [action, initLinkedChangeId, setIsChangeCorThenAuthor, currentUsername, author, isSuperAdmin]);

  useEffect(() => {
    //1632 if type in SDP link and action !== VIEW/CREATE then AUTO get SDP data, then if action UPDATE/VIEW... then it get data EVERY change
    if (valueInput && ChangeAssessmentAction.VIEW === action) {
      getAndValidateChangeData();
    }
  }, [action, valueInput, getAndValidateChangeData]);

  useEffect(() => {
    if (CHANGE_ACTIONS_DISABLE_LINK_SDP.includes(action)) {
      setDisableSdpLinkBtn(true);
      setDisableUnlinkBtn(true);
    }
    if (ChangeAssessmentAction.COPY === action) {
      unLinkSdp();
    }
  }, [action, unLinkSdp]);

  useEffect(() => {
    if (changeId) {
      setValueInput(changeId);
    }
  }, [changeId]);

  return (
    <Box mt='lg'>
      <Flex direction='column' gap='sm'>
        <Flex gap='xl'>
          <KanbanText fw={500} w={'10%'}>
            SDP Change ID
          </KanbanText>
          <Box w={'100%'}>
            <Flex gap='sm'>
              <KanbanNumberInput
                placeholder='Change ID'
                w={'40%'}
                value={valueInput}
                maxLength={15}
                allowNegative={false}
                allowDecimal={false}
                disabled={!(allowEdit && !disableSdpLinkBtn)}
                onChange={(e) => {
                  setValueInput(e);
                }}
              />
              <KanbanButton variant='filled' w={'10%'} disabled={disableSdpLinkBtn} onClick={getAndValidateChangeData}>
                SDP Link
              </KanbanButton>
              <KanbanButton disabled={disableUnlinkBtn} variant='filled' color='red' w={'10%'} onClick={unLinkSdp}>
                UnLink
              </KanbanButton>
            </Flex>
          </Box>
        </Flex>
        {showData && (
          <>
            <Flex gap='xl'>
              <KanbanText w={'10%'} fw={500}>
                SDP Change URL:
              </KanbanText>

              <CustomLink target={'_blank'} to={changeData.changeSdpLink}>
                {changeData.changeSdpLink}
              </CustomLink>
            </Flex>
            <ChangeDetailComponent data={changeData}></ChangeDetailComponent>
          </>
        )}
      </Flex>
    </Box>
  );
};
