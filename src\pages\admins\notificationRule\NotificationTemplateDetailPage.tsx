import { Editor, EditorContent } from '@tiptap/react';
import { NotificationTemplateApi } from '@api/NotificationTemplateApi';
import { NotificationError, NotificationSuccess } from '@common/utils/NotificationUtils';
import type { MentionQueryProps, NotificationTemplateDetailPageProps, NotificationTemplateDto } from '@models/NotificationTemplate';
import { KanbanButton, KanbanModal, KanbanTitle } from 'kanban-design-system';
import React, { useCallback, useEffect, useState } from 'react';
import { RichTextEditor, Link } from '@mantine/tiptap';
import Highlight from '@tiptap/extension-highlight';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import Superscript from '@tiptap/extension-superscript';
import SubScript from '@tiptap/extension-subscript';
import Mention from '@tiptap/extension-mention';
import Document from '@tiptap/extension-document';
import Paragraph from '@tiptap/extension-paragraph';
import Text from '@tiptap/extension-text';
import styleScss from './MentionList.module.scss';
import '@mantine/tiptap/styles.css';
import suggestion from './SuggestionOption';
import { NotificationTemplateParamPrefix } from '@common/constants/NotificationTemplateConstants';
import GuardComponent from '@components/GuardComponent';
import { AclPermission } from '@models/AclPermission';
import styled from 'styled-components';
const EditorItem = styled.div`
  border: 1px solid var(--mantine-color-gray-4);
  border-radius: var(--mantine-radius-xs);
`;
const getMentionDataList = (queryProps: MentionQueryProps, templateData: NotificationTemplateDto) => {
  if (!templateData.params) {
    return [];
  }
  return (
    templateData.params
      .map((item) => item.value?.replace(NotificationTemplateParamPrefix, ''))
      .filter((item) => item?.toLowerCase().startsWith(queryProps.query.toLowerCase())) || []
  );
};

enum NotificationTemplateParamEnum {
  // CIID,
  DRAFT_ID = 'DraftID',
  // CIname,
  // CItype,
  REQUESTER = 'Requester',
  // Approver,
  // DraftURL,
  ACTIONREQUIRINGAPPROVAL = 'ActionRequiringApproval',
}
const PARAM_LENGTH_LIST: Record<NotificationTemplateParamEnum, number> = {
  [NotificationTemplateParamEnum.ACTIONREQUIRINGAPPROVAL]: 10,
  [NotificationTemplateParamEnum.DRAFT_ID]: 10,
  [NotificationTemplateParamEnum.REQUESTER]: 35,
};
const SUBJECT_PARAM_LIST = [
  NotificationTemplateParamEnum.ACTIONREQUIRINGAPPROVAL,
  NotificationTemplateParamEnum.DRAFT_ID,
  NotificationTemplateParamEnum.REQUESTER,
];
const sampleStrSize = (size: number) => {
  return '#'.repeat(size);
};
const SUBJECT_MAX_LENGTH_IN_BYTE = 160;
export const NotificationTemplateDetailPage: React.FC<NotificationTemplateDetailPageProps> = ({
  closeModalEditTemplate,
  id,
  openedModalEditTemplate,
}) => {
  const [currentTemplate, setCurrentTemplate] = useState<NotificationTemplateDto>({});
  const [editor, setEditor] = useState<Editor>();
  const [subjectEditor, setSubjectEditor] = useState<Editor>();

  const handleChangeEditor = (templateData: NotificationTemplateDto) => {
    setEditor(
      new Editor({
        extensions: [
          StarterKit,
          Underline,
          Link,
          Superscript,
          SubScript,
          Highlight,
          TextAlign.configure({ types: ['heading', 'paragraph'] }),
          Mention.configure({
            HTMLAttributes: {
              class: styleScss['tiptap-mention'],
            },
            suggestion: {
              ...suggestion,
              items: (queryProps: MentionQueryProps) => {
                return getMentionDataList(queryProps, templateData);
              },
            },
          }),
        ],
        content: templateData.message,
      }),
    );
    setSubjectEditor(
      new Editor({
        extensions: [
          Document,
          Paragraph.configure({
            HTMLAttributes: {
              class: styleScss['editor-subject'],
            },
          }),
          Text,
          Mention.configure({
            HTMLAttributes: {
              class: styleScss['tiptap-mention'],
            },
            suggestion: {
              ...suggestion,
              items: (queryProps: MentionQueryProps) => {
                // Mail service limit  subject max =166 => limit param
                return getMentionDataList(queryProps, templateData).filter((it) => SUBJECT_PARAM_LIST.includes(it as NotificationTemplateParamEnum));
              },
            },
          }),
        ],
        content: templateData.subject,
      }),
    );
  };

  const fetchTemplate = useCallback(() => {
    if (id && openedModalEditTemplate) {
      NotificationTemplateApi.getById(id)
        .then((res) => {
          setCurrentTemplate((prev) => ({ ...prev, ...res.data }));
          handleChangeEditor(res.data);
        })
        .catch(() => {});
    }
  }, [id, setCurrentTemplate, openedModalEditTemplate]);

  useEffect(() => {
    setCurrentTemplate({});
    fetchTemplate();
  }, [fetchTemplate]);

  const handleCloseModalEditTemplate = () => {
    closeModalEditTemplate();
  };
  const validateSubject = (subjectText?: string) => {
    if (!subjectText) {
      return true;
    }

    const subjectLengthNoti = Object.entries(PARAM_LENGTH_LIST)
      .map(([key, value]) => `${NotificationTemplateParamPrefix}${key}: ${value}`)
      .join(', ');
    SUBJECT_PARAM_LIST.forEach((it) => {
      subjectText = subjectText?.replaceAll(NotificationTemplateParamPrefix + it, `${sampleStrSize(PARAM_LENGTH_LIST[it])}`);
    });

    const encoder = new TextEncoder();
    const byteArray = encoder.encode(subjectText);
    if (byteArray.length > SUBJECT_MAX_LENGTH_IN_BYTE) {
      NotificationError({
        message: `Subject length may exceed ${byteArray.length} bytes while limit by ${SUBJECT_MAX_LENGTH_IN_BYTE}, with param max length(${subjectLengthNoti})`,
      });
      return false;
    }
    return true;
  };
  const handleSaveTemplate = useCallback(() => {
    //2472: data has been insert by manual into db, then currentTemplate.id is always has value
    if (currentTemplate.id) {
      const subjectText = subjectEditor?.getText();

      if (!validateSubject(subjectText)) {
        return;
      }
      const updateTemplate: NotificationTemplateDto = { ...currentTemplate, message: editor?.getHTML(), subject: subjectEditor?.getText() };

      NotificationTemplateApi.updateTemplate(currentTemplate.id, updateTemplate)
        .then(() => {
          NotificationSuccess({
            message: 'Save mail template successfully',
          });
          closeModalEditTemplate();
        })
        .catch(() => {});
    }
  }, [currentTemplate, editor, subjectEditor, closeModalEditTemplate]);

  // const handleChangeSubject = (e: string) => {
  //   setCurrentTemplate((prev) => ({ ...prev, subject: e }));
  // };
  return (
    <KanbanModal
      size={'80%'}
      centered
      actions={
        <GuardComponent requirePermissions={[AclPermission.updateNotificationTemplate]}>
          <KanbanButton onClick={handleSaveTemplate}>Save</KanbanButton>
        </GuardComponent>
      }
      title={'Edit notification template'}
      onClose={handleCloseModalEditTemplate}
      opened={openedModalEditTemplate}>
      {/* <KanbanTextarea
        label={<KanbanTitle order={5}>Subject</KanbanTitle>}
        autosize
        value={currentTemplate.subject}
        onChange={(e) => handleChangeSubject(e.target.value)}
      /> */}
      {subjectEditor && (
        <>
          <KanbanTitle order={5}>Subject</KanbanTitle>
          <EditorItem>
            <EditorContent editor={subjectEditor}></EditorContent>
          </EditorItem>
        </>
      )}
      <KanbanTitle order={5}>Message</KanbanTitle>
      {editor && (
        <RichTextEditor editor={editor}>
          <RichTextEditor.Toolbar sticky stickyOffset={60}>
            <RichTextEditor.ControlsGroup>
              <RichTextEditor.Bold />
              <RichTextEditor.Italic />
              <RichTextEditor.Underline />
              <RichTextEditor.Strikethrough />
              <RichTextEditor.ClearFormatting />
              <RichTextEditor.Highlight />
              <RichTextEditor.Code />
            </RichTextEditor.ControlsGroup>

            <RichTextEditor.ControlsGroup>
              <RichTextEditor.H1 />
              <RichTextEditor.H2 />
              <RichTextEditor.H3 />
              <RichTextEditor.H4 />
            </RichTextEditor.ControlsGroup>

            <RichTextEditor.ControlsGroup>
              <RichTextEditor.Blockquote />
              <RichTextEditor.Hr />
              <RichTextEditor.BulletList />
              <RichTextEditor.OrderedList />
              <RichTextEditor.Subscript />
              <RichTextEditor.Superscript />
            </RichTextEditor.ControlsGroup>

            <RichTextEditor.ControlsGroup>
              <RichTextEditor.Link />
              <RichTextEditor.Unlink />
            </RichTextEditor.ControlsGroup>

            <RichTextEditor.ControlsGroup>
              <RichTextEditor.AlignLeft />
              <RichTextEditor.AlignCenter />
              <RichTextEditor.AlignJustify />
              <RichTextEditor.AlignRight />
            </RichTextEditor.ControlsGroup>
          </RichTextEditor.Toolbar>

          <RichTextEditor.Content />
        </RichTextEditor>
      )}
    </KanbanModal>
  );
};
