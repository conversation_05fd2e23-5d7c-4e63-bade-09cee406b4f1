import { Text } from '@mantine/core';
import React from 'react';
import { DragTranfromMapType } from '@models/DiscoveryTransformMap';
import { DraggableItemBase } from './DraggableItemBase';

interface TargetAttributeItemProps {
  item: DragTranfromMapType;
  index: number;
  onClick: () => void;
  classes: Record<string, string>;
  dragableRef?: React.Ref<HTMLDivElement | null>;
  paperStyle?: React.CSSProperties;
}

export const TargetAttributeItem = ({ classes, dragableRef, index, item, onClick, paperStyle: externalPaperStyle }: TargetAttributeItemProps) => {
  return (
    <DraggableItemBase
      dragableRef={dragableRef}
      item={item}
      index={index}
      classes={classes}
      onClick={onClick}
      draggableIdPrefix='target_map'
      paperProps={{
        c: item.deleted ? 'red' : 'black',
      }}
      paperStyle={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        ...externalPaperStyle,
      }}
      renderContent={(item) => (
        <Text w={200} truncate='end'>
          {item.label}
        </Text>
      )}
    />
  );
};
