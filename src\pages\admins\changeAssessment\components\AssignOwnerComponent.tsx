import UserPicker from '@components/commonCi/advanceSearch/UserPicker';
import type { PaginationRequestModel } from '@models/EntityModelBase';
import type { EntityUserInfoResponse } from '@api/systems/UsersApi';
import { useState } from 'react';
import { ChangeAssessmentApi } from '@api/ChangeAssessmentApi';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Flex } from '@mantine/core';
import { KanbanButton, KanbanModal } from 'kanban-design-system';
import { IconUser } from '@tabler/icons-react';

import { changeAssessmentPath } from '@common/utils/RouterUtils';

export type AssignOwnerProps = {
  id: number;
  draftId: string;
  closeModal: () => void;
};

export const AssignOwnerComponent: React.FC<AssignOwnerProps> = ({ closeModal, draftId, id }) => {
  const navigate = useNavigate();

  const [assignOwner, setAssignOwner] = useState('');

  const findListOwnerUser = (pageInfo: PaginationRequestModel<EntityUserInfoResponse>) => {
    return ChangeAssessmentApi.getAllOwnerUsers(pageInfo);
  };

  const onSubmitChangeUserOwner = () => {
    ChangeAssessmentApi.assignOwnerById(assignOwner, id)
      .then(() => {
        NotificationSuccess({
          message: 'Assign successfully',
        });
        closeModal();
        navigate(changeAssessmentPath);
      })
      .catch(() => {});
  };

  const onChangeAssignOwner = (_data: string) => {
    setAssignOwner(_data);
  };

  return (
    <>
      <KanbanModal
        size={'xl'}
        centered
        opened={true}
        onClose={closeModal}
        title={`Assign owner of ${draftId}`}
        actions={
          <Flex>
            <KanbanButton leftSection={<IconUser />} onClick={onSubmitChangeUserOwner} disabled={assignOwner === ''}>
              Assign owner
            </KanbanButton>
          </Flex>
        }>
        <UserPicker
          value={''}
          disabled={false}
          loadUser={(pageInfo) => findListOwnerUser(pageInfo)}
          onChange={(_data) => onChangeAssignOwner(_data)}
        />
      </KanbanModal>
    </>
  );
};

export default AssignOwnerComponent;
