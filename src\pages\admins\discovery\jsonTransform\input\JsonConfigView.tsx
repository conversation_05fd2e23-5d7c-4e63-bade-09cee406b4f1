import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import 'rc-tree/assets/index.css';
import { Flex, Paper, ScrollArea } from '@mantine/core';
import { KanbanButton } from 'kanban-design-system';
import { CustomDataNode, inValidCustomDataNodes } from '../helper/JsonTransformHelper';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import styles from './JsonConfigView.module.scss';
import styled from 'styled-components';
import DroppedItemsForm, { DroppedItemsFormMethods } from './DroppedItemsForm';
import { v4 as uuidv4 } from 'uuid';
import { useDataTransform } from '@context/DataTransformContext';
import { useJsonTransformContext } from '@context/JsonTransformContext';
import { JsonTransformActionType } from '@common/constants/JsonTransformActionType';
import { useDroppedItemsRefs } from '@context/DragScrollContext';

type JsonConfigViewerProps = {
  // droppedItems: CustomDataNode[];
  onDragOver: (e: React.DragEvent<HTMLDivElement>) => void;
  onDrop: (e: React.DragEvent<HTMLDivElement>) => void;
  sourceId: number;
};

const DragContainer = styled.div<{ isDragging: boolean }>`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  width: 100%;
  padding: 20px;
  overflow-y: auto;
  border-radius: 10px;
  border: 1px dashed ${(props) => (props.isDragging ? '#159cc5' : 'rgb(204, 204, 204)')};
  transition: border-color 0.3s ease;

  .json-config-add-field {
    color: red;
  }
`;

export type JsonConfigViewerMethods = {
  handleAddField: (newFields: CustomDataNode[]) => void;
};

const JsonConfigView = forwardRef<JsonConfigViewerMethods, JsonConfigViewerProps>(({ onDragOver, onDrop, sourceId }, ref) => {
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const formRef = useRef<DroppedItemsFormMethods>(null);
  const { state: dataTransform } = useDataTransform();
  const jsonData = dataTransform?.jsonData;
  const columnDatas = dataTransform?.columnDatasForInput; // all column before click Save
  const { dispatch: jsonTransformDispatch } = useJsonTransformContext();
  const { onUpdateItemIdWhenDropCurrent } = useDroppedItemsRefs();

  const handleAddField = (newFields: CustomDataNode[]) => {
    formRef.current?.handleAddField(newFields);
  };

  const handleTestSubmit = () => {
    if (inValidCustomDataNodes(columnDatas, jsonData)) {
      jsonTransformDispatch({ type: JsonTransformActionType.UPDATE_FORM_JSON, payload: false });
    }
    formRef.current?.submitForm();
  };

  useImperativeHandle(ref, () => ({
    handleAddField,
  }));

  return (
    <Paper
      withBorder
      p='xs'
      h={'calc(var(--kanban-modal-content-max-height) - var(--mantine-spacing-xl))'}
      style={{ display: 'flex', flexDirection: 'column' }}>
      <HeaderTitleComponent
        title='Add and Edit Fields'
        rightSection={
          <Flex gap={10}>
            <KanbanButton onClick={handleTestSubmit}>Test</KanbanButton>
          </Flex>
        }
      />
      {/* {fieldComponentList} */}
      <ScrollArea flex={1}>
        <DroppedItemsForm ref={formRef} sourceId={sourceId} />
      </ScrollArea>
      <DragContainer
        className={styles['json-config-drag']}
        isDragging={isDragging}
        onDragOver={(e) => {
          onDragOver(e);
          setIsDragging(true);
        }}
        onDrop={(e) => {
          onDrop(e);
          setIsDragging(false);
        }}
        onClick={() => {
          const createNewFieldDefault: CustomDataNode = {
            key: '',
            value: '',
            originalJsonPath: '',
            title: '',
            id: uuidv4(),
            jsonPath: '',
          };
          const elementKey = String(createNewFieldDefault.id).toLowerCase();
          onUpdateItemIdWhenDropCurrent(elementKey);
          handleAddField([createNewFieldDefault]);
        }}>
        {' '}
        <p className={styles['text-hover']}>
          Drag input fields here or <span className={styles['json-config-add-field']}>Add Field</span>
        </p>
      </DragContainer>
    </Paper>
  );
});
JsonConfigView.displayName = 'JsonConfigView';
export default JsonConfigView;
