.icon {
  opacity: 0.5;
  transition: opacity 0.3s ease-in-out;
}

.icon:hover {
  opacity: 1;
}

.toolbar {
  opacity: 1;
  transition: opacity 0.3s ease-in-out;
  position: absolute;
  z-index: 151;
  top: 50%;
  transform: translateY(-50%);
  padding: var(--mantine-spacing-xs);
  border: 0.3px solid var(--mantine-color-primary-6);
  background-color: white;
}

.toolbar:hover {
  opacity: 1;
}

.icon-graph-view-top-right {
  opacity: 1;
  transition: opacity 0.3s ease-in-out;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  padding: 10px;
}

.icon-graph-view-top-right:hover {
  opacity: 1;
}

.icon-zoom {
  position: absolute;
  z-index: 152;
  bottom: 40px;
  right: 10px;
}

.icon-zoom:hover {
  opacity: 1;
}

.diagram {
  height: 100%;
  min-height: 700px;
  width: 100%;
  background: white;
}

.clipText {
  white-space: nowrap;
  //   width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
}