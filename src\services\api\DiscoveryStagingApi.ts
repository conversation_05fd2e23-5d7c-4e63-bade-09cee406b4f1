import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import type { PaginationResponseModel } from '@models/EntityModelBase';
export type DiscoveryStagingModel = {
  id?: number;
  name: string;
};
export type DiscoveryStagingStructureModel = {
  id: number;
  versionId: number;
  columnName: string;
  columnMappingData: string;
};
export type DiscoveryStagingResponse = DiscoveryStagingModel;
export type DiscoveryStagingStructureResponse = DiscoveryStagingStructureModel;
export type DiscoveryStagingPagingResponse = PaginationResponseModel<DiscoveryStagingModel>;
export class DiscoveryStagingApi extends BaseApi {
  static baseUrl = BaseUrl.discoveryStagings;

  static getAll() {
    return BaseApi.getData<DiscoveryStagingResponse[]>(`${this.baseUrl}`);
  }

  static getAllColumns(id: number) {
    return BaseApi.getData<DiscoveryStagingStructureResponse[]>(`${this.baseUrl}/${id}/discovery-staging-stuctures`);
  }
}
