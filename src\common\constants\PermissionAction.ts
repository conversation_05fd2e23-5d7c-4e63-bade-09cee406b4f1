import type { ConfigItemPermissionOtherDto } from '@models/ConfigItemTypePermission';
import { PermissionActionType } from './PermissionActionType';

export enum PermissionAction {
  // System permissions
  SYSTEM__MANAGE = 'SYSTEM__MANAGE', // View list action of admin permissions
  SYSTEM__VIEW_GUIDE = 'SYSTEM__VIEW_GUIDE', // View develop Guide
  //Out going mail config
  OUT_GOING_MAIL_CONFIG__CREATE = 'OUT_GOING_MAIL_CONFIG__CREATE',
  OUT_GOING_MAIL_CONFIG__VIEW = 'OUT_GOING_MAIL_CONFIG__VIEW',
  OUT_GOING_MAIL_CONFIG__TEST_MAIL = 'OUT_GOING_MAIL_CONFIG__TEST_MAIL',

  // User permissions
  USER__VIEW_LIST = 'USER__VIEW_LIST', // View the list of users in the system.
  USER__VIEW_DETAIL = 'USER__VIEW_DETAIL', // View detailed information of users in the system.
  USER__ADD_TO_GROUP = 'USER__ADD_TO_GROUP', // Add users to a User Group.
  USER__DELETE_GROUP_IN_USER = 'USER__DELETE_GROUP_IN_USER', // Delete group of user.
  USER__UPDATE = 'USER__UPDATE', // Update status active/inactive users.
  USER__DELETE = 'USER__DELETE', // Delete users.
  USER__CREATE = 'USER__CREATE', // Add user

  // Group permissions
  GROUP__VIEW_LIST = 'GROUP__VIEW_LIST', // View the list of user groups in the system.
  GROUP__CREATE = 'GROUP__CREATE', // Create new user groups.
  GROUP__VIEW_DETAIL = 'GROUP__VIEW_DETAIL', // View detail group
  GROUP__UPDATE = 'GROUP__UPDATE', // Update information of user groups.
  // GROUP__VIEW_LIST_USER = 'GROUP__VIEW_LIST_USER', // View list user of group.
  // GROUP__UPDATE_PERMISSION_OTHER = 'GROUP__UPDATE_PERMISSION_OTHER', // Set other permissions.
  GROUP__ADD_USER = 'GROUP__ADD_USER', // Add new users to groups.
  GROUP__DELETE_USER = 'GROUP__DELETE_USER', // Delete users to groups.
  GROUP__DELETE = 'GROUP__DELETE', // Delete user groups.
  // GROUP__UPDATE_PERMISSION_CI_TYPE_OR_CI = 'GROUP__UPDATE_PERMISSION_CI_TYPE_OR_CI', // Configure permissions corresponding to CI and CI type.
  GROUP__ADD_ROLE_TO_GROUP = 'GROUP__ADD_ROLE_TO_GROUP', // Add role to group
  GROUP__DELETE_ROLE_IN_GROUP = 'GROUP__DELETE_ROLE_IN_GROUP', // Delete role in group
  // GROUP__VIEW_LIST_ROLE_IN_GROUP = 'GROUP__VIEW_LIST_ROLE_IN_GROUP', // View list role of group.
  // CI Type permissions
  CI_TYPE__VIEW_LIST = 'CI_TYPE__VIEW_LIST', // View the list of CiTypes.
  CI_TYPE__VIEW_LIST_CI = 'CI_TYPE__VIEW_LIST_CI', // View the list Ci of CiTypes.
  CI_TYPE__CREATE = 'CI_TYPE__CREATE', // Create new parent CI Types.
  CI_TYPE__CONFIG_ATTRIBUTE = 'CI_TYPE__CONFIG_ATTRIBUTE', // Add default/custom attributes to CI Types.
  // CI_TYPE__ADD_SUGGEST_RELATIONSHIP = 'CI_TYPE__ADD_SUGGEST_RELATIONSHIP', // Add suggested relationships.
  // CI_TYPE__CREATE_CHILD = 'CI_TYPE__CREATE_CHILD', // Create new child CI Types.
  CI_TYPE__UPDATE = 'CI_TYPE__UPDATE', // Update information of CI Types.
  CI_TYPE__DELETE = 'CI_TYPE__DELETE', // Delete CI Types.
  CI_TYPE__VIEW_DETAIL = 'CI_TYPE__VIEW_DETAIL', // View Detail CI Types.

  // CI permissions
  CI__VIEW_BASIC = 'CI__VIEW_BASIC', // View information default of Ci.
  CI__VIEW_ADVANCED = 'CI__VIEW_ADVANCED', // View detailed information of CIs.
  CI__APPROVE = 'CI__APPROVE', // Grant approve rights for CI drafts to users.
  CI__UPDATE = 'CI__UPDATE', // Update information of CIs.
  CI__CREATE = 'CI__CREATE', // Create new CIs.
  CI__IMPORT = 'CI__IMPORT', // Import new CIs.
  CI__DELETE = 'CI__DELETE', // Delete CIs.
  // CI__ADD_RELATIONSHIP = 'CI__ADD_RELATIONSHIP', // Add new relationships to CIs.

  // CI Draft permissions
  // CI_DRAFT__VIEW_LIST = 'CI_DRAFT__VIEW_LIST', // View the list of CI Drafts.
  CI_DRAFT__UPDATE = 'CI_DRAFT__UPDATE', // Update Ci draft
  CI_DRAFT__DELETE = 'CI_DRAFT__DELETE', // Update Ci draft

  // Business View permissions
  BUSINESS_VIEW__VIEW_LIST = 'BUSINESS_VIEW__VIEW_LIST', // View the list of Business views.
  BUSINESS_VIEW__CREATE = 'BUSINESS_VIEW__CREATE', // Create new Business views.
  BUSINESS_VIEW__VIEW_DETAIL = 'BUSINESS_VIEW__VIEW_DETAIL', // View detailed information of Business views.
  BUSINESS_VIEW__UPDATE = 'BUSINESS_VIEW__UPDATE', // Update information of Business views.
  BUSINESS_VIEW__DELETE = 'BUSINESS_VIEW__DELETE', // Delete Business views.

  // Service Map permissions
  SERVICE_MAP__VIEW_LIST = 'SERVICE_MAP__VIEW_LIST', // View the list of Service Maps.
  SERVICE_MAP__CREATE = 'SERVICE_MAP__CREATE', // Create new Service Maps.
  SERVICE_MAP__VIEW_DETAIL = 'SERVICE_MAP__VIEW_DETAIL', // View detailed information of Service Maps.
  SERVICE_MAP__UPDATE = 'SERVICE_MAP__UPDATE', // Update information of Service Maps.
  // SERVICE_MAP__DELETE = 'SERVICE_MAP__DELETE', // Delete Service Maps.

  // Change View permissions
  CHANGE__VIEW_LIST = 'CHANGE__VIEW_LIST', // View the list of Changes.
  CHANGE__CREATE = 'CHANGE__CREATE', // Create new Changes.
  CHANGE__VIEW_DETAIL = 'CHANGE__VIEW_DETAIL', // View detailed information of Changes.
  CHANGE__UPDATE = 'CHANGE__UPDATE', // Update information of Changes.
  CHANGE__DELETE = 'CHANGE__DELETE', // Delete Changes.

  // Relationship Type permissions
  RELATIONSHIP_TYPE__CREATE = 'RELATIONSHIP_TYPE__CREATE', // Create new Relationship Types.
  RELATIONSHIP_TYPE__UPDATE = 'RELATIONSHIP_TYPE__UPDATE', // Update information of Relationship Types.
  RELATIONSHIP_TYPE__DELETE = 'RELATIONSHIP_TYPE__DELETE', // Delete Relationship Types.

  // Ci Relationship Permissions
  CI_RELATIONSHIP__CREATE = 'CI_RELATIONSHIP__CREATE', //  Create new Ci Relationship.
  CI_RELATIONSHIP__IMPORT = 'CI_RELATIONSHIP__IMPORT', //  Import new Ci Relationship.
  CI_RELATIONSHIP__DELETE = 'CI_RELATIONSHIP__DELETE', //  Delete Ci Relationship.

  // Incident Permissions
  INCIDENT__VIEW_LIST = 'INCIDENT__VIEW_LIST', //  View list Incident.
  INCIDENT__CREATE = 'INCIDENT__CREATE', //  Create Incident.
  INCIDENT__VIEW_DETAIL = 'INCIDENT__VIEW_DETAIL', //  View detail Incident.
  INCIDENT__UPDATE = 'INCIDENT__UPDATE', //  Update Incident.
  INCIDENT__DELETE = 'INCIDENT__DELETE', //  Delete Incident.

  // Role permissions
  ROLE__VIEW_LIST = 'ROLE__VIEW_LIST', // View the list of role.
  ROLE__CREATE = 'ROLE__CREATE', // Create new role.
  ROLE__VIEW_DETAIL = 'ROLE__VIEW_DETAIL', // View detail role
  ROLE__UPDATE = 'ROLE__UPDATE', // Update information of role.
  ROLE__DELETE = 'ROLE__DELETE', // Delete role.
  ROLE__ADD_GROUP_TO_ROLE = 'ROLE__ADD_GROUP_TO_ROLE', // Add group to role
  ROLE__DELETE_GROUP_INTO_ROLE = 'ROLE__DELETE_GROUP_INTO_ROLE', // Delete group in role
  // ROLE__VIEW_LIST_GROUP_IN_ROLE = 'ROLE__VIEW_LIST_GROUP_IN_ROLE', // View list group of role

  //permission unit constant notification template ;
  NOTIFICATION_TEMPLATE__VIEW_DETAIL = 'NOTIFICATION_TEMPLATE__VIEW_DETAIL',
  NOTIFICATION_TEMPLATE__VIEW_LIST = 'NOTIFICATION_TEMPLATE__VIEW_LIST',
  NOTIFICATION_TEMPLATE__UPDATE = 'NOTIFICATION_TEMPLATE__UPDATE',
  NOTIFICATION_TEMPLATE__CHANGE_ACTIVE = 'NOTIFICATION_TEMPLATE__CHANGE_ACTIVE',
  NOTIFICATION_TEMPLATE__CREATE = 'NOTIFICATION_TEMPLATE__CREATE',
  NOTIFICATION_TEMPLATE__DELETE_LIST = 'NOTIFICATION_TEMPLATE__DELETE_LIST',

  IMPACTED_RULE__VIEW_LIST = 'IMPACTED_RULE__VIEW_LIST',
  IMPACTED_RULE__DELETE = 'IMPACTED_RULE__DELETE',
  IMPACTED_RULE__VIEW_DETAIL = 'IMPACTED_RULE__VIEW_DETAIL',
  IMPACTED_RULE__CREATE = 'IMPACTED_RULE__CREATE',
  IMPACTED_RULE__UPDATE = 'IMPACTED_RULE__UPDATE',

  // Job config
  JOB_CONFIG__VIEW_LIST = 'JOB_CONFIG__VIEW_LIST', // View the list of job.
  JOB_CONFIG__CREATE = 'JOB_CONFIG__CREATE', // Create new job.
  JOB_CONFIG__VIEW_DETAIL = 'JOB_CONFIG__VIEW_DETAIL', // View detail job
  JOB_CONFIG__UPDATE = 'JOB_CONFIG__UPDATE', // Update information of job.
  JOB_CONFIG__DELETE = 'JOB_CONFIG__DELETE', // Delete job.
  JOB_CONFIG__VIEW_LOG = 'JOB_CONFIG__VIEW_LOG', // View log.
}

const adminPermissionActions: PermissionAction[] = [
  PermissionAction.SYSTEM__MANAGE,
  PermissionAction.SYSTEM__VIEW_GUIDE,
  PermissionAction.USER__VIEW_LIST,
  PermissionAction.USER__VIEW_DETAIL,
  PermissionAction.USER__ADD_TO_GROUP,
  PermissionAction.USER__DELETE_GROUP_IN_USER,
  PermissionAction.USER__UPDATE,
  PermissionAction.USER__DELETE,
  // PermissionAction.USER__CREATE,
  PermissionAction.GROUP__VIEW_LIST,
  PermissionAction.GROUP__CREATE,
  PermissionAction.GROUP__VIEW_DETAIL,
  PermissionAction.GROUP__UPDATE,
  PermissionAction.GROUP__ADD_USER,
  PermissionAction.GROUP__DELETE,
  PermissionAction.GROUP__DELETE_USER,
  PermissionAction.GROUP__ADD_ROLE_TO_GROUP,
  PermissionAction.GROUP__DELETE_ROLE_IN_GROUP,
  PermissionAction.RELATIONSHIP_TYPE__CREATE,
  PermissionAction.RELATIONSHIP_TYPE__UPDATE,
  PermissionAction.RELATIONSHIP_TYPE__DELETE,
  PermissionAction.CI_TYPE__VIEW_LIST,
  // PermissionAction.CI_TYPE__VIEW_LIST_CI,
  PermissionAction.CI_TYPE__CREATE,
  PermissionAction.CI_TYPE__CONFIG_ATTRIBUTE,
  PermissionAction.CI_TYPE__UPDATE,
  PermissionAction.CI_TYPE__DELETE,
  PermissionAction.CI_TYPE__VIEW_DETAIL,
  PermissionAction.ROLE__CREATE,
  PermissionAction.ROLE__UPDATE,
  PermissionAction.ROLE__DELETE,
  PermissionAction.ROLE__VIEW_LIST,
  PermissionAction.ROLE__VIEW_DETAIL,
  //out going mail
  PermissionAction.OUT_GOING_MAIL_CONFIG__CREATE,
  PermissionAction.OUT_GOING_MAIL_CONFIG__VIEW,
  PermissionAction.OUT_GOING_MAIL_CONFIG__TEST_MAIL,

  // admin : permission unit template
  PermissionAction.NOTIFICATION_TEMPLATE__VIEW_DETAIL,
  PermissionAction.NOTIFICATION_TEMPLATE__UPDATE,
  PermissionAction.NOTIFICATION_TEMPLATE__CHANGE_ACTIVE,
  PermissionAction.NOTIFICATION_TEMPLATE__VIEW_LIST,
  PermissionAction.NOTIFICATION_TEMPLATE__CREATE,
  PermissionAction.NOTIFICATION_TEMPLATE__DELETE_LIST,
];

const applicationPermissionActions: PermissionAction[] = [
  PermissionAction.BUSINESS_VIEW__VIEW_LIST,
  PermissionAction.BUSINESS_VIEW__CREATE,
  PermissionAction.BUSINESS_VIEW__VIEW_DETAIL,
  PermissionAction.BUSINESS_VIEW__UPDATE,
  PermissionAction.BUSINESS_VIEW__DELETE,
  PermissionAction.SERVICE_MAP__VIEW_LIST,
  PermissionAction.SERVICE_MAP__CREATE,
  PermissionAction.SERVICE_MAP__VIEW_DETAIL,
  PermissionAction.SERVICE_MAP__UPDATE,
  // PermissionAction.SERVICE_MAP__DELETE,
  PermissionAction.CHANGE__VIEW_LIST,
  PermissionAction.CHANGE__CREATE,
  PermissionAction.CHANGE__VIEW_DETAIL,
  PermissionAction.CHANGE__UPDATE,
  PermissionAction.CHANGE__DELETE,
  PermissionAction.CI_RELATIONSHIP__CREATE,
  PermissionAction.CI_RELATIONSHIP__IMPORT,
  PermissionAction.CI_RELATIONSHIP__DELETE,
  PermissionAction.INCIDENT__VIEW_LIST,
  PermissionAction.INCIDENT__CREATE,
  PermissionAction.INCIDENT__VIEW_DETAIL,
  PermissionAction.INCIDENT__UPDATE,
  PermissionAction.INCIDENT__DELETE,
];

const ciPermissionActions: PermissionAction[] = [
  PermissionAction.CI__VIEW_BASIC,
  PermissionAction.CI__VIEW_ADVANCED,
  PermissionAction.CI__APPROVE,
  PermissionAction.CI__UPDATE,
  PermissionAction.CI__DELETE,
];

const ciTypePermissionActions: PermissionAction[] = [
  PermissionAction.CI__VIEW_BASIC,
  PermissionAction.CI__VIEW_ADVANCED,
  PermissionAction.CI__APPROVE,
  PermissionAction.CI__UPDATE,
  PermissionAction.CI__CREATE,
  PermissionAction.CI__DELETE,
  PermissionAction.CI__IMPORT,
  PermissionAction.CI_TYPE__VIEW_LIST_CI,
  PermissionAction.CI_DRAFT__DELETE,
  PermissionAction.CI_DRAFT__UPDATE,
];

export const permissionActionsMap: { [key in PermissionActionType]: PermissionAction[] } = {
  [PermissionActionType.ADMIN]: adminPermissionActions,
  [PermissionActionType.APPLICATIONS]: applicationPermissionActions,
  [PermissionActionType.CI]: ciPermissionActions,
  [PermissionActionType.CI_TYPE]: ciTypePermissionActions,
};

export type PermissionActionDescriptions = Partial<{
  [key in PermissionAction]: string;
}>;

export const adminPermissionDescriptions: ConfigItemPermissionOtherDto[] = [
  {
    action: PermissionAction.SYSTEM__MANAGE,
    meaning: 'Quyền truy cập vào trang quản trị hệ thống',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.SYSTEM__VIEW_GUIDE,
    meaning: 'Xem danh sách tài liệu hướng dẫn hệ thống',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.USER__VIEW_LIST,
    meaning: 'Xem danh sách user trong hệ thống',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.USER__VIEW_DETAIL,
    meaning: 'Xem chi tiết thông tin user trong hệ thống',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.USER__ADD_TO_GROUP,
    meaning: 'Thêm người dùng vào Group User',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.USER__DELETE_GROUP_IN_USER,
    meaning: 'Xóa group của user',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.USER__CREATE,
    meaning: 'Thêm user trong hệ thống',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.USER__UPDATE,
    meaning: 'Cập nhật thông tin user trong hệ thống',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.USER__DELETE,
    meaning: 'Xóa người dùng ra khỏi hệ thống',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.GROUP__VIEW_LIST,
    meaning: 'Xem danh sách group người dùng trong hệ thống',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.GROUP__CREATE,
    meaning: 'Thêm mới group người dùng',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.GROUP__VIEW_DETAIL,
    meaning: 'Xem thông tin chi tiết của group',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.GROUP__UPDATE,
    meaning: 'Cập nhật thông tin group',
    type: PermissionActionType.ADMIN,
  },
  // {
  //   action: PermissionAction.GROUP__VIEW_LIST_USER,
  //   meaning: 'Xem danh sách user của 1 group nào đó',
  //   type: PermissionActionType.ADMIN,
  //   isChecked: false,
  // },
  {
    action: PermissionAction.GROUP__ADD_USER,
    meaning: 'Thêm mới người dùng vào group',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.GROUP__DELETE,
    meaning: 'Xóa group người dùng',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.GROUP__DELETE_USER,
    meaning: 'Xóa người dùng ra khỏi group',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.GROUP__ADD_ROLE_TO_GROUP,
    meaning: 'Thêm role vào trong group',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.GROUP__DELETE_ROLE_IN_GROUP,
    meaning: 'Xóa role ra khỏi group',
    type: PermissionActionType.ADMIN,
  },
  // {
  //   action: PermissionAction.GROUP__VIEW_LIST_ROLE_IN_GROUP,
  //   meaning: 'Xem danh sách quyền có trong group',
  //   type: PermissionActionType.ADMIN,
  //   isChecked: false,
  // },
  //out going mail
  //out going mail
  {
    action: PermissionAction.OUT_GOING_MAIL_CONFIG__CREATE,
    meaning: 'Thêm mới outgoing mail config',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.OUT_GOING_MAIL_CONFIG__VIEW,
    meaning: 'Xem thông tin outgoing mail config',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.OUT_GOING_MAIL_CONFIG__TEST_MAIL,
    meaning: 'Kiểm thử chức năng outgoing mail config',
    type: PermissionActionType.ADMIN,
  },

  //notification template
  {
    action: PermissionAction.NOTIFICATION_TEMPLATE__UPDATE,
    meaning: 'Cập nhật notification template',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.NOTIFICATION_TEMPLATE__CHANGE_ACTIVE,
    meaning: 'Cập nhật trạng thái active/inactive notification template',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.NOTIFICATION_TEMPLATE__VIEW_DETAIL,
    meaning: 'Xem thông tin notification template',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.NOTIFICATION_TEMPLATE__VIEW_LIST,
    meaning: 'Xem danh sách notification template',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.NOTIFICATION_TEMPLATE__CREATE,
    meaning: 'Thêm mới notification template',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.NOTIFICATION_TEMPLATE__DELETE_LIST,
    meaning: 'Xóa notification templates',
    type: PermissionActionType.ADMIN,
  },

  {
    action: PermissionAction.RELATIONSHIP_TYPE__CREATE,
    meaning: 'Thêm mới thông tin Relationship Type',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.RELATIONSHIP_TYPE__UPDATE,
    meaning: 'Cập nhật thông tin Relationship Type',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.RELATIONSHIP_TYPE__DELETE,
    meaning: 'Xóa Relationship Type',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.CI_TYPE__VIEW_LIST,
    meaning: 'Xem danh sách CiType',
    type: PermissionActionType.ADMIN,
  },
  // {
  //   action: PermissionAction.CI_TYPE__VIEW_LIST_CI,
  //   meaning: 'Xem danh sách Ci của CiType',
  //   type: PermissionActionType.ADMIN,
  //   isChecked: false,
  // },
  {
    action: PermissionAction.CI_TYPE__CREATE,
    meaning: 'Thêm mới CiType',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.CI_TYPE__CONFIG_ATTRIBUTE,
    meaning: 'Config attribute default/custom vào CiType',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.CI_TYPE__UPDATE,
    meaning: 'Cập nhật thông tin CiType',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.CI_TYPE__VIEW_DETAIL,
    meaning: 'Xem thông tin chi tiết của CiType',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.CI_TYPE__DELETE,
    meaning: 'Xóa CiType',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.ROLE__CREATE,
    meaning: 'Thêm mới Role',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.ROLE__UPDATE,
    meaning: 'Cập nhật thông tin Role',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.ROLE__DELETE,
    meaning: 'Xóa Role',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.ROLE__VIEW_LIST,
    meaning: 'Xem danh sách Role',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.ROLE__VIEW_DETAIL,
    meaning: 'Xem thông tin chi tiết Role',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.ROLE__ADD_GROUP_TO_ROLE,
    meaning: 'Thêm group vào 1 role',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.ROLE__DELETE_GROUP_INTO_ROLE,
    meaning: 'Xóa group ra khỏi role',
    type: PermissionActionType.ADMIN,
  },
  // {
  //   action: PermissionAction.ROLE__VIEW_LIST_GROUP_IN_ROLE,
  //   meaning: 'Xem danh sách group trong 1 role',
  //   type: PermissionActionType.ADMIN,
  //   isChecked: false,
  // },

  {
    action: PermissionAction.IMPACTED_RULE__CREATE,
    meaning: 'Thêm mới impacted rule',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.IMPACTED_RULE__UPDATE,
    meaning: 'Cập nhật impcacted rule',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.IMPACTED_RULE__DELETE,
    meaning: 'Xóa impacted rule',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.IMPACTED_RULE__VIEW_LIST,
    meaning: 'Xem toàn bộ impacted rule',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.IMPACTED_RULE__VIEW_DETAIL,
    meaning: 'Xem chi tiết impacted rule',
    type: PermissionActionType.ADMIN,
  },

  {
    action: PermissionAction.JOB_CONFIG__CREATE,
    meaning: 'Thêm mới schedule jobs',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.JOB_CONFIG__UPDATE,
    meaning: 'Cập nhật schedule jobs',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.JOB_CONFIG__DELETE,
    meaning: 'Xóa schedule jobs',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.JOB_CONFIG__VIEW_LIST,
    meaning: 'Xem toàn bộ schedule jobs',
    type: PermissionActionType.ADMIN,
  },
  {
    action: PermissionAction.JOB_CONFIG__VIEW_DETAIL,
    meaning: 'Xem chi tiết schedule jobs',
    type: PermissionActionType.ADMIN,
  },
];

export const applicationPermissionDescriptions: ConfigItemPermissionOtherDto[] = [
  {
    action: PermissionAction.BUSINESS_VIEW__VIEW_LIST,
    meaning: 'Xem danh sách Bussiness view',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.BUSINESS_VIEW__CREATE,
    meaning: 'Thêm mới Bussiness view',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.BUSINESS_VIEW__VIEW_DETAIL,
    meaning: 'Xem thông tin chi tiết của Bussiness view',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.BUSINESS_VIEW__UPDATE,
    meaning: 'Cập nhật thông tin Bussiness view',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.BUSINESS_VIEW__DELETE,
    meaning: 'Xóa Bussiness view',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.SERVICE_MAP__VIEW_LIST,
    meaning: 'Xem danh sách Service Map',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.SERVICE_MAP__CREATE,
    meaning: 'Thêm mới Service Map',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.SERVICE_MAP__VIEW_DETAIL,
    meaning: 'Xem thông tin chi tiết Service Map',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.SERVICE_MAP__UPDATE,
    meaning: 'Cập nhật thông tin Service Map',
    type: PermissionActionType.APPLICATIONS,
  },
  // {
  //   action: PermissionAction.SERVICE_MAP__DELETE,
  //   meaning: 'Xóa Service Map',
  //   type: PermissionActionType.APPLICATIONS,
  // },
  {
    action: PermissionAction.CHANGE__VIEW_LIST,
    meaning: 'Xem danh sách Change',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.CHANGE__CREATE,
    meaning: 'Thêm mới Change',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.CHANGE__VIEW_DETAIL,
    meaning: 'Xem thông tin chi tiết Change',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.CHANGE__UPDATE,
    meaning: 'Cập nhật thông tin Change',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.CHANGE__DELETE,
    meaning: 'Xóa Change',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.CI_RELATIONSHIP__CREATE,
    meaning: 'Thêm mới Ci Relationship',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.CI_RELATIONSHIP__IMPORT,
    meaning: 'Cập nhật thông tin Ci Relationship',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.CI_RELATIONSHIP__DELETE,
    meaning: 'Xóa Ci Relationship',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.INCIDENT__VIEW_LIST,
    meaning: 'Xem danh sách Incident',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.INCIDENT__CREATE,
    meaning: 'Thêm mới Incident',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.INCIDENT__VIEW_DETAIL,
    meaning: 'Xem thông tin chi tiết Incident',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.INCIDENT__UPDATE,
    meaning: 'Cập nhật thông tin Incident',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.INCIDENT__DELETE,
    meaning: 'Xóa Incident',
    type: PermissionActionType.APPLICATIONS,
  },
];

export const ciTypePermissionDescriptions: ConfigItemPermissionOtherDto[] = [
  {
    action: PermissionAction.CI__VIEW_BASIC,
    meaning: 'Xem thông tin cơ bản của 1 Ci',
    type: PermissionActionType.CI_TYPE,
  },
  {
    action: PermissionAction.CI__VIEW_ADVANCED,
    meaning: 'Xem toàn bộ thông tin của 1 Ci',
    type: PermissionActionType.CI_TYPE,
  },
  {
    action: PermissionAction.CI__APPROVE,
    meaning: 'Cấp quyền approve các bản draft của Ci cho người dùng',
    type: PermissionActionType.CI_TYPE,
  },
  {
    action: PermissionAction.CI__UPDATE,
    meaning: 'Cập nhật thông tin Ci',
    type: PermissionActionType.CI_TYPE,
  },
  {
    action: PermissionAction.CI__CREATE,
    meaning: 'Thêm mới thông tin Ci',
    type: PermissionActionType.CI_TYPE,
  },
  {
    action: PermissionAction.CI__DELETE,
    meaning: 'Xóa Ci',
    type: PermissionActionType.CI_TYPE,
  },
  {
    action: PermissionAction.CI__IMPORT,
    meaning: 'Cấp quyền import Ci vào hệ thống',
    type: PermissionActionType.CI_TYPE,
  },
  {
    action: PermissionAction.CI_TYPE__VIEW_LIST_CI,
    meaning: 'Xem danh sách Ci của CiType',
    type: PermissionActionType.CI_TYPE,
  },
  {
    action: PermissionAction.CI_DRAFT__UPDATE,
    meaning: 'Cập nhật thông tin draft',
    type: PermissionActionType.APPLICATIONS,
  },
  {
    action: PermissionAction.CI_DRAFT__DELETE,
    meaning: 'Xóa draft',
    type: PermissionActionType.APPLICATIONS,
  },
];

export const ciPermissionDescriptions: ConfigItemPermissionOtherDto[] = [
  {
    action: PermissionAction.CI__VIEW_BASIC,
    meaning: 'Xem thông tin cơ bản của 1 Ci',
    type: PermissionActionType.CI,
  },
  {
    action: PermissionAction.CI__VIEW_ADVANCED,
    meaning: 'Xem toàn bộ thông tin của 1 Ci',
    type: PermissionActionType.CI,
  },
  {
    action: PermissionAction.CI__APPROVE,
    meaning: 'Cấp quyền approve các bản draft của Ci cho người dùng',
    type: PermissionActionType.CI,
  },
  {
    action: PermissionAction.CI__UPDATE,
    meaning: 'Cập nhật thông tin Ci',
    type: PermissionActionType.CI,
  },
  {
    action: PermissionAction.CI__DELETE,
    meaning: 'Xóa Ci',
    type: PermissionActionType.CI,
  },
];
