import { KanbanTabs } from 'kanban-design-system';
import { CiManagementComponent } from './CiManagementComponent';
import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ScreenTypeManagement } from '@common/constants/CiManagement';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { buildCiManageUrl } from '@common/utils/RouterUtils';
import { BreadcrumbComponent } from '@pages/admins/breadcrumb/BreadcrumbComponent';

export const CiManagementPage = () => {
  const [searchParams] = useSearchParams();

  const [activeTab, setActiveTab] = useState('DRAFT');
  const navigate = useNavigate();

  useEffect(() => {
    const screenType = searchParams.get('screenType');
    if (screenType) {
      setActiveTab(screenType);
    }
  }, [searchParams]);
  return (
    <>
      {/* 4746 ci managment in various stage screen*/}
      <BreadcrumbComponent />
      <HeaderTitleComponent title={'CIs Management'}></HeaderTitleComponent>
      <KanbanTabs
        configs={{
          defaultValue: ScreenTypeManagement.DRAFT,
          value: activeTab,
          variant: 'outline',
          onChange: (value) => {
            navigate(buildCiManageUrl(value || ''));
          },
        }}
        tabs={{
          DRAFT: {
            title: 'CIs draft',
            content: (
              <div>
                <CiManagementComponent screenType={ScreenTypeManagement.DRAFT}></CiManagementComponent>
              </div>
            ),
          },
          SENT: {
            title: 'CIs sent for approval',
            content: (
              <div>
                <CiManagementComponent screenType={ScreenTypeManagement.SENT}></CiManagementComponent>
              </div>
            ),
          },
          WAITING: {
            title: 'CIs waiting for approval',
            content: (
              <div>
                <CiManagementComponent screenType={ScreenTypeManagement.WAITING}></CiManagementComponent>
              </div>
            ),
          },
          APPROVED: {
            title: 'CIs approved',
            content: (
              <div>
                <CiManagementComponent screenType={ScreenTypeManagement.APPROVED}></CiManagementComponent>
              </div>
            ),
          },
          REJECTED: {
            title: 'CIs rejected',
            content: (
              <div>
                <CiManagementComponent screenType={ScreenTypeManagement.REJECTED}></CiManagementComponent>
              </div>
            ),
          },
        }}
      />
    </>
  );
};

export default CiManagementPage;
