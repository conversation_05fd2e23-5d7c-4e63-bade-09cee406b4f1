import React, { createContext, useReducer, useContext, ReactNode, useMemo } from 'react';
import { produce } from 'immer';
import { CustomDataNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';
import { JsonTransformActionType } from '@common/constants/JsonTransformActionType';

export interface JsonTransformState {
  isValidFormJson: boolean;
  keys: string[];
  stagingStructure: CustomDataNode[];
}

const initialState: JsonTransformState = {
  isValidFormJson: false,
  keys: [],
  stagingStructure: [],
};

type JsonTransformAction =
  | { type: JsonTransformActionType.UPDATE_FORM_JSON; payload: boolean }
  | { type: JsonTransformActionType.UPDATE_KEYS; payload: string[] }
  | { type: JsonTransformActionType.RESET_KEYS }
  | { type: JsonTransformActionType.UPDATE_STAGING_STRUCTURE; payload: CustomDataNode[] }
  | { type: JsonTransformActionType.RESET_STAGING_STRUCTURE };

// Reducer
const jsonTransformReducer = (state: JsonTransformState, action: JsonTransformAction): JsonTransformState => {
  return produce(state, (draft) => {
    switch (action.type) {
      case JsonTransformActionType.UPDATE_FORM_JSON:
        draft.isValidFormJson = action.payload;
        break;
      case JsonTransformActionType.UPDATE_KEYS:
        draft.keys = action.payload;
        break;
      case JsonTransformActionType.RESET_KEYS:
        draft.keys = [];
        break;
      case JsonTransformActionType.UPDATE_STAGING_STRUCTURE:
        draft.stagingStructure = action.payload;
        break;
      case JsonTransformActionType.RESET_STAGING_STRUCTURE:
        draft.stagingStructure = initialState.stagingStructure;
        break;
      default:
        throw new Error(`Unhandled action type`);
    }
  });
};

interface JsonTransformContextType {
  state: JsonTransformState;
  dispatch: React.Dispatch<JsonTransformAction>;
}

const JsonTransformContext = createContext<JsonTransformContextType | undefined>(undefined);

export const JsonTransformProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(jsonTransformReducer, initialState);
  const memoizedValue = useMemo(() => ({ state, dispatch }), [state, dispatch]);
  return <JsonTransformContext.Provider value={memoizedValue}>{children}</JsonTransformContext.Provider>;
};

export const useJsonTransformContext = () => {
  const context = useContext(JsonTransformContext);
  if (!context) {
    throw new Error('useJsonTransformContext must be used within an JsonTransformProvider');
  }
  return context;
};
