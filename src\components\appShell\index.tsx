import React, { ReactNode, useMemo } from 'react';
import cx from 'clsx';
import { useDisclosure, useMediaQuery } from '@mantine/hooks';
import {
  AppShell as AppShellMantine,
  Avatar,
  Burger,
  Center,
  DefaultMantineColor,
  Divider,
  Flex,
  Group,
  Menu,
  Space,
  ThemeIcon,
  UnstyledButton,
  rem,
} from '@mantine/core';
import { NavLink as NavLinkItem } from '@mantine/core';
import { IconChevronRight, TablerIconsProps, IconLogout, IconSettings, IconSearch, IconHome, IconMail, IconChevronDown } from '@tabler/icons-react';
import classes from './AppShell.module.scss';
import styled from 'styled-components';
import { PathMatch, useLocation, useMatch, useNavigate, useResolvedPath } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { getConfigs } from '@core/configs/Configs';
import { AclPermission } from 'models/AclPermission';
import KeycloakService from '@core/auth/Keycloak';
import { Spotlight, createSpotlight } from '@mantine/spotlight';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import type { ConfigItemTypeModel } from '@models/ConfigItemType';
import { buildCiManageUrl, buildCiTypeUrl, ciManagementPath, ciTypePath, isMatchPath, parsePathToObject } from '@common/utils/RouterUtils';
import { ZIndexAppShellHeader, ZIndexAppShellNavbar } from '@common/constants/ZIndexConstants';
import { ScreenTypeManagement } from '@common/constants/CiManagement';
import GuardComponent from '@components/GuardComponent';
import { KanbanIconButton, KanbanText, KanbanInput } from 'kanban-design-system';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import useResourceApplicationType from '@common/hooks/useResourceApplicationType';
import { CiTypePickerComponent } from '@components/commonCi/SelectCiTypeComponent';
import { ResourceApplicationType } from '@common/constants/ResourceTypeEnum';
const Logo = styled.div`
  font-weight: 500;
  font-size: 1rem;
  span.name {
    display: inline-block;
    margin-right: 1px;
    font-weight: 900;
    color: var(--mantine-color-primary-5);
  }
  span.description {
    font-weight: 300;
    color:;
  }
`;
const NavList = styled.div``;
const UserContainer = styled.div`
  display: flex;
  align-items: center;
  padding: 5px;
`;
const AppShellMainStyled = styled(AppShellMantine.Main)`
  display: flex;
  flex-direction: column;
  --kanban-appshell-header-height: calc(var(--app-shell-header-offset, 0px) + var(--app-shell-padding));
  --kanban-appshell-footer-height: calc(var(--app-shell-footer-offset, 0px) + var(--app-shell-padding));
  --kanban-appshell-maxheight-content: calc(100vh - var(--kanban-appshell-header-height) - var(--kanban-appshell-footer-height));
`;

export type RouterType = {
  path?: string;
  name: string;
  icon?: React.ComponentType<TablerIconsProps>;
  children?: RouterType[];
  requirePermissions?: AclPermission[];
  isHidden?: boolean;
  resourceApplicationType?: ResourceApplicationType;
};
type CustomLinkType = RouterType & {
  isRoot: boolean;
  iconColor?: DefaultMantineColor;
  userPermissions?: AclPermission[];
  onClick?: () => void;
  resourceApplicationType?: ResourceApplicationType;
};
export type AppShellProps = {
  routers: RouterType[];
  headerLinks?: RouterType[];
  discoveryLinks?: RouterType[];
  ciTypes: ConfigItemTypeModel[];
  ciRuleLinks?: RouterType[];
  children: ReactNode;
};

const CustomLink = (props: CustomLinkType) => {
  const navigate = useNavigate();
  const resolved = useResolvedPath(props.path || '');
  let match: PathMatch<string> | null = useMatch({ path: resolved.pathname, end: false });
  if (!props.path) {
    match = null;
  }
  const Icons = props.icon;

  if (props.requirePermissions?.length && !props.requirePermissions.some((permissions) => props.userPermissions?.includes(permissions))) {
    return <></>;
  }

  return (
    <NavLinkItem
      className={!props.isRoot ? classes.link : ''}
      label={props.name}
      leftSection={
        Icons && (
          <ThemeIcon variant='outline' color={props.iconColor || 'primary'} size={25}>
            {' '}
            <Icons size='1.3rem' stroke={2} />
          </ThemeIcon>
        )
      }
      rightSection={props.children?.length && <IconChevronRight size='0.8rem' stroke={1.5} />}
      active={!!match}
      variant='light'
      onClick={() => {
        if (props.onClick) {
          props.onClick();
          return;
        }
        if (!props.path) {
          return;
        }
        navigate(props.path);
      }}>
      {props.children?.map((item, key) => {
        return <CustomLink key={key} isRoot={false} {...item}></CustomLink>;
      })}
    </NavLinkItem>
  );
};

const getClassNameOfLayout = (
  classes: any,
  match: PathMatchResult,
  layout: ResourceApplicationType = ResourceApplicationType.DEFAULT_RESOURCE,
): string => {
  let className = `${classes['header-link']} ${match ? classes['header-link-active'] : ''}`;
  switch (layout) {
    case ResourceApplicationType.DISCOVERY_RESOURCE:
      className += ` ${classes['discovery-link']}  ${match ? classes['discovery-link-active'] : ''}`;
      break;
    case ResourceApplicationType.CI_RULE_RESOURCE:
      className += ` ${classes['ci-rule-link']}  ${match ? classes['ci-rule-link-active'] : ''}`;
      break;
  }

  return className;
};

const sizeIcon = new Map<ResourceApplicationType, number>([
  [ResourceApplicationType.DEFAULT_RESOURCE, 15],
  [ResourceApplicationType.DISCOVERY_RESOURCE, 20],
  [ResourceApplicationType.CI_RULE_RESOURCE, 20],
]);

type PathMatchResult = PathMatch<string> | boolean | null;

const CustomHeaderLink = (props: CustomLinkType) => {
  const navigate = useNavigate();
  const resolved = useResolvedPath(props.path || '');
  let match: PathMatchResult = useMatch({ path: resolved.pathname, end: false });
  // const matchDiscoveryPath = useMatchDiscoveryPath();
  // if (ResourceApplicationType.DISCOVERY_RESOURCE === props.resourceApplicationType && matchDiscoveryPath && match === null) {
  //   // eslint-disable-next-line no-console
  //   console.log(
  //     'match ',
  //     props.resourceApplicationType,
  //     ResourceApplicationType.DISCOVERY_RESOURCE === props.resourceApplicationType,
  //     matchDiscoveryPath,
  //     match,
  //   );
  //   match = matchDiscoveryPath;
  // }
  if (!props.path) {
    match = null;
  }
  if (props.isHidden === true) {
    return <></>;
  }
  if (props.requirePermissions?.length && !isCurrentUserMatchPermissions(props.requirePermissions)) {
    return <></>;
  }

  const Icons = props.icon;

  const menuItems = props.children?.map((item) => (
    <Menu.Item
      key={item.path}
      onClick={() => {
        if (props.onClick) {
          props.onClick();
          return;
        }
        if (!item.path) {
          return;
        }
        navigate(item.path);
      }}>
      {item.name}
    </Menu.Item>
  ));

  if (menuItems) {
    return (
      <Menu key={props.name} trigger='hover' transitionProps={{ exitDuration: 0 }} withinPortal>
        <Menu.Target>
          <a
            href={props.path}
            className={getClassNameOfLayout(classes, match, props.resourceApplicationType)}
            onClick={(event) => {
              event.preventDefault();
            }}>
            {
              <div className={classes['header-link-icon']}>
                {Icons && (
                  <ThemeIcon
                    variant='outline'
                    color={props.iconColor || 'primary'}
                    size={sizeIcon.get(props.resourceApplicationType ?? ResourceApplicationType.DEFAULT_RESOURCE)}>
                    {' '}
                    <Icons size='1.3rem' stroke={2} />
                  </ThemeIcon>
                )}
              </div>
            }
            <Center style={{ display: 'inline-block' }}>
              <span className={classes.linkLabel}>{props.name}</span>
              <IconChevronDown size='0.9rem' stroke={1.5} />
            </Center>
          </a>
        </Menu.Target>
        <Menu.Dropdown>{menuItems}</Menu.Dropdown>
      </Menu>
    );
  }

  return (
    <div>
      <a
        key={props.name}
        href={props.path}
        className={getClassNameOfLayout(classes, match, props.resourceApplicationType)}
        onClick={(event) => {
          event.preventDefault();
          if (props.onClick) {
            props.onClick();
            return;
          }
          if (!props.path) {
            return;
          }
          navigate(props.path);
        }}>
        {
          <div className={classes['header-link-icon']}>
            {Icons && (
              <ThemeIcon
                variant='outline'
                color={props.iconColor || 'primary'}
                size={sizeIcon.get(props.resourceApplicationType ?? ResourceApplicationType.DEFAULT_RESOURCE)}>
                {' '}
                <Icons size='1.3rem' stroke={2} />
              </ThemeIcon>
            )}
          </div>
        }
        <span>{props.name}</span>
      </a>
    </div>
  );
};

const configs = getConfigs();

export const [searchStore, searchSpotlight] = createSpotlight();

export const AppShell: React.FC<AppShellProps> = (props) => {
  const [opened, { toggle }] = useDisclosure(true);
  const currentUserState = useSelector(getCurrentUser);
  const currentUser = currentUserState.data;
  const navigate = useNavigate();
  const location = useLocation();
  const objectPath = useMemo(() => {
    return parsePathToObject(ciTypePath, location.pathname, {
      end: false,
    });
  }, [location.pathname]);
  const currentCiTypeId = objectPath.id ? Number(objectPath.id) : undefined;
  const headerLinks = useMemo(() => {
    return props.headerLinks?.map((link, index) => {
      return <CustomHeaderLink isRoot key={index} {...link} userPermissions={currentUser?.aclPermissions} />;
    });
  }, [currentUser?.aclPermissions, props.headerLinks]);

  const discoveryLinks = useMemo(() => {
    return props.discoveryLinks?.map((link, index) => {
      return <CustomHeaderLink isRoot key={index} {...link} userPermissions={currentUser?.aclPermissions} />;
    });
  }, [currentUser?.aclPermissions, props.discoveryLinks]);

  const ciRuleLinks = useMemo(() => {
    return props.ciRuleLinks?.map((link, index) => {
      return <CustomHeaderLink isRoot key={index} {...link} userPermissions={currentUser?.aclPermissions} />;
    });
  }, [currentUser?.aclPermissions, props.ciRuleLinks]);

  const appType = useResourceApplicationType();

  const renderNavbarContent = (): JSX.Element => {
    switch (appType) {
      case ResourceApplicationType.DISCOVERY_RESOURCE:
        return <>{discoveryLinks}</>;
      case ResourceApplicationType.CI_RULE_RESOURCE:
        return <>{ciRuleLinks}</>;
      default:
        return (
          <CiTypePickerComponent
            showAllCIs={true}
            onChange={(_, item) => {
              if (item) {
                const path = buildCiTypeUrl(item.id);
                navigate(path);
              }
            }}
            customActive={(item) => {
              return item.id === currentCiTypeId;
            }}
          />
        );
    }
  };

  const isLaptopSize = useMediaQuery('(min-width: 1270px)');

  return (
    <>
      <Spotlight
        store={searchStore}
        searchProps={{
          placeholder: 'Type to search',
          leftSection: <IconSearch style={{ width: rem(20), height: rem(20) }} stroke={1.5} />,
        }}
        limit={10}
        highlightQuery
        actions={[
          {
            id: 'home',
            label: 'Home',
            description: 'Get to home page',
            onClick: () => console.info('Home'),
            leftSection: <IconHome style={{ width: rem(24), height: rem(24) }} stroke={1.5} />,
          },
        ]}
      />
      <AppShellMantine
        header={{ height: 50 }}
        navbar={{
          width: 300,
          breakpoint: 'sm',
          collapsed: { mobile: !opened, desktop: !opened },
        }}
        padding='md'>
        <AppShellMantine.Header zIndex={ZIndexAppShellHeader}>
          <Group h='100%' px='md' justify='space-between' align='center'>
            <Burger opened={opened} onClick={toggle} size='sm' />
            <Logo
              onClick={() => {
                navigate('/');
              }}>
              <span className='name'>IT</span>
              {configs.name}
              {false && (
                <>
                  - <span className='description'>{configs.description}</span>
                </>
              )}
            </Logo>
            {isLaptopSize ? (
              <Group mr='auto'>
                <Group gap={5} visibleFrom='xs'>
                  {headerLinks}
                </Group>
              </Group>
            ) : (
              <Group mr='auto'>
                <Menu trigger='hover' openDelay={100} closeDelay={200} position='bottom-start' withinPortal>
                  <Menu.Target>
                    <UnstyledButton>
                      <Group align='center'>
                        {/* Nút mở menu */}
                        <span className={classes.linkLabel}>System function</span>
                        <IconChevronDown size='0.9rem' stroke={1.5} />
                      </Group>
                    </UnstyledButton>
                  </Menu.Target>

                  <Menu.Dropdown>{headerLinks}</Menu.Dropdown>
                </Menu>
              </Group>
            )}
            <Flex align={'center'} gap={'xs'}>
              <KanbanIconButton
                onClick={() => {
                  navigate(buildCiManageUrl(ScreenTypeManagement.DRAFT));
                }}
                variant={'white'}
                color={!isMatchPath(window.location.pathname, ciManagementPath) ? 'gray.7' : 'primary'}
                mb={0}>
                <IconMail />
              </KanbanIconButton>
              <Menu width={260} position='bottom-end' transitionProps={{ transition: 'pop-top-right' }} withinPortal>
                <Menu.Target>
                  <UnstyledButton className={cx(classes.user, { [classes.userActive]: false })} ml={'xs'}>
                    <Group gap={7}>
                      <Avatar src={null} alt={currentUser?.username || 'No one'} radius='xl' size={20} />
                      <KanbanText fw={500} size='sm' lh={1} mr={3}>
                        {currentUser?.username || 'No one'}
                      </KanbanText>
                      <IconChevronDown style={{ width: rem(12), height: rem(12) }} stroke={1.5} />
                    </Group>
                  </UnstyledButton>
                </Menu.Target>
                <Menu.Dropdown>
                  <GuardComponent requirePermissions={[AclPermission.manageSystem]} hiddenOnUnSatisfy>
                    <Menu.Item
                      leftSection={<IconSettings style={{ width: rem(16), height: rem(16) }} color={'blue'} stroke={1.5} />}
                      onClick={() => {
                        navigate('admins');
                      }}>
                      Admin Settings
                    </Menu.Item>
                  </GuardComponent>
                  <Menu.Item
                    leftSection={<IconLogout style={{ width: rem(16), height: rem(16) }} color={'red'} stroke={1.5} />}
                    onClick={() => {
                      KeycloakService.doLogout();
                    }}>
                    Logout
                  </Menu.Item>
                </Menu.Dropdown>
              </Menu>
              {false && (
                <KanbanInput
                  mb={0}
                  placeholder='Type to search'
                  radius={'lg'}
                  onClick={() => {
                    searchSpotlight.open();
                  }}
                  rightSection={
                    <KanbanIconButton variant='transparent'>
                      <IconSearch></IconSearch>
                    </KanbanIconButton>
                  }></KanbanInput>
              )}
            </Flex>
          </Group>
        </AppShellMantine.Header>
        <AppShellMantine.Navbar pt={'md'} pl={'md'} pr={'md'} zIndex={ZIndexAppShellNavbar}>
          <NavList className={classes.links}>
            <>
              {props.routers.map((item, index) => {
                return <CustomLink key={index} isRoot={true} userPermissions={currentUser?.aclPermissions} {...item} />;
              })}
              {false && <Divider />}
            </>

            <Space h={'xs'} />
            {renderNavbarContent()}
            {/* 
                        {
                            roots.map((item, index) => {
                                return (
                                    <RenderCiTypesCustomLink
                                        isRoot={true}
                                        node={item}
                                        controlOpenedCiTypes={controlOpenedCiTypes}
                                        setControlOpenedCiTypes={setControlOpenedCiTypes}
                                        ciTypes={props.ciTypes}
                                        key={index}
                                    />
                                );
                            })
                        } */}
          </NavList>
          {false && (
            <>
              <Divider />
              <NavList>
                <UserContainer>
                  <Avatar mr={'xs'} src={null} alt='no image here' />
                  <div>{currentUser?.username || 'No one'}</div>
                </UserContainer>
                <CustomLink
                  isRoot
                  path='admins'
                  name='Admin Settings'
                  userPermissions={currentUser?.aclPermissions}
                  //requirePermissions={[AclPermission.manage]}
                  icon={IconSettings}></CustomLink>
                <CustomLink
                  isRoot
                  path='logout'
                  name='Logout'
                  iconColor='red'
                  icon={IconLogout}
                  onClick={() => {
                    KeycloakService.doLogout();
                  }}></CustomLink>
              </NavList>
            </>
          )}
        </AppShellMantine.Navbar>
        <AppShellMainStyled>{props.children}</AppShellMainStyled>
      </AppShellMantine>
    </>
  );
};
export default AppShell;
