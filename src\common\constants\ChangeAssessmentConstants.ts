import type { ComboboxData } from '@mantine/core';

export enum ImpactedCiTableViewEnum {
  BY_PRIORITY_LEVEL = 'BY_PRIORITY_LEVEL',
  ALL = 'ALL',
}
export const IMPACTED_CI_TABLE_VIEW_OPTIONS: ComboboxData = [
  { label: 'View by priority level', value: ImpactedCiTableViewEnum.BY_PRIORITY_LEVEL },
  { label: 'View all', value: ImpactedCiTableViewEnum.ALL },
];

export enum CHANGE_STAGES {
  SUBMISSION = 'Submission',
  PLANNING = 'Planning',
  APPROVAL = 'Approval',
  IMPLEMENTATION = 'Implementation',
  REVIEW = 'Review',
  CLOSE = 'Close',
}
