import React from 'react';
import JsonTransformOutputPage from './output';
import JsonTransformInputPage from './input';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import styleIndexs from './input/Index.module.scss';
export type JsonTransformPageProps = {
  sourceId: number;
  discoveryStagingId: number;
};

export const JsonTransformPage: React.FC<JsonTransformPageProps> = ({ discoveryStagingId, sourceId }) => {
  // const jsonTransform = useSelector(getDataTransform)?.jsonTransform;
  return (
    // <Grid>
    <PanelGroup autoSaveId='transform-json' direction='horizontal'>
      <JsonTransformInputPage sourceId={sourceId} discoveryStagingId={discoveryStagingId} />
      <PanelResizeHandle />
      <Panel id='right' order={3} defaultSize={30} className={styleIndexs['panel-height']}>
        <JsonTransformOutputPage />
      </Panel>
    </PanelGroup>
  );
};
export default JsonTransformPage;
