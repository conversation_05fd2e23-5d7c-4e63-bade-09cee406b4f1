import { CiDetailScreenType, CiDetailSubTabType } from '@common/constants/CiDetail';
import { BusinessDomainDetailTab } from '@common/constants/BusinessDomainConstants';
import { SourceDataAction } from '@common/constants/SourceDataActionEnum';
import type { ChangeAssessmentAction } from '@models/ChangeAssessment';
import { RuleAction } from '@models/ImpactedRule';
import type { CiIdentifierRuleAction } from '@models/CiIdentifierRule';
import * as pathToRegexp from 'path-to-regexp';
import { matchPath, type NavigateFunction, type NavigateOptions, type To } from 'react-router-dom';
import { RoleAction } from '@common/constants/RoleActionEnum';
import { JobDiscoveryAction } from '@common/constants/JobDiscoveryActionEnum';
import { CiReconciliationRuleAction } from '@models/CiReconciliationRule';

type HistoryRouterType = {
  navigate?: NavigateFunction;
};
export const HistoryRouter: HistoryRouterType = {
  navigate: undefined,
};

// export const browserHistory = createBrowserHistory();

export const navigateTo = (to: To, options?: NavigateOptions | undefined) => {
  if (HistoryRouter.navigate) {
    HistoryRouter.navigate(to, options);
  }
};

export const ciTypeParam = 'id';
export const ciTypeIdParam = 'ciTypeId';
export const ciIdParam = 'ciId';

export const ciTypePath = `/ci-types/:${ciTypeParam}`;
export const ciPath = `/ci-types/:${ciTypeIdParam}/cis/:${ciIdParam}`;
export const ciImportPath = '/ci-types/:id/import';
export const businessViewsPath = '/business-views';
export const businessViewDetaisPath = '/business-views/:id';
export const ciManagementPath = '/ci-managements';
export const ciManagementDetailPath = '/ci-managements/:ciTempId';
export const ciRelationShipImportPath = '/ci-relationship/import';
export const serviceMappingPath = '/service-mapping';
export const changeAssessmentPath = '/change-assessments';
export const changeAssessmentDetailPath = '/change-assessments/:id';
export const incidentRequestPath = '/incident-requests';
export const incidentRequestDetailPath = '/incident-requests/:incidentId';
export const createOrUpdateRolePath = '/admins/roles/:id';
export const roleDetailPath = '/admins/roles/:id/detail';
export const roleViewListPath = '/admins/roles';
export const notificationTemplatePath = '/admins/notification-templates';
export const ciTypeConfigPath = '/admins/ci-types';
export const impactedRulesPath = '/admins/impacted-rules';
export const impactedRuleDetailPath = '/admins/impacted-rules/:id';
export const outgoingMailConfigUrl = '/admins/outgoing-mail-configs';
export const businessDomainPath = '/business-domains';
export const businessDomainDetailPath = '/business-domains/:ciId';
export const businessDomainAggregatePath = '/business-domains/aggregate';

// Path for Phase 2: Discovery
export const integrationPath = '/integrations';
export const dataSourcePath = '/admins/source-datas';
export const staggingTablePath = '/stagging-tables';
export const transformMapPath = '/admins/transform-maps';
export const createOrUpdateDiscoveryTransfromMap = `${transformMapPath}/:id`;
export const createOrUpdateDataSourcePath = `${dataSourcePath}/:id`;
export const ciIdentifierRulePath = '/admins/ci-identifier-rules';
export const ciIdentifierRuleDetailPath = '/admins/ci-identifier-rules/:ruleId';
export const ciReconciliationRulePath = '/admins/ci-reconciliation-rules';
export const ciReconciliationRuleDetailPath = '/admins/ci-reconciliation-rules/:ruleId';
export const discoveryPreviewDataCisPath = '/admins/discovery-preview-data-cis';
export const discoveryPreviewDataCiDetailPagePath = `${discoveryPreviewDataCisPath}/:id`;
export const discoveryPreviewDataCiFromJobDiscoveryPath = `${discoveryPreviewDataCisPath}/:id`;
export const discoveryPreviewDataCiGroupDetailPath = `${discoveryPreviewDataCisPath}/detail`;
export const dataSourceConfigPath = '/admins/source-configs';
export const dataSourceConfigDetailPath = `${dataSourceConfigPath}/:id`;

export const jobDiscoveryConfigPath = '/admins/job-discovery-configs';
export const createOrUpdateJobDiscoveryConfigPath = `${jobDiscoveryConfigPath}/:id`;
export const viewLogJobDiscoveryConfigPath = `${jobDiscoveryConfigPath}/job-histories/:id`;

export const systemParameterPagePath = 'system-parameters';

export const allDiscoveryDestinations = [
  integrationPath,
  dataSourceConfigPath,
  dataSourceConfigDetailPath,
  dataSourcePath,
  staggingTablePath,
  transformMapPath,
  discoveryPreviewDataCisPath,
  jobDiscoveryConfigPath,
  dataSourceConfigPath,
];

export const allCiRuleDestinations = [ciIdentifierRulePath, ciReconciliationRulePath];

export const buildRoleUrl = (id: number, action: RoleAction) => {
  const toPath = pathToRegexp.compile(createOrUpdateRolePath);
  const originPath = toPath({ id });
  const queryParams = new URLSearchParams();
  queryParams.append('action', action);
  const fullUrl = `${originPath}?${queryParams.toString()}`;
  return fullUrl;
};

export const buildSourceDataUrl = (id: number, path: string, action: SourceDataAction) => {
  const toPath = pathToRegexp.compile(path);
  const originPath = toPath({ id });
  const queryParams = new URLSearchParams();
  queryParams.append('action', action);
  const fullUrl = `${originPath}?${queryParams.toString()}`;
  return fullUrl;
};

export const buildSourceDataConfigUrl = (id: number, action: SourceDataAction) => {
  return buildSourceDataUrl(id, dataSourceConfigDetailPath, action);
};

export const buildCreateOrUpdateDiscoveryTransfromMapUrl = (id: number) => {
  const toPath = pathToRegexp.compile(createOrUpdateDiscoveryTransfromMap);
  return toPath({ id });
};

export const buildJobDiscoveryConfigUrl = (id: number, action: JobDiscoveryAction) => {
  const toPath = pathToRegexp.compile(createOrUpdateJobDiscoveryConfigPath);
  const originPath = toPath({ id });
  const queryParams = new URLSearchParams();
  queryParams.append('action', action);
  const fullUrl = `${originPath}?${queryParams.toString()}`;
  return fullUrl;
};

export const buildListJobDiscoveryConfigUrl = () => {
  const fullUrl = `${jobDiscoveryConfigPath}`;
  return fullUrl;
};

export const buildViewLogJobDiscoveryConfigUrl = (id: number) => {
  const toPath = pathToRegexp.compile(viewLogJobDiscoveryConfigPath);
  return toPath({ id });
};

export const buildViewDetailRoleUrl = (id: number) => {
  const toPath = pathToRegexp.compile(roleDetailPath);
  return toPath({ id });
};

export const buildCreateOrUpdateRoleUrl = (id: number) => {
  const toPath = pathToRegexp.compile(createOrUpdateRolePath);
  return toPath({ id });
};

export const buildCreateOrUpdateDataSourceUrl = (id: number) => {
  const toPath = pathToRegexp.compile(createOrUpdateDataSourcePath);
  return toPath({ id });
};

export const buildCiTypeUrl = (ciTypeId: number) => {
  const toPath = pathToRegexp.compile(ciTypePath);

  return toPath({ id: ciTypeId });
};
export const buildCiUrl = (ciTypeId: number, ciId: number, tab?: string, subTab?: string) => {
  const queryParams = new URLSearchParams();
  queryParams.append('tab', tab || CiDetailScreenType.INFO);
  if (tab === CiDetailScreenType.REQUEST || tab === CiDetailScreenType.RELATIONSHIPS) {
    queryParams.append('subTab', subTab || '');
  }
  const toPath = pathToRegexp.compile(ciPath);
  const fullUrl = `${toPath({ ciTypeId, ciId })}?${queryParams.toString()}`;
  return fullUrl;
};

export const buildCiManageUrl = (screenType?: string) => {
  if (!screenType) {
    return ciManagementPath;
  }
  const queryParams = new URLSearchParams();
  queryParams.append('screenType', screenType);
  const fullUrl = `${ciManagementPath}?${queryParams.toString()}`;
  return fullUrl;
};

export const buildCiManageDetailUrl = (ciTempId: number) => {
  const toPath = pathToRegexp.compile(ciManagementDetailPath);
  return toPath({ ciTempId });
};

export const buildCiImportUrl = (ciTypeId: number) => {
  const toPath = pathToRegexp.compile(ciImportPath);
  return toPath({ id: ciTypeId });
};

export const buildViewCreateGraphUrl = (id: number) => {
  const toPath = pathToRegexp.compile(businessViewDetaisPath);
  return toPath({ id });
};

export const buildCreateBusinessViewUrl = (ciTypeId: number, ciId: number) => {
  const urlWithoutQueryParams = buildCiUrl(ciTypeId, ciId, CiDetailScreenType.RELATIONSHIPS, CiDetailSubTabType.GRAPHVIEW);
  const queryParams = new URLSearchParams();
  queryParams.append('isBusinessView', 'true');
  const fullUrl = `${urlWithoutQueryParams}`;
  return fullUrl;
};

export const buildChangeAssessmentDetailUrl = (id: number, action: ChangeAssessmentAction) => {
  const toPath = pathToRegexp.compile(changeAssessmentDetailPath);
  const originPath = toPath({ id });
  const queryParams = new URLSearchParams();
  queryParams.append('action', action);
  const fullUrl = `${originPath}?${queryParams.toString()}`;
  return fullUrl;
};
export const buildImpactedRuleDetailUrl = (id: number, action: RuleAction) => {
  const toPath = pathToRegexp.compile(impactedRuleDetailPath);
  const originPath = toPath({ id });
  const queryParams = new URLSearchParams();
  queryParams.append('action', action);
  const fullUrl = `${originPath}?${queryParams.toString()}`;
  return fullUrl;
};

export const buildIncidentDetailUrl = (incidentId: number, action: ChangeAssessmentAction) => {
  const toPath = pathToRegexp.compile(incidentRequestDetailPath);
  const originPath = toPath({ incidentId });
  const queryParams = new URLSearchParams();
  queryParams.append('action', action);
  const fullUrl = `${originPath}?${queryParams.toString()}`;
  return fullUrl;
};

export const buildCiTypeConfigUrl = (ciTypeId?: number, activeTab?: string) => {
  if (!ciTypeId) {
    return ciTypeConfigPath;
  }
  const queryParams = new URLSearchParams();
  queryParams.append('ciTypeId', `${ciTypeId}`);
  queryParams.append('activeTab', `${activeTab}`);
  const fullUrl = `${ciTypeConfigPath}?${queryParams.toString()}`;
  return fullUrl;
};

export const buildCiIdentifierRuleUrl = () => {
  const fullUrl = `${ciIdentifierRulePath}`;
  return fullUrl;
};

export const buildCiIdentifierRuleDetailUrl = (ruleId: number, action: CiIdentifierRuleAction) => {
  const toPath = pathToRegexp.compile(ciIdentifierRuleDetailPath);
  const originPath = toPath({ ruleId });
  const queryParams = new URLSearchParams();
  queryParams.append('action', action);
  const fullUrl = `${originPath}?${queryParams.toString()}`;
  return fullUrl;
};

export const buildCiReconciliationRuleUrl = () => {
  const fullUrl = `${ciReconciliationRulePath}`;
  return fullUrl;
};

export const buildCiReconciliationRuleDetailUrl = (ruleId: number, action: CiReconciliationRuleAction) => {
  const toPath = pathToRegexp.compile(ciReconciliationRuleDetailPath);
  const originPath = toPath({ ruleId });
  const queryParams = new URLSearchParams();
  queryParams.append('action', action);
  const fullUrl = `${originPath}?${queryParams.toString()}`;
  return fullUrl;
};

export const buildDiscoveryDataCiDetailPage = (id: number) => {
  const toPath = pathToRegexp.compile(discoveryPreviewDataCiDetailPagePath);
  const originPath = toPath({ id });
  return originPath;
};

export const buildDiscoveryPreviewDataCiFromJobDiscoveryPath = (id: number) => {
  const queryParams = new URLSearchParams();
  queryParams.append('jobHistoryId', id.toString());

  const fullUrl = `${discoveryPreviewDataCisPath}?${queryParams.toString()}`;
  return fullUrl;
};

export const buildDiscoveryPreviewDataCiGroup = () => {
  return `${discoveryPreviewDataCisPath}`;
};

export const buildDiscoveryPreviewDataCiGroupDetailPath = (jobHistoryId: number = 0, summaryId: number = 0) => {
  const queryParams = new URLSearchParams();
  const jobHistoryStr = (jobHistoryId ?? 0).toString();
  const summaryStr = (summaryId ?? 0).toString();

  queryParams.append('jobHistoryId', jobHistoryStr);
  queryParams.append('summaryId', summaryStr);

  const fullUrl = `${discoveryPreviewDataCiGroupDetailPath}?${queryParams.toString()}`;
  return fullUrl;
};

export const isMatchPath = (aliasPath: string, realPath: string) => {
  return !!matchPath(realPath, aliasPath);
};

export const isPrefixMatchPath = (aliasPath: string, realPath: string): boolean => {
  return !!matchPath({ path: aliasPath, end: false }, realPath);
};

/**
 *
 * @param aliasPath /ci-types/:id
 * @param realPath /localhost/ci-types/4334
 * @returns `{id: 4334}`
 */
export const parsePathToObject = (aliasPath: string, realPath: string, options?: Parameters<typeof pathToRegexp.pathToRegexp>[2]) => {
  const keys: pathToRegexp.Key[] = [];
  const regexp = pathToRegexp.pathToRegexp(aliasPath, keys, options);

  const match = regexp.exec(realPath);

  const params: Record<string, any> = {};

  if (match) {
    keys.forEach((key, index) => {
      params[key.name] = match[index + 1];
    });
  }
  return params;
};

export const buildBusinessDomainDetailUrl = (
  ciId: number,
  tab?: string,
  ciBusinessFunction?: number,
  ciNameBusinessFunction?: string,
  notLeafErr?: boolean,
) => {
  const queryParams = new URLSearchParams();
  queryParams.append('tab', tab || BusinessDomainDetailTab.INFORMATION);
  if (ciBusinessFunction !== undefined) {
    queryParams.append('ciBusinessFunction', ciBusinessFunction.toString());
    queryParams.append('ciNameBusinessFunction', ciNameBusinessFunction || '');
  }

  if (notLeafErr) {
    queryParams.append('noRelationshipBusinessFunctionError', 'true');
  }

  const toPath = pathToRegexp.compile(businessDomainDetailPath);
  const fullUrl = `${toPath({ ciId })}?${queryParams.toString()}`;
  return fullUrl;
};

export const buildBusinessDomainAggregateUrl = (ciName: string) => {
  const queryParams = new URLSearchParams();
  queryParams.append('ciName', ciName);

  const fullUrl = `${businessDomainAggregatePath}?${queryParams.toString()}`;
  return fullUrl;
};
