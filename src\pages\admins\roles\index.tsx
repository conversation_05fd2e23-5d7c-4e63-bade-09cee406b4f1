import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanTable, ColumnType, TableAffactedSafeType, KanbanTableProps, KanbanModal } from 'kanban-design-system';
import { IconPlus, IconEdit, IconEye, IconUsersPlus, IconFriendsOff, IconCopy } from '@tabler/icons-react';
import React, { useEffect, useState, useMemo, useCallback, useRef } from 'react';
import { NotificationError, NotificationSuccess } from '@common/utils/NotificationUtils';
import { useNavigate } from 'react-router-dom';
import { buildCreateOrUpdateRoleUrl, buildRoleUrl, buildViewDetailRoleUrl } from '@common/utils/RouterUtils';
import { renderDateTime } from 'kanban-design-system';
import equal from 'fast-deep-equal';

import { KanbanText, KanbanButton, KanbanIconButton, KanbanTooltip } from 'kanban-design-system';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { RolePagingResponse, RoleResponse, RolesApi } from '@api/systems/RolesApi';
import GuardComponent from '@components/GuardComponent';
import { useDisclosure } from '@mantine/hooks';
import { AclPermission } from '@models/AclPermission';
import { Flex, Space } from '@mantine/core';
import { IconFriends } from '@tabler/icons-react';
import SettingGroupIntoRoleContent, { SettingGroupIntoRoleContentMethod } from './components/SettingGroupIntoRoleContent';
import { basicInfoRoleDefault } from './CreateOrUpdateRolePage';
import type { UserSettingGroupModel } from '@api/systems/GroupsApi';
import { RoleAction } from '@common/constants/RoleActionEnum';
import { BreadcrumbComponent } from '../breadcrumb/BreadcrumbComponent';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import { MAX_TEXT_LENGTH } from '@common/constants/FieldLengthConstants';

export const RoleManagementPage = () => {
  // const dispatch = useDispatch();
  const navigate = useNavigate();
  const [roleSelected, setRoleSelected] = useState<RoleResponse>(basicInfoRoleDefault);
  const [openedModalGroupInRole, { close: closeModalGroupInRole, open: openModalGroupInRole }] = useDisclosure(false);
  const [isFetched, setIsFetched] = useState<boolean>(false);

  const mountData = (value: boolean) => {
    setIsFetched(value);
  };

  const childRef = useRef<SettingGroupIntoRoleContentMethod>(null);
  const [selectedItems, setSelectedItems] = useState<UserSettingGroupModel[]>([]);
  const selectedItemIds = selectedItems.map((item) => item.id);
  const columns: ColumnType<RoleResponse>[] = useMemo(() => {
    return [
      {
        title: 'Name',
        name: 'name',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data) => {
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {data}
            </KanbanText>
          );
        },
        width: '20%',
      },
      {
        title: 'Description',
        name: 'description',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data) => {
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {data}
            </KanbanText>
          );
        },
        width: '30%',
      },
      {
        name: 'createdBy',
        title: 'Created by',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        name: 'createdDate',
        title: 'Created date',
        advancedFilter: {
          variant: 'date',
          customProps: {
            popoverProps: {
              withinPortal: false,
            },
          },
        },
        customRender: renderDateTime,
      },
      {
        name: 'modifiedBy',
        title: 'Modified by',
        hidden: true,
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        name: 'modifiedDate',
        title: 'Modified date',
        customRender: renderDateTime,
        hidden: true,
        advancedFilter: {
          variant: 'date',
          customProps: {
            popoverProps: {
              withinPortal: false,
            },
          },
        },
      },
    ];
  }, []);

  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);

  const [listData, setListData] = useState<RoleResponse[]>([]);

  const getAllRoles = useCallback(() => {
    if (!tableAffected) {
      return;
    }
    const dataSend = tableAffectedToMultiColumnFilterPaginationRequestModel<RolePagingResponse>(
      tableAffected.sortedBy ? tableAffected : { ...tableAffected, sortedBy: 'createdDate', isReverse: true },
    );

    RolesApi.getAll(dataSend)
      .then((res) => {
        if (res.data) {
          setListData(res.data?.content || []);
          setTotalRecords(res.data.totalElements);
        }
      })
      .catch(() => {});
  }, [tableAffected]);

  useEffect(() => {
    getAllRoles();
  }, [getAllRoles]);

  const deleteRoles = useCallback(
    (ids: number[]) => {
      RolesApi.deleteByIds(ids)
        .then(() => {
          NotificationSuccess({
            message: 'Deleted successfully',
          });
          // const newData = listData.filter((item) => !ids.includes(item.id));
          // setListData(newData);
          getAllRoles();
        })
        .catch(() => {});
    },
    [getAllRoles],
  );

  const tableViewListRolesProps: KanbanTableProps<RoleResponse> = useMemo(() => {
    return {
      columns: columns,
      key: 1,
      data: listData,
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 300,
      },
      advancedFilterable: {
        enable: true,
        debounceTime: 1000,
        resetOnClose: true,
        compactMode: true,
      },
      actions: {
        deletable: isCurrentUserMatchPermissions([AclPermission.deleteRole])
          ? {
              onDeleted: (data) => deleteRoles([data.id]),
            }
          : undefined,
        customAction: isCurrentUserMatchPermissions(AclPermission.actionTableRolePermissions)
          ? (data) => (
              <>
                {isCurrentUserMatchPermissions([AclPermission.viewDetailRole]) && (
                  <KanbanIconButton
                    variant='transparent'
                    size='sm'
                    onClick={() => {
                      navigate(buildViewDetailRoleUrl(data.id));
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                )}
                <GuardComponent requirePermissions={[AclPermission.createRole]} hiddenOnUnSatisfy>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      navigate(buildRoleUrl(data.id, RoleAction.COPY));
                    }}>
                    <IconCopy />
                  </KanbanIconButton>
                </GuardComponent>
                {isCurrentUserMatchPermissions([AclPermission.updateRole]) && (
                  <KanbanIconButton
                    variant='transparent'
                    size='sm'
                    onClick={() => {
                      if (!isCurrentUserMatchPermissions([AclPermission.viewDetailRole])) {
                        NotificationError({
                          title: `Request error`,
                          message: `You don't have permissions: View detail role`,
                        });
                        return;
                      }
                      navigate(buildCreateOrUpdateRoleUrl(data.id));
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                )}

                {isCurrentUserMatchPermissions([AclPermission.addGroupToRole, AclPermission.deleteGroupInRole]) && (
                  <KanbanTooltip label='Setting group'>
                    <KanbanIconButton
                      key={2}
                      size='sm'
                      color='blue'
                      variant='transparent'
                      onClick={() => {
                        setRoleSelected(data);
                        openModalGroupInRole();
                        setIsFetched(true);
                      }}>
                      <IconUsersPlus />
                    </KanbanIconButton>
                  </KanbanTooltip>
                )}
              </>
            )
          : undefined,
      },
      selectableRows: {
        enable: !!isCurrentUserMatchPermissions([AclPermission.deleteRole]),
        onDeleted: isCurrentUserMatchPermissions([AclPermission.deleteRole]) ? (rows) => deleteRoles(rows.map((x) => x.id)) : undefined,
      },
      onRowClicked: (data) => {
        if (isCurrentUserMatchPermissions([])) {
          navigate(buildViewDetailRoleUrl(data.id));
        }
      },
      pagination: {
        enable: true,
      },
      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
    };
  }, [columns, listData, totalRecords, deleteRoles, navigate, openModalGroupInRole, tableAffected]);

  const onRefreshAfterExcuteAction = () => {
    childRef.current?.clearSelected();
    childRef.current?.fetchData();
    setSelectedItems([]);
  };

  const onAddGroupsIntoRole = useCallback(() => {
    RolesApi.updateGroupIntoRole(roleSelected.id, selectedItemIds)
      .then((response) => {
        if (response.status === 200) {
          onRefreshAfterExcuteAction();
          NotificationSuccess({
            message: 'Add groups into role successfully',
          });
        }
      })
      .catch(() => {});
  }, [roleSelected.id, selectedItemIds]);

  const onRemoveGroupsFromRole = useCallback(() => {
    RolesApi.removeGroupsFromRole(roleSelected.id, selectedItemIds)
      .then((response) => {
        if (response.status === 200) {
          onRefreshAfterExcuteAction();
          NotificationSuccess({
            message: 'Delete groups from role successfully',
          });
        }
      })
      .catch(() => {});
  }, [roleSelected.id, selectedItemIds]);

  const onUpdateSelected = (datas: UserSettingGroupModel[]) => {
    setSelectedItems(datas);
  };

  return (
    <>
      {/* 4763 roles*/}
      <BreadcrumbComponent />
      <HeaderTitleComponent
        title='Role Setting'
        rightSection={
          <GuardComponent requirePermissions={[AclPermission.createRole]} hiddenOnUnSatisfy>
            <KanbanButton
              onClick={() => {
                navigate(buildCreateOrUpdateRoleUrl(0));
              }}
              leftSection={<IconPlus />}>
              Create Role
            </KanbanButton>
          </GuardComponent>
        }
      />
      <div style={{ flex: 2 }}>
        <KanbanTable {...tableViewListRolesProps} />
      </div>

      <div>
        <KanbanModal
          keepMounted={false}
          size={'xl'}
          opened={openedModalGroupInRole}
          onClose={() => {
            childRef.current?.clearSelected();
            closeModalGroupInRole();
          }}
          centered
          title={`Setting role group ${roleSelected.name}:`}
          actions={
            <Flex>
              <GuardComponent requirePermissions={[AclPermission.addGroupToRole]} hiddenOnUnSatisfy>
                <KanbanButton disabled={!selectedItems?.length} leftSection={<IconFriends />} onClick={onAddGroupsIntoRole}>
                  Add
                </KanbanButton>
              </GuardComponent>
              <Space w={'xs'} />
              <GuardComponent requirePermissions={[AclPermission.deleteGroupInRole]} hiddenOnUnSatisfy>
                <KanbanButton disabled={!selectedItems?.length} leftSection={<IconFriendsOff />} onClick={onRemoveGroupsFromRole}>
                  Remove
                </KanbanButton>
              </GuardComponent>
            </Flex>
          }>
          <SettingGroupIntoRoleContent
            isFetched={isFetched}
            mountData={mountData}
            role={roleSelected}
            onUpdateSelected={onUpdateSelected}
            isShowActiveColumn={true}
            ref={childRef}
          />
        </KanbanModal>
      </div>
    </>
  );
};
export default RoleManagementPage;
