import { allDiscoveryDestinations } from '@common/utils/RouterUtils';
import { useMemo } from 'react';
import { useLocation } from 'react-router-dom';

const useMatchDiscoveryPath = (): boolean => {
  const location = useLocation();
  const isMatch = useMemo(() => {
    return allDiscoveryDestinations.some((path) => location.pathname.includes(path ?? ''));
  }, [location.pathname]);

  return isMatch;
};

export default useMatchDiscoveryPath;
