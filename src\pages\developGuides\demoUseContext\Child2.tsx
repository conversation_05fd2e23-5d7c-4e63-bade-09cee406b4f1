import React, { useContext } from 'react';
import { DemoContext } from './DemoContext';
import { KanbanButton } from 'kanban-design-system';

const Child2 = () => {
  const context = useContext(DemoContext);

  return (
    <>
      Child2 Render: <br /> {context.value}
      <br />
      <KanbanButton
        onClick={() => {
          context.setValue((prev) => prev - 1);
        }}>
        Decrease
      </KanbanButton>
    </>
  );
};
export default Child2;
