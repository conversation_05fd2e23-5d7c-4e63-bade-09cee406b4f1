import { NotificationData, notifications } from '@mantine/notifications';

export const NotificationSuccess = (
  data: Partial<NotificationData> & {
    message: React.ReactNode;
  },
) => {
  notifications.show({
    color: 'primary',
    title: 'Success',
    withCloseButton: true,
    ...data,
  });
};
export const NotificationError = (
  data: Partial<NotificationData> & {
    message: React.ReactNode;
  },
) => {
  notifications.show({
    color: 'red',
    title: 'Error',
    withCloseButton: true,
    ...data,
  });
};
export const NotificationWarning = (
  data: Partial<NotificationData> & {
    message: React.ReactNode;
  },
) => {
  notifications.show({
    color: 'yellow',
    title: 'Warning',
    withCloseButton: true,
    ...data,
  });
};
