import { ColumnFilter } from '@tanstack/table-core';

export enum EntityAuditLogTypeEnum {
  CI_IDENTIFIER_RULE = 'CI_IDENTIFIER_RULE',
  CI_RECONCILIATION_RULE = 'CI_RECONCILIATION_RULE',
}

export type AuditLogModel = {
  id?: string;
  createdBy?: string;
  createdDate?: Date;
  entityId?: number;
  type?: EntityAuditLogTypeEnum;
  data?: string;
};

export type SearchHit<T> = {
  _id: string;
  _type: string;
  _score: string;
  _source: T;
};

export type DataChangeStyleObj = {
  title?: string;
  icon?: JSX.Element;
  a: ColumnFilter;
};

export enum EntityAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  IMPORT = 'IMPORT',
  UNKNOWN = 'UNKNOWN',
}
export type ColumnFilterStateWithType = ColumnFilter & {
  filterVariant: string;
};
