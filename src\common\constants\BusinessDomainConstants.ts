export enum HeightScrollItemComponent {
  HeightCollapse = 122,
  HeightExpand = 572,
}
// export const MIN_WIDTH_ITEM = 424;
// export const MAX_WIDTH_CONTENT = 270;
export const MAX_RECORD_IN_COLLAPSE = 3;
// export const POS_IDX_SHOW_BTN_EXPAND = 2;

export enum BusinessDomainDetailTab {
  // tab relationship
  DEFAULT = 'DEFAULT',
  INFORMATION = 'INFORMATION',
  BUSINESS_FUNCTION = 'BUSINESS_FUNCTION',
}

export enum BusinessModelAttributeEnum {
  STRATEGY_AND_PROPOSITION = 'Strategy & Proposition',
  CUSTOMER_INTERACTION = 'Customer Interaction',
  FRONT_OFFICE = 'Front Office',
  MIDDLE_OFFICE = 'Middle Office',
  BACK_OFFICE = 'Back Office',
  BUSINESS_ENABLEMENT_SERVICE = 'Business Enablement Service',
  RISK_FINANCE_AND_COMPLIANCE = 'Risk, Finance & Compliance',
}
export type BusinessModelAttributeValues = `${BusinessModelAttributeEnum}`;
export type BusinessModelAttributeKeys = keyof typeof BusinessModelAttributeEnum;

export enum BusinessDomainType {
  BUSINESS_DOMAIN = 'BUSINESS_DOMAIN',
  BUSINESS_FUNCTION = 'BUSINESS_FUNCTION',
}
