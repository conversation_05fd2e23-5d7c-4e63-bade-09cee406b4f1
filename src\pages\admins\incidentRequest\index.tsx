import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { ColumnType, KanbanTable, TableAffactedSafeType } from 'kanban-design-system';
import { IconEdit, IconEye } from '@tabler/icons-react';
import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { KanbanIconButton } from 'kanban-design-system';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { useNavigate } from 'react-router-dom';
import { buildIncidentDetailUrl } from '@common/utils/RouterUtils';
import { renderDateTime } from 'kanban-design-system';
import equal from 'fast-deep-equal';
import { KanbanText } from 'kanban-design-system';
import { ChangeAssessmentAction, type ChangeAssessmentDTO } from '@models/ChangeAssessment';
import ChangeAssessmentExecute from '@service/ChangeAssessmentExecute';
import { IncidentSdpApi } from '@api/IncidentSdpApi';
import { AclPermission } from '@models/AclPermission';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { BreadcrumbComponent } from '../breadcrumb/BreadcrumbComponent';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import { MAX_NUMBER_LENGTH, MAX_TEXT_LENGTH } from '@common/constants/FieldLengthConstants';

export const IncidentRequestPage = () => {
  const navigate = useNavigate();

  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);

  const [listData, setListData] = useState<ChangeAssessmentDTO[]>([]);
  const [isLoadingTable, setIsLoadingTable] = useState(false);

  const columns: ColumnType<ChangeAssessmentDTO>[] = useMemo(
    () => [
      {
        title: 'Incident ID',
        name: 'changeId',
        width: '10%',
        advancedFilter: {
          variant: 'number',
          filterModes: ['equals', 'notEquals', 'greaterThan', 'greaterThanOrEqualTo', 'lessThan', 'lessThanOrEqualTo'],
          customProps: { maxLength: MAX_NUMBER_LENGTH },
        },
      },
      {
        title: 'Affected Cis',
        name: 'ciName',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (_, rowData) => {
          const affectedCisString = ChangeAssessmentExecute.getCiNamesString(rowData.affectedCis);
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {affectedCisString}
            </KanbanText>
          );
        },
      },
      {
        title: 'Impacted Services',
        name: 'impactedServices',
        advancedFilter: {
          enable: false,
        },
        customRender: (data: any) => {
          const impactedServicesString = ChangeAssessmentExecute.getCiNamesString(data);
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {impactedServicesString}
            </KanbanText>
          );
        },
      },
      {
        title: 'Last modified by',
        name: 'modifiedBy',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Last modified date',
        name: 'modifiedDate',
        advancedFilter: {
          variant: 'date',
          customProps: {
            popoverProps: {
              withinPortal: false,
            },
          },
        },
        customRender: renderDateTime,
      },
    ],
    [],
  );

  const fetchListChangeAssessment = useCallback(() => {
    if (!tableAffected) {
      return;
    }
    const dataSend = tableAffectedToMultiColumnFilterPaginationRequestModel<ChangeAssessmentDTO>(
      tableAffected.sortedBy ? tableAffected : { ...tableAffected, sortedBy: 'createdDate', isReverse: true },
    );

    setIsLoadingTable(true);
    IncidentSdpApi.getAll(dataSend)
      .then((res) => {
        if (res.data) {
          setListData(res.data?.content || []);
          setTotalRecords(res.data.totalElements);
        }
      })
      .catch(() => {})
      .finally(() => {
        setIsLoadingTable(false);
      });
  }, [tableAffected]);

  useEffect(() => {
    fetchListChangeAssessment();
  }, [fetchListChangeAssessment]);

  const deleteRow = (id: number) => {
    IncidentSdpApi.deleteByIds([id])
      .then((res) => {
        if (res.data) {
          NotificationSuccess({
            message: 'Deleted successfully.',
          });
          // const newData = listData.filter((x) => x.id !== id);
          // setListData(newData);
          fetchListChangeAssessment();
        }
      })
      .catch(() => {});
  };

  const deleteRows = (ids: number[]) => {
    IncidentSdpApi.deleteByIds(ids)
      .then(() => {
        NotificationSuccess({
          message: 'Deleted successfully',
        });
        // const newData = listData.filter((item) => !ids.includes(item.id));
        // setListData(newData);
        fetchListChangeAssessment();
      })
      .catch(() => {});
  };

  return (
    <>
      {/* 4763  indedent list*/}

      <BreadcrumbComponent />
      <HeaderTitleComponent title='Incident Requests' />
      <div style={{ flex: 2 }}>
        <KanbanTable
          columns={columns}
          key={1}
          data={listData}
          isLoading={isLoadingTable}
          showNumericalOrderColumn={true}
          searchable={{
            enable: true,
            debounceTime: 800,
          }}
          advancedFilterable={{
            enable: true,
            debounceTime: 1000,
            resetOnClose: true,
            compactMode: true,
          }}
          actions={{
            deletable: isCurrentUserMatchPermissions([AclPermission.deleteIncidentRequest])
              ? {
                  onDeleted(data) {
                    deleteRow(data.id);
                  },
                }
              : undefined,
            customAction: isCurrentUserMatchPermissions(AclPermission.actionTableIncidentRequestPermissions)
              ? (data) => {
                  return (
                    <>
                      {isCurrentUserMatchPermissions([AclPermission.viewDetailIncidentRequest]) && (
                        <KanbanIconButton
                          variant='transparent'
                          size={'sm'}
                          onClick={() => {
                            navigate(buildIncidentDetailUrl(data.changeId, ChangeAssessmentAction.VIEW));
                          }}>
                          <IconEye />
                        </KanbanIconButton>
                      )}
                      {isCurrentUserMatchPermissions([AclPermission.updateIncidentRequest]) && (
                        <KanbanIconButton
                          variant='transparent'
                          size={'sm'}
                          onClick={() => {
                            navigate(buildIncidentDetailUrl(data.changeId, ChangeAssessmentAction.UPDATE));
                          }}>
                          <IconEdit />
                        </KanbanIconButton>
                      )}
                    </>
                  );
                }
              : undefined,
          }}
          selectableRows={{
            enable: !!isCurrentUserMatchPermissions([AclPermission.deleteIncidentRequest]),
            onDeleted: isCurrentUserMatchPermissions([AclPermission.deleteIncidentRequest])
              ? (rows) => {
                  deleteRows(rows.map((x) => x.id));
                }
              : undefined,
          }}
          onRowClicked={(data) => {
            isCurrentUserMatchPermissions([AclPermission.viewDetailChangeAssessment]) &&
              navigate(buildIncidentDetailUrl(data.changeId, ChangeAssessmentAction.VIEW));
          }}
          pagination={{
            enable: true,
          }}
          serverside={{
            totalRows: totalRecords,
            onTableAffected(dataSet) {
              if (!equal(tableAffected, dataSet)) {
                setTableAffected(dataSet);
              }
            },
          }}
        />
      </div>
    </>
  );
};
export default IncidentRequestPage;
