/* eslint-disable no-console */
import type { ConfigItemResponse } from '@api/ConfigItemApi';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import { permissionActionsMap } from '@common/constants/PermissionAction';
import { PermissionActionType } from '@common/constants/PermissionActionType';
import { CiTypePickerComponent } from '@components/commonCi/SelectCiTypeComponent';
import type { KanbanComponentWithLabelProps } from 'kanban-design-system';
import {
  KanbanTable,
  TableAffactedSafeType,
  KanbanButton,
  KanbanIconButton,
  KanbanText,
  getDefaultTableAffected,
  KanbanModal,
  KanbanConfirmModal,
  KanbanMultiSelect,
} from 'kanban-design-system';
import { Box, Center, Flex, List, ThemeIcon } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import type { ConfigItemTypeModel } from '@models/ConfigItemType';
import type { ConfigItemTypePermissionModel } from '@models/ConfigItemTypePermission';
import equal from 'fast-deep-equal';
import { tableAffectedToPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import type { SysPermissionCiModel } from '@api/systems/GroupsApi';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import { IconArrowNarrowLeft, IconArrowNarrowRight, IconEdit, IconEye, IconOctagonPlus } from '@tabler/icons-react';
import { useSelector, useDispatch } from 'react-redux';
import { ciTreePermissionsSlice, getCiTreePermissions } from '@slices/CiTreePermissionSlice';
import type { SysPermissionCiResponse } from '@api/SysPermissionCisApi';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import React from 'react';
import { IconCircleCheck } from '@tabler/icons-react';

export type SelectCiTypeComponentProps = KanbanComponentWithLabelProps & {
  value?: number;
  onChange?: (newValue?: number) => void;
  updateCiPermission?: (listTrees: ConfigItemTypePermissionModel[]) => void;
  roleId: number;
  renderOnlyOne?: boolean;
  setCallApiOnlyOne?: (value: boolean) => void;
  sysPermissionCi?: SysPermissionCiResponse[];
};

export type GroupSetting = {
  ciTypeId?: number;
  ciId?: number;
  name?: string;
  action?: string[];
};

const findAndSettingIsSettingAncestors = (
  node: ConfigItemTypePermissionModel,
  list: ConfigItemTypePermissionModel[],
): ConfigItemTypePermissionModel[] => {
  const item = { ...node };
  const updatedList = list.map((originalItem) => ({ ...originalItem }));

  const parent = updatedList.find((parent) => parent.id === item.parentId);

  if (parent) {
    parent.isSetting = true;
    return findAndSettingIsSettingAncestors(parent, updatedList);
  }

  return updatedList;
};
/**
 * enable all node setting when load setting from server
 * @param list
 */
const findAndSettingIsSettingInit = (list: ConfigItemTypePermissionModel[]): ConfigItemTypePermissionModel[] => {
  let listCopy = [...list];
  for (const item of listCopy) {
    if (item.isSetting) {
      listCopy = findAndSettingIsSettingAncestors(item, listCopy);
    }
  }
  return listCopy;
};

/**
 * Group All action by ciTypeId when get from server
 * @param data
 */
function groupByCiTypeId(data: SysPermissionCiModel[]): GroupSetting[] {
  const groupedData: { [key: number]: string[] } = {};

  data.forEach((item) => {
    if (item.type === PermissionActionType.CI_TYPE) {
      const { action, ciTypeId } = item;

      if (ciTypeId in groupedData) {
        groupedData[ciTypeId].push(action || '');
      } else {
        groupedData[ciTypeId] = [action || ''];
      }
    }
  });

  const result: GroupSetting[] = Object.entries(groupedData).map(([ciTypeId, actions]) => ({
    ciTypeId: parseInt(ciTypeId, 10),
    action: actions,
  }));

  return result;
}
/**
 * Group All action by ciTypeId and CI when get from server
 * @param data
 */
export function groupByCiId(data: SysPermissionCiModel[]): GroupSetting[] {
  const groupedData: { [key: string]: { [key: string]: string[] } } = {};

  data.forEach((item) => {
    if (item.type === PermissionActionType.CI_TYPE) {
      return;
    }
    const { action, ciId, ciTypeId } = item;
    const ciIdKey = ciId?.toString();
    const citypeIdKey = ciTypeId?.toString();

    if (!(ciIdKey in groupedData)) {
      groupedData[ciIdKey] = {};
    }

    if (citypeIdKey in groupedData[ciIdKey]) {
      groupedData[ciIdKey][citypeIdKey].push(action || '');
    } else {
      groupedData[ciIdKey][citypeIdKey] = [action || ''];
    }
  });

  const result: GroupSetting[] = [];

  for (const ciIdKey in groupedData) {
    for (const citypeIdKey in groupedData[ciIdKey]) {
      const ciId = parseInt(ciIdKey, 10);
      const citypeId = parseInt(citypeIdKey, 10);
      const actions = groupedData[ciIdKey][citypeIdKey];

      result.push({ ciId: ciId, ciTypeId: citypeId, action: actions });
    }
  }

  return result;
}
const ciTypeDefault: ConfigItemTypeModel = {
  id: 0,
  name: '',
};

const scrollHeightDefault: number = 600;
export const GroupSettingsPermissionModal: React.FC<SelectCiTypeComponentProps> = memo((_props: SelectCiTypeComponentProps) => {
  const [openedAddItem, { close: closeAddItem, open: openAddItem }] = useDisclosure(false);
  const [openedshowListPermissionOfCitype, { close: onCloseShowListPermissionOfCitype, open: onOpenShowListPermissionOfCitype }] =
    useDisclosure(false);
  const [openedCopyOther, { close: closeCopyOther, open: openCopyOther }] = useDisclosure(false);
  const [openedConfirmMinus, { close: closeConfirmMinus, open: openConfirmMinus }] = useDisclosure(false);
  const [openedModalSettingPermissionCi, { close: closeModalSettingPermissionCi, open: openModalSettingPermissionCi }] = useDisclosure(false);
  const [openedCIList, { close: closeCIList, open: openCIList }] = useDisclosure(false);
  // const [listTrees, setListTrees] = useState<ConfigItemTypePermissionModel[]>([]);
  const listTrees = useSelector(getCiTreePermissions);
  // const [listIgnore, setListIgnore] = useState<number[]>([]);
  const listCiTypes = useGetCiTypes().data;
  const [listCI, setListCI] = useState<ConfigItemResponse[]>([]);
  const [listCISetting, setListCISetting] = useState<GroupSetting[]>([]); // ci has permission from DB
  const [listCISettingDraft, setListCISettingDraft] = useState<GroupSetting[]>([]);
  const [listCISettingFE, setListCISettingFE] = useState<ConfigItemTypePermissionModel[]>([]);
  const [listSelectedCiType, setListSelectedCiType] = useState<ConfigItemTypePermissionModel[]>([]);
  const [listSelectedCiTypeInRight, setListSelectedCiTypeInRight] = useState<ConfigItemTypePermissionModel[]>([]);
  const [listCiIdSelected, setListCiIdSelected] = useState<number[]>([]); // setting permission for multi cis when tich
  const [listCiPermission, setListCiPermission] = useState<string[]>([]);
  const [ciTypeCurrent, setCiTypeCurrent] = useState<ConfigItemTypeModel>(ciTypeDefault);
  // const [moveLeft, setMoveLeft] = useState<boolean>(true);
  // const [moveRight, setMoveRight] = useState<boolean>(true);
  const moveLeft = listSelectedCiTypeInRight.length > 0;
  const moveRight = listSelectedCiType.length > 0;
  const [isSelectionInLeft, setIsSelectionInLeft] = useState<boolean>(true);
  // list CiType permissions of move left
  const [listCiTypePermission, setListCiTypePermission] = useState<string[]>([]);

  const dispatch = useDispatch();

  const [nodeSelected, setNodeSelected] = useState<ConfigItemTypePermissionModel>(ciTypeDefault);

  const [nodeSelectedCopyOther, setNodeSelectedCopyOther] = useState<ConfigItemTypePermissionModel>(ciTypeDefault);
  const isFirstTime = useRef<boolean>(false);
  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType>(getDefaultTableAffected());
  const { sysPermissionCi, updateCiPermission } = _props;

  const [listPermissionOfCiType, setListPermissionOfCiType] = useState<string[]>([]);

  /**
   * add/delete Ci Type into list Ci Type to add permission
   */
  const onCheckboxCiType = (value?: boolean, item?: ConfigItemTypePermissionModel) => {
    //logic execute when check box in left ci type
    if (!item) {
      return;
    }
    setIsSelectionInLeft(true);
    setNodeSelected(item);
    if (value) {
      setListSelectedCiType((pre) => [...pre, item]);
    } else {
      setListSelectedCiType(listSelectedCiType.filter((ciType) => ciType.id !== item.id));
    }
  };

  //logic execute when check box in right ci type
  const onCheckboxCiTypeInRight = (value?: boolean, item?: ConfigItemTypePermissionModel) => {
    if (!item) {
      return;
    }
    setIsSelectionInLeft(false);
    setNodeSelected(item);
    if (value) {
      setListSelectedCiTypeInRight((pre) => [...pre, item]);
    } else {
      setListSelectedCiTypeInRight(listSelectedCiTypeInRight.filter((ciType) => ciType.id !== item.id));
    }
  };

  /**
   * add permission for each Ci Type is selected
   */
  const addPermissionCiType = () => {
    if (listSelectedCiType.length > 0) {
      addItem(listSelectedCiType);
      setListSelectedCiType([]);
    } else {
      addItem([nodeSelected]);
    }
    closeAddItem();
  };
  /**
   * ignore right tree with item not setting permission
   */
  // useEffect(() => {
  //   setListIgnore(listTrees.ciTreePermissions.filter((item) => !item.isSetting).map((obj) => obj.id));
  // }, [listTrees]);

  /**
   * convert Setting exist from server to type tree
   */
  useEffect(() => {
    const cISettingFE: ConfigItemTypePermissionModel[] = listCISetting.map((item) => {
      return {
        id: item.ciId || 0,
        name: item.name || '',
        type: PermissionActionType.CI,
        action: item.action,
        isSetting: true,
        parentId: item.ciTypeId,
      };
    });
    setListCISettingFE(cISettingFE);
    setListCISettingDraft(cISettingFE);
  }, [listCISetting]);

  /**
   * get setting exist from server and enable all item that have been set
   * @param groupId
   */
  const getSettingByGroupId = useCallback(() => {
    const dataSetting = sysPermissionCi || [];
    const dataGroup = groupByCiTypeId(dataSetting);
    const dataGroupCi = groupByCiId(dataSetting);
    setListCISetting(dataGroupCi);
    const updatedConfigItemPermissions = listTrees.ciTreePermissions.map((item) => ({
      ...item,
      isSetting: false,
    }));
    const listTreesFilter = updatedConfigItemPermissions.length > 0 ? updatedConfigItemPermissions : listCiTypes;
    let updatedListTrees = listTreesFilter.map((item) => {
      const nodeSetting = dataGroup.find((child) => child.ciTypeId === item.id); //  ci types is config
      const nodeSettingCI = dataSetting.find((child) => child.ciTypeId === item.id && child.type === PermissionActionType.CI); //  cis is config
      if (nodeSetting) {
        return {
          ...item,
          action: nodeSetting.action || [],
          isSetting: true,
          type: PermissionActionType.CI_TYPE,
        };
      }
      if (nodeSettingCI) {
        return {
          ...item,
          isSetting: true,
        };
      }
      return item;
    });

    updatedListTrees = findAndSettingIsSettingInit(updatedListTrees);

    dispatch(ciTreePermissionsSlice.actions.updateCiTreePermissions(updatedListTrees));
    if (updateCiPermission) {
      updateCiPermission([...listCISettingFE, ...updatedListTrees]);
    }
  }, [updateCiPermission, sysPermissionCi, listTrees.ciTreePermissions, listCiTypes, dispatch, listCISettingFE]);
  /**
   * get setting exist from server
   */
  useEffect(() => {
    if (!isFirstTime.current) {
      getSettingByGroupId();
      // if (_props.setCallApiOnlyOne) {
      //   _props.setCallApiOnlyOne(false);
      // }
      isFirstTime.current = true;
    }
  }, [getSettingByGroupId]);

  /**
   * get all CI when open model setting CI
   * @param item
   */
  const getListCI = (item: ConfigItemTypeModel) => {
    // setNodeSelectedRight(item);
    setNodeSelected(item);
    setCiTypeCurrent(item);
    if (!tableAffected) {
      return;
    }
    fetchListCiByCiTypeId(item.id);
  };

  const fetchListCiByCiTypeId = useCallback(
    (ciTypeId: number) => {
      if (!tableAffected) {
        return;
      }

      ConfigItemTypeApi.findAllByCiTypeIdWithPermission(ciTypeId, tableAffectedToPaginationRequestModel(tableAffected))
        .then((res) => {
          setListCI(res.data?.content || []);
          setListCISettingDraft(listCISetting);
          setTotalRecords(res.data.totalElements);
          openCIList();
        })
        .catch(() => {});
    },
    [listCISetting, openCIList, tableAffected],
  );

  function openModelAddItem(item: ConfigItemTypeModel) {
    setNodeSelected(item);
    openAddItem();
  }
  const rightSectionLeft = (item: ConfigItemTypeModel) => (
    <>
      <KanbanIconButton
        onClick={(e) => {
          e.stopPropagation();
          getListCI(item);
        }}
        size='sm'
        variant='transparent'>
        <IconOctagonPlus></IconOctagonPlus>
      </KanbanIconButton>
      <KanbanIconButton
        onClick={(e) => {
          e.stopPropagation();
          openModelAddItem(item);
          setIsSelectionInLeft(false);
        }}
        size='sm'
        variant='transparent'>
        <IconEdit></IconEdit>
      </KanbanIconButton>
    </>
  );

  const sectionInRight = (item: ConfigItemTypeModel) => {
    return (
      _props.roleId &&
      item.action &&
      item.action.length > 0 && (
        <>
          <KanbanIconButton
            onClick={(e) => {
              e.stopPropagation();
              setListPermissionOfCiType(item.action ?? []);
              onOpenShowListPermissionOfCitype();
            }}
            size='sm'
            variant='transparent'>
            <IconEye />
          </KanbanIconButton>
        </>
      )
    );
  };

  /**
   * setting node when click to item in tree
   * @param value
   * @param node
   */
  const onChangeValue = (value?: number, node?: ConfigItemTypeModel) => {
    if (node) {
      setNodeSelected(node);
    }
  };

  /**
   * setting node when click to item in right tree
   * @param value
   * @param node
   */
  const onChangeValueRight = (value?: number, node?: ConfigItemTypeModel) => {
    if (node) {
      // setNodeSelectedRight(node);
      setNodeSelected(node);
    }
  };

  /**
   *  setting node when click selected to item on tree when use copy other permission
   * @param value
   * @param node
   */
  const onChangeValueCopyOther = (value?: number, node?: ConfigItemTypeModel) => {
    if (node) {
      setNodeSelectedCopyOther(node);
    }
  };

  // remove data from in right section
  const minusNode = () => {
    // if (nodeSelectedRight.id === 0) {
    //   return;
    // }
    const allListChild: ConfigItemTypePermissionModel[] = [];
    const allListParent: ConfigItemTypePermissionModel[] = [];

    listSelectedCiTypeInRight.forEach((node) => {
      // all ci type parent move all
      const allListParentOfNode = getNodeWithAllParents(node, listTrees.ciTreePermissions);
      allListParent.push(...allListParentOfNode);
    });
    listSelectedCiTypeInRight.forEach((node) => {
      const allListChildOfNode = getNodeWithAllChild(node, listTrees.ciTreePermissions);
      allListChild.push(...allListChildOfNode);
    });
    const allData = [...allListChild, ...allListParent];
    const updatedListTrees = listTrees.ciTreePermissions.map((item) => {
      if (allData.some((child) => child.id === item.id)) {
        return {
          ...item,
          action: [],
          isSetting: false,
        };
      }
      return item;
    });
    // setListTrees(updatedListTrees);
    const updatedListCISettingFE = listCISettingFE.filter((item) => !allListChild.some((child) => child.id === item.parentId));
    setListCISettingFE(updatedListCISettingFE);

    if (_props.updateCiPermission) {
      _props.updateCiPermission([...updatedListCISettingFE, ...updatedListTrees]);
    }
    dispatch(ciTreePermissionsSlice.actions.updateCiTreePermissions(updatedListTrees));
    closeConfirmMinus();
  };

  function copyFromParentInRight() {
    const parentNode = listTrees.ciTreePermissions.find((item) => item.id === nodeSelected.parentId);
    const allPermission = Array.from(new Set([...(nodeSelected.action ?? []), ...(parentNode?.action ?? [])]));
    const nodeSelectedCopy = { ...nodeSelected, action: allPermission };

    // const actionSet = new Set(nodeSelectedCopy.action);
    // listSelectedCiType.forEach((ciType) => {
    //   const parentCiType = listTrees.ciTreePermissions.find((item) => item.id === ciType.parentId);
    //   if (parentCiType) {
    //     parentCiType.action?.forEach((action) => actionSet.add(action));
    //   }
    // });
    // nodeSelectedCopy.action = Array.from(actionSet);
    setNodeSelected(nodeSelectedCopy);
  }

  function copyOtherInRight() {
    const nodeSelectedCopy = { ...nodeSelected };
    nodeSelectedCopy.action = Array.from(new Set([...(nodeSelectedCopy.action ?? []), ...(nodeSelectedCopyOther.action ?? [])]));
    setNodeSelected(nodeSelectedCopy);
    closeCopyOther();
  }

  function copyActionsToNodeSelected(actionCopys: string[]) {
    const nodeSelectedCopy = { ...nodeSelected };
    nodeSelectedCopy.action = actionCopys;
    setNodeSelected(nodeSelectedCopy);
    setListCiTypePermission(actionCopys);
  }

  function copyOtherInLeft() {
    const nodeSelectedCopyOtherCurrent = listTrees.ciTreePermissions.find((item) => item.id === nodeSelectedCopyOther.id);
    const allPermission = Array.from(new Set([...listCiTypePermission, ...(nodeSelectedCopyOtherCurrent?.action ?? [])]));
    // const actionCopys = nodeSelectedCopyOtherCurrent?.action || [];
    copyActionsToNodeSelected(allPermission);
    closeCopyOther();
  }

  function copyFromParentInLeft() {
    const parentNode = listTrees.ciTreePermissions.find((item) => item.id === nodeSelected.parentId);
    const allPermission = Array.from(new Set([...(nodeSelected.action ?? []), ...(parentNode?.action ?? [])]));
    // const actionCopys = parentNode?.action || [];
    copyActionsToNodeSelected(allPermission);
  }
  /**
   * confirm when setting permission for CI
   */
  function confirmSettingCI() {
    const cISettingFE: ConfigItemTypePermissionModel[] = listCISettingDraft.map((item) => {
      return {
        id: item.ciId || 0,
        name: item.name || '',
        type: PermissionActionType.CI,
        action: item.action,
        isSetting: true,
        parentId: item.ciTypeId,
      };
    });
    setListCISettingFE(cISettingFE);
    setListCISettingDraft(cISettingFE);
    if (_props.updateCiPermission) {
      _props.updateCiPermission([...cISettingFE, ...listTrees.ciTreePermissions]);
    }
    closeCIList();
  }

  /**
   * add item to right tree
   */
  function addItem(nodes: ConfigItemTypePermissionModel[]) {
    const updatedListTrees = listTrees.ciTreePermissions.map((item) => {
      const node = nodes.find((node) => node.id === item.id);
      if (node) {
        return {
          ...item,
          action: nodeSelected.action,
          isSetting: true,
          type: PermissionActionType.CI_TYPE,
        };
      }
      return item;
    });
    let listUpdateParent = updatedListTrees;
    nodes.forEach((node) => {
      listUpdateParent = findAndSettingIsSettingAncestors(node, listUpdateParent);
    });
    if (_props.updateCiPermission) {
      _props.updateCiPermission([...listCISettingFE, ...listUpdateParent]);
    }
    dispatch(ciTreePermissionsSlice.actions.updateCiTreePermissions(listUpdateParent));
  }

  function getNodeWithAllChild(parent: ConfigItemTypePermissionModel, list: ConfigItemTypePermissionModel[]): ConfigItemTypePermissionModel[] {
    const children: ConfigItemTypePermissionModel[] = [];

    const findChildrenRecursive = (currentParent: ConfigItemTypePermissionModel) => {
      const directChildren = list.filter((item) => item.parentId === currentParent.id);
      for (const child of directChildren) {
        children.push(child);
        findChildrenRecursive(child);
      }
    };

    findChildrenRecursive(parent);
    children.push(parent);
    return children;
  }

  function getNodeWithAllParents(node: ConfigItemTypePermissionModel, list: ConfigItemTypePermissionModel[]): ConfigItemTypePermissionModel[] {
    const parents: ConfigItemTypePermissionModel[] = [];

    const findParentsRecursive = (currentNode: ConfigItemTypePermissionModel): void => {
      const parent = list.find((item) => item.id === currentNode.parentId);
      if (parent) {
        parents.push(parent);
        findParentsRecursive(parent);
      }
    };

    findParentsRecursive(node);
    return parents;
  }

  /**
   * checkbox setting permission
   * @param e
   * @param name
   */
  // const _onChangeCheckbox = (e: React.ChangeEvent<HTMLInputElement>, name: string) => {
  //   const { currentTarget } = e;
  //   if (!nodeSelected) {
  //     return;
  //   }

  //   // Update nodeSelected state based on checkbox changes
  //   setNodeSelected((prevNodeSelected) => {
  //     const updatedAction = currentTarget.checked
  //       ? [...(prevNodeSelected?.action || []), name]
  //       : (prevNodeSelected?.action || []).filter((actionName) => actionName !== name);

  //     return {
  //       ...prevNodeSelected,
  //       action: updatedAction,
  //     };
  //   });
  // };

  const onChangeSelectPermisionCI = (ciIds: number[], value: string[]) => {
    ciIds.forEach((ciId) => {
      const itemToUpdateIndex = listCISettingDraft.findIndex((item) => item.ciId === ciId);
      if (itemToUpdateIndex !== -1) {
        listCISettingDraft[itemToUpdateIndex].action = value;
      } else {
        const newItem: GroupSetting = {
          ciId: ciId,
          ciTypeId: nodeSelected.id,
          action: value,
        };
        listCISettingDraft.push(newItem);
      }
    });

    setListCISettingDraft(listCISettingDraft);
    setListCiPermission([]);
  };

  // const customListCiTypeInLefts = useMemo(() => {
  //   return listTrees.ciTreePermissions.filter((item) => listIgnore.includes(item.id));
  // }, [listTrees, listIgnore]);

  const customListCiTypeInRights = useMemo(() => {
    return listTrees.ciTreePermissions.filter((item) => item.isSetting);
    // listTrees.ciTreePermissions.filter((item) => !listIgnore.includes(item.id));
  }, [listTrees]);

  const ciTypeIdInRights = useMemo(() => {
    return listTrees.ciTreePermissions.filter((item) => item.isSetting).map((obj) => obj.id);
  }, [listTrees]);

  const isButtonDisabled = useMemo(() => {
    if (isSelectionInLeft) {
      return listCiTypePermission.length === 0;
    } else {
      return nodeSelected?.action?.length === 0;
    }
  }, [isSelectionInLeft, listCiTypePermission, nodeSelected]);

  useEffect(() => {
    const ciTypeId = ciTypeCurrent.id;

    if (ciTypeId > 0) {
      fetchListCiByCiTypeId(ciTypeId);
    }
  }, [ciTypeCurrent.id, fetchListCiByCiTypeId]);

  /*
   Update expand all and disable citype selected and view detail permissions before copy other
  */

  const [listExpanded, setListExpanded] = useState<number[]>([]); // expand all left
  const [expandAll, setExpandAll] = useState(false);
  const ciTypes = useGetCiTypes();
  const onChangeExpand: (item: ConfigItemTypeModel, state: boolean) => void = useCallback((data, state) => {
    setListExpanded((prev) => {
      if (state) {
        return [...prev, data.id];
      }
      return prev.filter((x) => x !== data.id);
    });
  }, []);

  const customExpand: (item: ConfigItemTypeModel) => boolean = useCallback(
    (data) => {
      return listExpanded.includes(data.id);
    },
    [listExpanded],
  );

  // expand all right
  const [listExpandedRight, setListExpandedRight] = useState<number[]>([]);
  const [expandAllRight, setExpandAllRight] = useState(false);
  const onChangeExpandRight: (item: ConfigItemTypeModel, state: boolean) => void = useCallback((data, state) => {
    setListExpanded((prev) => {
      if (state) {
        return [...prev, data.id];
      }
      return prev.filter((x) => x !== data.id);
    });
  }, []);

  const customExpandRight: (item: ConfigItemTypeModel) => boolean = useCallback(
    (data) => {
      return listExpandedRight.includes(data.id);
    },
    [listExpandedRight],
  );

  return (
    <>
      <Flex align='stretch' justify='center' gap='sm'>
        <Box w={'100%'}>
          <KanbanButton
            mb={'sm'}
            onClick={() => {
              setListExpanded((_prev) => {
                if (expandAll) {
                  return [];
                }
                return ciTypes.data.map((x) => x.id);
              });
              setExpandAll(!expandAll);
            }}>
            {expandAll ? 'Collapse All' : 'Expand All'}
          </KanbanButton>
          <CiTypePickerComponent
            customListCiTypes={listTrees.ciTreePermissions}
            onChange={onChangeValue}
            isCheckbox={true}
            onCheckBox={onCheckboxCiType}
            dataCheckboxs={listSelectedCiType}
            scrollHeight={scrollHeightDefault}
            dataDisables={ciTypeIdInRights}
            onChangeExpand={onChangeExpand}
            customExpand={customExpand}
          />
        </Box>

        <Center w={'10%'}>
          <Flex direction='column'>
            <Box mx='sm'>
              <KanbanButton
                w={'100%'}
                mb={'xs'}
                disabled={!moveRight}
                onClick={() => {
                  setIsSelectionInLeft(true);
                  setListCiTypePermission([]);
                  openAddItem();
                }}
                rightSection={<IconArrowNarrowRight />}>
                Chuyển sang
              </KanbanButton>
            </Box>
            <Box mx='sm'>
              <KanbanButton
                w={'100%'}
                onClick={openConfirmMinus}
                disabled={!moveLeft || customListCiTypeInRights.length === 0}
                leftSection={<IconArrowNarrowLeft />}>
                Chuyển về
              </KanbanButton>
            </Box>
          </Flex>
        </Center>

        <Box w={'100%'}>
          <KanbanButton
            mb={'sm'}
            onClick={() => {
              setListExpandedRight((_prev) => {
                if (expandAllRight) {
                  return [];
                }
                return ciTypes.data.map((x) => x.id);
              });
              setExpandAllRight(!expandAllRight);
            }}>
            {expandAllRight ? 'Collapse All' : 'Expand All'}
          </KanbanButton>
          <CiTypePickerComponent
            customListCiTypes={customListCiTypeInRights}
            onChange={onChangeValueRight}
            isCheckbox={true}
            onCheckBox={onCheckboxCiTypeInRight}
            rightSection={rightSectionLeft}
            dataCheckboxs={listSelectedCiTypeInRight}
            scrollHeight={scrollHeightDefault}
            onChangeExpand={onChangeExpandRight}
            customExpand={customExpandRight}
          />
        </Box>
      </Flex>
      <KanbanModal
        size={'lg'}
        opened={openedAddItem}
        onClose={() => {
          setListSelectedCiType([]);
          closeAddItem();
        }}
        padding={10}
        centered
        title={'Setting item'}
        actions={
          <KanbanButton disabled={isButtonDisabled} onClick={addPermissionCiType}>
            Add
          </KanbanButton>
        }>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
          <KanbanMultiSelect
            data={permissionActionsMap[PermissionActionType.CI_TYPE]}
            label='Permissions'
            comboboxProps={{ position: 'top', middlewares: { flip: false, shift: false } }}
            value={isSelectionInLeft ? listCiTypePermission : nodeSelected?.action}
            searchable
            onChange={(e) => {
              if (isSelectionInLeft) {
                setListCiTypePermission(e);
                setNodeSelected((prev) => {
                  return {
                    ...prev,
                    action: e,
                  };
                });
              } else {
                setNodeSelected((prev) => {
                  return {
                    ...prev,
                    action: e,
                  };
                });
              }
            }}
          />
          <Flex gap={'xs'} align={'center'} justify={'center'}>
            {/* {listSelectedCiType.length === 1 && ( */}
            <KanbanButton label='Name' value='MANAGE' onClick={isSelectionInLeft ? copyFromParentInLeft : copyFromParentInRight} size='xs'>
              Copy Parent
            </KanbanButton>
            {/* )} */}
            <KanbanButton label='Name' value='MANAGE' onClick={openCopyOther} size='xs'>
              Copy Other
            </KanbanButton>
          </Flex>
        </div>
      </KanbanModal>

      <KanbanModal
        size={'lg'}
        opened={openedCopyOther}
        onClose={closeCopyOther}
        padding={10}
        centered
        title={'Setting item'}
        actions={<KanbanButton onClick={isSelectionInLeft ? copyOtherInLeft : copyOtherInRight}>Confirm</KanbanButton>}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
          <CiTypePickerComponent
            customListCiTypes={listTrees.ciTreePermissions}
            onChange={onChangeValueCopyOther}
            rightSection={sectionInRight}></CiTypePickerComponent>
        </div>
      </KanbanModal>

      <KanbanConfirmModal
        opened={openedConfirmMinus}
        onClose={closeConfirmMinus}
        title='Confirm delete permission'
        onConfirm={() => {
          minusNode();
        }}>
        All item child are also deleted ?
      </KanbanConfirmModal>

      <KanbanModal
        size={'100%'}
        opened={openedCIList}
        onClose={closeCIList}
        padding={10}
        centered
        title={'Setting CI'}
        actions={<KanbanButton onClick={confirmSettingCI}>Confirm</KanbanButton>}>
        <KanbanTable
          title='List CI'
          searchable={{
            enable: true,
            debounceTime: 300,
          }}
          selectableRows={{
            enable: true,
            customAction: (rows) => (
              <>
                <KanbanButton
                  c={'white'}
                  size='xs'
                  onClick={() => {
                    const ciIds = rows.map((ci) => ci.id);
                    setListCiIdSelected(ciIds);
                    openModalSettingPermissionCi();
                  }}>
                  Add permission
                </KanbanButton>
              </>
            ),
          }}
          columns={[
            {
              title: 'Name',
              name: 'name',
              width: '20%',
            },
            {
              title: 'Description',
              name: 'description',
              customRender: (data) => {
                return <KanbanText lineClamp={3}>{data}</KanbanText>;
              },
              width: '50%',
            },
          ]}
          data={listCI}
          actions={{
            customAction: (data) => (
              <div style={{ textAlign: 'left', width: '100%' }}>
                <KanbanMultiSelect
                  label='Select permission'
                  searchable
                  data={permissionActionsMap[PermissionActionType.CI]}
                  comboboxProps={{ position: 'top', middlewares: { flip: false, shift: false } }}
                  value={listCISetting.find((item) => item.ciId === data.id)?.action || []}
                  onChange={(value) => {
                    onChangeSelectPermisionCI([data.id], value);
                  }}
                />
              </div>
            ),
          }}
          pagination={{
            enable: true,
          }}
          serverside={{
            totalRows: totalRecords,
            onTableAffected(dataSet) {
              if (!equal(tableAffected, dataSet)) {
                setTableAffected(dataSet);
              }
            },
          }}
        />
      </KanbanModal>
      <KanbanModal
        size={'lg'}
        opened={openedModalSettingPermissionCi}
        onClose={closeModalSettingPermissionCi}
        padding={10}
        centered
        title={'Setting permission'}
        actions={
          <KanbanButton
            onClick={() => {
              onChangeSelectPermisionCI(listCiIdSelected, listCiPermission);
              closeModalSettingPermissionCi();
            }}>
            Add
          </KanbanButton>
        }>
        <KanbanMultiSelect
          label='Select permission'
          searchable
          data={permissionActionsMap[PermissionActionType.CI]}
          comboboxProps={{ position: 'top', middlewares: { flip: false, shift: false } }}
          defaultValue={listCiPermission}
          onChange={(value) => {
            setListCiPermission(value);
          }}
        />
      </KanbanModal>

      <KanbanModal
        size={'lg%'}
        opened={openedshowListPermissionOfCitype}
        onClose={() => {
          setListPermissionOfCiType([]);
          onCloseShowListPermissionOfCitype();
        }}
        title={'List permission'}
        padding={10}
        centered>
        <List
          spacing='xs'
          size='sm'
          center
          icon={
            <ThemeIcon color='teal' size={24} radius='xl'>
              <IconCircleCheck />
            </ThemeIcon>
          }>
          {listPermissionOfCiType.map((permission) => (
            <List.Item key={permission}>{permission}</List.Item>
          ))}
        </List>
      </KanbanModal>
    </>
  );
});
GroupSettingsPermissionModal.displayName = 'GroupSettingsPermissionModal';

export default GroupSettingsPermissionModal;
