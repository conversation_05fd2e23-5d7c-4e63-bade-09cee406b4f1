import React, { useCallback, useEffect, useState } from 'react';
import { CI_TYPE_ID_OF_SERVICE_MAP } from '@common/constants/CommonConstants';
import CiRelationship from '@pages/cis/ci/relationship/CiRelationship';
import ViewServiceModal from '@pages/cis/ci/graph/ViewServiceModal';
import { NotificationError } from '@common/utils/NotificationUtils';
import { ServiceMappingApi } from '@api/ServiceMappingApi';

type ImpactDataGraphViewDetailProps = {
  ciId: number;
  ciTypeId: number;
};

export const ImpactDataGraphViewDetail = (props: ImpactDataGraphViewDetailProps) => {
  const { ciId, ciTypeId } = props;
  const [currentServiceId, setCurrentServiceId] = useState(0);

  const fetchServiceInfo = useCallback(() => {
    if (ciId > 0) {
      ServiceMappingApi.getByCiId(ciId)
        .then((res) => {
          const serviceMapId = res.data.id;
          if (serviceMapId && serviceMapId > 0) {
            setCurrentServiceId(serviceMapId);
          } else {
            NotificationError({
              title: `Error`,
              message: 'Cannot find service map id',
            });
          }
        })
        .catch(() => {})
        .finally(() => {});
    }
  }, [ciId]);

  useEffect(() => {
    fetchServiceInfo();
  }, [fetchServiceInfo]);

  return (
    <>
      {ciTypeId === CI_TYPE_ID_OF_SERVICE_MAP && currentServiceId ? (
        <ViewServiceModal serviceMapId={currentServiceId} activeTab='2' />
      ) : (
        <CiRelationship ciId={ciId} ciTypeId={ciTypeId} isView isFromBusinessView={true} />
      )}
    </>
  );
};
export default ImpactDataGraphViewDetail;
