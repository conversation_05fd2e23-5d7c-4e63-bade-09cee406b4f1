import { configureStore } from '@reduxjs/toolkit';
import createSagaMiddleware from 'redux-saga';
import { countersSlice } from 'store/slices/CountersSlice';
import { currentUserSlice } from 'store/slices/CurrentUserSlice';
import { pageLoadingSlice } from 'store/slices/PageLoadingSlice';
import { ciTypesSlice } from 'store/slices/CiTypesSlice';
import { ciRelationshipTypesSlice } from '@slices/CiRelationshipTypesSlice';
import { ciTreePermissionsSlice } from '@slices/CiTreePermissionSlice';
import { ciHasPermissionsSlice } from '@slices/CiHasPermissionSlice';
import { systemParameterSlice } from '@slices/SystemParameterSlice';

const sagaMiddleware = createSagaMiddleware();

export const store = configureStore({
  reducer: {
    counters: countersSlice.reducer,
    currentUser: currentUserSlice.reducer,
    pageLoading: pageLoadingSlice.reducer,
    ciTypes: ciTypesSlice.reducer,
    ciRelationshipTypes: ciRelationshipTypesSlice.reducer,
    ciTreePermissions: ciTreePermissionsSlice.reducer,
    ciHasPermissions: ciHasPermissionsSlice.reducer,
    systemParams: systemParameterSlice.reducer,
  },
  middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(sagaMiddleware),
});
//sagaMiddleware.run(rootSaga);

export type RootStoreType = ReturnType<typeof store.getState>;

export const getDirectState = store.getState;
export const directDispath = store.dispatch;
export const runMiddleWare = (saga: any) => {
  sagaMiddleware.run(saga);
};
