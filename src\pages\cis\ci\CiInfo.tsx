import type { ConfigItemTypeAttrResponse } from '@api/ConfigItemTypeAttrApi';
import type { ConfigItemModel } from '@models/ConfigItem';
import type { ConfigItemAttrModel } from '@models/ConfigItemAttr';
import React from 'react';
import type { ConfigItemAttrCustomModel } from '@models/ConfigItemAttrCustom';
import { DataCiCompare } from '@models/CiDetailCompare';
import CiInfoDisplay from './CiInfoDisplay';

export type CiInfoProps = {
  ci: ConfigItemModel;
  ciTypeAttributes: ConfigItemTypeAttrResponse[];
  ciAttributes: Record<number, ConfigItemAttrModel>;
  ciAttributesCustom: ConfigItemAttrCustomModel[];
  hiddenCustomAttribute?: boolean;
  includeReferenceData?: boolean;
  onRemoveCiAttributeCustoms?: (ciAttributeCustom: ConfigItemAttrCustomModel) => void;
  hightLightChange?: boolean | false;
  //previewdci: hight light change
  differenceObject?: DataCiCompare | null;
};

export const CiInfo = (props: CiInfoProps) => {
  return (
    <>
      {/* 4763 new ci display info */}
      <CiInfoDisplay
        ci={props.ci}
        hightLightChange={props.hightLightChange}
        ciTypeAttributes={props.ciTypeAttributes}
        ciAttributes={props.ciAttributes}
        //previewdci: hight light change
        differenceObject={props.differenceObject}
        ciAttributesCustom={props.ciAttributesCustom}
        onRemoveCiAttributeCustoms={props.onRemoveCiAttributeCustoms}
        hiddenCustomAttribute={props.hiddenCustomAttribute}
        includeReferenceData={props.includeReferenceData}
      />
    </>
  );
};

export default CiInfo;
